# SegmentWithLoadingControl 使用示例

## 功能说明

SegmentWithLoadingControl 组件现在支持以下新功能：

1. **加载状态管理**：在用户点击切换时显示3秒的加载动画
2. **智能禁用逻辑**：加载期间整个组件被禁用，防止重复点击
3. **延迟回调**：onChange回调在加载完成后才触发
4. **内存安全**：组件卸载时自动清理定时器

## 使用方法

```jsx
import React, { Component } from 'react';
import { View } from 'react-native';
import SegmentWithLoadingControl from './car/widget/SegmentWithLoadingControl';

class ExamplePage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedIndex: 0,
      disabled: false
    };
  }

  handleSegmentChange = (index) => {
    console.log('分段切换完成，新索引:', index);
    // 这里的回调会在3秒加载完成后才触发
    this.setState({ selectedIndex: index });
  };

  render() {
    return (
      <View style={{ flex: 1, padding: 20 }}>
        <SegmentWithLoadingControl
          title="选择模式"
          values={['模式一', '模式二', '模式三']}
          tipsArray={['这是模式一的说明', '这是模式二的说明', '这是模式三的说明']}
          selectedIndex={this.state.selectedIndex}
          onChange={this.handleSegmentChange}
          disabled={this.state.disabled}
          style={{ marginTop: 20 }}
        />
      </View>
    );
  }
}

export default ExamplePage;
```

## 新增功能详解

### 1. 加载状态
- 用户点击任何分段时，在该分段内显示loading图标
- loading图标显示在文字左边，间隔8像素
- loading图标大小为40x40像素，带有旋转动画
- loading动画持续3秒钟

### 2. 防重复点击
- loading期间，所有点击事件都会被忽略
- 确保用户体验的流畅性

### 3. 立即回调
- onChange回调立即触发，不延迟
- 点击后立即切换到新选项
- 在新选项中显示loading状态3秒

### 4. 组件状态管理
- 新增 `isLoading` 状态用于控制加载视图
- 只有当前选中的分段会显示loading图标
- 与原有的 `disabled` 属性配合工作
- loading时组件自动禁用，loading结束后根据disabled属性决定最终状态

### 5. 视觉效果
- 使用自定义loading图标：`projects/com.xiaomi.cariot.gesture/Resources/Main/ic_loader.png`
- 图标以1秒为周期持续旋转
- 图标位于文字左侧，保持良好的视觉平衡

## 注意事项

1. **定时器管理**：组件会自动清理定时器，无需手动处理
2. **状态同步**：确保父组件的selectedIndex与组件内部状态保持同步
3. **样式兼容**：loading视图使用半透明背景，不会影响原有样式
4. **性能优化**：使用PureComponent，避免不必要的重渲染
