# 小米车载摄像头项目功能文档

## 项目概述

`com.xiaomi.cariot.camera` 是一个基于 React Native 开发的小米车载摄像头智能插件，支持手机端和车机端双平台运行，提供实时监控、AI智能检测、存储管理等核心功能。

## 项目信息

- **项目名称**: com.xiaomi.cariot.camera
- **版本**: 1.0.28
- **最低SDK版本**: 10080
- **支持平台**: Android、iOS、车机系统

## 项目结构

### 入口配置
- **手机端入口**: `Main/index.js` - 完整功能版本
- **车机端入口**: `Main/CarIndex.js` - 车机优化版本
- **平台判断**: 根据 `Package.entryInfo.mobileType` 自动选择对应入口

### 核心模块

#### 1. 直播监控模块 (`live/`)
- **LiveVideoPageV3.js**: 主要直播页面，支持实时视频流
- **MainPage.js**: 首页，集成多种功能入口
- **AudioVideoCallPage.js**: 音视频通话功能

**功能特性**:
- ✅ 实时视频流播放
- ✅ 双向语音通话
- ✅ 视频录制和截图
- ✅ 云台控制（上下左右转动）
- ✅ 多种视频编码支持
- ✅ 全屏播放模式
- ✅ 画质调节

#### 2. AI智能检测模块 (`aicamera/`)
- **AICameraSettingsV2.js**: AI功能设置页面
- **MotionDetectionPage.js**: 移动检测设置
- **OnTimeAlarmPage.js**: 定时报警设置
- **BabyCrying.js**: 婴儿哭声检测
- **PetIdentification.js**: 宠物识别
- **GestureCallPage.js**: 手势呼叫功能

**支持的AI功能**:
- ✅ 人形检测
- ✅ 人脸识别
- ✅ 移动检测
- ✅ 婴儿哭声检测
- ✅ 异常声音检测
- ✅ 手势识别
- ✅ 宠物识别
- ✅ 表情识别
- ✅ 区域入侵检测

#### 3. 存储管理模块 (`allVideo/`)
- **AllStorage.js**: 统一存储管理页面

**支持存储类型**:
- ✅ 云存储 (Cloud Storage)
- ✅ 本地存储 (Local Storage) 
- ✅ SD卡存储 (SD Card Storage)

**功能特性**:
- ✅ 视频回放
- ✅ 文件下载
- ✅ 批量删除
- ✅ 存储空间管理
- ✅ 智能存储策略
- ✅ 缩略图预览

#### 4. 设置模块 (`setting/`)
- **Setting.js**: 主设置页面
- **CameraSetting.js**: 摄像头参数设置
- **StorageSetting.js**: 存储设置
- **SDCardSetting.js**: SD卡设置
- **WXCallSetting.js**: 微信通话设置
- **SurveillancePeriodSettingV2.js**: 监控时段设置

#### 5. 车机专用模块 (`car/`)
- **MainCarPage.js**: 车机主页面
- **CarStorage.js**: 车机存储管理
- **针对车机优化的UI和交互**

### 核心工具类

#### 网络通信
- **API.js**: HTTP API 请求封装
- **RPC.js**: 设备 RPC 通信

#### 设备控制
- **CameraPlayer.js**: 摄像头控制核心类
- **CameraConfig.js**: 设备配置管理
- **AlarmUtilV2.js**: 报警功能工具类

#### 存储管理
- **SdFileManager.js**: SD卡文件管理
- **AlbumHelper.js**: 相册管理
- **CloudEventLoader.js**: 云存储事件加载

#### 辅助工具
- **LogUtil.js**: 日志管理
- **TrackUtil.js**: 埋点统计
- **VersionUtil.js**: 版本兼容性处理
- **Util.js**: 通用工具函数

## 主要功能特性

### 1. 实时监控
- 高清视频流播放（支持多种编码格式）
- 双向语音通话
- 云台远程控制
- 实时截图录像
- 多设备同时观看
- 网络自适应码率

### 2. AI智能检测
- 人形/人脸检测
- 移动物体检测
- 声音异常检测
- 婴儿哭声识别
- 手势识别控制
- 宠物识别
- 表情识别
- 区域入侵检测

### 3. 存储管理
- 多种存储方式支持
- 云存储自动备份
- SD卡本地存储
- 智能存储策略
- 文件批量管理
- 存储空间优化

### 4. 车机适配
- 车机专用UI界面
- 触控操作优化
- 车载环境适配
- 简化功能布局
- 温度保护机制

### 5. 设备管理
- 多设备型号支持
- 固件版本兼容
- 网络状态监控
- 设备状态同步
- 离线缓存机制

## 技术架构

### 前端技术栈
- **React Native**: 跨平台移动应用框架
- **React Navigation**: 页面路由管理
- **MIOT SDK**: 小米IoT设备SDK

### 第三方依赖
```json
{
  "base64-js": "^1.2",
  "dayjs": "^1.10.5",
  "micariot-ui-sdk": "file:../../micariot-ui-sdk",
  "patch-package": "^6.4.7",
  "react-native-canvas": "^0.1.37",
  "react-native-image-pan-zoom": "2.1.12",
  "react-native-image-zoom-viewer": "^2.2.27",
  "react-native-parsed-text": "0.0.22",
  "react-native-root-toast": "^3.2.0",
  "react-native-scrollable-tab-view": "^1.0.0"
}
```

### 通信协议
- **P2P通信**: 设备直连通信
- **HTTP API**: 云端服务接口
- **WebSocket**: 实时数据推送
- **RPC**: 远程过程调用

### 存储方案
- **云存储**: 小米云服务
- **本地存储**: 设备本地缓存
- **SD卡**: 外部存储扩展

## 开发指南

### 环境要求
- Node.js >= 12.0
- React Native >= 0.60
- MIOT SDK >= 10080

### 安装依赖
```bash
cd com.xiaomi.cariot.camera
npm install
```

### 启动项目
```bash
npm start
```

### 构建发布
```bash
npm run publish
```

## 平台支持
- ✅ Android 手机端
- ✅ iOS 手机端  
- ✅ 车机系统
- ✅ 平板设备

## 版本历史
- **v1.0.28**: 当前版本，支持完整功能
- 支持SDK API Level 10080

## 注意事项
1. 车机端功能相对简化，专注核心监控功能
2. AI功能需要设备固件支持
3. 云存储功能需要VIP服务
4. 部分功能需要网络连接

---
*最后更新时间: 2024年12月*
