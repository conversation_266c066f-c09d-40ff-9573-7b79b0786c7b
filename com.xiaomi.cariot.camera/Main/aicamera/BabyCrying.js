/**
 * @name liuxu
 * @desc ~/code/xiaomi/rn-plugin/miot-workspace/projects/com.xiaomi.cardcamera/Main/aicamera/BabyCrying.js
 * <AUTHOR>
 * @time 2022年06月27日 15:18:47 星期一
 * @param {Object} {}
 * @return  {*}
 */
import React from 'react';
import AIcard from '../components/AICard.js';
import { localStrings as LocalizedStrings } from '../../Main/MHLocalizableString';
import { NavigationBar, MessageDialog } from 'mhui-rn';
import { ListItemWithSwitch } from 'miot/ui/ListItem';
import TrackUtil from '../util/TrackUtil';
import Toast from '../components/Toast';
import { Device, Service } from 'miot';
import API from '../API';
import CameraConfig from '../util/CameraConfig';
import { CAMERA_ALARM, CAMERA_ALARM_051 } from '../Constants';
import StorageKeys from '../StorageKeys';

class BabyCrying extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      babyCrySwitch: false,
      isVip: false
    };
    this.is051 = CameraConfig.Model_chuangmi_051a01 == Device.model;
    this.isBabyCrySpec = /** Device.model === CameraConfig.Model_chuangmi_022 || CameraConfig.isCamera039(Device.model)* */ CameraConfig.isXiaomiCamera(Device.model) || this.is051;// 022走云端，039、049识别成功率低 改成了云端；目前只有c01a02的宝宝哭声开关走spec，051也走spec
    this.shouldDisableBabyCry = false;
    this.isBabyCrySpec = /** Device.model === CameraConfig.Model_chuangmi_022 || CameraConfig.isCamera039(Device.model)* */ CameraConfig.isXiaomiCamera(Device.model) || this.is051;// 022走云端，039、049识别成功率低 改成了云端；目前只有c01a02的宝宝哭声开关走spec，051也走spec
  }
  componentDidMount() {
    this.props.navigation.setParams({
      // show:true
      title: LocalizedStrings['as_baby_cry'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    // 宝宝哭声部分设备的
    // API.get('/miot/camera/app/v1/get/alarmSwitch', 'business.smartcamera').then((res) => {
    if (this.isBabyCrySpec) {
      Service.spec.getPropertiesValue([this.is051 ? CAMERA_ALARM_051[0] : CAMERA_ALARM[0]], 2)
        .then((result) => {
          console.log(result);
          if (result instanceof Array && result.length >= 1) {
            let htValue = result[0].value;
            console.log(`why!, htValue=${ htValue }`);
            this.setState({ babyCrySwitch: htValue });
          }
        })
        .catch((error) => {
          Toast.fail('c_get_fail', error);
        });
    }
    StorageKeys.IS_VIP_STATUS.then((vipStatus) => {
      StorageKeys.IN_CLOSE_WINDOW.then((windowStatus) => {
        if (vipStatus || windowStatus) {
          this.setState({ enableFaceManager: true, isVip: vipStatus });
        }
      });
    }).catch((err) => {
    });
  }
  componentWillMount() {
        
  }
  _onBabyCrySwitchValueChange(value) {

    this.shouldDisableBabyCry = false;

    value ?
      TrackUtil.reportResultEvent("AIsettings_BabyCrying_Status", "type", 1)
      :
      TrackUtil.reportResultEvent("AIsettings_BabyCrying_Status", "type", 2);

    Toast.loading('c_setting');
    if (this.isBabyCrySpec) {
      Service.spec.setPropertiesValue([this.is051 ? { ...CAMERA_ALARM_051[0], value: value } : { ...CAMERA_ALARM[0], value: value }])
        .then((result) => {
          let success = result[0].code == 0;
          if (success) {
            this.setState({
              babyCrySwitch: value
            });
            Toast.success('c_set_success');
          } else {
            this.setState({
              babyCrySwitch: this.state.babyCrySwitch
            });
            Toast.fail('c_set_fail');
          }
        })
        .catch((error) => {
          this.setState({
            babyCrySwitch: this.state.babyCrySwitch
          });
          Toast.fail('c_set_fail');
        });
    } else {
      API.post('/miot/camera/app/v1/put/babyCrySwitch', 'business.smartcamera', {
        open: value
      }).then((res) => {
        this.setState({ babyCrySwitch: res.code == 0 ? value : !value });
        if (res.code == 0) {
          Toast.success('c_set_success');
          if (CameraConfig.Model_chuangmi_026c02 == Device.model && !this.state.isVip && !value) { // 026c02 海外 主动关闭了宝宝哭声开关，就要刷新  是否要主动退出??
            this.shouldDisableBabyCry = true;
          }
        } else {
          Toast.fail('c_set_fail');
        }
      }).catch((err) => {
        this.setState({ babyCrySwitch: !value });
        Toast.fail('c_set_fail', err);
      });
    }
  }
  render() {
    let babyCry = (
      <ListItemWithSwitch
        showSeparator={false}
        title={LocalizedStrings['as_baby_cry']}
        value={this.state.babyCrySwitch}
        onValueChange={(value) => this._onBabyCrySwitchValueChange(value)}
        titleStyle={{ fontWeight: 'bold' }}

      />
    );
    return (
      <AIcard
        img={require('../../Resources/Images/faceRecognition/babyCryPic.webp')}
        desc={LocalizedStrings['ai_baby_cry_desc']}
        switchButton={babyCry}/>
    );
  }

}

export default BabyCrying;