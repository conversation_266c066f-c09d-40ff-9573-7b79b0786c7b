import React from 'react';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlbumHelper from '../util/AlbumHelper';
import Toast from '../components/Toast';
import { StyleSheet, View, Text, TouchableWithoutFeedback, TouchableOpacity, Image, BackHandler, StatusBar, Platform, SafeAreaView, ActivityIndicator, Modal, PermissionsAndroid } from 'react-native';
import NavigationBar from "miot/ui/NavigationBar";
import { Host, PackageEvent, DarkMode, Service, System } from "miot";
import { MessageDialog, ProgressDialog } from "miot/ui/Dialog";
import CommonMsgDialog from '../ui/CommonMsgDialog';
import LinearGradient from 'react-native-linear-gradient';
import Video from "react-native-video";
import ImageButton from "miot/ui/ImageButton";
import Slider from "react-native-slider";
import Orientation from 'react-native-orientation';
import { Dimensions } from "react-native";
import { IMG_DARKMODE_TINT } from '../util/CameraConfig';
import CameraConfig from '../util/CameraConfig';
import { DescriptionConstants } from '../Constants';
import vi from 'miot/resources/strings/vi';
import Singletons from '../framework/Singletons';
import { DldStatus } from '../framework/EventLoaderInf';
// import Service from 'miot/Service';
import AlarmUtil from '../util/AlarmUtil';
import { Device } from 'miot/device';
import DldMgr from '../framework/DldMgr';
import DailyStoryEvLdr from '../framework/DailyStoryLoader'
import DailyStorySingletons from "../framework/DailyStorySingletons";
import { BaseStyles } from '../BasePage';
import LogUtil from '../util/LogUtil';
import StatusBarUtil from "../util/StatusBarUtil";
const iconSize = 40; // 图标尺寸
const TAG = "DailyStoryVideoViewPage";

export default class DailyStoryVideoViewPage extends React.Component {

  static navigationOptions = ({ navigation }) => {
    const { titleProps } = navigation.state.params || {};
    if (!titleProps) {
      return { header: null };
    }

    return {
      headerTransparent: true,
      header: <View style={{ width: 0, height: 0 }} />
    };
  };
  constructor(props) {
    super(props);
    this.dateTime = new Date();
    this.receiverMap = new Map();
    this.receiveUrlArray = [];
    this.deleteIndex = -1;
    this.dialogDeleteContent = LocalizedStrings["delete_title"];
    this.title = null;
    this.videoTitle = null;
    this.video = null;
    this.duration = 0;
    this.mLastInactiveTime = 0;
    /*   this.dataMapValue = {
          path: '',
          name: ''
      } */
    this.state = {
      videoPath: null,
      // showLoadingDialog: true,
      deleteDialogVisible: false,
      isFullscreen: false,
      showPlayToolBar: true,
      startTimeStr: "",
      endTimeStr: "",
      progress: 0,
      isPlaying: false,
      playSpeed: 1.0,
      isMute: CameraConfig.getUnitMute(),  // 是否静音
      showLoading: true,
      duration: 100,
      step: 1,
      visProgress: false,
      downloadProgress: 0,
      showDownloadHint: undefined,
      aProgress: 102,
      isSeeking: false,
      showPermissionDialog: false,
      showToolbar: true
    };
    this.mDldInfo = {};
    this.isPluginForeGround = true;// sdcard页面不存在跳到其他页面的情况
    this.isPageForeGround = false;

    // // 每日故事下载监听
    // this.mDldL = DailyStoryDldMgr.addListener((aStatus) => {
    //   // console.log(this.tag, "dldL", aStatus);
    //   // if ("status" == aStatus.type) {
    //   //   Toast.show(aStatus.detail);
    //   // }
    //   this.onDldProgress(aStatus);
    // });

    // see https://blog.csdn.net/u011068702/article/details/83218639
    // 当前页面第一次加载的时候会调用一次
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        console.log("will focus");
        this.isPageForeGround = true;
        this.isPluginForeGround = true;
        if (this.duration != 0) {
          this.setState({ isMute: true, isPlaying: true });// here ignore
        }
        this.restoreOri();
        console.log("this.props.navigation.state.params.preOri:", this.props.navigation.state.params.preOri);
      }
    );
    // 指当前页面离开的时候会调用一次(前提是当前页面没有被销毁既没有执行componentWillUnmount()函数)
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'willBlur',
      () => {
        console.log("did blur");

        this.isPageForeGround = false;
        if (this.duration != 0) {
          this.setState({ isMute: true, isPlaying: false });
        }
      }
    );

    // 重新开始 
    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }

      this.isPluginForeGround = true;// rnactivity调用了onresume
      this.restoreOri();
      let interval = new Date().getTime() - this.mLastInactiveTime;
      console.log("==========",this.mLastInactiveTime,interval);
      if (Platform.OS == "ios" && interval > (1000 * 60)) {
        let videoAddress = this.state.videoPath;
        this.setState({ videoPath: null, progress: 0 },() => {
          console.log("==========");
          this.setState({ isMute: this.state.playSpeed != 1 ? this.state.isMute : CameraConfig.getUnitMute(), isPlaying: this.isLocalPlaying, videoPath: videoAddress },() => {
            console.log("+++++++++++",this.state.videoPath);
          });
        });

      } else {
        if (this.duration != 0) {
          this.setState({ isMute: this.state.playSpeed != 1 ? this.state.isMute : CameraConfig.getUnitMute(), isPlaying: this.isLocalPlaying });// here ignore
        }
      }

      // this.setState({ isMute: true, isPlaying: true });
    });

    // 即将暂停
    this.willPauseListener = PackageEvent.packageWillPause.addListener(() => {

      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      this.isPluginForeGround = false;// rnactivity调用了onpause
      this.isLocalPlaying = this.state.isPlaying;
      if (this.duration != 0) {
        this.setState({ isMute: true, isPlaying: false });
      }
      this.mLastInactiveTime = new Date().getTime();
    });
    if (Platform.OS == "ios") {
      this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.isPluginForeGround = true;// rnactivity调用了onresume
        if (this.duration != 0) {
          this.setState({ isMute: CameraConfig.getUnitMute(), isPlaying: this.isLocalPlaying });
        }
      });

      this.willDisappearListener = PackageEvent.packageViewWillDisappearIOS.addListener(() => {
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.isPluginForeGround = false;// rnactivity调用了onpause
        this.isLocalPlaying = this.state.isPlaying;
        if (this.duration != 0) {
          this.setState({ isMute: true, isPlaying: false });
        }
        this.mLastInactiveTime = new Date().getTime();
      });
    }
    this.mOri = "PORTRAIT";
    this.destroyed = false;
    this.lastTimePlayBtnPressed = 0;
    // this.mLoader = Singletons.CloudEventLoader;
    // this.mLoader = DailyStorySingletons.DailyStoryLoader;
    this.mLoader = Singletons.DailyStoryLoader;

  }
  // 设置导航
  setNavigation() {
    this.props.navigation.setParams({
      titleProps: {
        title: LocalizedStrings["s_photo_album"],
        type: NavigationBar.TYPE.DARK,
        left: [
          {
            key: NavigationBar.ICON.BACK,
            onPress: () => { this.onBack(); }
          }
        ],
        isFullscreen: this.state.isFullscreen
      }
    });
  }


  componentDidMount() {
    // const videoUrl = "https://www.bilibili.com/video/BV1R64y1U7MG?share_medium=android&share_plat=android&share_source=WEIXIN&share_tag=s_i&timestamp=1641970567&unique_k=yiIoNxA"
    this.setNavigation();
    CameraConfig.lockToPortrait();// 切换回竖屏
    // if(this.props.navigation.state.params.videoName){
    //   this.title = this.props.navigation.state.params.videoName
    // }
    if (this.props.navigation.state.params && this.props.navigation.state.params.url) {
      this.currentIndex = -1;
      // this.mType = this.props.navigation.state.params.item;
      this.mUrl = this.props.navigation.state.params.url;
      this.mCategory = this.props.navigation.state.params.category;
      this.mCallback = this.props.navigation.state.params.callback;
      // console.log("this.mCallback:",this.mCallback(this.mCategory))
      // console.log("this.mCategory:",this.mCategory)
      // console.log("看看这里的mUrl：", this.mUrl)
    } else if (this.props.navigation.state.params && this.props.navigation.state.params.index) {
      this.currentIndex = this.props.navigation.state.params.index;
    } else {
      this.currentIndex = 0;// 默认播放最后一个视频
    }

    this.currentIndex = 0;
    let videoItem = this.props.navigation.state.params.item;
    let category = this.props.navigation.state.params.category;
    console.log("this.props.navigation.state.params.category:",category);
    videoItem = Object.assign(videoItem, { path: this.mUrl });
    console.log("videoItem=", JSON.stringify(videoItem));
    this.title = AlbumHelper.getDailyTitleName(category == 1 ? LocalizedStrings["family"] : LocalizedStrings["pet"], videoItem.createTime);
    this.videoTitle = AlbumHelper.getDailyFileName(category == 1 ? "Daily_Family_" : "Daily_Pet_", videoItem.createTime);
    this.setState({ currentIndex: this.currentIndex, videoPath: videoItem, isPlaying: true, showLoading: true });
    setTimeout(() => { this.setState({ isPlaying: false }); });

    this.downloadItem = { duration: Number.parseInt(videoItem.duration), fileId: videoItem.fileId, mediaType: "dailystory", createTime: videoItem.createTime, imgStoreUrl: videoItem.imgUrl, videoUrl: this.videoTitle, playCfg: { loader: this.mLoader } };// 缩略图，下载路径，创建时间，fileId

    if (Platform.OS === "android") {
      BackHandler.addEventListener('hardwareBackPress', this.onBack);
    }
    this.restoreOri();
    Orientation.addOrientationListener(this._orientationListener);

    let winWidth = Dimensions.get('window').width;
    let winHeight = Dimensions.get('window').height;
    this.screenWidth = Math.min(winWidth, winHeight);
    this.videoHeight = this.screenWidth * 9 / 16;

    let colorScheme = DarkMode.getColorScheme();
    if (colorScheme == 'dark') {
      this.darkMode = true;
    } else {
      this.darkMode = false;
    }
  }

  componentWillUnmount() {
    this.destroyed = true;
    this.toPortrait();
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBack);
    }
    Orientation.removeOrientationListener(this._orientationListener);
    this.didFocusListener.remove();
    this.didBlurListener.remove();
    this.didResumeListener.remove();
    this.willPauseListener.remove();
    if (this.willAppearListener) {
      this.willAppearListener.remove();
    }
    if (this.willDisappearListener) {
      this.willDisappearListener.remove();
    }
    clearTimeout(this.timeout);
    this.mDldL?.remove();
    if (this.mDldInfo && this.mDldInfo['share']) {
      // 分享每日故事的视频处于下载中
      this.mLoader.cancelDownload(this.downloadItem, this);
    }


  }

  // 返回
  onBack = () => {
    if (this.state.isFullscreen) {
      this.toPortrait();
      return true;
    }
    // console.log("this.mCallback:",this.mCallback(this.mCategory))
    this.mCallback(this.mCategory);
    this.props.navigation.goBack();
    return true;
  }
  // 删除
  deleteImgClick() {
    // if (Platform.OS == "ios") {
    //   this.confirmDelete();
    // } else {
      this.setState({
        deleteDialogVisible: true
      });
    // }
  }
  // 下载
  downLoadClick(aForShare = false) {
    console.log("看看fileId:", this.state.videoPath.fileId)
    // 下载权限
    if (Platform.OS === "android") {
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this._realStartDownloadFiles(this.videoTitle, aForShare);// 指定路径
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            !this.destroyed && Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
          console.log('操作失败：', error)
          !this.destroyed && Toast.success("action_failed");
          LogUtil.logOnAll(TAG, "action_failed error:", JSON.stringify(error));
        });
    } else {
      // no ios's photos const use hardcode
      System.permission.request("photos").then((res) => {
        this._realStartDownloadFiles(this.videoTitle, aForShare);
      }).catch((error) => {
        LogUtil.logOnAll(TAG, "System permission error:", JSON.stringify(error));
        !this.destroyed && this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
      });
    }
  }

  _realStartDownloadFiles(dailyStoryVideoPath, isForShare) {
    let basePath = Host.file.storageBasePath;
    let path = `${basePath}/${dailyStoryVideoPath}`;
    this.mDldInfo["name"] = dailyStoryVideoPath;
    this.mDldInfo["path"] = path;
    this.mDldInfo["share"] = isForShare;
    if (!isForShare) {
      DldMgr.addDld([this.downloadItem], this.mLoader);
      this.setState({ showDownloadHint: true });
      this.timeout = setTimeout(() => {
        this.setState({ showDownloadHint: false });
      }, 5000);
    } else {
      Host.file.isFileExists(dailyStoryVideoPath).then((result) => {
        console.log("isFileExists success", result);
        if (result) {
          this.onDldProgress(DldStatus.Complete);
        } else {
          console.log("isFileExists failed");
          this.mLoader.downloadByUrl(this.downloadItem.fileId, this.state.videoPath?.path, path, this);
          // 这里是直接下载，给分享使用的，需要监听下载进度，UI刷新，并在下载完成时，调用分享
        }
      }).catch(() => {
        console.log("isFileExists failed catch");
        this.mLoader.downloadByUrl(this.downloadItem.fileId, this.state.videoPath?.path, path, this);
        // 这里是直接下载，给分享使用的，需要监听下载进度，UI刷新，并在下载完成时，调用分享
      });
    }
  }

  onDldCancelResult() {

  }

  // 完成进度
  onDldProgress(aProgress) {
    console.log("DailyStoryVideoViewPage", TAG, "onDldProgress", aProgress, "for", this.mDldInfo["path"], "name", this.mDldInfo["name"]);
    this.setState({
      aProgress: aProgress < 101 ? aProgress : 100,
      showToolbar: aProgress < 101 ? false : true
    });
    if (this.mDldInfo["path"]) {
      switch (aProgress) {
        case DldStatus.Complete:
          if (this.mDldInfo["share"]) {
            // Host.ui.openSystemShareWindow(this.mDldInfo["path"]);
            this.doShareImage();
          } else {
            console.log("看看保存地址:", this.mDldInfo["path"])
            console.log("看看保存 :", this.mDldInfo["path"])
            AlbumHelper.saveToAlbum(this.mDldInfo["name"], true)
              .then((result) => {
                console.log("saveToAlbum res=", result); 
                Toast.success('save_success');
                LogUtil.logOnAll(TAG, "saveToAlbum res=", JSON.stringify(result));
              })
              .catch((error) => {
                // 由于 第一次下载之后 关掉内存权限 进行再次下载  再次获取权限下载 会弹出保存失败对话框
                // Toast.success('save_faild');
                console.log("saveToAlbum err=", error);
                LogUtil.logOnAll(TAG, "saveToAlbum  error=", JSON.stringify(error));
              });
          }
          // this.mDldInfo = {};
          break;
        case DldStatus.Err:
          this.mDldInfo = {};
          this.setState({
            showToolbar: true
          })
          Toast.fail('camera_play_error_file');
          break;
        default:
          break;
      }
    }
  }

  doShareImage =() => {
    // todo
    if (this.state.isFullscreen) {
      this.isToShare = true;
      this.toPortrait();
    } else {
      Host.ui.openSystemShareWindow(this.mDldInfo["path"]);// 调用系统的分享界面。
    }

  }

  // 分享 但downLoadClick参数为true时 为分享  false为下载 废弃
  shareImage() {
    console.log("触发分享了吗？")
    this.downLoadClick(true);
    this.setState({
      showToolbar: false
    })
  }
  // 确认删除
  confirmDelete() {
    // let url = this.state.videoPath != null ? this.state.videoPath.url : null;
    // let url = (this.state.videoPath == null ? "" : this.state.videoPath.path);
    let fileId = (this.state.videoPath == null ? "" : this.state.videoPath.fileId);
    // let url = this.state.videoPath;

    console.log("这里的fileId有木有：", fileId)
    if (fileId == null) {
      console.log('没有url吗？')
      return;
    }
    AlbumHelper.checkPermission()
      .then(() => {
        // let params = { "did": Device.deviceID, "region": Host.locale.language.includes("en") ? "US" : "CN", "fileIds": this.state.videoPath.fileId };
        AlarmUtil.deleteDailyStoryVideo({ fileIds: { fileIds: [this.state.videoPath.fileId] } }).then((res) => {
          console.log(`deleteDailyStoryVideo ${JSON.stringify(res)}`);
          if (res.code == 0) {
            Toast.success("delete_success");
          }
          this.toPortrait();
          this.mCallback(this.mCategory);
          this.props.navigation.goBack();
          if (this.props && this.props.navigation.state.params.mCbDeleted) {
            this.props.navigation.state.params.mCbDeleted();
          }
        }).catch((err) => {
          if (Platform.OS != "android") {
            return;
          }
          console.log(`deleteDailyStoryVideo ${JSON.stringify(err)}`);
          Toast.fail("delete_failed", err);
          LogUtil.logOnAll(TAG, `deleteDailyStoryVideo ${JSON.stringify(err)}`);
        });
      })
      .catch((result) => {
        if (result == PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
          this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
        } else {
          Toast.success("camera_no_write_permission");
        }
      });
    // AlbumHelper.deleteAlbumFilesByUrl([url])
    //   .then(() => {
    //     Toast.success("delete_success");

    //     // 仿照android的逻辑，点删除了 退出吧。。。相册流览图控件刷新数据很慢。。。。
    //     this.toPortrait();
    //     this.props.navigation.goBack();
    //     if (this.props && this.props.navigation.state.params.mCbDeleted) {
    //       this.props.navigation.state.params.mCbDeleted();
    //     }
    //   })
    //   .catch((error) => {
    //     // console.log(error);
    //     Toast.fail("delete_failed", error);
    //   });
  }

  // 进度条
  onSlidedProgress(value) { // 0-duration
    // todo
    // 换算时间
    // let seekTime = Number.parseInt(this.duration * value / 100);// 查询时间
    !this.destroyed && this.video && this.video.seek(value);// 拖拽过去
    let progressNum = Number.parseInt(value);
    this.setState((state) => {
      return {
        progress: progressNum,
        isSeeking: true
      };
    }, () => { // 数据塞给listview了，渲染还需要时间吧，延迟一会再执行scroll
      this.updateTimeStr();
    });
  }
  // 拖拽过程中修改开始时间
  handleSeek(data) {
    if (this.state.isSeeking) {
      return; // seek中时，不响应progress的改变
    }
    let cur = new Date().getTime();
    let mMinClickInterval = 1500;
    if (this.state.progress > 0) {
      mMinClickInterval = 500;
    }
    if (cur - this.lastTimePlayBtnPressed < mMinClickInterval) {
      return;
    }
    this.setState({ progress: data });
    this.updateTimeStr();
  }
  // 视频播放过程中每个间隔进度单位（通常不足一秒，你可以打印日志测试下）调用的回调，其中包含有关媒体当前正在播放的位置的信息。
  onProgress = (data) => {
    let progress = data.currentTime;
    let progressNum = Number.parseInt(progress);
    if (this.state.progress > progressNum) { // 防止进度条抖动
      return;
    }
    if (this.state.isSeeking) {
      return; // seek中时，不响应progress的改变
    }
    this.setState({ progress: progressNum });
    this.updateTimeStr();
    // this.setState({ currentTime: data.currentTime });
  };

  // 播放失败后的回调 加载错误的时候
  onError = (error) => { // 修改进度条
    Toast.fail("action_failed", console.log("看看是不是这里的问题？", error));
    this.setState({ isPlaying: false });
    this.updateTimeStr();
    LogUtil.logOnAll(TAG, `onError${JSON.stringify(error)}`);
  }

  // 播放结束的时候 播放完成后的回调 
  onEnd = () => {
    this.setState({ isPlaying: false, progress: 0 });
    // 无论是安卓还是IOS都重新回到原点
    !this.destroyed && this.video && this.video.seek(0);
    // if (Platform.OS == "android") {
    //   // // 设置视频播放的位置（从0秒开始）
    //   !this.destroyed && this.video && this.video.seek(0);
    // }
    this.updateTimeStr(true);
  }

  // 加载 加载媒体并准备播放时调用的回调函数
  onLoad = (info) => {
    let duration = info.duration;// seconds
    this.duration = duration;

    this.updateTimeStr();
    this.setState({ isPlaying: true, duration: duration, showLoading: false });
  }

  onSeekComplete = (info) => {
    //这里seek成功
    this.setState({ isSeeking: false });
  }

  // 每一次改变进度条或者开始、结束 都要执行更新进度条时间函数
  updateTimeStr(isEnd = false) {
    let currentTime = Number.parseInt(this.state.progress);
    if (isEnd) {
      currentTime = 0;
    }
    this.dateTime.setTime(currentTime * 1000);
    let startMinute = this.dateTime.getMinutes();
    let startSeconds = this.dateTime.getSeconds();
    if (isNaN(startMinute)) {
      startMinute = 0;
    }
    if (isNaN(startSeconds)) {
      startSeconds = 0;
    }
    let beginStr = `${startMinute > 9 ? startMinute : (`0${startMinute}`)}:${startSeconds > 9 ? startSeconds : (`0${startSeconds}`)}`;

    this.dateTime.setTime(this.duration * 1000);
    startMinute = this.dateTime.getMinutes();
    startSeconds = this.dateTime.getSeconds();
    let endStr = `${startMinute > 9 ? startMinute : (`0${startMinute}`)}:${startSeconds > 9 ? startSeconds : (`0${startSeconds}`)}`;
    this.setState({ startTimeStr: beginStr, endTimeStr: endStr });
  }

  // 切换播放
  togglePlay(isPlay) {
    let cur = new Date().getTime();
    let mMinClickInterval = 1500;
    if (this.state.progress > 0) {
      mMinClickInterval = 500;
    }
    if (cur - this.lastTimePlayBtnPressed < mMinClickInterval) {
      return;
    }
    this.lastTimePlayBtnPressed = cur;
    console.log('toggle');
    if (Platform.OS == "ios" && this.state.progress == 0) {
      !this.destroyed && this.video && this.video.seek(0);
    }
    this.setState({ isPlaying: isPlay });
  }

  // 切换音量
  _toggleAudio(isMute) {
    this.setState({ isMute: isMute });
    CameraConfig.setUnitMute(isMute);
  }

  // 切换速度
  _toggleSpeed(speedRate) {
    this.setState({ playSpeed: speedRate });
    if (speedRate == 2) {
      this.setState({ isMute: true });
    } else {
      this.setState({ isMute: CameraConfig.getUnitMute() });
    }
  }


  render() {
    let bgColor = !this.state.isFullscreen ? "#ffffff" : "black";
    let path = (this.state.videoPath == null ? "" : this.state.videoPath.path);
    let buttonMarginTop = this.state.isFullscreen ? 35 : 0;

    let videoHeight = this.state.isFullscreen ? "100%" : this.videoHeight;
    let imgStyle = {
      width: 25,
      height: 25
    };

    if (this.darkMode || this.state.isFullscreen) {
      imgStyle.tintColor = IMG_DARKMODE_TINT;
    }
    let StatusBarheight = null;
    if (Platform.OS === "android") {
      StatusBarheight = StatusBar.currentHeight;
      if (StatusBarheight <= 0) {
        StatusBarheight = 15;
      }
      // -16 因为返回按钮的图片留白太大
    } else {
      StatusBarheight = StatusBarUtil._getInset("top") - 16;
    }
    return (
      <SafeAreaView style={{ backgroundColor: bgColor }}>
        <View style={{ display: "flex", flexDirection: "column", width: "100%", height: "100%", flexWrap: "nowrap", backgroundColor: bgColor }} >
          {/* <SafeAreaView style={{ backgroundColor: bgColor }}></SafeAreaView> */}
          {/* 标题 */}
          {this.renderTitleView()}
          {/* 视频 */}
          <View style={[!this.state.isFullscreen ? { justifyContent: "center", position: "relative", flexGrow: 1, width: "100%" } : { position: "relative", height: "100%", width: "100%" }]}>
            <View style={{ display: "flex", flexDirection: "column", width: "100%", height: "100%", position: "absolute", alignItems: "center", justifyContent: "center" }}>
              {/* <View style={{width: "100%", height: videoHeight}}> */}
              <TouchableWithoutFeedback
                style={{ width: "100%", height: "100%", alignContent: "center", justifyContent: "center" }}
                onPress={() => this.setState({ showPlayToolBar: !this.state.showPlayToolBar })}
              >
                <View style={{ width: "100%", height: videoHeight }}>
                  {
                    path ? <Video
                      ref={(ref) => { this.video = ref; }}
                      source={{ uri: path }}
                      style={{ width: "100%", height: "100%" }}
                      paused={!this.state.isPlaying}
                      rate={this.state.playSpeed}
                      muted={this.state.isMute}
                      onProgress={this.onProgress}
                      onEnd={this.onEnd}
                      onError={this.onError}
                      repeat={false}
                      resizeMode={'contain'}
                      onLoad={this.onLoad}
                      ignoreSilentSwitch={"ignore"}
                      onSeek={this.onSeekComplete}
                    /> : null
                  }

                  {this._renderVideoControlView()}
                </View>
              </TouchableWithoutFeedback>
            </View>
            {/* 视频加载缓慢时候显示Loading */}
            {this.renderLoadingView()}
          </View>
          {/* 下载进度条 */}
          {this.renderDownloadProgress()}
          {/* 底部分享、下载、删除工具栏 */}
          {
            this.state.showToolbar && (this.state.showPlayToolBar || !this.state.isFullscreen) ? <View style={this.state.isFullscreen ? { position: "absolute", right: 20, display: "flex", flexDirection: "row", width: "100%", height: 69, justifyContent: "flex-end" } : { width: "100%", height: 69, display: "flex", flexDirection: "row", justifyContent: "center", alignItems: "center" }}>
              <LinearGradient
                colors={this.state.isFullscreen ? ['#00000099', '#00000000'] : ["#00000000", "#00000000"]}
                style={{ width: "100%", height: "100%", position: "absolute" }} />
              {
                this.state.isFullscreen ? <TouchableOpacity
                  style={{ width: 50, display: "flex", position: "absolute", alignItems: "center", marginTop: buttonMarginTop, left: StatusBarheight }}
                  onPress={() => { this.toPortrait(); }}>
                  <Image
                    style={{ width: 35, height: 35 }}
                    source={require("../../Resources/Images/icon_back_black_nor_dark.png")}
                    accessibilityLabel={DescriptionConstants.sd_1}
                  />
                </TouchableOpacity> : null
              }
              <TouchableOpacity
                style={{ width: 50, display: "flex", alignItems: "center", marginTop: buttonMarginTop }}
                onPress={() => { this.downLoadClick(true) }}>
                <Image
                  style={imgStyle}
                  source={require("../../Resources/Images/camera_icon_loc_pic_share.png")}
                  accessibilityLabel={DescriptionConstants.lc_13}
                />
                {
                  this.state.isFullscreen ? null :
                    <Text style={{ fontSize: 11, color: 'black', marginTop: 3 }}>{LocalizedStrings['share_files']}</Text>
                }
              </TouchableOpacity>
              <TouchableOpacity
                style={{ marginLeft: 30, width: 60, display: "flex", alignItems: "center", marginTop: buttonMarginTop }}
                onPress={() => this.downLoadClick(false)} >
                <Image
                  style={imgStyle}
                  source={require("../../Resources/Images/daily_story_icon_download_nor.png")}
                  accessibilityLabel={DescriptionConstants.lc_14}
                />
                {
                  this.state.isFullscreen ? null :
                    <Text style={{ fontSize: 11, color: 'black', marginTop: 3 }}>{LocalizedStrings['f_download']}</Text>

                }
              </TouchableOpacity>
              <TouchableOpacity
                style={{ marginLeft: 30, width: 50, display: "flex", alignItems: "center", marginTop: buttonMarginTop }}
                onPress={() => this.deleteImgClick()} >
                <Image
                  style={imgStyle}
                  source={require("../../Resources/Images/camera_icon_loc_pic_delete.png")}
                  accessibilityLabel={DescriptionConstants.lc_14}
                />
                {
                  this.state.isFullscreen ? null :
                    <Text style={{ fontSize: 11, color: 'black', marginTop: 3 }}>{LocalizedStrings['delete_files']}</Text>
                }
              </TouchableOpacity>
            </View> : null
          }
 
          {/* 点击删除时弹出对话框    */}
          {this.renderDialog()}
          {/* 视频加载缓慢时候显示Loading */}
          {/*{this.renderLoadingView()}*/}
          {/* 下载进度条 跳转到下载中心 */}
          {this._renderDownloadHint()}
          {this._renderPermissionDialog()}
          {/* <SafeAreaView></SafeAreaView> */}
        </View>
      </SafeAreaView>
    );
  }

  // 标题
  renderTitleView() {
    // first change statusBar
    if (this.state.isFullscreen) {
      StatusBar.setBarStyle('light-content');
    } else {
      StatusBar.setBarStyle('dark-content');
    }
    if (Platform.OS == 'android') {
      // StatusBar.setTranslucent(true); // 测试过的机型几乎都无效：华为荣耀V9，红米Note4X，小米Mix2
    }

    // second get statusBar height;
    let containerHeight = StatusBar.currentHeight || 0;
    containerHeight += 65;
    let statusBarHeight = StatusBar.currentHeight;

    const textContainerStyle = {
      flexGrow: 1,
      alignSelf: 'stretch', // 控制自己填充满父类的高度
      display: "flex",
      flexDirection: "column",
      justifyContent: 'center',
      alignItems: 'stretch', // 控制子类填充满本身的宽度
      marginHorizontal: 5
    };

    const titleTextStyle = {
      fontSize: 16,
      // lineHeight: 22,
      fontFamily: 'D-DINCondensed-Bold',
      textAlignVertical: 'center',
      textAlign: 'center'
    };
    // const darkTitleColor = '#ffffff'; // 深色背景下标题颜色
    const lightTitleColor = '#000000'; // 浅色背景下标题颜色

    const titleColor = { color: lightTitleColor };
    if (this.state.isFullscreen) {
      return null;
    }

    let imgStyle = {
      width: iconSize,
      height: iconSize,
      position: "absolute"
    };
    if (this.darkMode) {
      imgStyle.tintColor = IMG_DARKMODE_TINT;
    }


    return (
      <View style={{ width: "100%", height: containerHeight, display: "flex", flexDirection: "row", flexWrap: "nowrap", alignItems: "center", paddingTop: statusBarHeight }}>
        <View
          style={{ width: iconSize, height: iconSize, position: "relative" }}>
          <ImageButton
            style={imgStyle}
            source={require("../../Resources/Images/icon_back_black.png")}
            highlightedSource={require("../../Resources/Images/icon_back_black.png")}
            onPress={() => { this.mCallback(this.mCategory); this.props.navigation.goBack() }}
          />
        </View>
        <View style={textContainerStyle}>
          <Text
            numberOfLines={1}
            style={[titleTextStyle, titleColor]}
          >
            {this.title}
          </Text>
        </View>
        <View
          style={{ width: iconSize, height: iconSize, position: "relative" }}>
        </View>
      </View>
    );
  }
  // 点击删除时弹出对话框
  renderDialog() {
    return (
      <MessageDialog
        visible={this.state.deleteDialogVisible}
        message={this.dialogDeleteContent}
        messageStyle={[BaseStyles.text17, { alignSelf: 'center', color: '#333333', fontWeight: '400'}]}
        canDismiss={false}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: (_) => this.setState({ deleteDialogVisible: false })
            // ignore
          },
          {
            text: LocalizedStrings["delete_confirm"],
            callback: (_) => {
              this.setState({ deleteDialogVisible: false });
              this.confirmDelete();
            }
            // ignore
          }
        ]}
        onDismiss={() => {
          console.log('onDismiss');
          this.setState({ deleteDialogVisible: false });
        }}
      />
    );
  }
  // 工具栏:播放 进度条 音量 全屏等
  _renderVideoControlView() {
    if (!this.state.showPlayToolBar) {
      return null;
    }

    const playIcons = [
      {
        source: require('../../Resources/Images/icon_camera_pause.png'),
        highlightedSource: null,
        onPress: () => { this.isUserPause = true; this.togglePlay(false); },
        accessibilityLabel: !this.state.isPlaying ? DescriptionConstants.lc_1 : DescriptionConstants.lc_7
      },
      {
        source: require('../../Resources/Images/icon_camera_play.png'),
        highlightedSource: null,
        onPress: () => { this.isUserPause = false; this.togglePlay(true); }, // 开始播放
        accessibilityLabel: !this.state.isPlaying ? DescriptionConstants.lc_1 : DescriptionConstants.lc_7
      }
    ];
    const audioIcons = [
      {
        source: require('../../Resources/Images/icon_camera_unmute_playback.png'),
        highlightedSource: require('../../Resources/Images/icon_camera_unmute_playback.png'),
        onPress: () => this._toggleAudio(true),
        accessibilityLabel: !this.state.isFullscreen ? DescriptionConstants.lc_5 : DescriptionConstants.lc_11

      },
      {
        source: require('../../Resources/Images/icon_camera_mute_playback.png'),
        highlightedSource: require("../../Resources/Images/icon_camera_mute_playback.png"),
        onPress: () => this._toggleAudio(false), // 默认是这个状态
        accessibilityLabel: !this.state.isFullscreen ? DescriptionConstants.lc_5 : DescriptionConstants.lc_11
      }
    ];

    const speedIcons = [
      {
        source: require('../../Resources/Images/playback_1x_nor.png'),
        highlightedSource: require('../../Resources/Images/playback_1x_nor.png'),
        onPress: () => this._toggleSpeed(2.0)
      },
      {
        source: require('../../Resources/Images/playback_2x_nor.png'),
        highlightedSource: require("../../Resources/Images/playback_2x_nor.png"),
        onPress: () => this._toggleSpeed(1.0)// 默认是这个状态
      }
    ];

    const fullScreenIcons = [
      {
        source: require('../../Resources/Images/icon_camera_fullscreen_playback.png'),
        highlightedSource: require('../../Resources/Images/icon_camera_fullscreen_playback.png'),
        onPress: () => { this.toLandscape(); },
        accessibilityLabel: !this.state.isFullscreen ? DescriptionConstants.lc_6 : DescriptionConstants.lc_12
      },
      {
        source: require('../../Resources/Images/icon_camera_fullscreen_exit_playback.png'),
        highlightedSource: require('../../Resources/Images/icon_camera_fullscreen_exit_playback.png'),
        onPress: () => { this.toPortrait(); },
        accessibilityLabel: !this.state.isFullscreen ? DescriptionConstants.lc_6 : DescriptionConstants.lc_12
      }
    ];
    let playIndex = this.state.isPlaying ? 0 : 1;
    let audioIndex = this.state.isMute ? 1 : 0;
    let speedIndex = this.state.playSpeed == 1 ? 0 : 1;


    let videoHeight = this.state.isFullscreen ? "100%" : this.videoHeight;
    return (
      // <View style={{width: "100%", height: videoHeight}}>
      <LinearGradient
        colors={['#00000000', '#00000099']}
        style={[{ bottom: 0, position: "absolute", width: "100%", height: 54 }]}>

        <View style={{ display: "flex", flexDirection: "row", flexWrap: "nowrap", alignItems: "center", height: "100%", width: "100%" }}>
          {/* 播放按键 */}
          <View style={styles.videoControlBarItem}>
            <ImageButton
              onPress={playIcons[playIndex].onPress}
              style={styles.videoControlBarItemImg}
              source={playIcons[playIndex].source}
              highlightedSource={playIcons[playIndex].highlightedSource}
              accessibilityLabel={playIcons[playIndex].accessibilityLabel}
            />
          </View>

          <Text style={{ fontSize: 10, color: "#ffffff", width: 30 }} accessibilityLabel={DescriptionConstants.lc_2+this.state.startTimeStr || "00:00" }>
            {this.state.startTimeStr || "00:00"}
          </Text>
          <Slider
            style={{ flexGrow: 1, height: 30, marginLeft: 10, marginRight: 10 }}
            maximumValue={this.state.duration}
            minimumValue={0}
            step={1}
            minimumTrackTintColor={"#32BAC0"}
            maximumTrackTintColor={"xm#ffffff"}
            value={this.state.progress}
            onSlidingComplete={(value) => this.onSlidedProgress(value)}
            onValueChange={(aPos) => {
              this.handleSeek(aPos)
            }}
            thumbTintColor={"xm#ffffff"}
            accessible={true}
            accessibilityLabel={!this.state.isFullscreen ? DescriptionConstants.lc_3 : DescriptionConstants.lc_9}
          />
          <Text style={{ fontSize: 10, color: "#ffffff", width: 30 }}
            accessibilityLabel={ DescriptionConstants.lc_4 +(this.state.endTimeStr || "00:00")}
          >
            {this.state.endTimeStr || "00:00"}
          </Text>
          {/* 音量 */}
          <View style={styles.videoControlBarItem}>
            <ImageButton
              disabled={this.state.playSpeed == 2 ? true : false}
              onPress={audioIcons[audioIndex].onPress}
              style={styles.videoControlBarItemImg}
              source={audioIcons[audioIndex].source}
              highlightedSource={audioIcons[audioIndex].highlightedSource}
              accessibilityLabel={audioIcons[audioIndex].accessibilityLabel}
            />
          </View>
          {/* 倍速 */}
          {/* <View style={styles.videoControlBarItem}>
            <ImageButton
              onPress={speedIcons[speedIndex].onPress}
              style={styles.videoControlBarItemImg}
              source={speedIcons[speedIndex].source}
              highlightedSource={speedIcons[speedIndex].highlightedSource}
            />
          </View> */}
          {/* 全屏 */}
          <View style={styles.videoControlBarItem}>
            <ImageButton
              onPress={fullScreenIcons[this.state.isFullscreen ? 1 : 0].onPress}
              style={styles.videoControlBarItemImg}
              source={fullScreenIcons[this.state.isFullscreen ? 1 : 0].source}
              highlightedSource={fullScreenIcons[this.state.isFullscreen ? 1 : 0].highlightedSource}
              accessibilityLabel={fullScreenIcons[this.state.isFullscreen ? 1 : 0].accessibilityLabel}
            />
          </View>
        </View>
      </LinearGradient>
      // </View>
    );
  }
  // 查看下载进度弹框
  _renderDownloadHint() {
    if (!this.state.showDownloadHint) {
      return null;
    }
    return (
      <TouchableOpacity style={{ position: "absolute", bottom: 0, minHeight: 30, width: "100%", backgroundColor: "#32BAC0", display: "flex", justifyContent: "center", alignItems: "center" }}
        onPress={() => {
          this.props.navigation.navigate("DldPage", { preOri: this.state.isFullscreen ? "landscape" : "portrait" });
        }}
      >
        <Text
          style={[BaseStyles.text13, { color: "white", paddingHorizontal: 10 }]}
        >
          {LocalizedStrings["download_hint"]}
        </Text>
      </TouchableOpacity>
    );
  }
  //读取权限弹框
  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 麦克风
    // 
    let message = "";
    if (this.state.permissionRequestState == 0) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_microphone"]);
    }
    return (
    // <AbstractDialog
      <MessageDialog
        title={LocalizedStrings["tips"]}
        message={message}
        messageStyle={{
          fontSize: 14,
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );
  }

  restoreOri() {
    console.log(TAG, "restoreOri");
    if ("PORTRAIT" === this.mOri) {
      this.toPortrait();
    } else {
      this.toLandscape();
    }
  }

  // 退出全屏点击
  toPortrait() {
    console.log(TAG, "toPortrait");
    this.mOri = "PORTRAIT";
    StatusBar.setHidden(false);
    CameraConfig.lockToPortrait();
  }

  // 全屏点击
  toLandscape() {
    console.log(TAG, "toLandscape");
    this.mOri = "LANDSCAPE";
    StatusBar.setHidden(true);
    if (Platform.OS === "android") {
      Orientation.lockToLandscape();
    } else {
      if (Platform.OS == 'ios' && Host.isPad) {
        Service.miotcamera.enterFullscreenForPad(true);
      } else {
        Orientation.lockToLandscapeRight();
      }
    }
  }

  _orientationListener = (orientation) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    if (!this.isPageForeGround || !this.isPluginForeGround) {
      return;
    }
    console.log(TAG, `device orientation changed :${orientation} want ${this.mOri}`);
    if (this.mOri === orientation) {
      if (orientation === 'LANDSCAPE') {
        // do something with landscape layout
        this.setNavigation();
        this.setState({ isFullscreen: true });
        if (Host.isPad && Platform.OS == 'android') {
          Service.miotcamera.enterFullscreenForPad(true);
        }
      } else {
        // do something with portrait layout
        this.setNavigation();
        this.setState((state) => { return { isFullscreen: false }; }, () => {
          if (this.isToShare) {
            Host.ui.openSystemShareWindow(this.mDldInfo["path"]);
            this.isToShare = false;
          }
        });
        if (Host.isPad && Platform.OS == 'android') {
          Service.miotcamera.enterFullscreenForPad(false);
        }
      }
    } else {
      // ios need restore for next lock
      this.restoreOri();
    }
  }
  // 加载时候的loading
  renderLoadingView() {
    if (!this.state.showLoading) {
      return;
    }
    let isDark = DarkMode.getColorScheme() == "dark";
    return (
      <View style={{ position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}>
        <ActivityIndicator
          style={{ width: 54, height: 54 }}
          color={"xm#ffffff"}
          size={"large"}
        />
        <Text style={{ marginTop: 10, fontSize: 12, color: "#ffffff" }}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );
  }

  // 渲染下载时候的进度条
  renderDownloadProgress() {
    if (this.state.aProgress !== 100 && this.state.aProgress !== 102 && this.state.aProgress !== -1) {
      return (
        <View style={{ width: "100%", height: 60, position: 'absolute', bottom: 10, backgroundColor: "#f5f5f5", display: "flex", justifyContent: "flex-start", flexDirection: "row", alignItems: "center", zIndex: 10 }}>
          <ActivityIndicator
            style={{ width: 54, height: 54 }}
            color={"xm#000"}
            size={"small"}
            animating={true}
          />
          <Text style={{ fontSize: 12, color: "#000" }}>
            {LocalizedStrings["action_downloading"]} {this.state.aProgress}%
          </Text>
        </View>
      )
    }

  }
}


const styles = StyleSheet.create({


  videoControl: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    height: 80
  },

  videoControlBar: {// 横向
    backgroundColor: '#FFF1',
    display: "flex",
    flexWrap: "nowrap",
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    paddingTop: 20
  },
  videoControlBarFull: {
    backgroundColor: '#FFF1',
    display: "flex",
    width: "100%",
    flexDirection: "row",
    justifyContent: "flex-end"
  },
  videoControlBarItem: {// 内容居中排列
    display: "flex",
    justifyContent: "center",
    alignItems: "center"
  },
  videoControlSeekbar: {
    flexGrow: 1,
    alignItems: "center"
  },

  videoControlBarItemImg: {
    width: 40,
    height: 40
  }

});