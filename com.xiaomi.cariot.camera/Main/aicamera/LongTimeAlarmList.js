import React from 'react';
import { ScrollView, View, BackHandler, FlatList, Platform, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Switch from 'miot/ui/Switch';
import Checkbox from 'miot/ui/Checkbox';
import { styles } from '../setting/SettingStyles';
import CameraConfig, { IMG_DARKMODE_TINT } from '../util/CameraConfig';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtil from '../util/AlarmUtil';
import Toast from '../Toast';
import { DarkMode } from 'miot/Device';
import { NavigationBar } from 'mhui-rn';
import { Device } from 'miot/device';
import { MessageDialog } from 'miot/ui/Dialog';

export default class LongTimeAlarmList extends React.Component {
  static navigationOptions = (navigation) => {
    return {// 不要导航条
      headerTransparent: true,
      header:
        null
    };
  };
  constructor(props, context) {
    super(props, context);
    this.state = {
      isLoading: false,
      listDatas: [],
      alarmValues: {},
      showDelDialog: false,
      editMode: false
    };
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.onResume();
      }
    );
    this.isPageForeGround = true;
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'didBlur',
      () => {
        this.isPageForeGround = false;
      }
    );
    this.keys = [];
    this.LONG_TIME_KEY_PREFIX = "prop.s_chuangmi_clocks";
    this.selectCount = 0;
  }

  renderTitleBar() {
    let titleBarContent = {
      title: LocalizedStrings['ss_long_time_nobody'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { 
            if (this.state.editMode) {
              this.state.listDatas.forEach((item) => item.select = false);
              this.setState({ editMode: false });
            } else {
              this.props.navigation.goBack();
            } 
          }
        }
      ],
      right: [
        {
          key: this.state.editMode ? NavigationBar.ICON.SELECT_ALL : null,
          onPress: () => {
            if (this.selectCount >= this.state.listDatas.length) {
              this.state.listDatas.forEach((item) => item.select = false);
              this.selectCount = 0;
            } else {
              this.state.listDatas.forEach((item) => item.select = true);
              this.selectCount = this.state.listDatas.length;
            }
            this.setState({ editMode: true });
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    };
    // this.props.navigation.setParams(titleBarContent);
    return (
      <NavigationBar {...titleBarContent} />
    );
  }

  componentDidMount() {
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.loadData();
  }

  onItemLongClick(item) {
    item.select = true;
    this.selectCount = 1;
    this.setState({ editMode: true });
  }

  renderItemView(item, index) {
    // {\"enable\":false,\"key\":1,\"time_start\":\"07:00\",\"time_end\":\"09:00\",\"repeat\":127}
    return (
      <View style={{ display: "flex", flexDirection: "column" }}>
        <View style={{ display: "flex", flexDirection: "row", alignItems: "center", paddingRight: 20 }}>
          <TouchableOpacity style={{ display: "flex", flexDirection: "column", flexGrow: 2, padding: 20, width: "80%" }}
            onLongPress={() => this.onItemLongClick(item) }
            onPress={() => { 
              if (this.state.editMode) {
                item.select = !item.select; 
                item.select ? this.selectCount++ : this.selectCount--;
              } else {
                this.props.navigation.navigate('SetLongTimeAlarm', 
                  { item: Object.assign(item, { alarmValue: this.state.alarmValues[`${ this.LONG_TIME_KEY_PREFIX }${ item.key }`] ? this.state.alarmValues[`${ this.LONG_TIME_KEY_PREFIX }${ item.key }`].value : null }),
                    callback: (data) => {
                      Object.assign(item, data);
                      this.onItemCheckChanged();
                    } });
              }
              this.forceUpdate();
            }}>
            <Text>{item.time_start} ~ {item.time_end}</Text>
            <Text>{this.state.alarmValues[`${ this.LONG_TIME_KEY_PREFIX }${ item.key }`] ? this.state.alarmValues[`${ this.LONG_TIME_KEY_PREFIX }${ item.key }`].value : null}</Text>
          </TouchableOpacity>
          {this.state.editMode ? <Checkbox
            style={{ width: 20, height: 20, borderRadius: 20 }}
            checked={item.select}
            onValueChange={(checked) => item.select = checked}
          /> : <Switch
            value={item.enable}
            disabled={false}
            onValueChange={(checked) => {
              item.enable = checked;
              this.onItemCheckChanged();
            }}
          />}
        </View>
        <View style={{ backgroundColor: "#FFFFFF", paddingLeft: 20 }}>
          <View style={{ height: 0, borderTopWidth: StyleSheet.hairlineWidth, borderColor: '#bdbdbd', opacity: 0.7, margin: StyleSheet.hairlineWidth }} />
        </View>
      </View>
    );
  }
  // 渲染底部提示
  renderFooter() {
    return(
      <View style={{backgroundColor: "#FFFFFF", paddingLeft: 20, paddingTop:20}}>
        <Text>{LocalizedStrings['ss_long_time_nobody_tips']}</Text>
      </View>
    )
  }
  render() {
    let isDark = DarkMode.getColorScheme() == "dark";
    return (<View style={{ display: "flex", height: "100%", width: "100%", flex: 1, flexDirection: "column", backgroundColor: "#FFFFFF", alignItems: "center" }}>
      { this.renderTitleBar() }
      { this.renderDeleteDialog() }
      <Image
        style={{ width: 250, height: 250, marginTop: -10 }}
        source={require("../../Resources/Images/long_time_alarm_header.webp")}
      />
      <FlatList
        style={{ width: "100%", marginBottom: this.state.editMode ? 0 : 80 }}
        data = { this.state.listDatas }
        renderItem = {(data) => this.renderItemView(data.item, data.index)}
        contentContainerStyle={[{ flexGrow: 1, paddingHorizontal: 12 }]}
        ListFooterComponent = { () => this.renderFooter() } 
        refreshing={this.state.isLoading}
        onRefresh={() => {
          this.loadData(); 
        }}>
         
      </FlatList>
      {this.state.editMode ? 
        <TouchableOpacity style={{ display: "flex", flexDirection: "column", alignItems: "center", width: "100%",marginBottom:40 }}
          onPress = {() => { this.setState({ showDelDialog: true }); }}>
          <View style={{ backgroundColor: "#FFFFFF", width: "100%" }}>
            <View style={{ height: 0, borderTopWidth: StyleSheet.hairlineWidth, borderColor: '#bdbdbd', opacity: 0.7, margin: StyleSheet.hairlineWidth }} />
          </View>
          <Image
            style={{ width: 35, height: 35 }}
            source={require("../../Resources/Images/icon_delete_normal.png")}
            tintColor={isDark ? IMG_DARKMODE_TINT : null}
          />
          <Text style={{ color: "#000000", fontSize: 11 }}>
            {LocalizedStrings["delete_files"]}
          </Text>
        </TouchableOpacity> 
      : 
        <TouchableOpacity style={{ position: 'absolute', right: 30, bottom: 30 }}
          onPress = {() => { this.onAddItem(); }}>
          <Image style={{ width: 50, height: 50 }}
            source={require("../../Resources/Images/mj_widget_add_nor.png")}/> 
        </TouchableOpacity>
      }
    </View>);
  }

  renderDeleteDialog() {
    return (
      <MessageDialog
        visible={this.state.showDelDialog}
        title={LocalizedStrings['plug_timer_del']}
        message={LocalizedStrings['plug_timer_del_hint']}
        canDismiss={true}
        buttons={[
          {
            text: LocalizedStrings["btn_cancel"],
            // style: { color: 'lightpink' },
            callback: (_) => {
              this.setState({ showDelDialog: false });
            }
          },
          {
            text: LocalizedStrings["delete_files"],
            // style: { color: 'lightblue' },
            callback: (_) => {
              this.onDeleteItem();
              this.setState({ showDelDialog: false });
            }
          }
        ]}
        onDismiss={(_) => {
        }}
      />
    );
  }

  onDeleteItem() {
    let array = [];
    let alarmKeys = {};
    let needDelete = false;
    this.state.listDatas.forEach((item) => {
      if (item.select == false) {
        if (item.alarmValue) {
          alarmKeys[`${ this.LONG_TIME_KEY_PREFIX }${ item.key }`] = item.alarmValue;
        }
        array.push({ enable: item.enable, key: item.key, time_start: item.time_start, time_end: item.time_end, repeat: item.repeat });
      } else {
        needDelete = true;
      }
    });
    if (needDelete == false) {
      console.log(`onDeleteItem no item to delete`);
      return;
    }
    let arrayStr = JSON.stringify({ values: array });
    console.log(`onDeleteItem ${ arrayStr }`);
    this.putLongTimeAlarmList(arrayStr, alarmKeys).then(() => {
      this.setState({ editMode: false });
      this.selectCount = 0;
      this.loadData();
    }).catch((err) => {
      console.log(`onDeleteItem error=${ JSON.stringify(err) }`);
    });
  }

  onAddItem() {
    this.props.navigation.navigate('SetLongTimeAlarm', {
      callback: (data) => {
        console.log(`alarmDataResultListener=${ JSON.stringify(data) }`);
        let array = [];
        let alarmKeys = {};
        let key = 1;
        this.state.listDatas.forEach((item, index) => {
          if (key == this.keys[index]) {
            key += 1;
          }
          if (item.alarmValue) {
            alarmKeys[`${ this.LONG_TIME_KEY_PREFIX }${ item.key }`] = item.alarmValue;
          }
          array.push({ enable: item.enable, key: item.key, time_start: item.time_start, time_end: item.time_end, repeat: item.repeat });
        });

        if (data.alarmValue) {
          alarmKeys[`${ this.LONG_TIME_KEY_PREFIX }${ key }`] = data.alarmValue;
        }
        array.push({ enable: true, key: key, time_start: data.time_start, time_end: data.time_end, repeat: data.repeat });

        let arrayStr = JSON.stringify({ values: array });
        console.log(`onAddItem ${ arrayStr }`);
        this.putLongTimeAlarmList(arrayStr, alarmKeys);
      }
    });
  }

  onResume() {
    
  }

  onItemCheckChanged() {
    let array = [];
    let alarmKeys = {};
    this.state.listDatas.forEach((item) => {
      if (item.alarmValue) {
        alarmKeys[`${ this.LONG_TIME_KEY_PREFIX }${ item.key }`] = item.alarmValue;
      }
      array.push({ enable: item.enable, key: item.key, time_start: item.time_start, time_end: item.time_end, repeat: item.repeat });
    });
    let data = JSON.stringify({ values: array });
    console.log(`onItemCheckChanged ${ data }`);
    this.putLongTimeAlarmList(data, alarmKeys);
  }

  setAlarmKey(alarmKeys) {
    let alarmKeysStr = JSON.stringify(alarmKeys);
    console.log(alarmKeysStr);
    if (alarmKeysStr == "{}") {
      console.log("alarmKeys is {}");
      return;
    }
    let data = { did: Device.deviceID, props: alarmKeys };
    AlarmUtil.setProps(data).then((res) => {
      console.log(JSON.stringify(res));
      this.loadData();
    }).catch((err) => {
      console.log(JSON.stringify(err));
    });
  }

  putLongTimeAlarmList(data, alarmKeys) {
    return new Promise((resolve, reject) => {
      AlarmUtil.putLongTimeAlarmList(data).then((res) => {
        this.setAlarmKey(alarmKeys);
        console.log(`putLongTimeAlarmList${ JSON.stringify(res) }`);
        resolve(data);
      }).catch((err) => {
        console.log(`putLongTimeAlarmList err=${ JSON.stringify(err) }`);
        this.loadData();
        reject(err);
      });
    });
  }

  productData() {
    let data = JSON.stringify({ values: [
      { enable: false, key: 1, time_start: "07:00", time_end: "09:00", repeat: 0b01111111 },
      { enable: false, key: 2, time_start: "11:00", time_end: "13:00", repeat: 0b01111111 },
      { enable: false, key: 3, time_start: "06:00", time_end: "20:00", repeat: 0b01111111 }
    ] });
    let alarmKeys = { "prop.s_chuangmi_clocks1": LocalizedStrings['detect_nobody_in_morning'],
      "prop.s_chuangmi_clocks2": LocalizedStrings['nobody_have_lunch'],
      "prop.s_chuangmi_clocks3": LocalizedStrings['detect_nobody_in_day'] };
    this.putLongTimeAlarmList(data, alarmKeys);
  }

  loadData() {
    this.setState({
      isLoading: true
    });
    AlarmUtil.getLongTimeAlarmList(2).then((result) => {
      console.log(result);
      // [{"code": 0, "did": "1020772087", "piid": 8, "siid": 7, "updateTime": 1625223700, 
      // "value": "{\"values\":[{\"enable\":false,\"key\":1,\"time_start\":\"07:00\",\"time_end\":\"09:00\",\"repeat\":127},{\"enable\":false,\"key\":2,\"time_start\":\"11:00\",\"time_end\":\"13:00\",\"repeat\":127},{\"enable\":false,\"key\":3,\"time_start\":\"06:00\",\"time_end\":\"20:00\",\"repeat\":127}]}"}]
      if (result instanceof Array && result.length > 0) {
        if (result[0].code === 0) {
          let value = result[0].value;
          if (typeof (value) != "undefined" && value != "") {
            let values = JSON.parse(value).values;
            this.keys = [];
            values.forEach((item) => {
              item.select = false;
              this.keys.push(item.key);
            });
            this.keys.sort((a1, a2) => {
              return a1 - a2;
            });
            console.log(this.keys);
            // console.log(values[0]);
            let propsArray = [];
            for (let i = 0; i < 11; i++) {
              propsArray.push(`${ this.LONG_TIME_KEY_PREFIX }${ i }`);
            }
            AlarmUtil.batchGetDatas([{ did: Device.deviceID, props: propsArray }]).then((res) => {
              console.log(`============${ JSON.stringify(res) }`);
              this.setState({
                alarmValues: res[Device.deviceID],
                isLoading: false,
                listDatas: values
              });
            }).catch((err) => {
              console.log(err);
            });
          } else {
            this.productData();
          }
        } else {
          console.log("getLongTimeAlarmList result code != 0");
        }
      } else {
        console.log("getLongTimeAlarmList error -999");
      }
    }).catch((err) => {
      this.setState({
        isLoading: false
      });
    });
  }

  componentWillUnmount() {
    if (Platform.OS === "android") {                                
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  onBackHandler = () => {
    if (this.state.editMode) {
      this.state.listDatas.forEach((item) => item.select = false);
      this.setState({ editMode: false });
      this.selectCount = 0;
      return true;
    } else {
      return false;
    }
  }
}

