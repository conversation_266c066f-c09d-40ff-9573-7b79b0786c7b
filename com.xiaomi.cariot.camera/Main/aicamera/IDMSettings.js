import React from 'react';
import { Service, Device } from "miot";
import { ScrollView, View, BackHandler, FlatList, Platform, Text, Button, StyleSheet, Image, TouchableOpacity, TouchableWithoutFeedback } from 'react-native';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Switch from 'miot/ui/Switch';
import Checkbox from 'miot/ui/Checkbox';
import { styles } from '../setting/SettingStyles';
import ItemIDMSetting from '../ui/ItemIDMSetting';
import CameraConfig, { IMG_DARKMODE_TINT } from '../util/CameraConfig';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtil from '../util/AlarmUtil';
import Toast from '../Toast';
import { DarkMode } from 'miot/Device';
import { NavigationBar } from 'mhui-rn';
import RPC from '../util/RPC';
import tr from '../../../../miot-sdk/resources/strings/tr';
import LogUtil from '../util/LogUtil';

export default class IDMSettings extends React.Component {
  static navigationOptions = (navigation) => {
    return {// 不要导航条
      headerTransparent: true,
      header:
        null
    };
  };
  constructor(props, context) {
    super(props, context);
    this.state = {
      isLoading: false,
      testChecked: false,
      switched: false,
      motionChecked: false,
      peopleChecked: false,
      cryChecked: false,
      sleepChecked: false,
      receivedData: -1 // -1加载中，0无设备， 1有设备
    };
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.onResume();
      }
    );
    this.isPageForeGround = true;
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'didBlur',
      () => {
        this.isPageForeGround = false;
      }
    );
    this.tvDids = [];
    this.idHashMap = {};
    this.currIdmInfo = {};
    this.eventType = 0;
    this.isDark = DarkMode.getColorScheme() == "dark";
  }

  renderTitleBar() {
    let titleBarContent = {
      title: LocalizedStrings['ss_event_type_idm_cast_screen'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: (this.currIdmInfo.idm_eventType && this.currIdmInfo.idm_eventType != "0") ? NavigationBar.ICON.CLOSE : NavigationBar.ICON.BACK,
          onPress: () => { 
            this.props.navigation.goBack();
          }
        }
      ],
      right: [
        {
          key: (this.currIdmInfo.idm_eventType && this.currIdmInfo.idm_eventType != "0") ? NavigationBar.ICON.COMPLETE : null,
          onPress: () => {
            this.onSubmit();
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    };
    // this.props.navigation.setParams(titleBarContent);
    return (
      <NavigationBar {...titleBarContent} />
    );
  }

  componentDidMount() {
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.loadData();
  }



  render() {
    
    return (this.state.receivedData == 0 ? <View style={{ display: "flex", height: "100%", width: "100%", flex: 1, flexDirection: "column", backgroundColor: this.isDark ? null : "#FFFFFF", alignItems: "flex-start" }}>
      { this.renderTitleBar() }
      <Image
        style={{ width: 35, height: 35, marginLeft: 30, marginTop: 30 }}
        source={require("../../Resources/Images/icon_camera_idm.webp")}></Image>
      <Text style={{ marginLeft: 30, marginTop: 10, fontSize: 19 }}>
        {LocalizedStrings["ss_event_type_idm_cast_screen"]}
      </Text>
      <Text style={{ marginLeft: 30, marginTop: 10 }}>
        {LocalizedStrings["event_type_idm_cast_screen_sub"]}
      </Text>
      <Image
        style={{ width: 350, height: 200, alignSelf: "center" }}
        source={require("../../Resources/Images/idm_no_tv_pic.webp")}></Image>
      <Text style={{ marginTop: 10, alignSelf: "center" }}>
        {LocalizedStrings["tv_device_idm_tips_no"]}
      </Text>
    </View> : this.state.receivedData == 1 ? 
      <View style={{ display: "flex", height: "100%", width: "100%", flex: 1, flexDirection: "column", backgroundColor: "#F5F5F5" }}>
        { this.renderTitleBar() }
        <ScrollView>
          { (this.currIdmInfo.idm_eventType && this.currIdmInfo.idm_eventType == "0") ?
            <Image
              style={{ width: 35, height: 35, marginLeft: 15, marginTop: 20 }}
              source={require("../../Resources/Images/icon_camera_idm.webp")}></Image> : null}
          <Text style={{ marginLeft: 15, marginTop: 10, fontSize: 17 }}>
            {LocalizedStrings["ss_event_type_idm_cast_screen"]}
          </Text>
          <Text style={{ marginLeft: 15, marginTop: 2, fontSize: 12 }}>
            {LocalizedStrings["event_type_idm_cast_screen_sub"]}
          </Text>
          { (this.currIdmInfo.idm_eventType && this.currIdmInfo.idm_eventType != "0") ?
            <ItemIDMSetting style={{ marginTop: 10 }}
              icon={require("../../Resources/Images/icon_camera_idm_idm_switch.webp")}
              title={LocalizedStrings['hint_enable_rec_scene']}
              isSwitch={true}
              onPress={() => {
                let isChecked = !this.state.switched;
                this.setState({ switched: isChecked });
                console.log(`onCheckChange=${ isChecked }`);
                this.currIdmInfo.idm_switch = isChecked ? "1" : "0";
              }}
              isChecked={this.state.switched}
            /> : null}
          <Text style={{ marginLeft: 15, marginTop: 15, marginBottom: 5, fontSize: 13 }}>
            {LocalizedStrings["event_select_idm_cast_screen"]}
          </Text>
          <ItemIDMSetting style={{ marginTop: 10 }}
            icon={require("../../Resources/Images/icon_camera_idm_area_move.webp")}
            title={LocalizedStrings['event_type_obj_motion_push']}
            onPress={() => {
              let isChecked = !this.state.motionChecked;
              this.setState({ motionChecked: isChecked });
              console.log(`onCheckChange=${ isChecked }`);
              this.eventType = isChecked ? (this.eventType | 0x01) : (this.eventType & 0x0E);
              console.log(`caculateEventType============${ this.eventType }`);
            }}
            isChecked={this.state.motionChecked}
          />
          <ItemIDMSetting style={{ marginTop: 10 }}
            icon={require("../../Resources/Images/icon_camera_idm_people_move.webp")}
            title={LocalizedStrings['nts_person_detected']}
            onPress={() => {
              let isChecked = !this.state.peopleChecked;
              this.setState({ peopleChecked: isChecked });
              console.log(`onCheckChange=${ isChecked }`);
              this.eventType = isChecked ? (this.eventType | 0x02) : (this.eventType & 0x0D);
              console.log(`caculateEventType============${ this.eventType }`);
            }}
            isChecked={this.state.peopleChecked}
          />
          <ItemIDMSetting style={{ marginTop: 10 }}
            icon={require("../../Resources/Images/icon_camera_idm_baby_cry.webp")}
            title={LocalizedStrings['nts_babycry_detected']}
            onPress={() => {
              let isChecked = !this.state.cryChecked;
              this.setState({ cryChecked: isChecked });
              console.log(`onCheckChange=${ isChecked }`);
              this.eventType = isChecked ? (this.eventType | 0x04) : (this.eventType & 0x0B);
              console.log(`caculateEventType============${ this.eventType }`);
            }}
            isChecked={this.state.cryChecked}
          />
          <ItemIDMSetting style={{ marginTop: 10 }}
            icon={require("../../Resources/Images/icon_camera_idm_baby_wakeup.webp")}
            title={LocalizedStrings['event_type_baby_sleep_push']}
            onPress={() => {
              let isChecked = !this.state.sleepChecked;
              this.setState({ sleepChecked: isChecked });
              console.log(`onCheckChange=${ isChecked }`);
              this.eventType = isChecked ? (this.eventType | 0x08) : (this.eventType & 0x07);
              console.log(`caculateEventType============${ this.eventType }`);
            }}
            isChecked={this.state.sleepChecked}
          />
          <Text style={{ marginLeft: 15, marginTop: 15, marginBottom: 5, fontSize: 13 }}>
            {LocalizedStrings["device_select_idm_cast_screen"]}
          </Text>
          {this.tvDids.map((did) => this.renderTVViews(did))}
          <Text style={{ margin: 15, fontSize: 12 }}>
            {LocalizedStrings["tv_device_idm_tips_has"]}
          </Text>
        </ScrollView>
        { (this.currIdmInfo.idm_eventType && this.currIdmInfo.idm_eventType == "0") ?
          <TouchableWithoutFeedback
            onPress={() => {
              if (!this.state.motionChecked && !this.state.peopleChecked && !this.state.cryChecked && !this.state.sleepChecked) {
                Toast.fail("idm_empty_tv_device_tips");
              } else {
                this.onSubmit(true);
              }
            }}>
            <Text style={{ textAlign: "center", borderRadius: 180, margin: 10, marginStart: 35, marginEnd: 35, padding: 12, fontSize: 15, backgroundColor: "#32BAC0", color: "#ffffff" }}>
              {LocalizedStrings["auto_discovery_setting_now"]}
            </Text></TouchableWithoutFeedback> : null}
      </View> : <View style={{ display: "flex", height: "100%", width: "100%", flex: 1, flexDirection: "column", backgroundColor: "#F5F5F5", alignItems: "flex-start" }}>
        { this.renderTitleBar() }
      </View>);
  }

  renderTVViews(did) {
    let idHashInfo = this.idHashMap[did];
    return (
      <ItemIDMSetting style={{ marginTop: 10 }}
        id={did}
        icon={{ uri: idHashInfo.iconUrl }}
        title={idHashInfo.roomName ? idHashInfo.name : LocalizedStrings['plug_timer_repeat']}
        sub_title={ idHashInfo.online ? idHashInfo.roomName : `${ idHashInfo.roomName } | ${ LocalizedStrings['idm_offline'] }`}
        onPress={(did) => {
          this.currIdmInfo.idm_idHash = idHashInfo.id_hash;
          this.currIdmInfo.idm_devDid = did;
          let isChecked = !this.state.testChecked;
          console.log(`onRadioChange=${ isChecked } did=${ did }`);
          this.setState({ testChecked: isChecked });
        }}
        isSingle={true}
        isChecked={did == this.currIdmInfo.idm_devDid}
      />
    );
  }

  onResume() {
    
  }

  loadData() {
    Service.smarthome.getTvList().then((res) => {
      console.log("getTvList res=", res);
      this.tvDids = [];
      let tvDidMap = {};
      let params = [];
      console.log(res.data.length);
      if (!res.data || !(res.data instanceof Array) || res.data.length <= 0) {
        this.setState({ receivedData: 0 });
      }
      res.data.forEach((item) => {
        tvDidMap[item.did] = { "online": item.isOnline, "iconUrl": item.iconURL, "roomName": item.roomName, "name": item.name };
        params.push({ "did": item.did, "props": ["prop.Idminfo"] });
      });
      AlarmUtil.batchGetDatas(params).then((res) => {
        // console.log(`batchGetDatas res============${ JSON.stringify(res) }`);
        this.idHashMap = {};
        let hasTv = false;
        for (let i in res) {
          try {
            let idHashInfo = JSON.parse(res[i]["prop.Idminfo"]["value"]);
            idHashInfo = Object.assign({}, tvDidMap[i], idHashInfo);
            if (idHashInfo["id_hash"]) {
              this.tvDids.push(i);
              this.idHashMap[i] = idHashInfo;
              hasTv = true;
            }
          } catch (e) {
            console.log(e);
          }
        }
        console.log(`idHashMap============${ JSON.stringify(this.idHashMap) }`);

        AlarmUtil.range_get_extra_data({ "prefix": "idm", "did": Device.deviceID, "offset": 0, "limit": 20 }).then((res) => {
          console.log(`range_get_extra_data res============${ JSON.stringify(res) }`);
          // {"idm_devDid":"mitv.4a7074688ea4a32cd257f7c672d56365:bcd978c00f1cb9df827fb0cc9ff39238",
          // "idm_eventType":"8","idm_idHash":"Njgx","idm_status":"3","idm_switch":"1"}
          this.currIdmInfo = res;
          if (!this.currIdmInfo.idm_eventType) {
            this.currIdmInfo.idm_eventType = "0";
          }
          let tempIdmInfo = {};
          let index = 0;
          let isMatched = false;
          for (let did in this.idHashMap) {
            if (index == 0) {
              tempIdmInfo = { "did": did, "id_hash": this.idHashMap[did].id_hash };
            }
            if (did == this.currIdmInfo.idm_devDid) {
              isMatched = true;
              break;
            }
          }
          if (!isMatched) {
            this.currIdmInfo.idm_devDid = tempIdmInfo.did;
            this.currIdmInfo.idm_idHash = tempIdmInfo.id_hash;
          }
          console.log(`range_get_extra_data after============${ JSON.stringify(this.currIdmInfo) }`);
          this.eventType = Number.parseInt(this.currIdmInfo.idm_eventType);
          this.setState({ 
            motionChecked: (this.eventType & 0x01) != 0 ? true : false,
            peopleChecked: (this.eventType & 0x02) != 0 ? true : false,
            cryChecked: (this.eventType & 0x04) != 0 ? true : false,
            sleepChecked: (this.eventType & 0x08) != 0 ? true : false,
            receivedData: hasTv ? 1 : 0, 
            switched: (this.currIdmInfo.idm_switch && this.currIdmInfo.idm_switch == "1") ? true : false 
          });
        }).catch((err) => {
          console.log(`range_get_extra_data err============${ JSON.stringify(err) }`);
          this.setState({ receivedData: hasTv ? 1 : 0 });
        });
      }).catch((err) => {
        console.log(`batchGetDatas err============${ JSON.stringify(err) }`);
        this.setState({ receivedData: 0 });
      });
      // console.log(this.tvDidMap);
    }).catch((err) => {
      console.log("getTvList err=", err);
    });
    // Service.miotcamera.getIDMTVDIDS(JSON.stringify("{}")).then((res) => {
    //   // console.log(res);
    // }).catch((err) => {
    //   console.log(err);
    // });
  }

  caculateEventType(needOpen) {
    // eventType = this.state.motionChecked ? (eventType | 0x01) : (eventType & 0x0E);
    // eventType = this.state.peopleChecked ? (eventType | 0x02) : (eventType & 0x0D);
    // eventType = this.state.cryChecked ? (eventType | 0x04) : (eventType & 0x0B);
    // eventType = this.state.sleepChecked ? (eventType | 0x08) : (eventType & 0x07);
    // console.log(`caculateEventType============${ this.eventType }`);
    if (this.eventType == 0) {
      this.currIdmInfo.idm_switch = "0";
    } else if (needOpen) {
      this.currIdmInfo.idm_switch = "1";
    }
    this.currIdmInfo.idm_eventType = `${ this.eventType }`;
  }

  onSubmit(needOpen = false) {
    this.caculateEventType(needOpen);
    LogUtil.logOnAll("_sync.set_idm_match_param=", JSON.stringify(this.currIdmInfo));
    RPC.callMethod("_sync.set_idm_match_param", [
      { "idm_devDid": this.currIdmInfo.idm_devDid, "idm_eventType": Number.parseInt(this.currIdmInfo.idm_eventType), "idm_idHash": this.currIdmInfo.idm_idHash, "idm_switch": Number.parseInt(this.currIdmInfo.idm_switch) }
    ]).then((res) => {
      console.log(`set_idm_match_param res============${ JSON.stringify(res) }`);
      this.props.navigation.goBack();
    }).catch((err) => {
      console.log(`set_idm_match_param err============${ JSON.stringify(err) }`);
      Toast.fail("action_failed", err);
      this.props.navigation.goBack();
    });
  }

  componentWillUnmount() {
    if (Platform.OS === "android") {                                
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  onBackHandler = () => {
    return false;
  }
}

