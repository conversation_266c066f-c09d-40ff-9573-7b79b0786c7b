import React from 'react';
import { ScrollView, View, BackHandler, Platform, Text } from 'react-native';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import { styles } from '../setting/SettingStyles';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtil from '../util/AlarmUtil';
import Toast from '../components/Toast';
import { InputDialog, NavigationBar } from 'mhui-rn';
import { Styles } from 'miot/resources';
import { Device } from 'miot';
import Util from '../util2/Util';
import ListItemWithIcon from '../widget/ListItemWithIcon';
import VersionUtil from '../util/VersionUtil';
import StorageKeys from '../StorageKeys';
import CameraConfig from '../util/CameraConfig';

export default class AICameraSettins extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      talkSwitch: false,
      talkForPushSwitch: false,
      isVip: undefined,
      inputNumDialog: false,
      dailyStorySwitch: false,
      commentErr: null
    };
    this.phoneNum = "";
  }

  render() {
    return (
      <View style={{ backgroundColor: Util.isDark() ? "#xm000000" : '#F6F6F6', flex: 1 }}>
        <ScrollView
          bounces={Platform.OS === "ios" ? false : true}
          showsVerticalScrollIndicator={false}
          onScroll={this.scrollViewScroll}
          scrollEventThrottle={10}>
          {/* <Text style={{ marginTop: 10, fontSize: 20, width: "90%", alignSelf: "center" }}>
            {LocalizedStrings.ai_settings_title}
          </Text> */}
          <ListItemWithIcon
            icon={require("../../Resources/Images/icon_aisettings_face.png")}
            style={{ marginTop: 10, borderRadius: 18, backgroundColor: "#ffffff", width: "92%", alignSelf: "center" }}
            title={LocalizedStrings['camera_face']}
            onPress={() => {
              if (!Device.isOwner) {
                Toast.success("face_deny_tips");
                return;
              }
              this.props.navigation.navigate('AIFaceSetting');
            }}
            // subtitle={LocalizedStrings['talk_for_push_tips']}
          />
          <ListItemWithIcon
            icon={require("../../Resources/Images/icon_aisettings_babycry.png")}
            style={{ marginTop: 10, borderRadius: 18, backgroundColor: "#ffffff", width: "92%", alignSelf: "center" }}
            title={LocalizedStrings['as_baby_cry']}
            onPress={() => {
              this.props.navigation.navigate('BabyCrying');
            }}
            // subtitle={LocalizedStrings['talk_for_push_tips']}
          />
          <ListItemWithIcon
            icon={require("../../Resources/Images/icon_aisettings_pet.png")}
            style={{ marginTop: 10, borderRadius: 18, backgroundColor: "#ffffff", width: "92%", alignSelf: "center" }}
            title={LocalizedStrings['pet_detection']}
            onPress={() => {
              this.props.navigation.navigate('PetIdentification');
            }}
            // subtitle={LocalizedStrings['talk_for_push_tips']}
          />
          <ListItemWithIcon
            icon={require("../../Resources/Images/icon_aisettings_dailystory.png")}
            style={{ marginTop: 10, borderRadius: 18, backgroundColor: "#ffffff", width: "92%", alignSelf: "center" }}
            title={LocalizedStrings['ss_daily_story']}
            onPress={() => {
              let is022or051 = VersionUtil.is022Model(Device.model) ? true : (this.isVip || this.isInExpireWindow); 
              console.log(is022or051);
              if (is022or051 && this.state.dailyStorySwitch) { // inexpireWindow== closeWindow= true 代表已经彻底过期了。
                console.log("是否开启每日故事：", this.state.dailyStorySwitch);
                this.props.navigation.navigate('DailyStoryList');
              } else {
                console.log("是不是vip：", this.state.isVip);
                // this.props.navigation.navigate('DailyStoryList');
                this.props.navigation.navigate('DailyStoryFirstEnter');
              }
            }}
            // subtitle={LocalizedStrings['talk_for_push_tips']}
          />
          <ListItemWithIcon
            icon={require("../../Resources/Images/icon_aisettings_smartmotion.png")}
            style={{ marginTop: 10, borderRadius: 18, backgroundColor: "#ffffff", width: "92%", alignSelf: "center" }}
            title={LocalizedStrings.setting_smart_monitoring}
            onPress={() => {
              this.props.navigation.navigate("SmartMonitorSetting");
            }}
            // subtitle={LocalizedStrings['talk_for_push_tips']}
          />
          
          { CameraConfig.supportGestureSwitchSetting() ? <ListItemWithIcon
            icon={require("../../Resources/Images/icon_aisettings_gesture.png")}
            style={{ marginTop: 10, borderRadius: 18, backgroundColor: "#ffffff", width: "92%", alignSelf: "center" }}
            title={LocalizedStrings['gesture_switch_txt']}
            onPress={() => {
              this.props.navigation.navigate("GestureSwitchSetting");
            }}
            // subtitle={LocalizedStrings['talk_for_push_tips']}
          /> : null}

          {/* <ListItem
            leftIcon={require("../../Resources/Images/icon_camera_idm_people_move.webp")}
            containerStyle={{ marginTop: 10, borderRadius: 18, backgroundColor: "#ffffff", width: "95%", alignSelf: "center" }}
            title={LocalizedStrings['talk_for_push_server']}
            subtitle={LocalizedStrings["one_key_call_tips"]}
            showSeparator={false}
          />
          <ListItemWithSwitch
            containerStyle={{ marginTop: 10, borderRadius: 18, backgroundColor: "#ffffff", width: "95%", alignSelf: "center" }}
            // subtitleNumberOfLines={1}
            title={LocalizedStrings['talk_for_push_server']}
            subtitle={LocalizedStrings["one_key_call_tips"]}
            showSeparator={false}
            value={this.state.talkForPushSwitch}
            onValueChange={(value) => {
            }}
          /> */}
        </ScrollView>
      </View>
    );
  }

  _onTalkSwitchValueChange(value) {
  }

  componentDidMount() {
    this.props.navigation.setParams({
      title: LocalizedStrings.ai_settings_title,
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      backgroundColor: "#f6f6f6",
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    // 查看是否在云存期内
    StorageKeys.IS_VIP_STATUS.then((res) => {
      if (typeof (res) === "string" || res == null) {
        StorageKeys.IS_VIP_STATUS = false;
        this.isVip = false;
      } else {
        this.isVip = res;
      }
      StorageKeys.IN_CLOSE_WINDOW.then((res) => {
        if (typeof (res) === "string" || res == null) {
          StorageKeys.IS_VIP_STATUS = false;
          this.isInExpireWindow = true;
        } else {
          this.isInExpireWindow = res;
        }
        // this._initData();
      });
    });
    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        this._getSetting();
      }
    );
    
  }


  componentWillUnmount() {

  }


  // 通过后端获取开关信息
  _getSetting() {
    AlarmUtil.getAlarmConfig().then((res) => {
      if (res.code != 0) {
        console.log("getdailyStorySwitch:", JSON.stringify(-1));
        Toast.fail('c_get_fail');
        return;
      }
      this.setState({
        dailyStorySwitch: res.data.dailyStorySwitch
      });
      console.log('先看看开关的状态:', res.data.dailyStorySwitch); // 开关为true
    }).catch((err) => {
      console.log("getdailyStorySwitch:", JSON.stringify(err));
      Toast.fail('c_get_fail', err);
    });
  }


  // android返回键处理
  onBackHandler = () => {
    if (this.props.navigation.state.params.onGoBack) {
      this.props.navigation.state.params.onGoBack();
      setTimeout(() => {
        this.props.navigation.popToTop();
      }, 300);
    } else {
      this.props.navigation.goBack();
    }
    return true;
  }
}