import React from 'react';
import { <PERSON>rollView, View, BackHandler, Platform, Text } from 'react-native';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import { styles } from '../setting/SettingStyles';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtilV2, {
  PIID_AI_EXTENSION_HOUR_SWITCH,
  PIID_COUGH_SWITCH,
  PIID_CRY_SWITCH, PIID_FACE_SWITCH, PIID_FENCE_SWITCH,
  PIID_MOVE_SWITCH,
  PIID_PEOPLE_SWITCH, PIID_PRIVATE_AREA_SWITCH, PIID_SOUND_SWITCH, SIID_AI_CUSTOM,
  SIID_AI_DETECTION, SIID_AI_EXTENSION, SIID_FENCE_DETECTION
} from '../util/AlarmUtilV2';
import Toast from '../components/Toast';
import { InputDialog, NavigationBar } from 'mhui-rn';
import { Styles } from 'miot/resources';
import { Device, Service } from 'miot';
import Util from '../util2/Util';
import ListItemWithIcon from '../widget/ListItemWithIcon';
import VersionUtil from '../util/VersionUtil';
import StorageKeys from '../StorageKeys';
import CameraConfig from '../util/CameraConfig';
import AlarmUtil from "../util/AlarmUtil";
import { Event } from '../config/base/CfgConst';
import BaseSettingPage from "../BaseSettingPage";
import DeviceSettingUtil from "../util/DeviceSettingUtil";

export default class AICameraSettingsV2 extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);
    this.state = {
      talkSwitch: false,
      talkForPushSwitch: false,
      isVip: undefined,
      inputNumDialog: false,
      dailyStorySwitch: false,
      peopleSwitch: false,
      faceSwitch: false,
      fenceSwitch: false,
      crySwitch: false,
      coughSwitch: false,
      abnormalSoundSwitch: false,
      hourAlarmSwitch: false,
      motionSwitch: false,
      commentErr: null,
      gestureCallSwitch: false,
      keyCallSwitch: false,
    };
    this.phoneNum = "";
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this._getSetting();
      }
    );
  }
  getTitle() {
    return LocalizedStrings.ai_settings_title;
  }

  getTitleBackgroundColor() {
    return Util.isDark() ? "#xm000000" : '#F6F6F6';
  }

  renderSettingContent() {
    return (
      <View style={{ backgroundColor: Util.isDark() ? "#xm000000" : '#F6F6F6', flex: 1 }}>
          <Text style={{ marginTop: 10, fontSize: 12, marginHorizontal: 28, color: "#666666" }}>
            {LocalizedStrings['ai_extend_fun']}
          </Text>


          <ListItemWithIcon
            icon={require("../../Resources/Images/icon_detect_on_time_alarm.png")}
            style={{ marginTop: 10, borderRadius: 18, backgroundColor: "#ffffff", width: "92%", alignSelf: "center" }}
            title={LocalizedStrings['on_time_alarm']}
            value={LocalizedStrings[this.state.hourAlarmSwitch ? 'already_open' : 'not_open']}
            onPress={() => {
              if (Device.isReadonlyShared) {
                Toast.success("share_user_permission_hint");
                return;
              }
              this.props.navigation.navigate('OnTimeAlarmPage');
            }}
          />
          <ListItemWithIcon
            icon={require("../../Resources/Images/icon_aisettings_dailystory.png")}
            style={{ marginTop: 10, borderRadius: 18, backgroundColor: "#ffffff", width: "92%", alignSelf: "center" }}
            title={LocalizedStrings['ss_daily_story']}
            iconTag={require("../../Resources/Images/icon_cloud_tag.png")}
            onPress={() => {
              if (Device.isReadonlyShared) {
                Toast.success("share_user_permission_hint");
                return;
              }
              let goList = this.isVip || this.isInExpireWindow;
              //后续逻辑需调整
              if (goList && this.state.dailyStorySwitch) { // inexpireWindow== closeWindow= true 代表已经彻底过期了。
                console.log("是否开启每日故事：", this.state.dailyStorySwitch);
                this.props.navigation.navigate('DailyStoryList');
              } else {
                console.log("是不是vip：", this.state.isVip);
                // this.props.navigation.navigate('DailyStoryList');
                this.props.navigation.navigate('DailyStoryFirstEnter');
              }
            }}
          />


        <Text style={{ marginTop: 20, fontSize: 12, marginHorizontal: 28, color: "#666666" }}>
            {LocalizedStrings['ai_detection_algorithm']}
          </Text>
          <ListItemWithIcon
            icon={require("../../Resources/Images/icon_detect_people.png")}
            style={{ marginTop: 10, borderRadius: 18, backgroundColor: "#ffffff", width: "92%", alignSelf: "center" }}
            title={LocalizedStrings['detect_people']}
            value={LocalizedStrings[this.state.peopleSwitch ? 'already_open' : 'not_open']}
            onPress={() => {
              if (Device.isReadonlyShared) {
                Toast.success("share_user_permission_hint");
                return;
              }
              this.props.navigation.navigate('MotionDetectionPage', { type: Event.PeopleMotion });
            }}
          />
          <ListItemWithIcon
            icon={require("../../Resources/Images/icon_aisettings_face.png")}
            style={{ marginTop: 10, borderRadius: 18, backgroundColor: "#ffffff", width: "92%", alignSelf: "center" }}
            title={LocalizedStrings['camera_face']}
            iconTag={require("../../Resources/Images/icon_cloud_tag.png")}
            value={LocalizedStrings[this.state.faceSwitch ? 'already_open' : 'not_open']}
            onPress={() => {
              if (!Device.isOwner) {
                Toast.success("face_deny_tips");
                return;
              }
              this.props.navigation.navigate('AIFaceSettingV2');
            }}
          />

          <ListItemWithIcon
            icon={require("../../Resources/Images/icon_detect_family.png")}
            style={{ marginTop: 10, borderRadius: 18, backgroundColor: "#ffffff", width: "92%", alignSelf: "center" }}
            title={LocalizedStrings['family_detection']}
            // value={LocalizedStrings[this.state.familySwitch ? 'already_open' : 'not_open']}
            onPress={() => {
              if (Device.isReadonlyShared) {
                Toast.success("share_user_permission_hint");
                return;
              }
              this.props.navigation.navigate('FamilyDetectionSetting');
            }}
          />

          <ListItemWithIcon
            icon={require("../../Resources/Images/icon_detect_sound.png")}
            style={{ marginTop: 10, borderRadius: 18, backgroundColor: "#ffffff", width: "92%", alignSelf: "center" }}
            title={LocalizedStrings['detect_sound']}
            value={LocalizedStrings[this.state.abnormalSoundSwitch ? 'already_open' : 'not_open']}
            onPress={() => {
              if (Device.isReadonlyShared) {
                Toast.success("share_user_permission_hint");
                return;
              }
              this.props.navigation.navigate('MotionDetectionPage', { type: Event.LouderSound });
            }}
          />

          <ListItemWithIcon
            icon={require("../../Resources/Images/icon_detect_motion.png")}
            style={{ marginTop: 10, borderRadius: 18, backgroundColor: "#ffffff", width: "92%", alignSelf: "center" }}
            title={LocalizedStrings['detect_move']}
            value={LocalizedStrings[this.state.motionSwitch ? 'already_open' : 'not_open']}
            onPress={() => {
              if (Device.isReadonlyShared) {
                Toast.success("share_user_permission_hint");
                return;
              }
              this.props.navigation.navigate('MotionDetectionPage', { type: Event.ObjectMotion });
            }}
          />
          <View style={{ height: 40 }} />
      </View>
    );
  }

  componentDidMount() {
    super.componentDidMount();
    // this.props.navigation.setParams({
    //   title: LocalizedStrings.ai_settings_title,
    //   type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
    //   left: [
    //     {
    //       key: NavigationBar.ICON.BACK,
    //       onPress: () => { this.props.navigation.goBack(); }
    //     }
    //   ],
    //   backgroundColor: "#f6f6f6",
    //   titleStyle: {
    //     fontSize: 18,
    //     color: '#333333',
    //     fontWeight: 500
    //   }
    // });
    // 查看是否在云存期内
    StorageKeys.IS_VIP_STATUS.then((res) => {
      if (typeof (res) === "string" || res == null) {
        StorageKeys.IS_VIP_STATUS = false;
        this.isVip = false;
      } else {
        this.isVip = res;
      }
      StorageKeys.IN_CLOSE_WINDOW.then((res) => {
        if (typeof (res) === "string" || res == null) {
          StorageKeys.IS_VIP_STATUS = false;
          this.isInExpireWindow = true;
        } else {
          this.isInExpireWindow = res;
        }
        // this._initData();
      });
    });
  }


  componentWillUnmount() {
    this.setState = () => {
      return;
    };
  }


  // 通过后端获取开关信息
  _getSetting() {

    // let params = [
    //   { "sname": SIID_AI_DETECTION, "pname": PIID_PEOPLE_SWITCH },
    //   { "sname": SIID_AI_DETECTION, "pname": PIID_MOVE_SWITCH },
    //   { "sname": SIID_AI_DETECTION, "pname": PIID_CRY_SWITCH },
    //   { "sname": SIID_AI_DETECTION, "pname": PIID_FACE_SWITCH },
    //   { "sname": SIID_AI_DETECTION, "pname": PIID_COUGH_SWITCH },
    //   { "sname": SIID_AI_DETECTION, "pname": PIID_SOUND_SWITCH },
    //   { "sname": SIID_FENCE_DETECTION, "pname": PIID_FENCE_SWITCH },
    //   { "sname": SIID_AI_CUSTOM, "pname": PIID_PRIVATE_AREA_SWITCH }
    // ]
    // M300 人形、人脸、响声、画面
    let params = [
      { "sname": SIID_AI_DETECTION, "pname": PIID_PEOPLE_SWITCH },
      { "sname": SIID_AI_DETECTION, "pname": PIID_MOVE_SWITCH },
      { "sname": SIID_AI_DETECTION, "pname": PIID_SOUND_SWITCH },
      { sname: SIID_AI_EXTENSION, pname: PIID_AI_EXTENSION_HOUR_SWITCH }
    ];
    if (VersionUtil.isAiCameraModel(Device.model)) {
      params.push({ "sname": SIID_AI_DETECTION, "pname": PIID_FACE_SWITCH });
    }
    AlarmUtilV2.getSpecPValue(params).then((res) => {
      console.log("getSpecPValue", res);
      if (res[0].code != 0) {
        Toast.fail('c_get_fail');
        return;
      }
      let stateObj = {
        peopleSwitch: res[0].value,
        motionSwitch: res[1].value,
        abnormalSoundSwitch: res[2].value,
        hourAlarmSwitch: res[3].value
      }
      if (VersionUtil.isAiCameraModel(Device.model)) {
        stateObj.faceSwitch = res[4].value;
      }

      this.setState(stateObj);
      console.log('先看看开关的状态:', res.data.dailyStorySwitch); // 开关为true
    }).catch((err) => {
      console.log("getdailyStorySwitch:", JSON.stringify(err));
      // Toast.fail('c_get_fail', err);
    });

    //获取每日故事开关，通过云端获取
    AlarmUtil.getAlarmConfig().then((res) => {
      if (res.code != 0) {
        console.log("getdailyStorySwitch:", JSON.stringify(-1));
        Toast.fail('c_get_fail');
        return;
      }
      if (!VersionUtil.isAiCameraModel(Device.model)) {
        // 使用云端人脸开关
        this.setState({
          dailyStorySwitch: res.data.dailyStorySwitch,
          faceSwitch: res.data.faceSwitch
        });
      } else {
        this.setState({
          dailyStorySwitch: res.data.dailyStorySwitch
        });
      }

      console.log('先看看开关的状态:', res.data.dailyStorySwitch); // 开关为true
    }).catch((err) => {
      console.log("getdailyStorySwitch:", JSON.stringify(err));
      Toast.fail('c_get_fail', err);
    });
    // this.getCallSettingData();
  }

  getCallSettingData() {
    DeviceSettingUtil.getDeviceSettingByKey(DeviceSettingUtil.clickCallSetting).then(res => {
      if (res.code == 0) {
        let settingsData = res.result.settings;
        if (settingsData && settingsData.call_setting) {
          let data = JSON.parse(settingsData.call_setting);
          this.callSettingData = data;
          let stateProps = {};
          if (data.hasOwnProperty("switch") && data.switch.hasOwnProperty('hand')) {
            stateProps.gestureCallSwitch = data.switch.hand;
          }

          if (data.hasOwnProperty("switch") && data.switch.hasOwnProperty('mijia')) {
            stateProps.keyCallSwitch = data.switch.mijia;
          }

          this.setState(stateProps);
        }
      }
    }).catch(error => {
      console.log("======= error: ", error);
    });
  }

  // android返回键处理
  onBackHandler = () => {
    if (this.props.navigation.state.params.onGoBack) {
      this.props.navigation.state.params.onGoBack();
      setTimeout(() => {
        this.props.navigation.popToTop();
      }, 300);
    } else {
      this.props.navigation.goBack();
    }
    return true;
  }
}