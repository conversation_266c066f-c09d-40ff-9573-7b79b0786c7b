import { ListItemWithSwitch, NavigationBar, ListItem } from "mhui-rn";
import React, { Component } from "react";
import { Styles } from "miot/resources";
import { StyleSheet, View, Text, ScrollView, Image, Dimensions, TouchableOpacity, DeviceEventEmitter, Platform } from "react-native";
import { DarkMode } from 'miot';
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import { MessageDialog } from "miot/ui/Dialog";
import Toast from "../Toast";
import AbstractDialog from "miot/ui/Dialog/AbstractDialog";
import MHDatePicker from "miot/ui/MHDatePicker";
import AlarmUtil from "../util/AlarmUtil";
const { width: screenWidth, height: screenHeight } = Dimensions.get("screen");
const TAG = "CruiseSettingPage";
let alarmTimeInfoModel = { intervalRepeatArray: [] };

export default class CruiseSettingPage extends Component {
  static navigationOptions = ({ navigation }) => {
    return {
      header: (
        <NavigationBar
          type={NavigationBar.TYPE.LIGHT}
          left={[
            {
              key: NavigationBar.ICON.BACK,
              onPress: (_) => navigation.goBack()
            }
          ]}
          title={navigation.getParam("navTitle")}
        />
      )
    };
  };
  constructor(props) {
    super(props);
    this.prePositions = [];
    this.positionCount = 0;
    this.state = {
      alarmTimeSlot: "", // 报警时间段
      isPanoramicCruise: false, // 是否为全景巡航
      cruiseItemsArray: [
        { cruiseTitle: LocalizedStrings.cruise_setting_default_title,
          subTitle: LocalizedStrings.cruise_setting_default_hint,
          key: "item0" },
        { cruiseTitle: LocalizedStrings.cruise_setting_custom_title,
          subTitle: LocalizedStrings.cruise_setting_custom_hint,
          key: "item1" }
      ],

      isShowCustomCruiseFrequencyDialog: false,
      switchButtonOpen: false,
      alarmTimeInterval: "", // 巡航 频率
      addOftenLocationGuidViewVisible: false
    };
    this.darkMode = false;
    this.cruiseFrequencyItems = [
      { title: `${ LocalizedStrings.cruiseSetting_cruiseInterval }10${ LocalizedStrings.tip_time_minute }`,
        select: false },
      { title: `${ LocalizedStrings.cruiseSetting_cruiseInterval }30${ LocalizedStrings.tip_time_minute }`,
        select: false },
      { title: `${ LocalizedStrings.cruiseSetting_cruiseInterval }1${ LocalizedStrings.cruiseSetting_cruiseIntervalhour }`,
        select: false },
      { title: `${ LocalizedStrings.cruiseSetting_cruiseInterval }2${ LocalizedStrings.cruiseSetting_cruiseIntervalhour }`,
        select: false },
      { title: `${ LocalizedStrings.cruiseSetting_cruiseInterval }24${ LocalizedStrings.cruiseSetting_cruiseIntervalhour }`,
        select: false },
      { title: LocalizedStrings.plug_timer_sef_define,
        select: false }];
    this.lastFreqIndexArray = 5;
    this.isCallBack = false;
    this.refreshTimeData = false;
    this.rawData = { "freq": "00:10", "start_time": "00:00", "end_time": "23:59", "repeat": 0, "mode": 0, "position": "[]" }; // 巡航设置原始数据
    this.didFocusListener = this.props.navigation.addListener(
      // 回到当前页面 或者第一次进来
      "didFocus",
      () => {
        console.log(`回到当前页面非第一次进来_isCallBack:${ this.isCallBack } this.refreshTimeData:${ this.refreshTimeData }`);
      }
    );
    let colorScheme = DarkMode.getColorScheme();
    if (colorScheme == 'dark') {
      this.darkMode = true;
    } else {
      this.darkMode = false;
    }
  }

  onSelect = (data) => {
    console.log(TAG, `selectInterval ${ JSON.stringify(data) }`);
    this.timePickerConfirm = true;
    let hour = Number.parseInt(data.rawArray[0]);
    let minute = Number.parseInt(data.rawArray[1]);
    if (hour > 0) {
      if (minute > 0) {
        this.timeIntervalText = `${ LocalizedStrings.cruiseSetting_cruiseInterval }${ hour }${ LocalizedStrings.cruiseSetting_cruiseIntervalhour }${ minute }${ LocalizedStrings.tip_time_minute }`;
      } else {
        this.timeIntervalText = `${ LocalizedStrings.cruiseSetting_cruiseInterval }${ hour }${ LocalizedStrings.cruiseSetting_cruiseIntervalhour }`;
      }
    } else if (minute >= 5) {
      this.timeIntervalText = `${ LocalizedStrings.cruiseSetting_cruiseInterval }${ minute }${ LocalizedStrings.tip_time_minute }`;
    } else {
      Toast.loading("cruiseSetting_cruiseInterval_default_tips");
      this.setState({ selectCruiseFrequencyViewVisible: false });
      return;
    }
    alarmTimeInfoModel.intervalRepeatArray = data.rawArray;
    this.lastFreqIndexArray = 5;
    this.cruiseFrequencyItems.forEach((item) => {
      item.select = false;
    });
    this.cruiseFrequencyItems[this.lastFreqIndexArray].select = true;
    console.log("onSelect=", this.timeIntervalText);
  };

  componentDidMount() {
    this.props.navigation.setParams({
      navTitle: ""
    });
    this.queryCruiseProperty();
  }

  state = {
    isAddCruisePoint: false // 巡航点添加提示弹窗
  };

  getFreqStr() {
    let freqStrs = this.rawData.freq.split(":");
    let hour = Number.parseInt(freqStrs[0]);
    let minute = Number.parseInt(freqStrs[1]);
    if (hour > 0) {
      if (minute > 0) {
        this.timeIntervalText = `${ LocalizedStrings.cruiseSetting_cruiseInterval }${ hour }${ LocalizedStrings.cruiseSetting_cruiseIntervalhour }${ minute }${ LocalizedStrings.tip_time_minute }`;
      } else {
        this.timeIntervalText = `${ LocalizedStrings.cruiseSetting_cruiseInterval }${ hour }${ LocalizedStrings.cruiseSetting_cruiseIntervalhour }`;
        this.lastFreqIndexArray = hour == 1 ? 2 : hour == 2 ? 3 : hour == 24 ? 4 : 5;
      }
    } else {
      this.timeIntervalText = `${ LocalizedStrings.cruiseSetting_cruiseInterval }${ minute }${ LocalizedStrings.tip_time_minute }`;
      this.lastFreqIndexArray = minute == 10 ? 0 : minute == 30 ? 1 : 5;
    }
    this.cruiseFrequencyItems.forEach((item) => {
      item.select = false;
    });
    this.cruiseFrequencyItems[this.lastFreqIndexArray].select = true;
    this.updateAlarmTimeIntervalTime(this.lastFreqIndexArray);
    console.log("getFreqStr=", this.timeIntervalText);
  }

  // 获取巡航设置
  queryCruiseProperty() {
    AlarmUtil.getCruiseConfig().then((res) => {
      console.log("getCruiseConfig res=", JSON.stringify(res));
      // {"freq":"00:10","start_time":"00:00","end_time":"23:59","repeat":127,"mode":1,"position":"[1,2,3,4]"}
      this.rawData = JSON.parse(res[0].value);
      // this.rawData = {"freq":"00:10","start_time":"10:00","end_time":"08:59","repeat":127,"mode":1,"position":"[1,2,3,4]"};
      this.getFreqStr();
      this.getCruisePosition();
    }).catch((err) => {
      console.log("getCruiseConfig err=", JSON.stringify(err));
    });
    AlarmUtil.getCruiseSwitch().then((res) => {
      console.log("getCruiseSwitch res=", JSON.stringify(res));
      this.setState({ switchButtonOpen: res[0].value });
    }).catch((err) => {
      console.log("getCruiseSwitch err=", JSON.stringify(err));
    });
  }

  getCruisePosition() {
    AlarmUtil.getPrePositions().then((res) => { 
      console.log(`get_preset_position = ${ JSON.stringify(res) }`);
      this.prePositions = JSON.parse(res[0].value);
      this.prePositions.sort(function(a, b) { return a.location - b.location; });
      console.log(`get_preset_position prePositions = ${ JSON.stringify(this.prePositions) }`);
      
      this.positionCount = 0;
      let cruisePosition = JSON.parse(this.rawData.position);
      this.prePositions.forEach((item) => {
        if (cruisePosition && cruisePosition.length && cruisePosition.length > 0) {
          let flag = cruisePosition.indexOf(item.idx, 0);
          if (flag >= 0) {
            this.positionCount++;
          }
        }
      });
      this.setState({ isPanoramicCruise: this.rawData.mode == 0, alarmTimeInterval: this.timeIntervalText,
        alarmTimeSlot: `${ this.rawData.start_time } ~ ${ this.rawData.end_time }` });
    }).catch((err) => {
      console.log(`get_preset_position error = ${ err }`);
      this.setState({ isPanoramicCruise: this.rawData.mode == 0, alarmTimeInterval: this.timeIntervalText,
        alarmTimeSlot: `${ this.rawData.start_time } ~ ${ this.rawData.end_time }` });
    });
  }

  /**
   * 设置巡航模式
   * @param {*} value true：全景巡航 false：自定义巡航
   */
  setCruiseMode(value) {
    let positions = JSON.parse(this.rawData.position);
    if (!value) {
      if (this.prePositions.length < 2) {
        this.setState({ addOftenLocationGuidViewVisible: true });
        return;
      }
      if (positions.length < 2) {
        positions = [];
        this.prePositions.forEach((item) => {
          positions.push(item.idx);
        });
        positions.sort();
        this.rawData.position = JSON.stringify(positions);
        console.log("setCruiseMode positions ======================", this.rawData.position);
      }
    }
    let cruiseConfigurationObj = {
      freq: this.rawData.freq,
      start_time: this.rawData.start_time,
      end_time: this.rawData.end_time,
      repeat: this.rawData.repeat,
      mode: value == true ? 0 : 1,
      position: this.rawData.position
    };
    Toast.loading("c_setting");
    AlarmUtil.putCruiseConfig(JSON.stringify(cruiseConfigurationObj)).then(() => {
      this.positionCount = positions.length;
      this.setState({
        isPanoramicCruise: value
      });
      Toast.loading("c_set_success");
      this.rawData = cruiseConfigurationObj;
    }).catch((err) => {
      console.log("setCruiseMode err=", JSON.stringify(err));
    });
  }

  /**
   * 跳转到巡航点管理
   */
  goToCruiseCustomPage() {
    this.props.navigation.navigate("EditSelectPrePositions", {
      isSelectPosition: true,
      positionArray: this.prePositions,
      selectPositions: this.rawData.position,
      callBack: (params) => {
        console.log("call back data=", params);
        let cruiseConfigurationObj = {
          freq: this.rawData.freq,
          start_time: this.rawData.start_time,
          end_time: this.rawData.end_time,
          repeat: this.rawData.repeat,
          mode: 1,
          position: params
        };
        console.log("call back data cruiseConfigurationObj=", JSON.stringify(cruiseConfigurationObj));
        Toast.loading("c_setting");
        AlarmUtil.putCruiseConfig(JSON.stringify(cruiseConfigurationObj)).then(() => {
          this.rawData = cruiseConfigurationObj;
          this.positionCount = JSON.parse(params).length;
          this.forceUpdate();
          Toast.loading("c_set_success");
        }).catch((err) => {
          console.log("setCruiseMode err=", JSON.stringify(err));
          Toast.loading("c_set_fail");
          this.forceUpdate();
        });
      }
    });
  }

  // 监听列表滚动事件
  scrollViewScroll = (event) => {
    const y = event.nativeEvent.contentOffset.y;
    // 设置标题栏和状态栏的透明度 titleOpacity
    // 当页面滚动的距离等于标题栏的高度时，其透明度变为1
    let flag = y > 28;
    if (this.showTitle == flag) {
      return;
    }
    if (flag) {
      this.showTitle = true;
      this.props.navigation.setParams({ navTitle: this.props.navigation.state.params.title });
    } else {
      this.showTitle = false;
      this.props.navigation.setParams({ navTitle: "" });
    }
  };

  render() {
    let backGroundColor = this.darkMode ? {} : { backgroundColor: "#fff" };
    return (
      <View style={[styles.container, backGroundColor]}>

        <ScrollView
          bounces={Platform.OS === "ios" ? false : true}
          showsVerticalScrollIndicator={false}
          onScroll={this.scrollViewScroll}
          scrollEventThrottle={10}
        >
          <View style={{ flexDirection: "row", flexWrap: "wrap" }} key={6}>
            <Text style={{ fontSize: 30, color: "#333333", position: "relative", marginLeft: 25, marginTop: 5 }}>
              {this.props.navigation.state.params.title}
            </Text>
            <View style={{ marginTop: 12, flexDirection: "row", flexWrap: "wrap", justifyContent: "space-between", width: "100%", position: "relative" }}>
              {[{
                text: LocalizedStrings["nvs_night"],
                uri: require("../../Resources/Images/ai2_cruise_setting_img.webp") // 后面可能需要改成白天的配图
              }].map((item, i) => {
                return (
                  <View style={{ width: "100%" }} key={i}>
                    <Image key={i} source={item.uri} style={{ marginLeft: 20, width: screenWidth - 40, height: (179 / 320) * (screenWidth - 40), borderRadius: 9 }}/>
                  </View>
                );
              })}
            </View>
            <Text style={{ marginTop: 10, fontSize: 12, color: "#999999", position: "relative", marginLeft: 27, marginRight: 27 }} >
              {LocalizedStrings.auto_cruise_sub_title}
            </Text>
          </View>
          <View style={{ marginLeft: 27, marginRight: 27, backgroundColor: "#E5E5E5", height: 0.6, marginTop: 15, marginBottom: 20 }}/>

          <ListItemWithSwitch
            title={this.props.navigation.state.params.title}
            value={this.state.switchButtonOpen}
            // titleStyle={{ fontSize: 12, color: '#000000' }}
            // subtitleStyle={{ fontSize: 10, color: '#000000' }}
            titleNumberOfLines={3}
            subtitleNumberOfLines={4}
            unlimitedHeightEnable={true}
            onValueChange={(value) =>
              this._onSwitchAutoCruiseValueChange(value)
            }
            showSeparator={false}/>
          <View style={{ marginLeft: 27, marginRight: 27, backgroundColor: "#E5E5E5", height: 0.6, marginTop: 20 }}/>
          {this.state.switchButtonOpen && (
            <View style={{ marginBottom: 10 }}>
              <Text style={{ marginLeft: 27, fontSize: 12, color: "#666666", marginTop: 27 }}>
                {LocalizedStrings.cruise_time}
              </Text>
              <ListItem
                allowFontScaling={false}
                containerStyle={{ marginLeft: 27, width: screenWidth - 54, height: 54, paddingLeft: 0, paddingRight: 0, marginTop: 10 }}
                titleStyle={{ fontSize: 16, color: "#000000" }}
                valueStyle={{ fontSize: 13, color: "#999999" }}
                title={LocalizedStrings.cruise_frequency}
                value={this.state.alarmTimeInterval}
                onPress={(_) => {
                  this.beforeFreqIndex = this.lastFreqIndexArray;
                  this.setState({
                    selectCruiseFrequencyViewVisible: true
                  });
                }}
                showSeparator={false}/>
              <ListItem
                allowFontScaling={false}
                containerStyle={{ marginLeft: 27, width: screenWidth - 54, height: 54, paddingLeft: 0, paddingRight: 0 }}
                titleStyle={{ fontSize: 16, color: "#000000" }}
                valueStyle={{ fontSize: 13, color: "#999999" }}
                title={LocalizedStrings["cruise_period"]}
                value={this.state.alarmTimeSlot}
                onPress={(_) => {
                  this.onEnterCruiseTimeSettingPage();
                }}
                showSeparator={false}/>
              <View style={{ marginLeft: 27, marginRight: 27, backgroundColor: "#E5E5E5", height: 0.6, marginTop: 10 }}></View>
              <Text style={{ marginLeft: 27, fontSize: 12, color: "#666666", marginTop: 27 }}>
                {LocalizedStrings.cruise_mode}
              </Text>

              {this.state.cruiseItemsArray.map((item, index) => {
                return (
                  <View key={index}>
                    <TouchableOpacity onPress={() => {
                      if (item.key == "item0") {
                        this.setCruiseMode(true);
                      } else {
                        this.setCruiseMode(false);
                      }
                    }}>
                      <View style={{
                        marginLeft: 22, marginRight: 22, minHeight: 70,
                        backgroundColor:
                            (item.key == "item0" &&
                              this.state.isPanoramicCruise) ||
                            (item.key == "item1" &&
                              !this.state.isPanoramicCruise)
                              ? "rgba(50,186,192,0.1)"
                              : "rgba(0,0,0,0.06)", borderRadius: 12, flexDirection: "row", alignItems: "center", marginTop: 16, paddingLeft: 10,
                        paddingRight: 19, borderBottomLeftRadius:
                            item.key == "item1" && !this.state.isPanoramicCruise
                              ? 0
                              : 12,
                        borderBottomRightRadius:
                            item.key == "item1" && !this.state.isPanoramicCruise
                              ? 0
                              : 12
                      }}>
                        {(item.key == "item0" && this.state.isPanoramicCruise) || (item.key == "item1" && !this.state.isPanoramicCruise) ? (
                          <Image style={{ width: 22, height: 22 }} source={require("../../Resources/Images/plugin_icon_customVoice_selectItem.png")}></Image>
                        ) : (<View style={{ width: 22, height: 22 }}></View>)}

                        <View style={{ marginLeft: 10 }}>
                          <Text style={{ color:
                            (item.key == "item0" &&
                              this.state.isPanoramicCruise) ||
                            (item.key == "item1" &&
                              !this.state.isPanoramicCruise)
                              ? "#32BAC0"
                              : "#000000", fontSize: 16 }}>

                            {item.cruiseTitle}
                          </Text>
                          <Text style={{ color:
                            (item.key == "item0" &&
                              this.state.isPanoramicCruise) ||
                            (item.key == "item1" &&
                              !this.state.isPanoramicCruise)
                              ? "#32BAC0"
                              : "#000000", fontSize: 13, marginTop: 5 }}>

                            {item.subTitle}
                          </Text>
                        </View>
                      </View>
                    </TouchableOpacity>
                    {item.key == "item1" && !this.state.isPanoramicCruise && (
                      <ListItem
                        allowFontScaling={false}
                        containerStyle={{
                          backgroundColor: "rgba(50,186,192,0.1)", marginLeft: 22, width: screenWidth - 44, height: 54, paddingLeft: 0, paddingRight: 20, borderBottomLeftRadius: 12, borderBottomRightRadius: 12
                        }}
                        titleStyle={{
                          fontSize: 16,
                          color: "#000000",
                          marginLeft: 42
                        }}
                        valueStyle={{ fontSize: 13, color: "#32BAC0" }}
                        title={LocalizedStrings.pre_set_position_str}
                        value={`${ this.positionCount }`}
                        onPress={(_) => {
                          if (this.prePositions.length <= 0) {
                            this.setState({
                              addOftenLocationGuidViewVisible: true
                            });
                          } else {
                            this.goToCruiseCustomPage();
                          }
                        }}
                        showSeparator={false}
                        hideArrow={false}
                      />
                    )}
                  </View>
                );
              })}
            </View>
          )}

        </ScrollView>

        <MessageDialog
          visible={this.state.isAddCruisePoint}
          message={LocalizedStrings.cruise_setting_custom_dialog_title}
          onDismiss={(_) => this.setState({ isAddCruisePoint: false })}
          messageStyle={{ textAlign: "center" }}
          buttons={[
            { text: LocalizedStrings.cancel },
            { text: LocalizedStrings.cruise_setting_custom_dialog_OK,
              callback: (result) => {
                this.setState({ isAddCruisePoint: false });
                this.goToCruiseCustomPage();
              }
            }
          ]}
        />
        {this._renderSelectCruiseFrequencyView()}
        {this._renderCustomCruiseFrequencyView()}
        {this._renderAddOftenLocationGuidView()}
      </View>
    );
  }

  _renderAddOftenLocationGuidView() {
    return (
      <AbstractDialog
        style={[styles.selectCruisePointViewStyle, { height: 402 }]}
        visible={this.state.addOftenLocationGuidViewVisible}
        title={LocalizedStrings.not_set_position_now}
        showSubtitle={false}
        onDismiss={() => {
          this.setState({ addOftenLocationGuidViewVisible: false });
        }}
        useNewTheme
        canDismiss={true}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ addOftenLocationGuidViewVisible: false });
              console.log('取消');
            }
          },
          {
            text: LocalizedStrings.cruise_setting_custom_dialog_OK,
            callback: () => {
              this.setState({ addOftenLocationGuidViewVisible: false });
              this.props.navigation.navigate("LiveVideoPageV2");
              DeviceEventEmitter.emit("ADD_OFTEN_LOOK_POSITION", {});
              console.log('开始添加');
            }
          }
        ]}>
        <Text style={{ fontSize: 16, color: "#333333", marginTop: -10, marginLeft: 27, marginRight: 27 }}>
          {LocalizedStrings.not_set_position_now_subtips}
        </Text>
        <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
          <Image style={{ marginLeft: 27, marginRight: 27, height: 179, width: 325 }}
            source={require("../../Resources/Images/ai2_pre_position_img.webp")}></Image></View>
      </AbstractDialog>
    );
  }

  _onSelectCruiseFrequencyDismiss() {
    this.setState({ selectCruiseFrequencyViewVisible: false });
    // this._handleRecoverFreq();
    this.lastFreqIndexArray = this.beforeFreqIndex;
    
    let selectIndex = this.lastFreqIndexArray;
    let cruiseFrequencyItemNewArray = this.cruiseFrequencyItems;
    cruiseFrequencyItemNewArray.forEach(function(currentValue, currentIndex, arr) {
      if (selectIndex == currentIndex) {
        currentValue.select = true;
      } else {
        currentValue.select = false;
      }
    });
  }

  _renderSelectCruiseFrequencyView() {
    return (
      <AbstractDialog
        style={[styles.selectCruiseFrequencyViewStyle]}
        visible={this.state.selectCruiseFrequencyViewVisible}
        showSubtitle={false}
        onDismiss={() => {
          this._onSelectCruiseFrequencyDismiss();
        }}
        showTitle={false}
        showButton={false}
        canDismiss={true}
      >
        <Text style={{ fontSize: 16, textAlign: "center", color: "#000000", marginTop: 25 }}>
          {LocalizedStrings.cruise_frequency}
        </Text>
        <View style={{ marginTop: 15 }}>
          {this.cruiseFrequencyItems.map((item, index) => {
            return (
              <TouchableOpacity key={index}
                onPress={() => {
                  this.onClickCruiseFrequencyItem({ index: index });
                }}>
                <View key={index} style={{ maxWidth: "100%", width: screenWidth, height: 54, backgroundColor: 
                  item.select == true ? "rgba(50,186,192,0.1)" : "#ffffff", flexDirection: "row", alignItems: "center", justifyContent: "space-between" }}>
                  <View style={{ flexDirection: "row", alignItems: "center", justifyContent: "flex-start" }}>
                    <Text style={{ marginLeft: 30, fontSize: 16, color: item.select ? "#32BAC0" : "#000000" }}>
                      {item.title}
                    </Text>
                  </View>
                  {item.select == true ? 
                    <Image source={require("../../Resources/Images/plugin_icon_customVoice_selectItem.png")} style={{ width: 22, height: 22, marginRight: 30 }}></Image>
                    : <View style={{ width: 22, height: 22, marginRight: 30 }}></View>}
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
        <View style={{ maxWidth: "100%", flexDirection: "column", justifyContent: "center", alignItems: "center", marginTop: 10, paddingBottom: 20 }}>
          <View style={{ flexDirection: "row" }}>
            <TouchableOpacity style={{ }}
              onPress={() => {
                this._onSelectCruiseFrequencyDismiss();
              }}>
              <View style={{ width: 147, height: 46, backgroundColor: "#F5F5F5", borderRadius: 23, justifyContent: "center", alignItems: "center" }}>
                <Text style={{ fontSize: 16, color: "#4C4C4C" }}>{LocalizedStrings.action_cancle}</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity style={{ marginStart: 20 }}
              onPress={() => {
                this.setState({
                  selectCruiseFrequencyViewVisible: false,
                  alarmTimeInterval: this.timeIntervalText
                });

                this.setCameraCruiseFrequency(alarmTimeInfoModel);
              }}>
              <View style={{ width: 147, height: 46, backgroundColor: "#32BAC0", borderRadius: 23, justifyContent: "center", alignItems: "center" }}>
                <Text style={{ fontSize: 16, color: "#ffffff" }}>{LocalizedStrings.btn_confirm}</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </AbstractDialog>
    );
  }

  onClickCruiseFrequencyItem(item) {
    // this.cruiseFrequencyItems = cruiseFrequencyItemNewArray;
    if (item.index == 5) {
      // this.selectedFreqIndexArray = [5];
      // this.lastFreqIndexArray = item.index;
      this.setState({
        isShowCustomCruiseFrequencyDialog: true
      });
    } else {
      let cruiseFrequencyItemNewArray = this.cruiseFrequencyItems;
      cruiseFrequencyItemNewArray.forEach(function(currentValue, currentIndex, arr) {
        if (item.index == currentIndex) {
          currentValue.select = true;
        } else {
          currentValue.select = false;
        }
      });
      this.lastFreqIndexArray = item.index;
      this.updateAlarmTimeIntervalTime(item.index);
      this.forceUpdate();
    }
  }

  /** 更新展示巡航频率 */
  updateAlarmTimeIntervalTime(result) {
    // this.selectedFreqIndexArray = result;
    let timeIntervalFlag;
    let hourFlag;

    switch (result) {
      case 0: {
        hourFlag = 0;
        timeIntervalFlag = 10;
        this.timeIntervalText = `${ LocalizedStrings.cruiseSetting_cruiseInterval }10${ LocalizedStrings.tip_time_minute }`;
        break;
      }
      case 1: {
        hourFlag = 0;
        timeIntervalFlag = 30;
        this.timeIntervalText = `${ LocalizedStrings.cruiseSetting_cruiseInterval }30${ LocalizedStrings.tip_time_minute }`;
        break;
      }
      case 2: {
        hourFlag = 1;
        timeIntervalFlag = 0;
        this.timeIntervalText = `${ LocalizedStrings.cruiseSetting_cruiseInterval }1${ LocalizedStrings.cruiseSetting_cruiseIntervalhour }`;
        break;
      }
      case 3: {
        hourFlag = 2;
        timeIntervalFlag = 0;
        this.timeIntervalText = `${ LocalizedStrings.cruiseSetting_cruiseInterval }2${ LocalizedStrings.cruiseSetting_cruiseIntervalhour }`;
        break;
      }
      case 4: {
        hourFlag = 24;
        timeIntervalFlag = 0;
        this.timeIntervalText = `${ LocalizedStrings.cruiseSetting_cruiseInterval }24${ LocalizedStrings.cruiseSetting_cruiseIntervalhour }`;
        break;
      }
      default:
        break;
    }
    alarmTimeInfoModel.intervalRepeatArray = [hourFlag, timeIntervalFlag];
    console.log("alarmTimeInfoModel.intervalRepeatArray=", alarmTimeInfoModel.intervalRepeatArray);
  }

  padNumString(num, n) {
    if (num == undefined || num == null) {
      return 0;
    }
    let len = num.toString().length;
    while (len < n) {
      num = `0${ num }`;
      len++;
    }
    return num;
  }

  setCameraCruiseFrequency(alrmTimeZoonInfo) {
    let cruiseConfigurationObj = {
      freq: `${ this.padNumString(alrmTimeZoonInfo.intervalRepeatArray[0], 2) }:${ this.padNumString(alrmTimeZoonInfo.intervalRepeatArray[1], 2) }`,
      start_time: this.rawData.start_time,
      end_time: this.rawData.end_time,
      repeat: this.rawData.repeat,
      mode: this.rawData.mode,
      position: this.rawData.position
    };
    Toast.loading("c_setting");
    // cruiseConfigurationObj = {"freq":"00:10","start_time":"00:00","end_time":"23:59","repeat":127,"mode":1,"position":"[1,2,3,4]"}
    console.log("cruiseConfigurationObj=", JSON.stringify(cruiseConfigurationObj));
    AlarmUtil.putCruiseConfig(JSON.stringify(cruiseConfigurationObj)).then(() => {
      this.rawData = cruiseConfigurationObj;
      Toast.loading("c_set_success");
    }).catch((err) => {
      console.log("putCruiseConfig err=", JSON.stringify(err));
    });

  }

  // 选择自定义巡航频率弹框
  _renderCustomCruiseFrequencyView() {
    return (
      // 时间选择器（24小时制）
      <MHDatePicker
        visible={this.state.isShowCustomCruiseFrequencyDialog}
        title={LocalizedStrings.cruise_frequency_custom}
        type={MHDatePicker.TYPE.TIME24}
        onDismiss={(_) => this.onDismiss()}
        onSelect={(res) => this.onSelect(res)}
        showSubtitle={false}
        current={["00", "05"]} // 进入默认显示00时,05分
      />
    );
  }

  onDismiss() {
    this.setState({
      isShowCustomCruiseFrequencyDialog: false
    });
    // if (this.timePickerConfirm !== true) {
    //   this._handleRecoverFreq();
    // }
  }

  _onSwitchAutoCruiseValueChange(value) {
    console.log(TAG, `切换自动巡航开关${ value }`);
    Toast.loading("c_setting");
    AlarmUtil.putCruiseSwitch(value).then(() => {
      this.setState({
        switchButtonOpen: value
      });
      Toast.loading("c_set_success");
    }).catch((err) => {
      console.log("putCruiseSwitch err=", JSON.stringify(err));
    });
  }

  onEnterCruiseTimeSettingPage() {
    console.log(TAG, `进入巡航时间段设置页面`);
    this.isCallBack = true;
    if (this.refreshTimeData) {
      this.refreshTimeData = false;
    }
    this.props.navigation.navigate("CruiseTimeSlotPage", {
      paramModel: this.rawData,
      callBack: (alarmParams) => this.callBackAlarmTimeInfo(alarmParams)
    });
  }

  /**
   *子页面设置结束回调信息
   *
   * @param {*} alarmParams
   */
  callBackAlarmTimeInfo(data) {
    this.refreshTimeData = true;
    Toast.loading("c_setting");

    console.log("callBackAlarmTimeInfo=", JSON.stringify(data));
    AlarmUtil.putCruiseConfig(JSON.stringify(data)).then(() => {
      this.rawData = data;
      Toast.loading("c_set_success");
      let endTime = this.rawData.start_time > this.rawData.end_time
        ? `次日${ this.rawData.end_time }`
        : this.rawData.end_time;
      this.setState({ alarmTimeSlot: `${ this.rawData.start_time } ~ ${ endTime }` });
    }).catch((err) => {
      console.log("callBackAlarmTimeInfo err=", JSON.stringify(err));
    });
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Styles.common.backgroundColor
  },
  blank: {
    height: 20,
    backgroundColor: Styles.common.backgroundColor,
    borderTopColor: Styles.common.hairlineColor,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderBottomColor: Styles.common.hairlineColor,
    borderBottomWidth: StyleSheet.hairlineWidth
  },
  cruiseSettingCustom: {
    fontSize: 16,
    color: "#00B58B",
    textAlign: "center",
    padding: 15,
    backgroundColor: "rgba(255,255,255,1.0)"
  },
  selectCruiseFrequencyViewStyle: {
    width: screenWidth,
    bottom: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    marginHorizontal: 0,
    height: 475
  },
  selectCruisePointViewStyle: {
    width: screenWidth,
    bottom: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    marginHorizontal: 0,
    height: 197
  }
});
