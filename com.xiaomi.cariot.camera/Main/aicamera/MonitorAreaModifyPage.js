import React from 'react';
import { View, Text, Image, ImageBackground, Dimensions, ART, PanResponder, Platform, BackHandler } from 'react-native';

let {
  Surface,
  Shape,
  Path
} = ART;

import { Device, Service, Host } from 'miot';
import { NavigationBar } from 'mhui-rn';
import { LoadingDialog } from 'miot/ui/Dialog';
import Toast from '../components/Toast';
import Util from "../util2/Util";
import VersionUtil from "../util/VersionUtil";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';

const ScreenOptions = Dimensions.get('window');
const viewWidth = ScreenOptions.width - 24 * 2;
const viewHeight = viewWidth * 9 / 16;
// 将直播流播放器分成8*4个矩形块
const itemWidth = viewWidth / 8;
const itemHeight = viewHeight / 4;
const REACT_MARGIN = 3; // 矩形线框的到画面边缘的外边距
const CIRCLE_RADIUS = 3; // 矩形线框这个角上实心圆的半径
const DEVIATION_VALUE = 20; // 线框宽度、圆半径、除法取整引起的误差，试用
const VALID_TOUCH_RANGE = 15; // 四个边角的有效触摸范围
export default class MonitorAreaModifyPage extends React.Component {
  static navigationOptions = (navigation) => {
    return { headerTransparent: true, header: null };
  };

  constructor(props, context) {
    super(props, context);
    this.state = {
      progressing: false
    };
    this.timeStamp = Date.now();
    // 用户划定线框区域的左上和右下角坐标
    this.rectDatas = this.props.navigation.getParam('areaData');
    // 有效看护区域矩形背景框的左上和右下角坐标
    this.rectBackGround = this.rectBackGround = [Math.floor(this.rectDatas[0] / itemWidth) * itemWidth, Math.floor(this.rectDatas[1] / itemHeight) * itemHeight,
      Math.ceil(this.rectDatas[2] / itemWidth) * itemWidth, Math.ceil(this.rectDatas[3] / itemHeight) * itemHeight];
    this.distanceData = [0, 0, 0, 0];
    this.touchPosition = 0; // 开始拖拽的点的坐标位或得出的值

    this.panResponder = PanResponder.create({
      onStartShouldSetPanResponder: () => true, // 刚开始的时候
      onMoveShouldSetPanResponder: () => true,
      onShouldBlockNativeResponder: () => true,
      onPanResponderTerminationRequest: () => true, // 不允许其他人抢占。
      onPanResponderGrant: (evt) => {
        let x = evt.nativeEvent.locationX;// 开始触摸点相对于父View的横坐标
        let y = evt.nativeEvent.locationY; // 开始触摸点相对于父View的纵坐标
        this.touchBeginCoordX = x;
        this.touchBeginCoordY = y;

        this.distanceData = [0, 0, 0, 0];
        let smallest = [VALID_TOUCH_RANGE, VALID_TOUCH_RANGE]; // 矩形框四个角的有效触摸范围，x轴和y轴方向均在VALID_TOUCH_RANGE以内
        let positionCoord = [0, 0]; // 用户开始触摸点的x和y轴坐标

        // 触摸点在线框左上角，则设定触摸坐标为[8,4]
        this.distanceData[0] = Math.abs(x - this.rectDatas[0]);
        if (this.distanceData[0] < smallest[0]) { // 触摸点在线框左上角坐标的x轴方向的有效范围内
          positionCoord[0] = 8;
        }
        this.distanceData[1] = Math.abs(y - this.rectDatas[1]);
        if (this.distanceData[1] < smallest[1]) { // 触摸点在线框左上角坐标y轴方向的有效范围内
          positionCoord[1] = 4;
        }

        // 触摸点在线框右下角，则设定触摸坐标为[2,1]
        this.distanceData[2] = Math.abs(x - this.rectDatas[2]);
        if (this.distanceData[2] < smallest[0]) { // 触摸点在线框右下角坐标的x轴方向的有效范围内
          positionCoord[0] = 2;
        }
        this.distanceData[3] = Math.abs(y - this.rectDatas[3]);
        if (this.distanceData[3] < smallest[1]) { // 触摸点在线框右下角坐标y轴方向的有效范围内
          positionCoord[1] = 1;
        }
        this.touchPosition = positionCoord[0] | positionCoord[1]; // 通过位或运算得出共有12，3，6，9四个值
      },

      onPanResponderMove: (evt, gestureState) => {
        /* let x = evt.nativeEvent.locationX;
        let y = evt.nativeEvent.locationY; */
        // 通过触摸开始坐标加上横纵方向的位移算出当前坐标位置，可以解决拖动时locationX和locationY跳变问题
        let x = this.touchBeginCoordX + gestureState.dx; // dx 从触摸操作开始时的累计横向位移
        let y = this.touchBeginCoordY + gestureState.dy;// dy 从触摸操作开始时的累计纵向位移

        switch (this.touchPosition) {
          case 12: { // 拖动左上角 触摸点[8,4]
            if (x >= REACT_MARGIN && x < this.rectDatas[2] - itemWidth + DEVIATION_VALUE) {
              this.rectDatas[0] = x;
              this.rectBackGround[0] = Math.floor(x / itemWidth) * itemWidth;
            }
            if (y >= REACT_MARGIN && y < this.rectDatas[3] - itemHeight + DEVIATION_VALUE) {
              this.rectDatas[1] = y;
              this.rectBackGround[1] = Math.floor(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 6: { // 拖动右上角 触摸点[2,4]
            if (x < viewWidth - REACT_MARGIN && x > this.rectDatas[0] + itemWidth - DEVIATION_VALUE) {
              this.rectDatas[2] = x;
              this.rectBackGround[2] = Math.ceil(x / itemWidth) * itemWidth;
            }
            if (y >= REACT_MARGIN && y < this.rectDatas[3] - itemHeight + DEVIATION_VALUE) {
              this.rectDatas[1] = y;
              this.rectBackGround[1] = Math.floor(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 3: { // 拖动右下角 触摸点[2,1]
            if (x < viewWidth - REACT_MARGIN && x > this.rectDatas[0] + itemWidth - DEVIATION_VALUE) {
              this.rectDatas[2] = x;
              this.rectBackGround[2] = Math.ceil(x / itemWidth) * itemWidth;
            }
            if (y < viewHeight - REACT_MARGIN && y > this.rectDatas[1] + itemHeight - DEVIATION_VALUE) {
              this.rectDatas[3] = y;
              this.rectBackGround[3] = Math.ceil(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 9: { // 拖动左下角 触摸点[8,1]
            if (x >= 3 && x < this.rectDatas[2] - itemWidth + DEVIATION_VALUE) {
              this.rectDatas[0] = x;
              this.rectBackGround[0] = Math.floor(x / itemWidth) * itemWidth;
            }
            if (y < viewHeight - REACT_MARGIN && y > this.rectDatas[1] + itemHeight + DEVIATION_VALUE) {
              this.rectDatas[3] = y;
              this.rectBackGround[3] = Math.ceil(y / itemHeight) * itemHeight;
            }
            break;
          }
        }
        this.setState({ canSave: true });
      },

      onPanResponderRelease: () => { },

      onPanResponderTerminate: () => { }
    });

  }

  componentDidMount() {
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  renderTitleBar() {
    let titleBarContent = {
      title: LocalizedStrings["setting_monitor_area_modify"],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: (this.state.canSave) ? NavigationBar.ICON.CLOSE : NavigationBar.ICON.BACK,
          onPress: () => {
            this.props.navigation.goBack();
          }
        }
      ],
      right: [
        {
          key: (this.state.canSave) ? NavigationBar.ICON.COMPLETE : null,
          onPress: () => {
            this.onSubmit();
            this.props.navigation.getParam('callback')(this.rectDatas); // 回调当前用户选定的线框坐标值
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    };
    return (
      <NavigationBar {...titleBarContent} />
    );
  }

  // 点击保存看护区域坐标数据
  onSubmit() {
    this.setState({ progressing: true });
    let positions = [Math.ceil(this.rectDatas[0] / viewWidth * 100), Math.ceil(this.rectDatas[1] / viewHeight * 100),
      Math.ceil(this.rectDatas[2] / viewWidth * 100), Math.ceil(this.rectDatas[3] / viewHeight * 100)];
    let valueString = JSON.stringify([{ area: `[${ positions[0] }, ${ positions[1] }],[${ positions[2] }, ${ positions[3] }]` }]);
    console.log("onSubmit params=", valueString);

    let params = [{ did: Device.deviceID, siid: 11, piid: 2, value: valueString }];
    Service.spec.setPropertiesValue(params).then((vo) => {
      this.setState({ progressing: false });
      if (vo[0].code == 0) {
        // Toast.success("c_set_success");
        this.props.navigation.goBack();
      } else {
        Toast.fail("action_failed");
      }
    }).catch((err) => {
      this.setState({ progressing: false });
      Toast.fail("action_failed", err);
    });
  }

  render() {
    let imageSource = `${ Host.file.storageBasePath }/${ VersionUtil.settingsImgPath }`;
    if (Platform.OS !== "ios") {
      imageSource = `${ Host.file.storageBasePath }/${ VersionUtil.settingsImgPath }?timestamp=${ this.timeStamp }`;
    }
    imageSource = { uri: imageSource };
    
    // 可拖拽线框的绘制路径
    let draggable_rectangle_path = Path()
      .moveTo(this.rectDatas[0], this.rectDatas[1])
      .lineTo(this.rectDatas[2], this.rectDatas[1])
      .lineTo(this.rectDatas[2], this.rectDatas[3])
      .lineTo(this.rectDatas[0], this.rectDatas[3])
      .close();

    // 表示有效看护区域的半透明矩形背景的绘制路径
    let background_path = Path()
      .moveTo(this.rectBackGround[0], this.rectBackGround[1])
      .lineTo(this.rectBackGround[2], this.rectBackGround[1])
      .lineTo(this.rectBackGround[2], this.rectBackGround[3])
      .lineTo(this.rectBackGround[0], this.rectBackGround[3])
      .close();

    let top_left_circle = new Path()
      .moveTo(this.rectDatas[0], this.rectDatas[1] - CIRCLE_RADIUS)
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();

    let top_right_circle = new Path()
      .moveTo(this.rectDatas[2], this.rectDatas[1] - CIRCLE_RADIUS)
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();

    let bottom_right_circle = new Path()
      .moveTo(this.rectDatas[2], this.rectDatas[3] + CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();
    let bottom_left_circle = new Path()
      .moveTo(this.rectDatas[0], this.rectDatas[3] + CIRCLE_RADIUS)
      .arc(0, -CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .arc(0, CIRCLE_RADIUS * 2, CIRCLE_RADIUS)
      .close();


    // TODO 需要替换ImageBackground图片
    return (<View style={{
      display: "flex",
      height: "100%",
      width: "100%",
      flex: 1,
      flexDirection: "column",
      backgroundColor: Util.isDark() ? "#000000" : "#FFFFFF"
    }}>
      {this.renderTitleBar()}

      {/* TODO 选择看护点功能二期开发 */}
      {/*
            <ListItem
          title={LocalizedStrings.setting_monitor_point_select}
          containerStyle={{marginTop:13,marginBottom:20}}
          onPress={()=>{}}
      />
      <Separator style={{marginHorizontal: 24,backgroundColor:"#E5E5E5"}}/> */}

      {/* <Text style={{
        fontSize: 12,
        color: "#666666",
        marginLeft: 27,
        marginTop: 28
      }}>{LocalizedStrings.setting_monitor_scene}</Text> */}

      <View {...this.panResponder.panHandlers}>
        <ImageBackground style={{
          width: viewWidth,
          height: viewHeight,
          marginHorizontal: 24,
          marginTop: 13,
          marginBottom: 20
        }} source={require("../../Resources/Images/ai2_monitor_img.webp")} imageStyle={{ borderRadius: 0 }}>
          {/* 防止直播流截图不存在，不能让其做ImageBackground。需要让一个固定图片来兜底 */}
          <Image style={{
            width: viewWidth,
            height: viewHeight
          }} source={imageSource} key={ this.timeStamp } />
          <View style={{ position: 'absolute' }}>
            <Surface width={viewWidth} height={viewHeight}>
              {/* <Shape d={background_path} fill="#32BAC0" opacity="0.3" /> */}
              <Shape d={draggable_rectangle_path} fill="#32BAC0" opacity="0.3" />
              <Shape d={draggable_rectangle_path} stroke="#32BAC0" strokeWidth={1} />

              <Shape d={top_left_circle} fill="#32BAC0" />
              <Shape d={top_right_circle} fill="#32BAC0" />
              <Shape d={bottom_right_circle} fill="#32BAC0" />
              <Shape d={bottom_left_circle} fill="#32BAC0" />
            </Surface>
          </View>
        </ImageBackground>
      </View>

      <LoadingDialog
        visible={this.state.progressing}
        message={LocalizedStrings['c_setting']}
        onModalHide={() => this.setState({ progressing: false })} />

    </View>);
  }

  componentWillUnmount() {
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  onBackHandler = () => {
    // if (this.state.canSave) {
    //   this.onSubmit();
    //   return true;
    // }
    return false;
  };

}