import React from 'react';
import { Dimensions, Image, Text, TouchableOpacity, View } from 'react-native';

import { ListItem } from 'miot/ui/ListItem';
import { AbstractDialog, NavigationBar } from 'mhui-rn';
import { ChoiceDialog } from 'miot/ui/Dialog';
import MHDatePicker from 'miot/ui/MHDatePicker';
import Toast from '../components/Toast';
import Util from "../util2/Util";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
const { width: screenWidth, height: screenHeight } = Dimensions.get("screen");


export default class MonitorDurationSetting extends React.Component {
    static navigationOptions = (navigation) => {
      return {
        headerTransparent: true,
        header: null
      };
    };

    constructor(props, context) {
      super(props, context);
      this.state = {
        showNameDialog: false,
        showRepeatModeDialog: false,
        showRepeatWeekDialog: false,
        showTimeDialog: false,
        setTime: 0 // 0 开始时间，1 结束时间
      };
      this.data = {
        multiIndexArray: []
      };

      this.item = this.props.navigation.getParam('durationData');
      this.durationDataArray = this.props.navigation.getParam('durationDataArray'); // TODO 用于比对当前编辑或新增是否与原列表重复
      console.log("current duration list data----->", JSON.stringify(this.durationDataArray));
      this.isEditItem = true;
      if (this.item) { // 修改时间段
        this.setWeekByRepeat(this.item.repeat);
      } else { // 添加新时间段，未传值
        this.item = { repeat: 0 };
        this.isEditItem = false;
      }
      this._updateRepeatItems(this.item.repeat);
      console.log("current duration item", this.item);
      this.durationListPageCallback = this.props.navigation.getParam('callback');
    }

    // 转化出重复模式(执行一次、每天、自定义模式)和自定义模式选择的哪几天
    setWeekByRepeat(repeat) {
      let i = 0;
      this.data.multiIndexArray = [];
      for (i = 1; i <= 7; i++) {
        if (repeat & (0b00000001 << (i % 7))) {
          this.data.multiIndexArray.push(i);
        }
      }
      console.log(`this.data.multiIndexArray-------->${ this.data.multiIndexArray }`);
    }


    render() {
      return (<View style={{
        display: "flex",
        height: "100%",
        width: "100%",
        flex: 1,
        flexDirection: "column",
        backgroundColor: Util.isDark() ? "#000000" : "#FFFFFF",
        alignItems: "center"
      }}>
        {this._renderTitleBar()}

        <ListItem
          title={LocalizedStrings['csps_start']}
          value={this.item.start ? this.item.start : LocalizedStrings['plug_timer_no_set']}
          showSeparator={false}
          onPress={() => {
            this.setState({ setTime: 0, showTimeDialog: true });
          }}
        />
        <ListItem
          title={LocalizedStrings['csps_end']}
          value={this.item.end ? this.item.end : LocalizedStrings['plug_timer_no_set']}
          showSeparator={false}
          onPress={() => {
            this.setState({ setTime: 1, showTimeDialog: true });
          }}
        />
        <ListItem
          title={LocalizedStrings['plug_timer_repeat']}
          subtitle={this.item ? Util.getRepeatString(this.item.repeat) : LocalizedStrings['plug_timer_onetime']}
          showSeparator={false}
          onPress={() => {
            this.setState({ showRepeatModeDialog: true });
          }}
        />
        {this._repeatModeDialog()}
        {this._repeatWeekDialog()}
        {this._renderTimeDialog()}
      </View>);
    }

    _renderTitleBar() {
      let titleBarContent = {
        title: LocalizedStrings['long_time_type_period'],
        type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
        left: [
          {
            key: NavigationBar.ICON.CLOSE,
            onPress: () => {
              this.props.navigation.goBack();
            }
          }
        ],
        right: [
          {
            key: NavigationBar.ICON.COMPLETE,
            onPress: () => {
              this.checkAndSubmit();
            }
          }
        ],
        titleStyle: {
          fontSize: 18,
          color: '#333333',
          fontWeight: 500
        }
      };
      return (
        <NavigationBar {...titleBarContent} />
      );
    }

    checkAndSubmit() {
      let unSetedStartTime = !this.item.start || this.item.start.trim() == "";
      let unSetedEndTime = !this.item.end || this.item.end.trim() == "";
      if (unSetedStartTime) {

        if (unSetedEndTime) {
          Toast.fail("setting_monitor_time");
        } else {
          Toast.fail("setting_monitor_time_start");
        }
        return;

      } else if (unSetedEndTime) {

        Toast.fail("setting_monitor_time_end");
        return;
      }
      if (this.item.start && this.item.end && this.item.start == this.item.end) { // TODO 文案需要替换
        Toast.fail("plug_timer_offtime_illegal");
        return;
      }
      if (!(this.item.repeat >= 0)) {
        Toast._showToast("请选择重复模式");
        return;
      }
      // TODO 和现存时间段相同，给与提示
      // if (!this.isEditItem) {
      let exists = false;
      for (let index in this.durationDataArray) {
        let item = this.durationDataArray[index];
        if (this.item.start == item.start && this.item.end == item.end && this.item.repeat == item.repeat) {
          exists = true;
          break;
        }
      }
      if (exists) {
        Toast._showToast(LocalizedStrings.monitor_already_exists, false);
        return;
      }
      // }
      this.item.enable = true;
      this.durationListPageCallback(this.item);
      this.props.navigation.goBack();
    }

    _renderTimeDialog() {
      return (
        <MHDatePicker
          visible={this.state.showTimeDialog}
          title={this.state.setTime == 0 ? LocalizedStrings['csps_start'] : LocalizedStrings['csps_end']}
          type={MHDatePicker.TYPE.TIME24}
          onDismiss={() => this.setState({ showTimeDialog: false })}
          onSelect={(res) => {
            let str = `${ res.rawArray[0] }:${ res.rawArray[1] }`;
            this.state.setTime == 0 ? this.item.start = str : this.item.end = str;
          }}
          current={this.state.setTime == 0 ? (this.item.start ? this.item.start.split(':') : new Date()) :
            (this.item.end ? this.item.end.split(':') : new Date())}
        />
      );
    }

    _repeatDismiss() {
      this._updateRepeatItems(this.item.repeat);
      this.setWeekByRepeat(this.item.repeat);
      this.setState({ showRepeatModeDialog: false });
    }

    _repeatModeDialog() {
      let repeatViewStyle = { width: screenWidth, bottom: 0, borderTopLeftRadius: 20, borderTopRightRadius: 20, borderBottomLeftRadius: 0, borderBottomRightRadius: 0, marginHorizontal: 0 };
      return (
        <AbstractDialog
          style={[repeatViewStyle]}
          visible={this.state.showRepeatModeDialog}
          showSubtitle={false}
          onDismiss={() => {
            this._repeatDismiss();
            // this.setState({ showRepeatModeDialog: false });
          }}
          showTitle={false}
          showButton={false}
          // canDismiss={false}
        >
          {this._renderRepeatView()}
        </AbstractDialog>
      );
    }

    _renderRepeatView() {
      return (
        <View style={{ alignItems: "center" }}>
          <Text
            style={{
              fontSize: 16, color: "#000000", marginTop: 25, fontWeight: "700"
            }}
          >
            {LocalizedStrings["plug_timer_repeat"]}
          </Text>
          <View style={{ marginTop: 15 }}>
            {this.repeatItems.map((item, index) => {
              return (
                <TouchableOpacity
                  key={index}
                  onPress={() => {
                    this.onClickRepeatItem({ index: index });
                  }}
                >
                  <View
                    style={{
                      width: screenWidth, height: 54,
                      backgroundColor:
                        item.select == true ? "rgba(50,186,192,0.1)" : "#ffffff", flexDirection: "row", alignItems: "center", justifyContent: "space-between"
                    }}
                    key={index}
                  >
                    <Text
                      style={{
                        marginLeft: 30, fontSize: 16, color: item.select == true ? "#32BAC0" : "#000000"
                      }}
                    >
                      {item.itemTile}
                    </Text>
                    {item.select == true && (
                      <Image
                        style={{ width: 22, height: 22, marginRight: 22 }}
                        source={require("../../Resources/Images/plugin_icon_customVoice_selectItem.png")}
                      ></Image>
                    )}
                  </View>
                </TouchableOpacity>
              );
            })}
          </View>
          <View style={{ flexDirection: "row", justifyContent: "space-between", marginTop: 20, marginLeft: 0, marginBottom: 10 }}>
            <TouchableOpacity
              onPress={() => {
                this._repeatDismiss();
              }}
            >
              <View style={{ width: 147, height: 46, backgroundColor: "#F5F5F5", borderRadius: 23, justifyContent: "center", alignItems: "center" }}>
                <Text style={{ fontSize: 16, color: "#4C4C4C" }}>{LocalizedStrings.btn_cancel}</Text>
              </View>
            </TouchableOpacity>
  
            <TouchableOpacity
              onPress={() => {
                this.item.repeat = this.editedRepeat;
                this.setState({ showRepeatModeDialog: false });
              }}
            >
              <View style={{ width: 147, height: 46, backgroundColor: "#32BAC0", borderRadius: 23, justifyContent: "center", alignItems: "center", marginLeft: 20 }}>
                <Text style={{ fontSize: 16, color: "#ffffff" }}>{LocalizedStrings.btn_confirm}</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    onClickRepeatItem(item) {
      // this.setState({
      //   repeatItems: repeatItemNewArray
      // });
      this.editedRepeat = this.item.repeat;
      if (item.index == 4) {
        this.setState({ showRepeatWeekDialog: true });
      } else {
        if (item.index == 0) {
          // 每天
          this.editedRepeat = 0b01111111;
        } else if (item.index == 1) {
          // 工作日
          this.editedRepeat = 0b00111110;
        } else if (item.index == 2) {
          // 休息日
          this.editedRepeat = 0b01000001;
        } else if (item.index == 3) {
          // 休息日
          this.editedRepeat = 0b00000000;
        }
        this._updateRepeatItems(this.editedRepeat);
        this.setWeekByRepeat(this.editedRepeat);
        this.forceUpdate();
      }
    }

    _updateRepeatItems(repeat) {
      this.editedRepeat = repeat;
      this.repeatItems = [
        { itemTile: LocalizedStrings.plug_timer_everyday,
          select: repeat == 0b01111111
        },
        { itemTile: LocalizedStrings.weekdays,
          select: repeat == 0b00111110
        },
        { itemTile: LocalizedStrings.weekends,
          select: repeat == 0b01000001
        },
        { itemTile: LocalizedStrings.plug_timer_onetime,
          select: repeat == 0b00000000
        },
        { itemTile: LocalizedStrings.plug_timer_sef_define,
          select: repeat != 0b00000000 && repeat != 0b01000001 && repeat != 0b00111110 && repeat != 0b01111111
        }
      ];
    }

    _repeatWeekDialog() {
      let initSelectData = [];
      this.data.multiIndexArray.forEach((item) => {
        initSelectData.push(--item);
      });
      return (
        <ChoiceDialog
          type={ChoiceDialog.TYPE.MULTIPLE}
          visible={this.state.showRepeatWeekDialog}
          title={LocalizedStrings['plug_timer_custom_repeat']}
          options={[
            { title: LocalizedStrings['monday1'] },
            { title: LocalizedStrings['tuesday1'] },
            { title: LocalizedStrings['wednesday1'] },
            { title: LocalizedStrings['thursday1'] },
            { title: LocalizedStrings['friday1'] },
            { title: LocalizedStrings['saturday1'] },
            { title: LocalizedStrings['sunday1'] }
          ]}
          selectedIndexArray={initSelectData}
          color="#32BAC0"
          buttons={[
            {},
            {
              callback: (result) => {
                if (result && result.length <= 0) {
                  Toast.fail("smarthome_span_error");
                }
                this.editedRepeat = 0b00000000;
                this.data.multiIndexArray = [];
                result.forEach((item) => {
                  this.data.multiIndexArray.push(++item);
                  this.editedRepeat = this.editedRepeat | (0b00000001 << (item % 7));
                });
                this._updateRepeatItems(this.editedRepeat);
                this.setWeekByRepeat(this.editedRepeat);
                this.setState({ showRepeatWeekDialog: false });
              }
            }
          ]}
          onDismiss={() => this.setState({ showRepeatWeekDialog: false })}
        />);
    }

}