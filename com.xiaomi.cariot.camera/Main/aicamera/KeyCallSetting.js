import React from 'react';
import {
  <PERSON><PERSON>View,
  View,
  <PERSON>H<PERSON>ler,
  FlatList,
  Platform,
  Text,
  StyleSheet,
  Image,
  TouchableWithoutFeedback,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { AbstractDialog, ChoiceDialog, ImageButton, InputDialog, MessageDialog, NavigationBar } from 'mhui-rn';
import AlarmUtil from '../util/AlarmUtil';
import StorageKeys from '../StorageKeys';
import Toast from '../components/Toast';
import CameraConfig from '../util/CameraConfig';
import { Service, Host, Device, Entrance } from 'miot';
import LogUtil from '../util/LogUtil';
import { PackageEvent } from 'miot/Package';
import API from '../API';
import { ListItem, ListItemWithSwitch } from "miot/ui/ListItem";
import { strings as I18n } from "miot/resources";
import { styles } from "../setting/SettingStyles";
import AlarmUtilV2, {
  PIID_COUGH_SENSITIVITY,
  PIID_COUGH_SWITCH,
  PIID_CRY_SENSITIVITY,
  PIID_CRY_SWITCH,
  PIID_MOVE_SENSITIVITY,
  PIID_MOVE_SWITCH,
  PIID_PEOPLE_SWITCH,
  PIID_SOUND_SENSITIVITY,
  PIID_SOUND_SWITCH, SIID_AI_DETECTION, SIID_AI_SENSITIVITY
} from "../util/AlarmUtilV2";
import { Event } from '../config/base/CfgConst';
import Util from '../util2/Util';
import { HLSettingStyles } from "./HLSettingStyles";
import ListItemWithIcon from "../widget/ListItemWithIcon";
import ChoiceItem from "mhui-rn/dist/components/listItem/ChoiceItem";
import { getAccessibilityConfig } from "mhui-rn/dist/utils/accessibility-helper";
import DeviceSettingUtil from "../util/DeviceSettingUtil";
import CallUtil, { CALL_TYPE } from "../util/CallUtil";
import BaseSettingPage from "../BaseSettingPage";

const { width: screenWidth, height: screenHeight } = Dimensions.get("screen");

const TAG = "KeyCallSetting";


/**
 * @Author: byh
 * @Date: 2023/10/26
 * @explanation:
 * 按键通话
 *********************************************************/
export default class KeyCallSetting extends BaseSettingPage {
  // static navigationOptions = (navigation) => {
  //   return { headerTransparent: true, header: null };
  // };

  constructor(props, context) {
    super(props, context);
    this.pageType = this.props.navigation.getParam("type");
    this.state = {
      isLoading: false,
      isVip: false,
      switchValue: false,
      sensitiveIndex: 0,
      sensitivityVisible: false,
      showCloseDialog: false,
      tempName: '',
      showRenameDialog: false,
      showChoiceContactDialog: false,
      commentErr: null,
      oneClickSet: null,
      doubleClickSet: null,
      longClickSet: null
    };
    this.isReadOnlyShared = Device.isReadonlyShared;
    this.titleStr = LocalizedStrings['detect_move'];
    this.detectionDesc = LocalizedStrings['ai_move_desc'];
    this.attentionDesc = LocalizedStrings['ai_note_attention'];
    this.topImageSrc = require("../../Resources/Images/faceRecognition/ai_pic_move.webp");
    this.changeContactType = CALL_TYPE.SINGLE;
    this.changeUid = undefined;
    this.callSettingData = {};
  }

  getTitle(): string {
    return LocalizedStrings['talk_for_push'];
  }

  getTitleBackgroundColor(): string {
    return "#f6f6f6";
  }

  componentDidMount() {
    super.componentDidMount();

    console.log(TAG, "this.isReadOnlyShared", this.isReadOnlyShared);
    // 获取vip的状态
    StorageKeys.IS_VIP_STATUS.then((res) => {
      StorageKeys.IN_CLOSE_WINDOW.then((res1) => {
        this.setState({ isVip: res || res1 });
      });
      console.log("看看vip的状态：", this.state.isVip);
    }).catch(() => {
      this.setState({ isVip: false });
    });

    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        // this._getSetting();
      }
    );
    this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {//// 原生页面关闭后回调
      // this.autoPlay();
      LogUtil.logOnAll(TAG, "did resume ios");
      this._onResume();
    });
    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      LogUtil.logOnAll(TAG, "did resume");
      this._onResume();
    });
    // let parms = { did: Device.deviceID, settings: { 'CallRecord': "121212" } };
    // Service.smarthome.setDeviceSetting(parms).then((data) => {
    //   console.log(`selectTimeLapseMode data=${ JSON.stringify(data) }`);
    // }).catch((err) => {
    //   console.log(`selectTimeLapseMode err=${ JSON.stringify(err) }`);
    // });

    DeviceSettingUtil.getDeviceSettingByKey(DeviceSettingUtil.clickCallSetting).then(res => {
      console.log("=======prefix success", res);
      if (res.code == 0) {
        let settingsData = res.result.settings;
        if (settingsData && settingsData.call_setting) {
          let data = JSON.parse(settingsData.call_setting);
          this.callSettingData = data;
          let stateProps = {};
          if (data.hasOwnProperty("switch")) {
            stateProps.switchValue = data.switch.mijia;
          }
          if (data.hasOwnProperty("key1")) {
            let singleData = data.key1;
            stateProps.oneClickSet = singleData;
            this.queryLastAccount('oneClickSet',"key1", singleData);
          }

          if (data.hasOwnProperty("key2")) {
            let doubleData = data.key2;
            stateProps.doubleClickSet = doubleData;
            this.queryLastAccount('doubleClickSet',"key2", doubleData);
          }

          if (data.hasOwnProperty("key3")) {
            let longData = data.key3;
            stateProps.longClickSet = longData;
            this.queryLastAccount('longClickSet',"key3", longData);
          }

          this.setState(stateProps);
        }
      }
    }).catch(error => {
      console.log("=======prefix error: ", error);
    });
  }

  queryLastAccount(key,settingKey, data) {
    CallUtil.getAccountInfoById(`${data.mijia}`).then((res) => {
      console.log("getAccountInfoById",res);
      let icon = this.callSettingData[settingKey].icon;
      let nickname = this.callSettingData[settingKey].nickname;
      if (nickname != res.nickName || icon != res.avatarUrl) {
        // 有不一致才更新
        data.icon = res.avatarURL;
        data.nickname = res.nickName;
        this.callSettingData[settingKey] = data;
        this.setState({ [key]: data });
      }
    }).catch(error => {
      // 失败了就不做其他处理
    });
  }

  componentWillUnmount() {
    console.log("DailyStoryFirstEnter componentWillUnmount.....");
    this.willFocusSubscription && this.willFocusSubscription.remove();
    this.didResumeListener && this.didResumeListener.remove();
    this.willAppearListener && this.willAppearListener.remove();
  }

  _onResume() {
    this._fetchVipStatus();
  }

  _fetchVipStatus() {
    console.log(TAG, "_fetchVipStatus start");
    // todo:需要框架端提供能够指定hostName前缀的api
    API.get("/miot/camera/app/v1/vip/status", "business.smartcamera")
      .then((result) => {
        if (result.code != 0) {
          return;
        }
        let data = result.data;
        if (data == null) {
          return;
        }
        console.log(TAG, "_fetchVipStatus", JSON.stringify(result));
        let vip = data["vip"];
        let status = data["status"];
        let inWindow = !data["closeWindow"]; // false表示在窗口内，true表示不在
        let endTime = data["endTime"];
        StorageKeys.IS_VIP_STATUS = vip;
        StorageKeys.IN_CLOSE_WINDOW = inWindow;
        StorageKeys.VIP_DETAIL = data;
        this.setState({ isVip: vip || inWindow });
      })
      .catch((err) => {
        console.log(err);
      });
  }

  // 通过后端获取开关信息
  _getSetting() {
    //多个侦测共用一个页面
    let params = this._getSpecParams();
    AlarmUtilV2.getSpecPValue(params, 2, TAG).then((res) => {
      if (res[0]?.code != 0) {
        console.log(TAG, "getSpecPValue:", JSON.stringify(-1));
        Toast.fail('c_get_fail');
        return;
      }
      let sensitiveIndex = 0;
      if (this.pageType != Event.PeopleMotion) {
        sensitiveIndex = res[1]?.value == 0 ? 2 : res[1]?.value == 2 ? 0 : 1;
      }
      this.setState({
        switchValue: res[0].value,
        sensitiveIndex: sensitiveIndex
      });
    }).catch((err) => {
      console.log(TAG, "getSpecPValue:", JSON.stringify(err));
      Toast.fail('c_get_fail', err);
      LogUtil.logOnAll(TAG, "getSpecPValue:", JSON.stringify(err));
    });
  }

  _getSpecParams() {
    let params = [
      { "sname": SIID_AI_DETECTION, "pname": PIID_MOVE_SWITCH },
      { "sname": SIID_AI_SENSITIVITY, "pname": PIID_MOVE_SENSITIVITY }];
    switch (this.pageType) {
      case Event.ObjectMotion:
        params = [
          { "sname": SIID_AI_DETECTION, "pname": PIID_MOVE_SWITCH },
          { "sname": SIID_AI_SENSITIVITY, "pname": PIID_MOVE_SENSITIVITY }
        ];
        break;
      case Event.PeopleMotion:
        params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_PEOPLE_SWITCH }];
        break;
      case Event.LouderSound:
        params = [
          { "sname": SIID_AI_DETECTION, "pname": PIID_SOUND_SWITCH },
          { "sname": SIID_AI_SENSITIVITY, "pname": PIID_SOUND_SENSITIVITY }
        ];
        break;
      case Event.BabyCry:
        params = [
          { "sname": SIID_AI_DETECTION, "pname": PIID_CRY_SWITCH },
          { "sname": SIID_AI_SENSITIVITY, "pname": PIID_CRY_SENSITIVITY }
        ];
        break;
      case Event.PeopleCough:
        params = [
          { "sname": SIID_AI_DETECTION, "pname": PIID_COUGH_SWITCH },
          { "sname": SIID_AI_SENSITIVITY, "pname": PIID_COUGH_SENSITIVITY }
        ];
        break;
    }
    return params;
  }


  setSensitivity(value) {
    let params = this.getSetSensitivityParams(value);
    AlarmUtilV2.setSpecPValue(params, TAG).then((res) => {
      console.log("setSensitivity success", res);
      if (res[0].code != 0) {
        Toast.fail("c_set_fail");
      } else {
        this.setState({ sensitiveIndex: value, sensitivityVisible: false });
      }
    }).catch((err) => {
      console.log("setSensitivity error", err);
      Toast.fail("c_set_fail");
      this.setState({ sensitivityVisible: false });
    });

  }

  _onSwitchValue(value) {

    if (!value) {
      //关闭的时候弹框提示
      this.setState({ showCloseDialog: true });
      return;
    }
    this.updateCallSettingSwitch(value);


    // let params = this.getSetParams(value);
    // AlarmUtilV2.setSpecPValue(params, TAG).then((res) => {
    //   console.log("_onSwitchValue success", res);
    //   if (res[0].code != 0) {
    //     this.setState({ switchValue: !value });
    //     Toast.fail("c_set_fail");
    //   } else if (!value) {
    //     AlarmUtilV2.setAISettingEventClose(this.pageType);
    //   }
    //   if (res[0].code == 0){
    //     this.setState({ switchValue: value });
    //   }
    // }).catch((err) => {
    //   this.setState({ switchValue: !value });
    //   Toast.fail("c_set_fail", err);
    //   console.log("_onSwitchValue error", err);
    // })
  }

  updateCallSettingSwitch(value) {
    if (!Device.isOnline) {
      Toast.success("c_set_fail");
      return;
    }
    let upData = JSON.parse(JSON.stringify(this.callSettingData));
    upData.switch.mijia = value ? 1 : 0;
    // upData.key1.mijia = 2864076881
    console.log("======",JSON.stringify(upData));
    DeviceSettingUtil.setDeviceSetting(DeviceSettingUtil.clickCallSetting, JSON.stringify(upData)).then((res) => {
      this.callSettingData = upData;
      CallUtil.updateSettingToDevice([JSON.stringify(upData)]);
      this.setState({ switchValue: value });
      Toast.success("c_set_success");
    }).catch((error) => {
      this.setState({ switchValue: !value });
      Toast.fail("c_set_fail");
    });
  }

  getSetParams(value) {
    let params = [];
    switch (this.pageType) {
      case Event.ObjectMotion:
        params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_MOVE_SWITCH, value: value }];
        break;
      case Event.PeopleMotion:
        params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_PEOPLE_SWITCH, value: value }];
        break;
      case Event.LouderSound:
        params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_SOUND_SWITCH, value: value }];
        break;
      case Event.BabyCry:
        params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_CRY_SWITCH, value: value }];
        break;
      case Event.PeopleCough:
        params = [{ "sname": SIID_AI_DETECTION, "pname": PIID_COUGH_SWITCH, value: value }];
        break;
    }
    return params;
  }

  getSetSensitivityParams(value) {
    value = value == 0 ? 2 : value == 2 ? 0 : 1;
    let params = [];
    switch (this.pageType) {
      case Event.ObjectMotion:
        params = [{ "sname": SIID_AI_SENSITIVITY, "pname": PIID_MOVE_SENSITIVITY, value: value }];
        break;
      case Event.LouderSound:
        params = [{ "sname": SIID_AI_SENSITIVITY, "pname": PIID_SOUND_SENSITIVITY, value: value }];
        break;
      case Event.BabyCry:
        params = [{ "sname": SIID_AI_SENSITIVITY, "pname": PIID_CRY_SENSITIVITY, value: value }];
        break;
      case Event.PeopleCough:
        params = [{ "sname": SIID_AI_SENSITIVITY, "pname": PIID_COUGH_SENSITIVITY, value: value }];
        break;
    }
    return params;
  }

  renderSettingContent() {
    let containerStyleAllRadius = {
      backgroundColor: "#ffffff",
      marginLeft: 12,
      marginRight: 12,
      marginTop: 12,
      width: screenWidth - 24,
      borderBottomLeftRadius: 16,
      borderBottomRightRadius: 16,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      paddingLeft: 20,
      height: 70
    };

    let containerStyleTopRadius = {
      backgroundColor: "#ffffff",
      marginLeft: 12,
      marginRight: 12,
      marginTop: 12,
      width: screenWidth - 24,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      paddingLeft: 20,
      height: 70
    };

    let containerStyleBottomRadius = {
      backgroundColor: "#ffffff",
      marginLeft: 12,
      marginRight: 12,
      width: screenWidth - 24,
      borderBottomLeftRadius: 16,
      borderBottomRightRadius: 16,
      paddingLeft: 20,
      height: 70
    };

    return (
      <View style={ {
        display: "flex",
        height: "100%",
        flex: 1,
        flexDirection: "column",
        // backgroundColor: Util.isDark() ? "#xm000000" : '#F6F6F6',
        alignItems: "center"
      } }>
          <View key={ 102 }>

            <View style={ { alignItems: "center", marginHorizontal: 20, marginTop: 20 } }>
              <Image style={ { width: '100%', height: 200, borderRadius: 9 } }
                     source={ this.topImageSrc }/>
            </View>

            <View style={ stylesDetection.white_blank }/>
            <Text style={ styles.desc_subtitle }>{ LocalizedStrings['ai_click_call_desc'] }</Text>

            <View style={ styles.whiteblank }/>

            <ListItemWithSwitch
              titleNumberOfLines={ 3 }
              unlimitedHeightEnable={ true }
              showSeparator={ false }
              title={ LocalizedStrings['talk_for_push'] }
              value={ this.state.switchValue }
              onValueChange={ (val) => this._onSwitchValue(val) }
              containerStyle={ { backgroundColor: '#F6F6F6' } }
              titleStyle={ { fontWeight: 'bold' } }
              accessibilitySwitch={ {
                accessibilityLabel: LocalizedStrings['care_screen_close_show_protect']
              } }
            />
            { this.state.switchValue ?
              <View style={ { marginTop: 10 } }>
                {
                  this.state.oneClickSet ?
                    <View>
                      <View style={ [containerStyleTopRadius, {
                        flex: 1,
                        flexDirection: "row",
                        minHeight: 70,
                        alignItems: "center"
                      }] }>
                        <Text style={ {
                          fontSize: 16,
                          fontWeight: 'bold',
                          flex: 1,
                          marginRight: 10
                        } }>{ LocalizedStrings['ai_one_click_call'] }</Text>
                        <ImageButton
                          style={ { width: 32, height: 32, marginRight: 20 } }
                          source={ Util.isDark() ? require("../../Resources/Images/icon_call_edit_dark.png") : require("../../Resources/Images/icon_call_edit.png") }
                          onPress={ () => {
                            console.log("=======");
                            if (!Device.isOwner) {
                              Toast.success("share_user_permission_hint");
                              return;
                            }
                            this.changeContactType = CALL_TYPE.SINGLE;
                            this.changeUid = this.state.oneClickSet.mijia;
                            // this.setState({ showChoiceContactDialog: true });
                            // 单击呼叫只支持更改、不支持删除
                            this.selectUser(this.changeContactType, 1, this.changeUid);
                          } }/>
                      </View>
                      <ListItemWithIcon
                        icon={ this.state.oneClickSet.icon ? { uri: this.state.oneClickSet.icon } : require("../../Resources/Images/icon_user.png") }
                        style={ {
                          backgroundColor: "#ffffff", marginLeft: 12,
                          marginRight: 12, width: screenWidth - 24, alignSelf: "center"
                        } }
                        hideArrow={ true }
                        iconStyle={{width: 28, height: 28, borderRadius: 14}}
                        title={ Service.account.ID == this.state.oneClickSet.mijia ? `${ this.state.oneClickSet.nickname }${ LocalizedStrings['call_me'] }` : this.state.oneClickSet.nickname }
                        sub_title={ `${this.state.oneClickSet.mijia}` }
                      />

                      <ListItem
                        allowFontScaling={ false }
                        containerStyle={ containerStyleBottomRadius }
                        titleStyle={ { fontWeight: 'bold', fontSize: 16 } }
                        valueStyle={ { fontSize: 13, color: "#979797" } }
                        title={ LocalizedStrings['ai_call_name'] }
                        value={ this.state.oneClickSet.callName }
                        onPress={ (_) => {
                          this.changeContactType = CALL_TYPE.SINGLE;
                          this.setState({
                            showRenameDialog: true,
                            tempName: this.state.oneClickSet ? this.state.oneClickSet.callName : ''
                          });
                        } }
                        showSeparator={ false }/>
                    </View>
                    :
                    <ListItem
                      allowFontScaling={ false }
                      containerStyle={ containerStyleAllRadius }
                      titleStyle={ { fontWeight: 'bold', fontSize: 16 } }
                      valueStyle={ { fontSize: 13, color: "#979797" } }
                      title={ LocalizedStrings['ai_one_click_call'] }
                      value={ LocalizedStrings['targetPushTitle_subtitle1'] }
                      onPress={ (_) => this.selectUser(CALL_TYPE.SINGLE, 0) }
                      showSeparator={ false }/>
                }

                {
                  this.state.doubleClickSet ?
                    <View>
                      <View style={ [containerStyleTopRadius, {
                        flex: 1,
                        flexDirection: "row",
                        minHeight: 70,
                        alignItems: "center"
                      }] }>
                        <Text style={ {
                          fontSize: 16,
                          fontWeight: 'bold',
                          flex: 1,
                          marginRight: 10
                        } }>{ LocalizedStrings['ai_double_click_call'] }</Text>
                        <ImageButton
                          style={ { width: 32, height: 32, marginRight: 20 } }
                          source={ Util.isDark() ? require("../../Resources/Images/icon_call_edit_dark.png") : require("../../Resources/Images/icon_call_edit.png") }
                          onPress={ () => {
                            if (!Device.isOwner) {
                              Toast.success("share_user_permission_hint");
                              return;
                            }
                            console.log("=======");
                            this.changeContactType = CALL_TYPE.DOUBLE;
                            this.changeUid = this.state.doubleClickSet.mijia;
                            this.setState({ showChoiceContactDialog: true });
                          } }/>
                      </View>
                      <ListItemWithIcon
                        icon={ this.state.doubleClickSet.icon ? { uri: this.state.doubleClickSet.icon } : require("../../Resources/Images/icon_user.png") }
                        style={ {
                          backgroundColor: "#ffffff", marginLeft: 12,
                          marginRight: 12, width: screenWidth - 24, alignSelf: "center"
                        } }
                        iconStyle={{width: 28, height: 28, borderRadius: 14}}
                        hideArrow={ true }
                        title={ Service.account.ID == this.state.doubleClickSet.mijia ? `${ this.state.doubleClickSet.nickname }${ LocalizedStrings['call_me'] }` : this.state.doubleClickSet.nickname }
                        sub_title={ `${this.state.doubleClickSet.mijia}` }
                      />

                      <ListItem
                        allowFontScaling={ false }
                        containerStyle={ containerStyleBottomRadius }
                        titleStyle={ { fontWeight: 'bold', fontSize: 16 } }
                        valueStyle={ { fontSize: 13, color: "#979797" } }
                        title={ LocalizedStrings['ai_call_name'] }
                        value={ this.state.doubleClickSet.callName }
                        onPress={ (_) => {
                          this.changeContactType = CALL_TYPE.DOUBLE;
                          this.setState({
                            showRenameDialog: true,
                            tempName: this.state.doubleClickSet ? this.state.doubleClickSet.callName : ''
                          });
                        } }
                        showSeparator={ false }/>
                    </View>
                    :
                    <ListItem
                      allowFontScaling={ false }
                      containerStyle={ containerStyleAllRadius }
                      titleStyle={ { fontWeight: 'bold', fontSize: 16 } }
                      valueStyle={ { fontSize: 13, color: "#979797" } }
                      title={ LocalizedStrings['ai_double_click_call'] }
                      value={ LocalizedStrings['targetPushTitle_subtitle1'] }
                      onPress={ (_) => this.selectUser(CALL_TYPE.DOUBLE) }
                      showSeparator={ false }/>
                }

                {
                  this.state.longClickSet ?
                    <View>
                      <View style={ [containerStyleTopRadius, {
                        flex: 1,
                        flexDirection: "row",
                        minHeight: 70,
                        alignItems: "center"
                      }] }>
                        <Text style={ {
                          fontSize: 16,
                          fontWeight: 'bold',
                          flex: 1,
                          marginRight: 10
                        } }>{ LocalizedStrings['ai_long_click_call'] }</Text>
                        <ImageButton
                          style={ { width: 32, height: 32, marginRight: 20 } }
                          source={ Util.isDark() ? require("../../Resources/Images/icon_call_edit_dark.png") : require("../../Resources/Images/icon_call_edit.png") }
                          onPress={ () => {
                            if (!Device.isOwner) {
                              Toast.success("share_user_permission_hint");
                              return;
                            }
                            this.changeContactType = CALL_TYPE.LONG;
                            this.changeUid = this.state.longClickSet.mijia;
                            this.setState({ showChoiceContactDialog: true });
                          } }/>
                      </View>
                      <ListItemWithIcon
                        icon={ this.state.longClickSet.icon ? { uri: this.state.longClickSet.icon } : require("../../Resources/Images/icon_user.png") }
                        style={ {
                          backgroundColor: "#ffffff", marginLeft: 12,
                          marginRight: 12, width: screenWidth - 24, alignSelf: "center"
                        } }
                        hideArrow={ true }
                        iconStyle={{width: 28, height: 28, borderRadius: 14}}
                        title={ Service.account.ID == this.state.longClickSet.mijia ? `${ this.state.longClickSet.nickname }${ LocalizedStrings['call_me'] }` : this.state.longClickSet.nickname }
                        sub_title={ `${this.state.longClickSet.mijia}` }
                      />

                      <ListItem
                        allowFontScaling={ false }
                        containerStyle={ containerStyleBottomRadius }
                        titleStyle={ { fontWeight: 'bold', fontSize: 16 } }
                        valueStyle={ { fontSize: 13, color: "#979797" } }
                        title={ LocalizedStrings['ai_call_name'] }
                        value={ this.state.longClickSet.callName }
                        onPress={ (_) => {
                          this.changeContactType = CALL_TYPE.LONG;
                          this.setState({
                            showRenameDialog: true,
                            tempName: this.state.longClickSet ? this.state.longClickSet.callName : ""
                          });
                        } }
                        showSeparator={ false }/>
                    </View>
                    :
                    <ListItem
                      allowFontScaling={ false }
                      containerStyle={ containerStyleAllRadius }
                      titleStyle={ { fontWeight: 'bold', fontSize: 16 } }
                      valueStyle={ { fontSize: 13, color: "#979797" } }
                      title={ LocalizedStrings['ai_long_click_call'] }
                      value={ LocalizedStrings['targetPushTitle_subtitle1'] }
                      onPress={ (_) => this.selectUser(CALL_TYPE.LONG) }
                      showSeparator={ false }/>
                }

              </View> : null
            }

            <View style={ { height: 30 } }/>

          </View>

        { this._renderCloseDialog() }
        { this._renderRenameDialog() }
        { this._renderChoiceContactDialog() }
      </View>
    );
  }

  _renderCloseDialog() {
    return (
      <MessageDialog
        visible={ this.state.showCloseDialog }
        message={ LocalizedStrings['ai_contact_call_close_desc'] }
        useNewType={ true }
        dialogStyle={ {
          allowFontScaling: true,
          unlimitedHeightEnable: true,
          titleStyle: {
            fontSize: 18
          },
          itemSubtitleNumberOfLines: 0,
          itemSubtitleStyle: {
            marginRight: 10
          }
        } }
        buttons={ [{
          text: LocalizedStrings['still_close'],
          callback: () => {
            this.setState({ showCloseDialog: false });
            this.updateCallSettingSwitch(false);
          }
        }, {
          text: LocalizedStrings['not_close'],
          callback: (res) => {
            console.log("ssssss", res);
            this.setState({ showCloseDialog: false });
          }
        }] }
      />
    );
  }

  _renderRenameDialog() {
    return (
      <InputDialog
        visible={ this.state.showRenameDialog }
        title={ LocalizedStrings["ai_call_name"] }
        inputs={ [{
          placeholder: LocalizedStrings["please_enter_name"],
          defaultValue: this.state.tempName,
          textInputProps: {
            autoFocus: true
          },
          onChangeText: (text) => {
            if (this.state.commentErr != null) {
              this.setState({ commentErr: null });
            }
            let isEmoji = Util.containsEmoji(text);
            let length = text.length;
            // let isCommon = this.isTextcommon(result);
            if (isEmoji) {
              this.setState({ prePositionNameTooLong: true, commentErr: LocalizedStrings["Special_symbol_input_not_supported_temporarily"] });
            } else if (length > 10) {
              this.setState({ prePositionNameTooLong: true, commentErr: LocalizedStrings["input_name_too_long2"] });
            } else if (length <= 0) {
              this.setState({ prePositionNameTooLong: true, commentErr: LocalizedStrings["add_feature_empty_tips"] });
            } else {
              this.setState({ prePositionNameTooLong: false, commentErr: "error" });
            }
          },
          type: 'DELETE',
          isCorrect: !this.state.prePositionNameTooLong
        }] }
        inputWarnText={ this.state.commentErr }
        noInputDisButton={ true }
        buttons={ [
          {
            // style: { color: 'lightpink' },
            callback: () => this.setState({ showRenameDialog: false, commentErr: null, prePositionNameTooLong: false })
          },
          {
            // style: { color: 'lightblue' },
            callback: (result) => {
              console.log(`结果`, result.textInputArray[0]);
              if (this.state.prePositionNameTooLong) {
                return;
              }
              let text = result.textInputArray[0].trim();
              if (text.length > 0 && !Util.containsEmoji(text)) {
                this.setState({ showRenameDialog: false, commentErr: null });
                if (!Device.isOnline) {
                  Toast.success("c_set_fail");
                  return;
                }
                let needUpData = JSON.parse(JSON.stringify(this.callSettingData));
                let updateObj = '';
                LogUtil.logOnAll("KeyCallSetting",this.changeContactType,needUpData.hasOwnProperty("key1"),needUpData.hasOwnProperty("key2"),needUpData.hasOwnProperty("key3"))
                switch (this.changeContactType) {
                  case CALL_TYPE.SINGLE:
                    if (needUpData.key1) {
                      needUpData.key1.callName = text;
                    }
                    updateObj = { oneClickSet: needUpData.key1 };
                    break;
                  case CALL_TYPE.DOUBLE:
                    if (needUpData.key2) {
                      needUpData.key2.callName = text;
                    }
                    updateObj = { doubleClickSet: needUpData.key2 };
                    break;
                  case CALL_TYPE.LONG:
                    if (needUpData.key3) {
                      needUpData.key3.callName = text;
                    }
                    updateObj = { longClickSet: needUpData.key3 };
                    break;
                }
                let paramsStr = JSON.stringify(needUpData);
                DeviceSettingUtil.setDeviceSetting(`${ DeviceSettingUtil.clickCallSetting }`, paramsStr).then((res) => {
                  this.callSettingData = needUpData;
                  CallUtil.updateSettingToDevice([paramsStr]);
                  this.setState(updateObj);
                }).catch(error => {
                  Toast.fail("c_set_fail");
                });

              } else {
                if (Util.containsEmoji(text)) {
                  this.setState({ commentErr: LocalizedStrings["Special_symbol_input_not_supported_temporarily"] });
                }
              }
            }
          }
        ] }
        onDismiss={ () => this.setState({ showRenameDialog: false, commentErr: null, prePositionNameTooLong: false }) }
      />
    );
  }

  _renderChoiceContactDialog() {
    return (
      <AbstractDialog
        visible={ this.state.showChoiceContactDialog }
        useNewTheme={ true }
        title={ LocalizedStrings["edit_contact"] }
        dialogStyle={ {
          allowFontScaling: false,
          titleStyle: {
            fontSize: 16,
            fontWeight: "bold"
          }
        } }
        onDismiss={ (_) => this.setState({ showChoiceContactDialog: false }) }
        buttons={ [
          {
            text: LocalizedStrings["action_cancle"],
            colorType: "grayLayerBlack",
            callback: () => {
              this.setState({ showChoiceContactDialog: false });
            }
          }
        ] }
      >
        <View style={ {
          marginBottom: 16,
          marginTop: 10
        } }>
          { [{
            "title": LocalizedStrings['delete_contact'],
            onPress: () => {
              this.setState({ showChoiceContactDialog: false });
              // 删除按键联系人
              this.deleteClickContact();
            }
          }, {
            "title": LocalizedStrings['replace_contact'],
            onPress: () => {
              this.setState({ showChoiceContactDialog: false });
              this.selectUser(this.changeContactType, 1, this.changeUid);
              // this.props.navigation.navigate("ChoiceContactPage", { type: 1 });
            }
          }].map((option, index) => <View key={ (option.title || '') + index }>
            <ChoiceItem
              type={ ChoiceItem.TYPE.SINGLE }
              titleStyle={ {
                fontSize: 16,
                fontWeight: "bold"
              } }
              title={ option.title || '' }
              onPress={ () => option.onPress() }/>
          </View>) }
        </View>
      </AbstractDialog>
    );
  }

  selectUser(type, titleType, uid) {
    if (!Device.isOnline) {
      Toast.success("c_set_fail");
      return;
    }
    if (!Device.isOwner) {
      Toast.success("share_user_permission_hint");
      return;
    }
    this.props.navigation.navigate("ChoiceContactPage", {
      type: titleType, choiceType: type, uid: uid, callback: (res) => {
        console.log(TAG, res);
        switch (type) {
          case CALL_TYPE.SINGLE:
            this.callSettingData.key1 = res;
            this.setState({ oneClickSet: res });
            break;
          case CALL_TYPE.DOUBLE:
            this.callSettingData.key2 = res;
            this.setState({ doubleClickSet: res });
            break;
          case CALL_TYPE.LONG:
            this.callSettingData.key3 = res;
            this.setState({ longClickSet: res });
            break;
        }
      }
    });
  }

  deleteClickContact() {
    if (!Device.isOnline) {
      Toast.success("c_set_fail");
      return;
    }
    let updateObj = {};
    let needUpData = JSON.parse(JSON.stringify(this.callSettingData));
    switch (this.changeContactType) {
      case CALL_TYPE.SINGLE:
        delete needUpData.key1;
        updateObj = { oneClickSet: null };
        break;
      case CALL_TYPE.DOUBLE:
        delete needUpData.key2;
        updateObj = { doubleClickSet: null };
        break;
      case CALL_TYPE.LONG:
        delete needUpData.key3;
        updateObj = { longClickSet: null };
        break;
    }
    let paramsStr = JSON.stringify(needUpData);

    DeviceSettingUtil.setDeviceSetting(`${ DeviceSettingUtil.clickCallSetting }`, paramsStr).then((res) => {
      // 成功后，
      CallUtil.updateSettingToDevice([paramsStr]);
      this.setState(updateObj);
      switch (this.changeContactType) {
        case CALL_TYPE.SINGLE:
          delete this.callSettingData.key1;
          break;
        case CALL_TYPE.DOUBLE:
          delete this.callSettingData.key2;
          break;
        case CALL_TYPE.LONG:
          delete this.callSettingData.key3;
          break;
      }
    }).catch(error => {
      Toast.fail("c_set_fail");
    });
  }
}

const stylesDetection = StyleSheet.create({
  white_blank: {
    height: 0.5,
    marginTop: 20
  },
  whiteblank: {
    height: 0.5,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginBottom: 30,
    marginTop: 20

  },
  algorithm_title: {
    color: "#999",
    fontSize: 18,
    paddingHorizontal: 24
  },
  algorithm_subtitle: {
    color: "#999",
    fontSize: 12,
    marginTop: 10,
    paddingHorizontal: 24
  },
  optionsPage: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 20,
    paddingHorizontal: 24
  },
  optionContainer: {
    minHeight: 80,
    alignItems: 'center'
  },
  icon: {
    width: 48,
    height: 48
  },
  optionText: {
    marginTop: 4,
    marginBottom: 10,
    width: 48,
    textAlign: 'center',
    fontSize: 12
  }

});