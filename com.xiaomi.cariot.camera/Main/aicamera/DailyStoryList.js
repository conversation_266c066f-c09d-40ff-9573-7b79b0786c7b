import React from 'react';
import { ScrollView, View, BackHandler, FlatList, Platform, Text, StyleSheet, Image, TouchableWithoutFeedback, TouchableOpacity, ActivityIndicator, DeviceEventEmitter } from 'react-native';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Switch from 'miot/ui/Switch';
import Checkbox from 'miot/ui/Checkbox';
import { styles } from '../setting/SettingStyles';
import CameraConfig, { IMG_DARKMODE_TINT } from '../util/CameraConfig';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtil from '../util/AlarmUtil';
import Util, { DayInMilli } from "../util2/Util";
import Toast from '../components/Toast';
import { NavigationBar } from 'mhui-rn';
import { Host, Device } from "miot";
import Singletons from "../framework/Singletons";
import Service from 'miot/Service';
import LogUtil from '../util/LogUtil';
import VersionUtil from "../util/VersionUtil";
const TAG = "DailyStoryList";

export default class DailyStoryList extends React.Component {
  constructor(props, context) {
    super(props, context);
    this.state = {
      isLoading: false,
      alarmValues: {},
      editMode: false,
      isShowActiveStyle: true,
      listDatas: [],
      isShowMessageDialog: false,
      isChangeCategoryLoading: false,
      isVip: undefined,
      vipEndTime: 0,
      category: 1,
      dailyStorySwitch: true
    };
    // this.mLoader = Singletons.CloudEventLoader;
    this.mLoader = Singletons.DailyStoryLoader;
    this.listDatas = [];
    // 当前页面第一次加载的时候会调用一次
    // this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
    //   'didFocus',
    //   () => {
    //     console.log("will focus");
    //     this.loadData(new Date().getTime(), this.category);
    //     console.log("this.category:", this.category)
    //   }
    // );
    this.isReadOnlyShared = Device.isReadonlyShared;
    this.is022or051 = VersionUtil.is022Model(Device.model);
  }
  // 导航函数
  _navigate(name, params = null) {
    this.props.navigation.navigate(name, params);
  }

  componentDidMount() {
    this.props.navigation.setParams({
      title: LocalizedStrings['ss_daily_story'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
            if (this.state.editMode) {
              this.listDatas.forEach((item) => item.select = false);
              this.setState({ editMode: false });
            } else {
              this.props.navigation.goBack();
            }
          }
        }
      ],
      right: [
        {
          key: this.isReadOnlyShared ? null : NavigationBar.ICON.SETTING,
          onPress: () => {
            this._navigate("DailyStorySetting");
          }
        }
      ],
      titleStyle: {
        width: "100%",
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    //componentDidMount 先于 will focus 执行
    console.log("componentDidMount");
    console.log("this.props.navigation.state.routeName:",this.props.navigation.state.routeName)
    this.routeName = this.props.navigation.state.routeName;
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    // 获取vip信息
    this.fetchVipStatus();
    // 获取数据
    this.loadData(new Date().getTime());

    console.log("就是这里查看listDatas是不是有数据：", JSON.stringify(this.listDatas))
    // 查看每日故事开关 当前页面第一次加载的时候会调用一次
    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        console.log("will focus");
        this._getSetting();
        this.loadData(new Date().getTime(), this.state.category);
      }
    );
  }
  // 通过后端获取开关信息
  _getSetting() {
    AlarmUtil.getAlarmConfig().then((res) => {
      if (res.code != 0) {
        console.log(TAG, `getdailyStorySwitch${JSON.stringify(-1)}`);
        Toast.fail('c_get_fail');
        return;
      }
      this.setState({
        dailyStorySwitch: res.data.dailyStorySwitch,
      });
      console.log('先看看开关的状态:', res.data.dailyStorySwitch) // 开关为true
    }).catch((err) => {
      console.log("getdailyStorySwitch:", JSON.stringify(err));
      Toast.fail('c_get_fail', err);
      LogUtil.logOnAll(TAG, `getdailyStorySwitch${JSON.stringify(err)}`);
    });
  }
  // 点击立即开启按钮 开启每日故事
  _onEnableValueChange(value) {
    console.log(`_onEnableValueChange ${value}`);
    AlarmUtil.putDailyStorySwitch({
      open: value,
    }).then((res) => {
      this.setState({ dailyStorySwitch: res.code == 0 ? value : !value });
      if (res.code == 0) {
        Toast.success('c_set_success');
      } else {
        Toast.fail('c_set_fail');
      }
    }).catch((err) => {
      Toast.fail('c_set_fail', err);
      LogUtil.logOnAll(TAG, `_onEnableValueChange ${value}` ,JSON.stringify(err));
    });
  }
  // 点击上方家人、宠物  切换数据类型
  changeCategory(category) {
    console.log(category)
    if (category == 1) {
      this.setState({
        isShowActiveStyle: true,
        category: 1
      })
    } else {
      this.setState({
        isShowActiveStyle: false,
        category: 2
      })
    }
    // this.onDealOverTimeVideo();
    // console.log("有没有清空:",this.listDatas)
    this.loadData(new Date().getTime(), category, this.state.pacakgeType);
    console.log("切换后的数据:",this.listDatas)
  }
  // 时间处理函数
  formatYMD(timestamp) {
    let date = new Date(timestamp);// 时间戳为10位需*1000，时间戳为13位的话不需乘1000
    let y = `${date.getFullYear()}.`
    let m = `${date.getMonth() < 9 ? `0${date.getMonth() + 1}` : (date.getMonth() + 1)}.`;
    let d = (date.getDate() < 10 ? `0${date.getDate()}` : date.getDate());
    return y + m + d;
  }
  // 获取数据
  loadData(endTime, category = 1, limit = 30) {
    let limit022 = this.is022or051 ? 7 : limit;
    this.setState({
      isLoading: true
    });
    let params = { "did": Device.deviceID, "category": category, "model": Device.model, "endTime": endTime, "limit": limit022, "region": Host.locale.language.includes("en") ? "US" : "CN" };
    AlarmUtil.getDailyStoryList(params).then((res) => {
      console.log(`getDailyStoryList ${JSON.stringify(res)}`);
      if (res.code == 0) {
        // this.listDatas = []
        this.listDatas = [...res.data.playUnits];
        // console.log("切换后的listDatas:",this.listDatas);
        // 是vip且开启了每日故事 判断是否获取数据 可以获取到数据 但当数据为空时 显示暂位图
        if (JSON.stringify(this.listDatas) === '[]') {
          this.setState({
            isShowMessageDialog: true
          })
        }
      }
      this.downloadImag();
      this.setState({
        isLoading: false
      });
      console.log("async downloadImag()")
    }).catch((err) => {
      console.log(`getDailyStoryList ${JSON.stringify(err)}`);
      this.setState({
        isLoading: false
      });
      LogUtil.logOnAll(TAG, `getDailyStoryList ${JSON.stringify(err)}`);
    });
  }

  // 获取封面
  async downloadImag() {
    // console.log("进来了吗？")
    for (let i = 0; i < this.listDatas.length; ++i) {
      // console.log("有几张图片:",this.listDatas.length)
      try {
        let item = this.listDatas[i];
        console.log("start get img...");
        item.imgUrl = await this.mLoader.getThumb(item);
        // console.log("图片可以看吗？",item.imgUrl);
        this.setState({
          isLoading: false
        });
      } catch (err) {
        console.log("getThumb", err);
        LogUtil.logOnAll(TAG, `downloadImag()${JSON.stringify(err)}`);
      }
    }
  }

  // 获取vip的状态
  async fetchVipStatus() {
    try {
      let newVipD = await Util.fetchVipStatus();
      // StorageKeys.VIP_DETAIL = newVipD;
      console.log("我想拿到vip信息：", newVipD)
      this.setState({
        isVip: newVipD.vip,
        vipEndTime: newVipD.endTime
      })
      // 试试
      if (newVipD.pacakgeType) {
        if (newVipD.pacakgeType.indexOf('7') != -1) {
          this.setState({
            pacakgeType : 7
          })
        } else if (newVipD.pacakgeType.indexOf('30') != -1) {
          this.setState({
            pacakgeType : 30
          })
        } 
      }
      console.log("pacakgeType:", this.state.pacakgeType)
      // this.updateVipRelated(newVipD);
    } catch (aErr) {
      console.log(this.tag, "fetchVipStatus err", aErr);
      LogUtil.logOnAll(TAG, `fetchVipStatus err${JSON.stringify(aErr)}`);
    }
  }
  // 渲染列表子项
  renderItemView(item, index) {
    return (
      <View style={{ display: "flex", flexDirection: "column", marginTop: 20 }}>
        <View style={{ height: 34, width: "100%", display: "flex", justifyContent: "center" }}>
          <Text style={{ fontSize: 12, color: "#8C93B0" }}>{this.formatYMD(item.createTime)}</Text>
        </View>
        {item.fileId == "" || item.imgStoreId == "" ?
          <Text>{LocalizedStrings['empty_daily_story_tips']}</Text> :
          <TouchableWithoutFeedback
            onPress={() => {
              console.log("test click item");
              // let params = {
              //   "prefix": "business.smartcamera.", "method": "GET", "path": "/miot/camera/app/v1/dailyStory/m3u8",
              //   "did": Device.deviceID, "region": Host.locale.language.includes("en") ? "US" : "CN", "fileId": item.fileId,
              //   "model": Device.model
              // };
              let params = {
                "method": "GET",
                "did": Device.deviceID, "region": Host.locale.language.includes("en") ? "US" : "CN", "fileId": item.fileId,
                "model": Device.model
              };
              Service.miotcamera.getVideoFileUrlV2("business.smartcamera.", "/miot/camera/app/v1/dailyStory/m3u8", params)
                .then((res) => {
                  // console.log("res这里是什么：", res);
                  // console.log("item这里是什么：", item);
                  this.props.navigation.navigate("DailyStoryVideoViewPage", {
                    url: res, item: item, category: this.state.category, callback: data => {
                      this.category = data
                    }
                  });
                }).catch((err) => {
                  console.log(err);
                  Toast.fail("action_failed")
                });
            }}>
            <View style={{ position: 'relative' }}>
              <Image
                style={{ width: "100%", height: 210, marginTop: 9, borderRadius: 10 }}
                source={item.imgUrl ? { uri: `file://${item.imgUrl}` } : null}
              ></Image>
              <Image
                style={{ width: 66, height: 66, position: 'absolute', top: "50%", left: "50%", marginLeft: -33, marginTop: -33 }}
                source={require("../../Resources/Images/camera_icon_center_play_nor.png")}
              />
            </View>
          </TouchableWithoutFeedback>
        }
      </View>
    );
  }
  // 底部渲染
  renderFooter() {
    let pacakgeType = this.is022or051 ? 7 : this.state.pacakgeType
    return (
      <Text style={{ textAlign: "center", color: "#999999", fontSize: 12, lineHeight: 19, marginTop: 25 }}>
        {LocalizedStrings['daily_story_empty_content_tips2'].replace("%1$d", pacakgeType)}
      </Text>
    )
  }
  // 渲染故事列表
  _renderDailyStoryList() {
    const cate = this.state.isShowActiveStyle ? 1 : 2
    return (
      <FlatList
        style={{ width: "100%", flex: 1 }}
        data={this.listDatas}
        showsVerticalScrollIndicator={false}
        keyExtractor={this._keyExtractor}
        renderItem={(data) => this.renderItemView(data.item, data.index)}
        contentContainerStyle={[{ paddingBottom: 50, flexGrow: 1, paddingHorizontal: 24 }]}
        refreshing={this.state.isLoading}
        ListFooterComponent={() => this.renderFooter()}
        ListEmptyComponent={() => this._renderEmptyList()}
        onRefresh={() => {
          this.loadData(new Date().getTime(), cate, this.state.pacakgeType);
        }}
      >
      </FlatList>
    )
  }

  // 渲染空列表
  _renderEmptyList() {
    let emptyPicUrl = "../../Resources/Images/empty_daily_story_common.webp";
    if (this.state.isShowMessageDialog) {
      return (
        <View style={{ width: "100%", diplay: 'flex', justifyContent: "center", alignItems: "center" }}>
          <Image style={{ width: "100%", height: 200 }} source={require(emptyPicUrl)}></Image>
          <Text style={{ lineHeight: 20, color: "#666666", fontSize: 15, textAlign: 'center', marginTop: 5, paddingBottom: 20 }}>{LocalizedStrings['daily_story_empty_content_tips1']}</Text>
        </View>
      )
    } else {
      return null
    }
  }

  // 当用户没有开启每日故事时，渲染立即开启视图
  _renderMessageDialog() {
    if (!this.state.dailyStorySwitch && !this.isReadOnlyShared) {
      return (
        <View style={{ height: 95, width: 332, backgroundColor: "#F5F5F5", borderRadius: 12, display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "space-around", marginTop: 25 }}>
          <Text style={{ width: 190, fontFamily: "MILanPro_MEDIUM--GB1-4", fontSize: 13, color: "#333" }}>{LocalizedStrings['daily_story_close_tips']}</Text>
          <TouchableOpacity
            style={{ height: 34, width: 80, backgroundColor: "#32BAC0",  borderRadius: 17, display: "flex", alignItems: "center", justifyContent: "center" }}
            onPress={() => { this._onEnableValueChange(true) }}
          >
            <Text style={{ fontFamily: "MILanPro_MEDIUM--GB1-4", color: "#fff", fontSize: 12 }}>{LocalizedStrings['auto_discovery_setting_now']}</Text>
          </TouchableOpacity>
        </View >
      )
    }
  }

  // 这里注意 有的地方没有视频也就没有item.fileId 因此要加一个唯一标识 可以用创建时间 因为一天只会生成一个视频
  _keyExtractor = (item) => item.fileId || item.createTime.toString();
  render() {
    // let isDark = DarkMode.getColorScheme() == "dark";
    return (
      <View style={{ display: "flex", height: "100%", width: "100%", flex: 1, flexDirection: "column", backgroundColor: "#FFFFFF", alignItems: "center" }}>
        {CameraConfig.isDailySupportSelectFamilyOrPet(Device.model) ?
          <>
          <View style={{ height: 34, width: "100%" }}>
            <Text style={{ lineHeight: 16, fontSize: 12, color: "#8C93B0", marginLeft: 24, marginTop: 9 }}>{LocalizedStrings['capturing_object']}</Text>
          </View>
          <View style={{ display: "flex", height: 116, width: "100%", flexDirection: "row", alignItems: "center" }}>
            <TouchableWithoutFeedback onPress={() => { this.changeCategory(1) }}>
              <View style={{ display: "flex", justifyContent: "center", alignItems: "center", marginRight: 37, marginLeft: 24 }}>
                <Image
                  style={this.state.isShowActiveStyle ? {
                    width: 50, height: 50,
                    borderWidth: 2, borderColor: "#32BAC0", borderRadius: 50
                  } : { width: 50, height: 50 }}
                  source={require("../../Resources/Images/icon_face_manager_unknown.webp")}
                />
                <Text style={{ marginTop: 8 }}>{LocalizedStrings['family']}</Text>
              </View>
            </TouchableWithoutFeedback>
            {CameraConfig.isSupportPet(Device.model) ?
              <TouchableWithoutFeedback onPress={() => { this.changeCategory(2) }}>
                <View style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
                  <Image
                    style={this.state.isShowActiveStyle ? {
                      width: 50, height: 50
                    } : {
                      width: 50, height: 50,
                      borderWidth: 2, borderColor: "#32BAC0", borderRadius: 50
                    }}
                    source={require("../../Resources/Images/icon_face_animal_unknown.png")}
                  />
                  <Text style={{ marginTop: 8 }}>{LocalizedStrings['pet']}</Text>
                </View>
              </TouchableWithoutFeedback> : null}
          </View>
          </> : null
        }

        {/* 这里要做一个判断，有没有开启每日故事 */}
        {this._renderMessageDialog()}
        <View style={{ width: "100%", height: 40, display: "flex", justifyContent: "center", alignItems: "center" }}>
          <View style={{ width: 312, height: 0.5, backgroundColor: "#E5E5E5" }}></View>
        </View>
        {this._renderDailyStoryList()}
      </View>
    );
  }
  componentWillUnmount() {
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.willFocusSubscription.remove();
  }

  onBackHandler = () => {
    return false;
  }
}