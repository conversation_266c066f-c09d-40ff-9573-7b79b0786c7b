import React from 'react';
import {
  ScrollView,
  View,
  BackHandler,
  FlatList,
  Platform,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity
} from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import StorageKeys from '../StorageKeys';
import Toast from '../components/Toast';
import CameraConfig from '../util/CameraConfig';
import { Service, Host, Device, Entrance } from 'miot';
import LogUtil from '../util/LogUtil';
import { PackageEvent } from 'miot/Package';
import API from '../API';
import Util from "../util2/Util";
import CallUtil, { CALL_TYPE } from "../util/CallUtil";
import DeviceSettingUtil from "../util/DeviceSettingUtil";
import BaseSettingPage from "../BaseSettingPage";

const TAG = "MotionDetectionPage";


/**
 * @Author: byh
 * @Date: 2023/11/11
 * @explanation:
 * 选择、更换联系人
 * 1、已选择的联系人不可选择
 *********************************************************/
export default class ChoiceContactPage extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);
    this.pageType = this.props.navigation.getParam("type") ? this.props.navigation.getParam("type") : 0;
    // 选择联系人方式
    this.choiceType = this.props.navigation.getParam("choiceType") ? this.props.navigation.getParam("choiceType") : CALL_TYPE.SINGLE;
    // 用户的id
    this.uid = this.props.navigation.getParam("uid") ? this.props.navigation.getParam("uid") : undefined;
    this.state = {
      isLoading: false,
      isVip: false,
      switchValue: false,
      sensitiveIndex: 0,
      sensitivityVisible: false,
      contactsData: [],
      shareContactsData: [],
      // 专门存储已经设置了的联系人的uid，不包含当前更好的联系人
      usedUidArr: []
    };
    this.isReadOnlyShared = Device.isReadonlyShared;
    this.titleStr = LocalizedStrings['detect_move'];
    this.detectionDesc = LocalizedStrings['ai_move_desc'];
    this.attentionDesc = LocalizedStrings['ai_note_attention'];
    this.topImageSrc = require("../../Resources/Images/faceRecognition/ai_pic_move.webp");
    this.callSettingData = null;
  }

  getTitle() {
    return  LocalizedStrings['select_contact'];
  }

  componentDidMount() {
    super.componentDidMount();
    // this.props.navigation.setParams({
    //   title: LocalizedStrings['select_contact'],
    //   type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
    //   left: [
    //     {
    //       key: NavigationBar.ICON.BACK,
    //       onPress: () => {
    //         this.props.navigation.goBack();
    //       }
    //     }
    //   ],
    //   titleStyle: {
    //     fontSize: 18,
    //     color: '#333333',
    //     fontWeight: 500
    //   }
    // });
    console.log(TAG, "this.isReadOnlyShared", this.isReadOnlyShared);
    // 获取vip的状态
    StorageKeys.IS_VIP_STATUS.then((res) => {
      StorageKeys.IN_CLOSE_WINDOW.then((res1) => {
        this.setState({ isVip: res || res1 });
      });
      console.log("看看vip的状态：", this.state.isVip);
    }).catch(() => {
      this.setState({ isVip: false });
    });

    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        // this._getSetting();
      }
    );
    this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {//// 原生页面关闭后回调
      // this.autoPlay();
      LogUtil.logOnAll(TAG, "did resume ios");
      this._onResume();
    });
    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      LogUtil.logOnAll(TAG, "did resume");
      this._onResume();
    });

    // let did = Device.extraObj?.split?.parentId;
    Service.callSmartHomeAPI('/share/get_share_user', { did: Device.deviceID, pid: Device.pd_id })
      .then((res) => {
        console.log("res=====", res,this.uid);
        let shareList = res.list;
        let newArray = shareList.filter((item) => item.status == 1);
        newArray.map(item => {
          item.isSelect = this.uid == item.userid;
          return item;
        });
        console.log("res=====", newArray);
        this.setState({ shareContactsData: newArray });
      })
      .catch((e) => console.log("'''''", e));

    let options = {};
    Device.getHomeMemberList(options).then((res) => {
      console.log("++++++",res);
      if (res.code == 0) {
        let resData = res.data;
        resData.map(item => {
          item.nickname = item.nick_name;
          item.userid = item.uid;
          item.isSelect = this.uid == item.userid;
          return item;
        });
        // 排序，把当前账号的排在第一个
        resData.map((item, index) => {
          if (item.userid == Service.account.ID) {
            resData.unshift(resData.splice(index, 1)[0]);
          }
        });
        this.setState({ contactsData: resData });
        // ********** - ********** = **********
      }
    }).catch((error) => {
      console.log("++++++err",error);
    });

    DeviceSettingUtil.getDeviceSettingByKey(DeviceSettingUtil.clickCallSetting).then(res => {
      console.log("=======prefix success", res);
      if (res.code == 0) {
        let settingsData = res.result.settings;
        if (settingsData && settingsData.call_setting) {
          let data = JSON.parse(settingsData.call_setting);
          this.callSettingData = data;
          let uidArr = [];
          if (this.choiceType != CALL_TYPE.GESTURE) {
            // 手势：联系人都可以设置
            if (data.hasOwnProperty("key1")) {
              let key1Data = data.key1;
              if (this.uid || key1Data.mijia != this.uid) {
                uidArr.push(key1Data.mijia);
              }
            }

            if (data.hasOwnProperty("key2")) {
              let key2Data = data.key2;
              if (this.uid || key2Data.mijia != this.uid) {
                uidArr.push(key2Data.mijia);
              }
            }

            if (data.hasOwnProperty("key3")) {
              let key3Data = data.key3;
              if (this.uid || key3Data.mijia != this.uid) {
                uidArr.push(key3Data.mijia);
              }
            }

            // if (data.hasOwnProperty("hand1")) {
            //   let key4Data = data.hand1;
            //   if (this.uid || key4Data.mijia != this.uid) {
            //     uidArr.push(key4Data.mijia);
            //   }
            // }
          }


          this.setState({ usedUidArr: uidArr });
        }
      }
    }).catch(error => {
      console.log("======= error: ", error);
    });

  }

  componentWillUnmount() {
    console.log("DailyStoryFirstEnter componentWillUnmount.....");
    this.willFocusSubscription && this.willFocusSubscription.remove();
    this.didResumeListener && this.didResumeListener.remove();
    this.willAppearListener && this.willAppearListener.remove();
  }

  _onResume() {
    this._fetchVipStatus();
  }

  _fetchVipStatus() {
    console.log(TAG, "_fetchVipStatus start");
    // todo:需要框架端提供能够指定hostName前缀的api
    API.get("/miot/camera/app/v1/vip/status", "business.smartcamera")
      .then((result) => {
        if (result.code != 0) {
          return;
        }
        let data = result.data;
        if (data == null) {
          return;
        }
        console.log(TAG, "_fetchVipStatus", JSON.stringify(result));
        let vip = data["vip"];
        let status = data["status"];
        let inWindow = !data["closeWindow"]; // false表示在窗口内，true表示不在
        let endTime = data["endTime"];
        StorageKeys.IS_VIP_STATUS = vip;
        StorageKeys.IN_CLOSE_WINDOW = inWindow;
        StorageKeys.VIP_DETAIL = data;
        this.setState({ isVip: vip || inWindow });
      })
      .catch((err) => {
        console.log(err);
      });
  }

  renderSettingContent() {

    return (
      <View style={ {
        display: "flex",
        height: "100%",
        flex: 1,
        flexDirection: "column",
        backgroundColor: Util.isDark()? "#xm000000" : "#FFFFFF"
      } }>

          <View style={ styles.container } key={ 102 }>
            <Text
              style={ styles.title_group }>{ LocalizedStrings["family_contact"].replace("%d", this.state.contactsData.length) }</Text>

            <FlatList
              data={ this.state.contactsData }
              renderItem={ this._renderContactItem }
              ItemSeparatorComponent={ () => <View style={ { height: 12 } }/> }
              keyExtractor={ (item, index) => `key_${ index }` }
            />
            <Text
              style={ styles.title_group }>{ LocalizedStrings["share_contact"].replace("%d", this.state.shareContactsData ? this.state.shareContactsData.length : 0) }</Text>
            {
              this.state.shareContactsData && this.state.shareContactsData.length > 0 ?
                <FlatList
                  data={ this.state.shareContactsData }
                  ItemSeparatorComponent={ () => <View style={ { height: 12 } }/> }
                  renderItem={ this._renderContactItem }
                  keyExtractor={ (item, index) => `key_${ index }` }
                /> :
                <View style={ { alignItems: 'center', marginBottom: 60, marginTop: 30 } }>
                  <Image
                    style={ { height: 60, width: 92 } }
                    source={ require('../../Resources/Images/icon_share_no.webp') }/>
                  <Text style={ styles.empty_share_title }>{ LocalizedStrings["no_share_user"] }</Text>
                  <Text style={ styles.empty_share_subtitle }>{ LocalizedStrings["no_share_user_desc"] }</Text>
                  <TouchableOpacity
                    style={ {
                    marginTop: 12,
                    paddingHorizontal: 20,
                    paddingVertical: 8,
                    backgroundColor: 'rgba(0, 0, 0, 0.04)',
                    minHeight: 34,
                    borderRadius: 17 } }
                    onPress={()=>{
                      let newData = { url: "https://home.mi.com/views/article.html?articleId=577935712000000001" };
                      this.props.navigation.navigate("NativeWebPage", newData);
                    } }>
                    <Text
                      style={ { color: 'rgba(0, 0, 0, 0.80)', fontSize: 13, fontWeight: 'bold' } }>{ LocalizedStrings["know_more"] }</Text>
                  </TouchableOpacity>
                </View>
            }


          </View>
      </View>
    );
  }

  _renderContactItem = ({ item, index }) => {
    let isSelected = item.isSelect;
    let itemBgColor = isSelected ? "rgba(50, 186, 192, 0.10)" : Util.isDark() ? "#FFFFFFB2" : "#F0F0F0";
    let disabled = this.state.usedUidArr.includes(item.userid) || this.state.usedUidArr.includes(parseInt(item.userid));
    let opacity = disabled ? { opacity: 0.5 } : { opacity: 1 };
    let nickname = item.nickname ? item.nickname : "";
    if (item.userid == Service.account.ID) {
      nickname = `${nickname}${LocalizedStrings["call_me"]}`
    }
    return (
      <TouchableOpacity
        style={ [{
          height: 70,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          borderRadius: 12,
          marginHorizontal: 12,
          backgroundColor: itemBgColor
        }, opacity] }
        disabled={ disabled }
        onPress={ () => {
          this.userPress(item);

        } }>
        <View
          style={ { display: "flex", flexDirection: "row", alignItems: "center", paddingRight: 20, width: "100%" } }>
          {
            isSelected ?
              <Image
                style={ { width: 28, height: 28, marginStart: 15 } }
                source={ require('../../Resources/Images/plugin_icon_customVoice_selectItem.png') }/> :
              <Image
                style={ { width: 28, height: 28, marginStart: 15 } }/>
          }

          <Image
            style={ { width: 40, height: 40, marginLeft: 12, borderRadius: 20 } }
            source={ item.icon ? { uri: item.icon } : require('../../Resources/Images/icon_user.png') }/>

          <View style={ {
            display: "flex",
            flexDirection: "row",
            flexGrow: 1,
            paddingLeft: 12,
            paddingRight: 15,
            flex: 1,
            alignItems: "center"
          } }>
            <View style={ { display: "flex", flexDirection: "column" } }>
              <Text style={ [{
                fontSize: 16,
                fontWeight: 'bold'
              }, { color: isSelected ? "#32BAC0" : "#000000" }] }>{ nickname }</Text>
              <Text
                style={ [{ fontSize: 13 }, { color: isSelected ? "#32BAC0" : "rgba(0, 0, 0, 0.60)" }] }>{ item.userid ? item.userid : "" }</Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  userPress(item) {
    if (item.isSelect) {
      return;
    }
    CallUtil.getOpenIdByUid(item.userid).then(openId=>{
      this.uploadCallSetting(item,openId);
    }).catch(error => {
      console.log("{{{{{{{{error",error);
      this.uploadCallSetting(item)
    });

  }

  uploadCallSetting(item,openId) {
    console.log("--------",item.userid, typeof (item.userid))
    let uid = item.userid;
    if (typeof (item.userid) != "number") {
      uid = parseInt(item.userid);
    }
    let params = {
      mijia: uid,
      nickname: item.nickname,
      callName: item.nickname,
      icon: item.icon,
      wx: openId ? openId : ""
    };
    switch (this.choiceType){
      case CALL_TYPE.SINGLE:
        this.callSettingData.key1 = params;
        break
      case CALL_TYPE.DOUBLE:
        this.callSettingData.key2 = params;
        break
      case CALL_TYPE.LONG:
        this.callSettingData.key3 = params;
        break;
      case CALL_TYPE.GESTURE:
        this.callSettingData.hand1 = params;
        break;
    }
    DeviceSettingUtil.setDeviceSetting(`${ DeviceSettingUtil.clickCallSetting }`, JSON.stringify(this.callSettingData)).then((res) => {
      // 成功后，
      // 1、刷新UI展示
      // 2、callback通知出去

      this.state.shareContactsData.forEach(itm => {
        if (this.uid == itm.userid) {
          itm.isSelect = false;
        }
        if (item.userid == itm.userid) {
          itm.isSelect = true;
        }
      });

      this.state.contactsData.forEach(itm => {
        if (this.uid == itm.userid) {
          itm.isSelect = false;
        }

        if (item.userid == itm.userid) {
          itm.isSelect = true;
        }
      });
      let newUsedArr = this.state.usedUidArr.filter((itm) => this.uid != itm);
      this.uid = item.userid;
      // item.isSelect = true;
      this.setState({usedUidArr: newUsedArr});
      let paramsStr = JSON.stringify(this.callSettingData);
      CallUtil.updateSettingToDevice([paramsStr]);
      this.props.navigation.getParam("callback")(params);
    }).catch(error => {

    });
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Util.isDark() ? "#xm000000" : 'white'
  },
  title_group: {
    color: '#8C93B0',
    fontSize: 12,
    paddingVertical: 10,
    marginHorizontal: 27
  },
  empty_share_title: {
    color: 'rgba(0, 0, 0, 0.40)',
    fontSize: 14,
    marginTop: 12,
    marginHorizontal: 27
  },
  empty_share_subtitle: {
    color: 'rgba(0, 0, 0, 0.38)',
    fontSize: 12,
    paddingVertical: 5,
    marginHorizontal: 27
  },
  white_blank: {
    height: 0.5,
    marginTop: 20
  },
  whiteblank: {
    height: 0.5,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginBottom: 30,
    marginTop: 20

  },
  algorithm_title: {
    color: "#999",
    fontSize: 18,
    paddingHorizontal: 24
  },
  algorithm_subtitle: {
    color: "#999",
    fontSize: 12,
    marginTop: 10,
    paddingHorizontal: 24
  },
  optionsPage: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 20,
    paddingHorizontal: 24
  },
  optionContainer: {
    minHeight: 80,
    alignItems: 'center'
  },
  icon: {
    width: 48,
    height: 48
  },
  optionText: {
    marginTop: 4,
    marginBottom: 10,
    width: 48,
    textAlign: 'center',
    fontSize: 12
  }

});