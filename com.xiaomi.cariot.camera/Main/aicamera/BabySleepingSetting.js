import React from 'react';
import { Service, Device } from "miot";
import { PanResponder, ART, View, BackHandler, Dimensions, Platform, Text, Button, StyleSheet, Image, TouchableOpacity, TouchableWithoutFeedback, ImageBackground } from 'react-native';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Switch from 'miot/ui/Switch';
import { styles } from '../setting/SettingStyles';
import CameraConfig, { IMG_DARKMODE_TINT } from '../util/CameraConfig';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtil from '../util/AlarmUtil';
import Toast from '../components/Toast';
import { DarkMode } from 'miot/Device';
import { NavigationBar } from 'mhui-rn';
import RPC from '../util/RPC';
import Host from 'miot/Host';
import { LoadingDialog } from 'miot/ui/Dialog';
import VersionUtil from '../util/VersionUtil';

let {
  Surface, //  一个矩形可渲染的区域，是其他元素的容器
  Shape, // 形状定义，可填充
  Path // 路径
} = ART;
const kWindowHeight = Math.max(Dimensions.get("screen").height, Dimensions.get("screen").width);
const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width);// use this.winPortraitWidth instead

const viewWidth = kWindowWidth;
const viewHeight = Math.floor(kWindowWidth) * 0.5625;

const itemWidth = viewWidth / 8;
const itemHeight = viewHeight / 4;
export default class BabySleepingSetting extends React.Component {
  static navigationOptions = (navigation) => {
    return {// 不要导航条
      headerTransparent: true,
      header:
        null
    };
  };
  constructor(props, context) {
    super(props, context);
    this.state = {
      progressing: false,
      edited: false,
      babySwitch: false
    };
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.onResume();
      }
    );
    this.isPageForeGround = true;
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'didBlur',
      () => {
        this.isPageForeGround = false;
      }
    );
    this.rectDatas = [0, 0, 0, 0];
    this.rectBackGround = [0, 0, 0, 0];
    this.distanceData = [0, 0, 0, 0];
    this.moveMode = 0;
    this.canvas = null;
    this.panResponder = PanResponder.create({
      onStartShouldSetPanResponder: () => true, // 刚开始的时候
      onMoveShouldSetPanResponder: () => true,
      onShouldBlockNativeResponder: () => false,
      onPanResponderTerminationRequest: () => false, // 不允许其他人抢占。
      onPanResponderGrant: (evt) => {
        // console.log("page:", evt.nativeEvent.pageX, "-", evt.nativeEvent.pageY);
        let x = evt.nativeEvent.locationX;
        let y = evt.nativeEvent.locationY;
        console.log("move:down:", x, "-", y);
        this.distanceData = [0, 0, 0, 0];
        let smallest = [80, 80];
        let mode = [0, 0];
        this.distanceData[0] = Math.abs(x - this.rectDatas[0]);
        console.log("move:this.distanceData[0]", this.distanceData[0]);
        if (this.distanceData[0] < smallest[0]) {
          smallest[0] = this.distanceData[0];
          mode[0] = 8;
        }
        this.distanceData[1] = Math.abs(y - this.rectDatas[1]);
        console.log("move:this.distanceData[1]", this.distanceData[1]);
        if (this.distanceData[1] < smallest[1]) {
          smallest[1] = this.distanceData[1];
          mode[1] = 4;
        }
        this.distanceData[2] = Math.abs(x - this.rectDatas[2]);
        console.log("move:this.distanceData[2]", this.distanceData[2]);
        if (this.distanceData[2] < smallest[0]) {
          smallest[0] = this.distanceData[2];
          mode[0] = 2;
        }
        this.distanceData[3] = Math.abs(y - this.rectDatas[3]);
        console.log("move:this.distanceData[3]", this.distanceData[3]);
        if (this.distanceData[3] < smallest[1]) {
          smallest[1] = this.distanceData[3];
          mode[1] = 1;
        }
        this.moveMode = mode[0] | mode[1];
        console.log("move:moveMode:", this.moveMode);
      },

      onPanResponderMove: (evt) => {
        // console.log("page:", evt.nativeEvent.pageX, "-", evt.nativeEvent.pageY);
        let x = evt.nativeEvent.locationX;
        let y = evt.nativeEvent.locationY;
        // console.log("move:", x, "-", y);
        switch (this.moveMode) {
          case 12: {
            if (x > 0 && x < this.rectDatas[2] - itemWidth) {
              this.rectDatas[0] = x;
              this.rectBackGround[0] = Math.floor(x / itemWidth) * itemWidth;
            }
            if (y < this.rectDatas[3] - itemHeight) {
              this.rectDatas[1] = y;
              this.rectBackGround[1] = Math.floor(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 6: {
            if (y < this.rectDatas[3] - itemHeight) {
              this.rectDatas[1] = y;
              this.rectBackGround[1] = Math.floor(y / itemHeight) * itemHeight;
            }
            if (x > this.rectDatas[0] + itemWidth) {
              this.rectDatas[2] = x;
              this.rectBackGround[2] = Math.ceil(x / itemWidth) * itemWidth;
            }
            break;
          }
          case 3: {
            if (x > this.rectDatas[0] + itemWidth) {
              this.rectDatas[2] = x;
              this.rectBackGround[2] = Math.ceil(x / itemWidth) * itemWidth;
            }
            if (y > this.rectDatas[1] + itemHeight && y < viewHeight - 1) {
              this.rectDatas[3] = y;
              this.rectBackGround[3] = Math.ceil(y / itemHeight) * itemHeight;
            }
            break;
          }
          case 9: {
            if (x > 0 && x < this.rectDatas[2] - itemWidth) {
              this.rectDatas[0] = x;
              this.rectBackGround[0] = Math.floor(x / itemWidth) * itemWidth;
            }
            if (y > this.rectDatas[1] + itemHeight && y < viewHeight - 1) {
              this.rectDatas[3] = y;
              this.rectBackGround[3] = Math.ceil(y / itemHeight) * itemHeight;
            }
            break;
          }
        }
        // console.log("move:", this.rectL, this.rectT, this.rectR, this.rectB);
        this.setState({ edited: true });
      },

      onPanResponderRelease: () => {
        // let x = evt.nativeEvent.locationX;
        // let y = evt.nativeEvent.locationY;
        // console.log("Rele:", x, "-", y);
      },

      onPanResponderTerminate: () => { }
    });
  }

  renderTitleBar() {
    let titleBarContent = {
      title: LocalizedStrings['ss_baby_sleep_setting'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: (this.state.edited) ? NavigationBar.ICON.CLOSE : NavigationBar.ICON.BACK,
          onPress: () => { 
            this.props.navigation.goBack();
          }
        }
      ],
      right: [
        {
          key: (this.state.edited) ? NavigationBar.ICON.COMPLETE : null,
          onPress: () => {
            this.onSubmit();
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    };
    // this.props.navigation.setParams(titleBarContent);
    return (
      <NavigationBar {...titleBarContent} />
    );
  }

  onSubmit() {
    this.setState({ progressing: true });
    let positions = [Math.ceil(this.rectDatas[0] / viewWidth * 100), Math.ceil(this.rectDatas[1] / viewHeight * 100),
      Math.ceil(this.rectDatas[2] / viewWidth * 100), Math.ceil(this.rectDatas[3] / viewHeight * 100)];
    let params = JSON.stringify([{ "start_coor": `[${ positions[0] }, ${ positions[1] }]`, "end_coor": `[${ positions[2] }, ${ positions[3] }]` }]);
    console.log("onSubmit params=", params);
    AlarmUtil.putBabySleepArea(params).then((res) => {
      console.log(`putBabySleepArea res=${ JSON.stringify(res) }`);
      Toast.success("c_set_success");
      this.setState({ progressing: false });
      this.props.navigation.goBack();
    }).catch((err) => {
      this.setState({ progressing: false });
      Toast.fail("action_failed", err);
      console.log(`putBabySleepArea err=${ JSON.stringify(err) }`);
    });
  }

  componentDidMount() {
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.loadData();
  }

  render() {
    let isDark = DarkMode.getColorScheme() == "dark";
    let juxing_path = Path()
      .moveTo(this.rectDatas[0], this.rectDatas[1])
      .lineTo(this.rectDatas[2], this.rectDatas[1])
      .lineTo(this.rectDatas[2], this.rectDatas[3])
      .lineTo(this.rectDatas[0], this.rectDatas[3])
      .close();
    let backGround_path = Path()
      .moveTo(this.rectBackGround[0], this.rectBackGround[1])
      .lineTo(this.rectBackGround[2], this.rectBackGround[1])
      .lineTo(this.rectBackGround[2], this.rectBackGround[3])
      .lineTo(this.rectBackGround[0], this.rectBackGround[3])
      .close(); // close 封闭

    let imageSource = require("../../Resources/Images/baby_sleep_tips_icon.webp");
    if (this.state.babySwitch) {
      imageSource = { uri: `${ Host.file.storageBasePath }/${ VersionUtil.settingsImgPath }` };
    }
    return (<View style={{ display: "flex", height: "100%", width: "100%", flex: 1, flexDirection: "column", backgroundColor: "#FFFFFF", alignItems: "flex-start" }}>
      { this.renderTitleBar() }
      <View style={{ alignSelf: "center", backgroundColor: "#ffffff" }}>
        <View {...this.panResponder.panHandlers}>
          <ImageBackground style={{ alignSelf: "center", width: viewWidth, height: viewHeight }}
            source={ imageSource }>
            { this.state.babySwitch ?
              <Surface width={viewWidth} height={viewHeight}>
                <Shape d={juxing_path} stroke="#32BAC0" strokeWidth={1}/> 
                <Shape d={backGround_path} fill="#32BAC0" opacity="0.3"/>
              </Surface> : null }
          </ImageBackground>
        </View>
        <ListItemWithSwitch
          title={LocalizedStrings['ss_baby_sleep_setting']}
          showSeparator={false}
          subtitle={LocalizedStrings['settings_alarm_baby_sleep_sub']}
          value={this.state.babySwitch}
          onValueChange={(value) => this._onSwitchChange(value)}
          titleStyle={{ fontWeight: 'bold' }}
        />
        <View style={{ marginStart: 28, borderBottomColor: "#f0f0f0", borderBottomWidth: 0.8 }}></View>
        <Text style={{ paddingStart: 30, paddingEnd: 20, paddingTop: 10, color: "#999999" }}>
          {LocalizedStrings['settings_alarm_baby_sleep_tip']}</Text>
        <LoadingDialog
          visible={this.state.progressing}
          message={LocalizedStrings['c_setting']}
          onDismiss={(_) => console.log("dismiss")}
        />
      </View>
    </View>);
  }

  _onSwitchChange(value) {
    this.setState({ progressing: true });
    AlarmUtil.putBabySleepSwitch(value).then((res) => {
      console.log(`putBabySleepSwitch res=${ JSON.stringify(res) }`);
      this.setState({ babySwitch: value, progressing: false });
    }).catch((err) => {
      console.log(`putBabySleepSwitch err=${ JSON.stringify(err) }`);
    });
  }

  onResume() {
    
  }

  loadData() {
    // console.log(kWindowHeight, "-", kWindowWidth);
    AlarmUtil.getBabySleepParams(2).then((res) => {
      console.log(`getBabySleepParams res=${ JSON.stringify(JSON.parse(res[1].value)[0]) }`);
      if (res instanceof Array) {
        let switchValue = res[0].value;
        let start_coor = JSON.parse(JSON.parse(res[1].value)[0].start_coor);
        let end_coor = JSON.parse(JSON.parse(res[1].value)[0].end_coor);
        if (start_coor[0] <= 0) start_coor[0] = 1;
        if (start_coor[1] <= 0) start_coor[1] = 2;
        if (end_coor[0] <= 0) end_coor[0] = 5;
        if (end_coor[1] <= 0) end_coor[1] = 10;
        if (start_coor[0] > 100) start_coor[0] = 1;
        if (start_coor[1] > 100) start_coor[1] = 20;
        if (end_coor[0] > 100) end_coor[0] = 30;
        if (end_coor[1] > 100) end_coor[1] = 90;
        this.rectDatas = [start_coor[0] / 100.0 * viewWidth, start_coor[1] / 100.0 * viewHeight,
          end_coor[0] / 100.0 * viewWidth, end_coor[1] / 100.0 * viewHeight];
        this.rectBackGround = [Math.floor(this.rectDatas[0] / itemWidth) * itemWidth, Math.floor(this.rectDatas[1] / itemHeight) * itemHeight,
          Math.ceil(this.rectDatas[2] / itemWidth) * itemWidth, Math.ceil(this.rectDatas[3] / itemHeight) * itemHeight];
        this.setState({ babySwitch: switchValue });
      }
    }).catch((err) => {
      console.log(`getBabySleepParams err=${ JSON.stringify(err) }`);
    });
  }

  componentWillUnmount() {
    if (Platform.OS === "android") {                                
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  onBackHandler = () => {
    if (this.state.edited) {
      this.onSubmit();
      return true;
    }
    return false;
  }
}

