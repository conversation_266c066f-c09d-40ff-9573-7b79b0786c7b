import React from 'react';
import { View, Text, StyleSheet, Image, Dimensions } from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { ChoiceDialog, NavigationBar } from 'mhui-rn';
import Toast from '../components/Toast';
import { Service, Host, Device, Entrance } from 'miot';
import LogUtil from '../util/LogUtil';
import { PackageEvent } from 'miot/Package';
import { ListItem, ListItemWithSwitch } from "miot/ui/ListItem";
import { styles } from "../setting/SettingStyles";
import AlarmUtilV2, {
  PIID_AI_EXTENSION_HOUR_SWITCH, SIID_AI_EXTENSION
} from "../util/AlarmUtilV2";
import BaseSettingPage from "../BaseSettingPage";
import Util from "../util2/Util";
const viewHeight = (Dimensions.get('window').width - 48) * 9 / 16;

const TAG = "OnTimeAlarmPage";

/**
 * @Author: byh
 * @Date: 2024/3/02
 * @explanation:
 * 整点报时
 *********************************************************/
export default class OnTimeAlarmPage extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);
    this.state = {
      switchValue: false,
    };
    this.topImageSrc = require("../../Resources/Images/faceRecognition/ai_pic_hour_alarm.webp");
  }

  getTitle(): string {
    return LocalizedStrings['on_time_alarm'];
  }

  componentDidMount() {
    super.componentDidMount();
    this._getSetting();
  }
  componentWillUnmount() {
  }

  _getSetting() {
    let params = [{ sname: SIID_AI_EXTENSION, pname: PIID_AI_EXTENSION_HOUR_SWITCH }];
    AlarmUtilV2.getSpecPValue(params).then((res) => {
      if (res[0].code == 0) {
        this.setState({ switchValue: res[0].value })
      } else {
        Toast.fail("c_get_fail");
      }
    }).catch((err) => {
      Toast.fail("c_get_fail",err);
    });
  }

  _onSwitchValue(value) {
    let params = [{ sname: SIID_AI_EXTENSION, pname: PIID_AI_EXTENSION_HOUR_SWITCH, value: value }];
    AlarmUtilV2.setSpecPValue(params, TAG).then((res) => {
      console.log("_onSwitchValue success", res);
      if (res[0].code != 0) {
        this.setState({ switchValue: !value });
        Toast.fail("c_set_fail");
      } else {
        this.setState({ switchValue: value });
        Toast.success("c_set_success");
      }
    }).catch((err) => {
      this.setState({ switchValue: !value });
      Toast.fail("c_set_fail", err);
    });
  }


  renderSettingContent() {

    return (
      <View style={{ display: "flex", height: "100%", flex: 1, flexDirection: "column", alignItems: "center" }}>
        {/*{this.renderTitleBar()}*/}
          <View style={styles.featureSetting} key={102}>

            <View style={{ alignItems: "center", marginHorizontal: 24, marginTop: 0 }}>
              <Image style={{ width: '100%', height: viewHeight, borderRadius: 9 }}
                source={this.topImageSrc} />
            </View>

            <View style={stylesDetection.white_blank} />
            <Text style={[styles.desc_subtitle, { marginTop: 0 }]}>{LocalizedStrings['ai_alarm_desc']}</Text>

            <View style={styles.whiteblank} />

            <ListItemWithSwitch
              titleNumberOfLines={3}
              unlimitedHeightEnable={true}
              showSeparator={false}
              title={LocalizedStrings['on_time_alarm']}
              value={this.state.switchValue}
              onValueChange={(val) => this._onSwitchValue(val)}
              onPress={() => {
              }}
              titleStyle={{ fontWeight: 'bold' }}
              accessibilitySwitch={{
                accessibilityLabel: LocalizedStrings['care_screen_close_show_protect']
              }}
            />
            <ListItem
              title={LocalizedStrings['on_time_alarm_time']}
              showSeparator={false}
              onPress={() =>{
                this.props.navigation.navigate("OnTimeSetting");
              }}
              titleStyle={{ fontWeight: 'bold' }}
              titleNumberOfLines={3} />

          </View>

      </View>
    )
  }
  renderTitleBar() {
    let titleBarContent = {
      title: LocalizedStrings['on_time_alarm'],
      type: this.state.darkMode ? NavigationBar.TYPE.DARK : NavigationBar.TYPE.LIGHT,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
            this.props.navigation.goBack();
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    };
    return (
      <NavigationBar {...titleBarContent} />
    );
  }
}

const stylesDetection = StyleSheet.create({
  white_blank: {
    height: 0.5,
    marginTop: 20,
  },
  whiteblank: {
    height: 0.5,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginBottom: 30,
    marginTop: 20

  },
  algorithm_title: {
    color: "#999",
    fontSize: 18,
    paddingHorizontal: 24
  },
  algorithm_subtitle: {
    color: "#999",
    fontSize: 12,
    marginTop: 10,
    paddingHorizontal: 24
  },
  optionsPage: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 20,
    paddingHorizontal: 24
  },
  optionContainer: {
    minHeight: 80,
    alignItems: 'center'
  },
  icon: {
    width: 48,
    height: 48
  },
  optionText: {
    marginTop: 4,
    marginBottom: 10,
    width: 48,
    textAlign: 'center',
    fontSize: 12
  },

});