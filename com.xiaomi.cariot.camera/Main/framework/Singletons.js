import CloudEvLdr from './CloudEventLoader';
import DailyStoryEvLdr from './DailyStoryLoader';
const SInst = {};
export default class Singletons {
  
  // static get DeviceStatusHelperWithSubscribe() {
  //   let val = SInst["DeviceStatusHelperWithSubscribe"];
  //   if (val == null) {
  //     val = require('./DeviceStatusHelperWithSubscribe')._DeviceStatusHelperWithSubscribe;
  //     SInst["DeviceStatusHelperWithSubscribe"] = val;
  //   }
  //   return val;
  // }
  
  
  // static get DeviceStatusHelperWithPoll() {
  //   let val = SInst["DeviceStatusHelperWithPoll"];
  //   if (val == null) {
  //     let t = require('./DeviceStatusHelperWithPoll')._DeviceStatusHelperWithPoll;
  //     val = new t();
  //     SInst["DeviceStatusHelperWithPoll"] = val;
  //   }
  //   return val;
  // }
  
  // static get P2PPlayer() {
  //   let val = SInst["Player"];
  //   if (val == null) {
  //     let t = require('./Player').Player;
  //     val = new t();
  //     SInst["Player"] = val;
  //   }
  //   return val;
  // }
  
  // static get ProfPushHandler() {
  //   let val = SInst["ProfPushHandler"];
  //   if (val == null) {
  //     let t = require('./ProfPushHandler')._ProfPushHandler;
  //     val = new t();
  //     SInst["ProfPushHandler"] = val;
  //   }
  //   return val;
  // }
  
  static get CloudEventLoader() {
    let val = SInst["CloudEventLoader"];
    if (val == null) {
      // let tt = require('./CloudEventLoader')._CloudEventLoader;
      // val = new tt();
      val = CloudEvLdr;
      SInst["CloudEventLoader"] = val;
    }
    return val;
  }
  // 每日故事
  static get DailyStoryLoader() {
    let val = SInst["DailyStoryLoader"];
    if (val == null) {
      // let tt = require('./CloudEventLoader')._CloudEventLoader;
      // val = new tt();
      val = DailyStoryEvLdr;
      SInst["DailyStoryLoader"] = val;
    }
    return val;
  }

}
