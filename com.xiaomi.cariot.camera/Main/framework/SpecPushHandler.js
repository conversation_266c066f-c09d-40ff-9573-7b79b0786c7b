import PushHandler, { Go2LiveLimit, DBDlg } from "./PushHandler";
import Util from "../util/Util";

const StrongPush = "10.19";
const DefaultPush = "10.18";
const AppearPush = "10.13";
const StayPush = "10.14";

class _SpecPushHandler extends PushHandler {
  async handlePush(aPush, aIsInLive, aRootPush) {
    let ev = aPush.event;
    if (StrongPush === ev || DefaultPush === ev || AppearPush === ev || StayPush === ev) {
      if (Util.isAndroid()) {
        return this.analyseSpecPushAndroid(aPush, aIsInLive, aRootPush);
      } else {
        return this.analyseSpecPushIOS(aPush, aIsInLive, aRootPush);
      }
    } else {
      return Promise.reject(`don't care ${ ev }`);
    }
  }

  analyseSpecContent(aContent, showDoorBell, goLivePlay, goVideoPlay, videoFileId, createTime, event = null, aRootPush = false) {
    for (const itm of aContent) {
      const piid = itm.piid;
      if (35 === piid) {
        goVideoPlay = true;
        videoFileId = itm.value;
      }
    }
    if (goVideoPlay) {
      if (Math.abs(Date.now() - createTime) < Go2LiveLimit) {
        if (StrongPush == event) {
          this.mDoorBellTime = new Date(createTime);
          if (aRootPush) {
            showDoorBell = DBDlg.Big;
          } else {
            showDoorBell = DBDlg.Small;
          }
        }

        if (!Util.isAndroid() && aRootPush) {
          showDoorBell = DBDlg.None;
        }

        goLivePlay = true;
      }
      return Promise.resolve({ showDoorBell, goLivePlay, goVideoPlay, videoFileId, createTime });
    } else {
      return Promise.resolve({ showDoorBell, goLivePlay, goVideoPlay, videoFileId, createTime });
    }
  }


  analyseSpecPushIOS(aEntry, isLiveVideoForeGround, aRootPush) {
    let event = aEntry.event;
    let vArr = aEntry.value;
    let createTime = aEntry.time * 1000; // to ms
    let showDoorBell = DBDlg.None;
    let goLivePlay = false;
    let goVideoPlay = false;
    let videoFileId = null;

    // let fgPush = "kMHPluginReceivingForegroundPushEvent" === aEntry.eventName;
    if (StrongPush == event || aRootPush) {
      return this.analyseSpecContent(vArr, showDoorBell, goLivePlay, goVideoPlay, videoFileId, createTime, event, aRootPush);
    } else {
      return Promise.resolve({ showDoorBell, goLivePlay, goVideoPlay, videoFileId, createTime });
    }
  }

  analyseSpecPushAndroid(aEntry, isLiveVideoForeGround, aRootPush) {
    let type = aEntry.type;
    let event = aEntry.event;
    let extra = aEntry.extra;
    // isNotified is String in some case. This works fine whether isNotified is String or Boolean
    let isFromNotify = aEntry.isNotified.toString().toLowerCase() === 'true';
    let createTime = aEntry.time * 1000;

    let showDoorBell = DBDlg.None;
    let goLivePlay = false;
    let goVideoPlay = false;
    let videoFileId = 0;


    if (type === "ScenePush") {
      return this.analyseSpecContent(JSON.parse(extra), showDoorBell, goLivePlay, goVideoPlay, videoFileId, createTime, isFromNotify ? null : event, aRootPush);
    } else {
      return Promise.resolve({ showDoorBell, goLivePlay, goVideoPlay, videoFileId });
    }
  }
}

const SpecPushHandler = new _SpecPushHandler();
export default SpecPushHandler;
