import React from 'react';
// import AlbumHelper, { SNAPSHOT_IMG_PATH } from '../util/AlbumHelper';
// import { DeviceEventEmitter, Platform, View } from 'react-native';
// import { MISSCommand, MISSError, MISSConnectState } from "miot/service/miotcamera";
// import { Device, Service, Host } from 'miot';
// import { HostEvent } from "miot/Host";
// import NetInfo from "@react-native-community/netinfo";
// import StorageKeys from '../StorageKeys';
// import CameraRenderView, { MISSCodec, MISSSampleRate, MISSDataBits, MISSAudioChannel } from 'miot/ui/CameraRenderView';
// import { localStrings as LocalizedStrings } from "../MHLocalizableString";
// import base64js from 'base64-js';
// import Util from "../util2/Util";

// // export const MISSConnectState = {
// //   MISS_Connection_Disconnected: 0,
// //   MISS_Connection_Connecting: 1,
// //   MISS_Connection_Connected: 2,
// //   MISS_Connection_ReceivedFirstFrame: 3
// // };

// const kConnectionCallBackName = 'connectionCallBack';
// const kBpsDataReceiveCallbackName = "bpsDataReceiveCallback";
// const kRecordTimeCallbackName = "recordTimeCallback";

// const TAG = "P2P_Player";

// export const PlayerState = {
//   Idel: "idel",
//   Connecting: "connecting",
//   Playing: "playing",
//   DisConnecting: "disconnecting",
//   Disconnected: "disconnected",
//   DisconnectWithErr: "disconnected_with_err"
// };

// export const Resolution = {
//   R1080P: { value: 3, title: "1080P", recording: { width: 1920, height: 1080 },
//     icon: [require('../../resources2/images/icon_res_1080.png'), require('../../resources2/images/icon_res_1080.png')] },
//   R720P: { value: 2, title: "720P", recording: { width: 1280, height: 720 },
//     icon: [require('../../resources2/images/icon_res_720.png'), require('../../resources2/images/icon_res_720.png')] },
//   R360P: { value: 1, title: "360P", recording: { width: 480, height: 320 },
//     icon: [require('../../resources2/images/icon_res_360.png'), require('../../resources2/images/icon_res_360.png')] },
//   RAUTO: { value: 0, title: LocalizedStrings["camera_quality_auto"], recording: { width: 1920, height: 1080 },
//     icon: [require('../../resources2/images/icon_res_auto.png'), require('../../resources2/images/icon_res_auto.png')] }
// };

// export const PlayerErr = {
//   Unknown: "unknown",
//   TimeOut: "timeout",
//   NeedWifi: "needwifi",
//   Disconnect: "disconnect",
//   UserCancel: "usercancel",
//   WakeupFailed: "wakeupfailed"
// };

export const Info = {
  Bps: 1,
  Rec: 2,
  Resolution: 3,
  Resp: 4,
  Load: 5,
  Progress: 6,
  End: 7,
  ViewUpdate: 8,
  SeekComplete: 9,
  StateChange: 10,
  PlayState: 11,
  PlayerLive:12
};

// export class Player {
//   constructor() {
//     this.connectionListener = DeviceEventEmitter.addListener(kConnectionCallBackName, this.mConnL);
//     this.cmdReceiveListener = null;
//     this.mConnState = {};
//     this.speed = 1;
//     this.networkState = 0;
//     // 监听手机网络状态变化
//     this.cellPhoneNetworkStateChanged = HostEvent.cellPhoneNetworkStateChanged.addListener((aState) => this.networkChange(aState));
//     this.mute = true;
//     this.mResolTmp = Resolution.RAUTO;
//     this.muteBeforeCall = true;
//     this.mState = PlayerState.Idel;
//     this.mNetState = -1;
//     this.mBps = { bps: 0 };
//     this.mBpsL = DeviceEventEmitter.addListener(kBpsDataReceiveCallbackName, this.mBpsR);
//     this.mCamL = [];
//     this.mStatusHelper = null;
//     this.mRecordingR = Resolution.R1080P;
//     this.mSupportR = [Resolution.R1080P, Resolution.R360P, Resolution.RAUTO];

//     StorageKeys.IS_DATA_USAGEE_WARNING
//       .then((aRes) => {
//         if (aRes || aRes === "true") {
//           this.mUseData = false;
//         } else {
//           this.mUseData = true;
//         }
//       })
//       .catch((aErr) => {
//         StorageKeys.IS_DATA_USAGEE_WARNING = false;
//         this.mUseData = true;
//       });
//     // TODO ensure all field init after constructor
//   }

//   async initWithConfig(aCfg) {
//     this.mStatusHelper = aCfg.statusHelper;
//     if (aCfg.player) {
//       if (aCfg.player.recording) {
//         this.mRecordingR = aCfg.player.recording;  
//       }
//       if (aCfg.player.resolution) {
//         this.mSupportR = aCfg.player.resolution;
//       }
//     }
//     let reset = true;
//     try {
//       let savedRes = await StorageKeys.LIVE_VIDEO_RESOLUTION;
//       let supported = this.mSupportR.findIndex((aRes) => {
//         return aRes.value == savedRes;
//       });
//       if (supported != -1) {
//         reset = false;
//         this.mResol = this.mSupportR[supported];
//       }
//     } catch (aErr) {
//       console.log(TAG, "initWithConfig got err", aErr);
//     } finally {
//       if (reset) {
//         this.mResol = Resolution.RAUTO;
//         StorageKeys.LIVE_VIDEO_RESOLUTION = Resolution.RAUTO.value;  
//       }
//     }
    
//   }

//   updateState(aState) {
//     if (aState != this.mState) {
//       let info = { old: this.mState, new: aState };
//       console.log(TAG, "updateState", info);
//       this.mState = aState;
//       this.notify(Info.StateChange, info);
//     }
//   }

//   pause() {

//   }

//   resume() {

//   }

//   seek(aPos) {

//   }

//   clear() {
//     this.connectionListener.remove();
//     this.cmdReceiveListener && this.cmdReceiveListener.remove();
//     this.cellPhoneNetworkStateChanged.remove();
//   }

//   setMute(aMute) {
//     return this.setAudioState(aMute);
//   }

//   /*
//   *             -1 ：DefaultState
//   *              0 ：网络不可用
//   *              1 ：蜂窝网络 2G 3G 4G
//   *              2 ：WiFi网络
//   */
//   networkChange(aState) {
//     if (this.mNetState != aState) {
//       this.mNetState = aState;
//       switch (this.mNetState) {
//         case 0:
//           this.disconnectToDevice();
//           break;
//         case 2:
//           if (this.mState === PlayerState.Connecting) {
//             this.start();
//           }
//       }

//     }
//   }


//   getP2PCommandCallbackName() {
//     return 'P2PCmdCallBack';
//   }

//   bindP2PCommandCallback() {
//     let cbName = this.getP2PCommandCallbackName();
//     if (null == this.cmdReceiveListener) {
//       this.cmdReceiveListener = DeviceEventEmitter.addListener(cbName, this.mP2pL);
//     }
//     Service.miotcamera.bindP2PCommandReceiveCallback(cbName);
//   }


//   isAndroid() {
//     return Platform.OS === 'android';
//   }


//   disconnectToDevice() {
//     Service.miotcamera.disconnectToDevice();
//     this.mConnState = { state: 0, error: MISSError.MISS_ERR_CLOSE_BY_LOCAL };
//   }

//   destroy() {
//     this.cellPhoneNetworkStateChanged.remove();// 避免内存泄漏
//     this.connectionListener.remove();
//     this.cmdReceiveListener && this.cmdReceiveListener.remove();
//     this.mBpsL.remove();
//     Service.miotcamera.disconnectToDevice();// 只调用一次。
//     Player.destroy();
//   }



//   getVideoView(aFullScreenState = false) {
//     return (<CameraRenderView
//       ref={(ref) => { this.mCamV = ref; }}
//       maximumZoomScale={3.0}
//       style={{ flex: 1 }}
//       videoCodec={MISSCodec.MISS_CODEC_VIDEO_H265}
//       audioCodec={MISSCodec.MISS_CODEC_AUDIO_G711A}
//       audioRecordSampleRate={MISSSampleRate.FLAG_AUDIO_SAMPLE_8K}
//       audioRecordChannel={MISSAudioChannel.FLAG_AUDIO_CHANNEL_MONO}
//       audioRecordDataBits={MISSDataBits.FLAG_AUDIO_DATABITS_16}
//       videoRate={15}
//       correctRadius={1.2}
//       osdx={0.243}
//       osdy={0.03964}
//       useLenCorrent={false} // 门铃疯景199不需要畸变纠正
//       did={Device.deviceID}
//       isFull={false}
//       fullscreenState={aFullScreenState}
//       recordingVideoParam={{ width: this.mRecordingR.recording.width, height: this.mRecordingR.recording.height }}
//     />);
//   }


//   addCameraListener(aCamL) {
//     this.mCamL.push(aCamL);
//     return {
//       remove: () => {
//         this.mCamL = this.mCamL.filter((aL) => {
//           return aL !== aCamL;
//         });
//       }
//     };
//   }

//   changeResolution(aRes, aSave = true) {
    
//     let supported = this.mSupportR.findIndex((aVal) => {
//       return aVal.value == aRes;
//     });
//     if (supported > -1) {
//       if (aSave) {
//         StorageKeys.LIVE_VIDEO_RESOLUTION = aRes;
//       }
//       this.mResol = this.mSupportR[supported];
//       console.log(TAG, "changeResolution", aRes, aSave);
//       return Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_STREAM_CTRL_REQ, { "videoquality": aRes });  
//     } else {
//       return Promise.reject("not support");
//     }
    
//   }

//   setAudioState(aMute) {
//     return new Promise((resolve, reject) => {
//       let cmd = aMute ? MISSCommand.MISS_CMD_AUDIO_STOP : MISSCommand.MISS_CMD_AUDIO_START;
//       Service.miotcamera.sendP2PCommandToDevice(cmd, {})
//         .then((retCode) => {
//           console.log(TAG, "setAudioState mute", aMute, "success", retCode);
//           aMute ? this.mCamV.stopAudioPlay() : this.mCamV.startAudioPlay();
//           this.mute = aMute;
//           resolve(aMute);
//         })
//         .catch((err) => {
//           console.log("setAudioState mute", aMute, "failed", err);
//           reject(err);
//         });
//     });
//   }

//   startRecord() {
//     return new Promise((resolve, reject) => {
//       this.mRecordP = AlbumHelper.getFileName(true);
//       let cmd;
//       if (this.mResol.value != this.mRecordingR.value) {
//         this.mResolTmp = this.mResol;
//         console.log(TAG, "record Resolution", this.mResol.title, "=>", this.mRecordingR.title);
//         cmd = this.changeResolution(this.mRecordingR.value, false);
//       }
//       if (this.mute) {
//         let audioCmd = Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {});
//         if (cmd != null) {
//           cmd = cmd.then(() => { return audioCmd; });
//         } else {
//           cmd = audioCmd;
//         }
//       }

//       if (cmd != null) {
//         cmd = cmd.then(() => { return this.mCamV.startRecord(`${ Host.file.storageBasePath }/${ this.mRecordP }`, kRecordTimeCallbackName); });
//       } else {
//         cmd = this.mCamV.startRecord(`${ Host.file.storageBasePath }/${ this.mRecordP }`, kRecordTimeCallbackName);
//       }
//       cmd.then((ret) => {
//         resolve(this.mRecordP);
//         this.mRecL = DeviceEventEmitter.addListener(kRecordTimeCallbackName, this.mRecR);
//       })
//         .catch((err) => {
//           if (this.mResolTmp.value != this.mRecordingR.value) {
//             cmd = this.changeResolution(this.mResolTmp.value, false);
//           }
//           reject(err);
//         });
//     });
//   }

//   stopRecord() {
//     return new Promise((resolve, reject) => {
//       if (this.mRecL) {
//         this.mRecL.remove();
//         this.mRecL = null;
//       }
//       let path = AlbumHelper.getFileName(true);
//       let cmd;
//       if (this.mResolTmp.value != this.mRecordingR.value) {
//         cmd = this.changeResolution(this.mResolTmp.value, false);
//       }
//       if (this.mute) {
//         if (cmd != null) {
//           cmd = cmd.then(() => { return Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {}); });
//         } else {
//           cmd = Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {});
//         }
//       }

//       if (cmd != null) {
//         cmd = cmd.then(() => { return this.mCamV.stopRecord(); });
//       } else {
//         cmd = this.mCamV.stopRecord();
//       }

//       cmd.then(() => { return AlbumHelper.saveToAlbum(this.mRecordP, true); })
//         .then(() => { return AlbumHelper.justSnapshot(this.mCamV); })
//         .then(() => {
//           resolve(`${ SNAPSHOT_IMG_PATH }?timestamp=${ Date.now() }`);
//         })
//         .catch((err) => {
//           reject(err);
//         });
//     });
//   }


//   snapShot() {
//     return AlbumHelper.snapShot(this.mCamV);
//   }

//   startCall() {
//     return new Promise((resolve, reject) => {
//       Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_SPEAKER_START_REQ, {})
//         .then((retCode) => {
//           console.log(TAG, "MISS_CMD_SPEAKER_START_REQ success", retCode);
//           return Promise.race([new Promise((resolve) => { this.mCallResol = resolve; }),
//             new Promise((_, reject) => { setTimeout(() => { reject(TimeoutReject); }, 50000); })]);
//         })
//         .then(() => {
//           this.muteBeforeCall = this.mute;
//           if (this.mute) {
//             this.setAudioState(false).then(() => {
//               this.mCamV.startAudioRecord();
//               resolve();
//             });
//           } else {
//             this.mCamV.startAudioRecord();
//             resolve();
//           }

//         })
//         .catch((err) => {
//           console.log(TAG, "MISS_CMD_SPEAKER_START_REQ failed", err);
//           this.stopCall();
//           reject(err);
//         });
//     });
//   }


//   stopCall() {
//     return new Promise((resolve, reject) => {
//       Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_SPEAKER_STOP, {})
//         .then((retCode) => {
//           console.log(TAG, "MISS_CMD_SPEAKER_STOP success", retCode, "muteBeforeCall", this.muteBeforeCall, "vs", this.mute, "ret", this.mute !== this.muteBeforeCall);
//           this.mCamV.stopAudioRecord();
//           if (this.mute !== this.muteBeforeCall) {
//             this.setAudioState(this.muteBeforeCall)
//               .then(() => {
//                 resolve(this.mute);
//               })
//               .catch(() => {
//                 resolve(this.mute);
//               });
//           } else {
//             resolve(this.mute);
//           }

//         })
//         .catch((err) => {
//           console.log(TAG, "MISS_CMD_SPEAKER_STOP failed", err);
//           this.cameraGLView.stopAudioRecord();
//           resolve();
//         });
//     });
//   }

//   doStart() {
//     return new Promise((resolve, reject) => {
//       let waitP = this.connect();
//       waitP
//         .then(() => {
//           console.log(TAG, "connected send start");
//           Service.miotcamera.bindBPSReceiveCallback(kBpsDataReceiveCallbackName);
//           Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_VIDEO_START, {})
//             .then(() => {
//               console.log(TAG, "MISS_CMD_VIDEO_START sucess start render this.mResol", this.mResol.title);
//               this.updateState(PlayerState.Playing);
//               this.mCamV.startRender();
//               this.changeResolution(this.mResol.value, false);
//               resolve();
//             })
//             .catch((err) => {
//               console.log(TAG, "MISS_CMD_VIDEO_START failed start render", err);
//               reject(err);
//             });
//         })
//         .catch((aErr) => {
//           console.log(TAG, "connect  failed", this.mState, "aErr", aErr);
//           reject(aErr);
//         });
//     });
//   }

//   notify(aType, aDat) {
//     this.mCamL.forEach((aL, aIdx) => {
//       // console.log(TAG, "notify", aL.constructor.name, aType, aDat);
//       aL.onInfo(aType, aDat);
//     });
//   }

//   error(aError) {
//     this.mCamL.forEach((aL, aIdx) => {
//       aL.onError(aError);
//     });
//   }

//   start(aForce = false) { // 这里负责连接
//     this.notify(Info.Resolution, this.mResol);
//     console.log(TAG, "start force", aForce);
//     if (this.mUseData || aForce) {
//       this.mUseData = true;
//       return this.doStart();
//     } else {
//       return new Promise((aResolve, aReject) => {
//         NetInfo.fetch()
//           .then((aInfo) => {
//             let isWifi = aInfo.toString().toLowerCase() === "wifi" || (aInfo.type && aInfo.type.toLowerCase() === "wifi");
//             if (this.mUseData || isWifi) {
//               aResolve(this.doStart());
//             } else {
//               aReject(PlayerErr.NeedWifi);
//             }
//           })
//           .catch((err) => {
//             aReject(PlayerErr.NeedWifi);
//           });
//       });
//     }
//   }


//   stop() {
//     this.updateState(PlayerState.DisConnecting);
//     // bindBPSReceiveCallback null cause native crash
//     // Service.miotcamera.bindBPSReceiveCallback(null);
//     Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_VIDEO_STOP, {})
//       .then(() => {
//         console.log(TAG, "MISS_CMD_VIDEO_STOP success");
//       })
//       .catch((err) => {
//         console.log(TAG, "MISS_CMD_VIDEO_STOP failed", err);
//       });
//     this.mCamV.stopRender();
//   }


//   async connect() {
//     this.updateState(PlayerState.Connecting);
//     let wakeup = true;
//     if (this.mStatusHelper) {
//       wakeup = await this.mStatusHelper.prepareLiveVideo(() => { this.mState != PlayerState.Connecting; });
//     }
//     if (wakeup) {
//       if (!this.isConnected()) {
//         Service.miotcamera.setCurrentDeviceIsMissFirmware(true);// 每次连接的时候都更新。
//         this.mConnState = {};
//         Service.miotcamera.connectToDeviceWithStateChangeCallBack(kConnectionCallBackName);
//         let totalW = 15 * 1000;
//         let polling = async() => {
//           if (this.mState == PlayerState.DisConnecting) {
//             this.updateState(PlayerState.Disconnected);
//             throw PlayerErr.UserCancel;
//           } else {
//             switch (this.mConnState.state) {
//               case MISSConnectState.MISS_Connection_Connected:
//                 this.updateState(PlayerState.Connecting);
//                 polling = null;
//                 break;
//               case MISSConnectState.MISS_Connection_Disconnected:
//                 this.updateState(PlayerState.Disconnected);
//                 throw PlayerErr.TimeOut;
//                 break;
//               default:
//                 totalW -= 150;
//                 console.log(TAG, "current connection state", this.mConnState, totalW > 0 ? "recheck after 150" : "wait timeout", "totalW", totalW);
//                 if (totalW > 0) {
//                   await Util.sleep(150);
//                 } else {
//                   throw PlayerErr.TimeOut;
//                 }
//                 break;
//             }
//           }
//         };
//         while (polling) {
//           await polling();
//         }
//       } else {
//         this.bindP2PCommandCallback();
//       }
//     } else {
//       throw PlayerErr.WakeupFailed;
//     }
//     return;
//   }

//   isConnected() {
//     return this.mConnState.state >= MISSConnectState.MISS_Connection_Connected;// 已经连接了
//   }

//   setVoice(aVoice) {
//     Service.miotcamera.setCurrrentVoiceChangerType(MISSSampleRate.FLAG_AUDIO_SAMPLE_8K, aVoice);
//     return Promise.resolve();
//   }

//   processCmdResp({ command, data }) {
//     switch (parseInt(command)) {
//       case MISSCommand.MISS_CMD_SPEAKER_START_RESP: {
//         let ba = base64js.toByteArray(data);
//         if (ba.length > 0) {
//           console.log(TAG, 'receive MISS_CMD_SPEAKER_START_RESP mute', this.mute, ba[0]);
//           let sCode = 0;
//           if (this.isAndroid()) {
//             sCode = 48;
//           }
//           if (sCode === ba[0]) {
//             if (this.mCallResol) {
//               this.mCallResol();
//               this.mCallResol = null;
//             }
//           }
//         }
//       }
//         break;
//       default:
//         this.notify(Info.Resp, { command, data });
//         break;
//     }
//   }


//   mConnL = (aConnState) => {
//     if (aConnState.state != this.mConnState.state) {
//       console.log(TAG, 'mConnL', this.mConnState, "=>", aConnState);
//       this.mConnState = aConnState;
//       switch (this.mConnState.state) {
//         case MISSConnectState.MISS_Connection_Disconnected:
//           this.error(PlayerErr.Disconnect);
//           break;
//         case MISSConnectState.MISS_Connection_Connecting:
//           break;
//         case MISSConnectState.MISS_Connection_Connected:
//           this.bindP2PCommandCallback();
//           break;
//         case MISSConnectState.MISS_Connection_ReceivedFirstFrame:
//           this.notify(Info.FirstFrame, null);
//           break;
//       }
//     } else {
//       console.log(TAG, "state not change");
//     }
//   }

//   mP2pL = (aResp) => {
//     console.log(TAG, 'mP2pL command resp', aResp.command);
//     this.processCmdResp(aResp);
//   };


//   mBpsR = (aDat) => {
//     let curBps = this.mBps.bps;
//     let data = aDat.data;
//     if (curBps >= 1024 && (data < 1024 || Math.abs(data - curBps) > 1024)
//         || (data >= 1024 || data !== curBps)) {
//       this.mBps = aDat;
//       this.notify(Info.Bps, data);
//     }
//   };

//   mRecR = (aDat) => {
//     this.notify(Info.Rec, aDat.recordTime);
//   };
// }

// const P2PPlayer = new Player();
// export default P2PPlayer;
