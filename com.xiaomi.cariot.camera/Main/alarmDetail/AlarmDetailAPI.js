import { Service, Device } from 'miot';
import API from '../util2/API';

export default class AlarmDetailAPI {

  static myInstacne = null

  static instance() {
    if (this.myInstacne === null) {
      this.myInstacne = new AlarmDetailAPI();
    }
    return this.myInstacne;
  }

  // 获取看家视频在线url
  getVideoUrl(item) {
    return new Promise((resolve, reject) => {
      Service.miotcamera.getVideoFileUrl(item.fileId, true, "H265", Device.deviceID)
        .then((res) => {
          resolve(res);
        }).catch((err) => {
          reject(err);
        });
    });
  }

  deleteVideo(fileIDs = []) {
    let params = {
      fileIds: {
        fileIds: fileIDs
      }
    };
    return new Promise((resolve, reject) => {
      API.instance().post('/common/app/v2/delete/files', 'business.smartcamera', params)
        .then((result) => {
          resolve(result);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }
}
