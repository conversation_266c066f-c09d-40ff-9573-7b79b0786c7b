import React from 'react';
import { Host, Device, Package, Service, DarkMode } from "miot";
import { MessageDialog } from "miot/ui/Dialog";
import { TouchableOpacity, Image, Text, StyleSheet, View, SafeAreaView, Platform, StatusBar, PixelRatio, Dimensions } from 'react-native';
import { SCREEN_WIDTH, SCREEN_HEIGHT } from '../util2/Const';
import PlayerToolbar from './components/PlayerToolbar';
import OperateToolbar from './components/OperateToolbar';
import AlbumHelper from '../util/AlbumHelper';
import Toast from '../components/Toast';
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import BasePage, { BaseStyles } from "../BasePage";
import LinearGradient from 'react-native-linear-gradient';
import { Info } from "../framework/Player";
import Util, { EvArray, EvMap } from "../util2/Util";
import { DldStatus } from "../framework/EventLoaderInf";
import EventGrid from "../widget/EventGrid";
import EventSectionList from "../widget/EventSectionList";
import VipEventSectionList from "../widget/VipEventSectionList";
import ImageButton from "miot/ui/ImageButton";
import AlbumHelperCard from "../util/AlbumHelper";
import DldMgr from '../framework/DldMgr';
import InputDlgEx from '../widget/InputDlgEx';
import CloudEvLdr from '../framework/CloudEventLoader';
import StorageKeys from '../StorageKeys';
import { CldDldTypes } from "../framework/CloudEventLoader";
import CameraConfig from '../util/CameraConfig';
import TrackUtil, { AlarmPlayerCtl } from '../util/TrackUtil';
import { DescriptionConstants } from '../Constants';
import Orientation from 'react-native-orientation';
import VipUtil from '../util/VipUtil';
import LogUtil from '../util/LogUtil';
import StatusBarUtil from '../util/StatusBarUtil';
import CameraPlayer from '../util/CameraPlayer';
import Calendar from '../widget/Calendar';
import CoverLayer from '../widget/CoverLayer';
import { Event } from '../config/base/CfgConst';

// import { Event, Filters, LocalEvents } from "../stdConfig/StdMonitorConst";
import AlarmUtil from '../util/AlarmUtil';
import { ChoiceDlgWithIconSel } from "../widget/ChoiceDlgEx";
import { ChoiceDialog, AbstractDialog } from "miot/ui/Dialog";
import UriPlayer from "../framework/UriPlayer";
import dayjs from 'dayjs';
import CloudAdvertising, { PlatoTipsOn } from '../util/CloudAdvertising';

const TAG = "AlarmVideoUI";
const ABTest = {
  Types: {
    A: 'a',
    B: 'b'
  }
};

/**
 * @Author: byh
 * @Date: 2023/11/29
 * @explanation:
 * ABTest执行 ！= A操作
 *********************************************************/
export default class AlarmVideoUI extends BasePage {

  static navigationOptions = ({ navigation }) => {
    return {
      headerTransparent: true,
      header: null,
      headerShown: false,
      headerStyle: {
        height: 0
      }
    };
  };

  constructor(props) {
    super(props);

    this.isEuropeServer = CameraConfig.getIsEuropeServer();
    
    this.duration = 0;
    this.mCurTime = 0;
    this.mNewPlay = true;
    this.mNewEvent = false;//user click in vip event list
    this.mDefaultCfg = { loader: CloudEvLdr, player: UriPlayer };
    this.mDefaultLoaderArg = this.props.navigation.state?.params?.loaderArgs || { startDate: new Date(), filter: "Default", nextDate: null };
    this.mLoader = props.navigation.state.params?.cfg?.loader || this.mDefaultCfg.loader;
    this.fromFaceManager = this.props.navigation.state.params.fromFaceManager;
    let isCldLdr = this.mLoader == CloudEvLdr;
    this.mFullScreenBlackBorderWidth = this.getHorizonallyBorderWidth();
    let item = this.props.navigation?.state?.params?.item;
    let startDate = item != null ? this.mDefaultLoaderArg.startDate : new Date(); // 如果进来时已经有数据了，就不需要触发刷新逻辑。
    this.initState({
      item: props.navigation.state.params.item,
      event: props.navigation.state.params.event,
      videoSource: {
        uri: ''
      },
      isMute: CameraConfig.getUnitMute(),
      isPlaying: true,
      loadingStr: null,
      deleteVisible: false,
      errReportPermissionVisible: false,
      hiddenBars: false,
      rate: 1.0,
      showDownloadHint: false,
      isCldLdr: isCldLdr,
      fastPlay: false,
      showBuyCloudVideoTip: false,
      peopleMove: false,
      uploadingFace: false,
      startDate: startDate,
      filter: this.mDefaultLoaderArg?.filter || EvArray[0]?.key,
      showEvFilter: false,
      mDate: startDate,
      selectDateStr: this.formatDateDes(startDate),
      showCommentsDlg: false,
      mDlgTips: "", // 对话框提示语
      showBackOnly: false,
      mEmptyDes: null,
      mNoVideoEvent: null
    });
    this.commentsItem = null;
    this.mSltDate = startDate;
    LogUtil.logOnAll("AlarmVideoUI-=-=-=-=item======", props.navigation.state.params.item);
    this.mSeeking = false;
    this.mOri = "PORTRAIT";
    this.mPlayer = props.navigation.state.params?.cfg?.player || this.mDefaultCfg.player;
    this.mDldInfo = {};
    this.mEvList = null;
    this.mDelCb = props.navigation.state.params?.deleteCb;
    this.mFaceUpdateInfoCb = props.navigation.state.params.updateFaceInfoCb;
    this.mDelSet = null;
    this.mLstItm = null;
    this.mAllFigureInf = null;
    this.mDldL = DldMgr.addListener((aStatus) => {
      // console.log(this.tag, "dldL", aStatus);
      // if ("status" == aStatus.type) {
      //   Toast.show(aStatus.detail);
      // }
      this.onDldProgress(aStatus);
    });
    this.refreshFigureInfo();
    this.isVip = CameraConfig.isVip;
    this.inCloudWindow = false;
    this.isCalledForMonitoring = !(this.props.navigation.state.params.lstType == 'cloud');
    this.showSnapShot = Util.isShowSnapShop();
    this.mPxDpScale = this.state.screenSize.scale || 1.83;
    this.mLastInactiveTime = 0;
    this.mIsInternationalServer = CameraConfig.getInternationalServerStatus();
    this.mIsSupportCloudCountry = CameraConfig.isSupportCloud();
    this.mStartupPushFirstStartPlayerDone = false;
    this.mItemForFaceComment = null;
    this.isInCloudWindow = false;
    this.ref_tip = this.props.navigation.state.params?.srcTip;
    this.rollingInterval = 8 * 24 * 60 * 60 * 1000; // default 8 days for nov vip and 7 days vip

    this.mSupportDailyStory = CameraConfig.supportNonVipDailyStory(Device.model) || CameraConfig.supportVipDailyStory(Device.model);
    if (this.mSupportDailyStory) {
      this._getSetting();
    }
    
    // 推送冷启动中从首页进入的可以按ab测试类型直接进入，从消息中心和通知中心进入的只能进入老UI，type a
    let showOriginalUI = (this.ref_tip == null) && (this.props.navigation.state.params.lstType == "push") && (this.props.navigation.state.params.pushType == "startup");
    this.mABType = 'b';
    if (showOriginalUI || this.props.navigation.state.params.lstType == "cloud" || (this.isVip && this.props.navigation.state.params.lstType == "push" && this.props.navigation.state.params.pushType == "inner")) {
      this.mABType = 'a'; // 对推送保持线上逻辑不变，只显示当前事件所在文件的所有事件
    }

    this._initEvFilter();
    this.refreshVipStatus(true);


    StorageKeys.VIP_DETAIL.then((res) => {
      if (typeof (res["vip"]) === "string") {
        this.isVip = false;
      } else {
        this.isVip = res["vip"];
      }
      PlatoTipsOn() && this._getCloudTips();
    });
    
    // 这里换成刷新servername  缓存的有可能压根就没读取
    Service.getServerName()
      .then((serverBean) => {
        let countryCode = serverBean.countryCode;
        let serverCode = serverBean.serverCode;
        CameraConfig.updateCloudSupportCountry(serverCode.toUpperCase());
        this.isEuropeServer = CameraConfig.getIsEuropeServer();
        this.mIsSupportCloudCountry = CameraConfig.isSupportCloud();
        this.mIsInternationalServer = countryCode.toLowerCase() != "cn";
        CameraConfig.setIsInternationalServer(this.mIsInternationalServer);

        if (countryCode.toLowerCase() == "in") {
          CameraConfig.setIsIndiaServer(true);
        } else {
          CameraConfig.setIsIndiaServer(false);
        }
      });

    if (Host.isAndroid) {
      Host.getPhoneScreenInfo()
        .then((result) => {
          this.displayCutoutTop = PixelRatio.roundToNearestPixel(result.displayCutoutTop / PixelRatio.get() || 0);
          if (isNaN(this.displayCutoutTop)) {
            this.displayCutoutTop = 0;
          }
        })
        .catch((error) => {

        });
    } else {
      this.displayCutoutTop = StatusBarUtil._getInset("top");
    }

    CameraPlayer.getInstance(); // 直接进推送播放，需要这里注册inner推送监听。
  }

  refreshVipStatus(force = false) {
    if (force || CameraConfig.isToUpdateVipStatue) {
      VipUtil.getVipStatus().then(({ isVip, inCloseWindow, data, vipBindStatus }) => {
        let lastVipState = this.isVip;
        this.isVip = isVip;
        this.isInCloudWindow = inCloseWindow;
        let isAlarmVideo = this.state?.item?.isAlarm;
        this.freeHomSurStatus = CameraConfig.vipDetail?.freeHomSurStatus; // CameraConfig.vipDetail 是最新拉取的值
        this.freeHomeSurExpireTime = CameraConfig.vipDetail?.freeHomeSurExpireTime;
        if (!PlatoTipsOn()) {
          isAlarmVideo = isAlarmVideo == null ? false : isAlarmVideo;
          if (this.props.navigation.state.params.lstType != "cloud") {
            isAlarmVideo = true;
          }
          if (!this.isVip && this.mIsSupportCloudCountry && isAlarmVideo) { // 非国际服的才可以提示购买 非云存视频才可以购买。
            let showTips = true;
            if (this.mIsInternationalServer && inCloseWindow && vipBindStatus === false) {
              showTips = false; // 海外 过期窗口期中，bindStatus为false 不展示。
            }
            this.setState({ showBuyCloudVideoTip: showTips });
          }
        } else {
          this._getCloudTips();
        }

        let days30 = 30 * 24 * 60 * 60 * 1000;
        this.rollingInterval = data?.rollingSaveInterval ? (data?.rollingSaveInterval > days30 ? data?.rollingSaveInterval : data?.rollingSaveInterval + 1 * 24 * 60 * 60 * 1000) : 8 * 24 * 60 * 60 * 1000;
        // if (isVip != lastVipState) 
        // {
        //   this.mEvFilter = EvArray.filter((aVal) => {
        //
        //     if (aVal.key == Event.CALL) {
        //       return true;
        //     }
        //
        //     // 本地人脸识别的尚未添加  还有宠物的也没有添加
        //     // 某些属性需要判断是否是vip，如果该属性下，不是vip，再看是否是设备支持的本地能力
        //     if (this.isInternationalServer && this.mSupportCloudCountry && aVal.key == Event.KnownFace) { // 如果即是海外服务器 又支持云存  为海外云存，去掉人脸相关
        //       return false;
        //     }
        //     // 停止免费看家需求，因为这些设备（009/019/v3）的有人移动事件是在服务端生成的，没有看家视频后就无法获得了，所以要屏蔽掉。
        //     if (aVal.key == Event.PeopleMotion && !isVip && data?.closeWindow && CameraConfig.isPostNoFreeSVL()) {
        //       return false;
        //     }
        //
        //     let vipSupport = aVal.needVip ? isVip : true;
        //     if (aVal.key == Event.BabyCry) {
        //       if (this.isInternationalServer && !LocalEvents().includes(Event.BabyCry)) { // 海外只支持本地哭声，不支持vip哭声，特殊case后面特殊处理
        //         vipSupport = false;
        //       }
        //       // 宝宝哭声有特别的行为。 如果是特别的设备，特别的场景，就认为具备本地宝宝哭声能力
        //       let sBabyCry = CameraConfig.babyCrySpecialModel(Device.model);
        //       if (sBabyCry != -1) {
        //         vipSupport = sBabyCry;
        //       }
        //       if (this.mSupportNonVipBabyCry) { // 本地设备行为需要额外做处理。
        //         vipSupport = true;
        //       }
        //     }
        //     if (aVal.key == Event.AI) {
        //       return true;
        //     }
        //     return vipSupport;
        //   });
        //   //let dateItems = this.genDateItem(isVip, saveDays); // 计算日历显示的日期
        // }
      }).catch((err) => {
        console.log("get vip err", err);
      }).finally(() => {
        if (this.props.navigation.state.params.lstType == "push" && this.props.navigation.state.params.pushType == "startup" && force && this.state.item) { // force not enter
          this.mStartupPushFirstStartPlayerDone = true;
          this.startPlayer(this.state.item);
        }
      });
    }
  }

  _initEvFilter() {
    this.mEvFilter = EvArray.filter((item) => {

      if (item.key == Event.CameraCalling && !CameraConfig.displayCameraCallingTimeline(Device.model)) return null;

      if (item.key == Event.Pet && !CameraConfig.displayPetInTimeline(Device.model)) return null;
      if (item.key == Event.BabyCry && !CameraConfig.displayBabyCryInTimeline(Device.model)) return null;

      if (item.key == Event.KnownFace && !this.isVip) {
        return null;
      }
      // if (item.key !== Event.Default) {
      //   this.storedEvSelMap[item.key] = true;
      //   this.EvSelMap[item.key] = true;
      // }

      if (item.key == this.state.evFilterKey) {
        item.selected = true;
      } else {
        item.selected = false;
      }
      item.name = EvMap[item.key]?.des;
      return item;
    });
  }

  getHorizonallyBorderWidth() {
    let w = this.state.screenSize.width;
    let h = this.state.screenSize.height;
    let scl = w / h;
    let result = 0;
    if (scl > 16 / 9) {
      result = (w - h * 16 / 9) / 2;
    }
    console.log(this.tag, 'horizonally border: ', result, this.state.screenSize, scl);
    return result;
  }

  refreshFigureInfo(aForce = false) {
    if (this.state.isCldLdr && (aForce || null == this.mAllFigureInf)) {
      Util.getAllFigure().then((ret) => {
        this.mAllFigureInf = ret;
      })
        .catch((aErr) => {
          console.log(this.tag, "getAllFigure failed", aErr);
        });
    }
  }

  setNavigation(isFullscreen) {
    this.props.navigation.setParams({
      show: true
    });
  }

  onResume() {
    this.statCloudTipsExposeDone = false;
    this.statButtonsExposeDone = false; // 播放器按钮和事件过滤器按钮曝光
    this.statEvFilterDialogExopseDone = false; // 事件过滤器和事件弹窗曝光
    this.statFixBarButtonsExposeDone = false; // 事件过滤器、日历入口曝光

    super.onResume();
    DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');

    let interval = new Date().getTime() - this.mLastInactiveTime;
    if (this.mPlayer.getUrlSrc() && ((Platform.OS == 'android' && this.props.navigation.state.params.pushType != "inner") || (Platform.OS == 'ios' && interval < (1000 * 90)))) {
      // 已经在播放中，间隔时间也足够短，继续播放
      setTimeout(() => {
        if (this.state.isPlaying) {
          this.mPlayer.resume();
        } else {
          this.mPlayer.pause();
        }
      }, 500);
      
    } else {
      // 如果有播放item，而且不是推送冷启动的第一次，或者是其他带item的推送或者看家列表页面点击事件进入
      // 冷启动推送在vip状态拉取之后才进入播放状态。
      console.log("==========>show loading");
      this.setState({ loadingStr: LocalizedStrings['camera_loading'] });
      if ((this.props.navigation.state.params.pushType != "startup" || this.mStartupPushFirstStartPlayerDone) && this.state.item) {
        this.startPlayer(this.state.item);
        TrackUtil.newOneTrack(['play'], { ...AlarmPlayerCtl.bPlay, play_result: 'prepare' });
        TrackUtil.newOneTrack(['play'], { ...AlarmPlayerCtl.bPlay, play_result: 'playing' });
      }
    }

    if (this.mPlayer) {
      let mmUnitMute = CameraConfig.getUnitMute();
      if (this.state.fastPlay) {
        mmUnitMute = true;
      }
      this.mPlayer.setMute(mmUnitMute);
      if (this.playToolbar) {
        this.playToolbar.setMute(mmUnitMute);
        this.playToolbar.updateOrientation(this.state.fullScreen);
      }
    }
    this.hidePlayToolBar(true);
    this.enterPlayTimer = new Date().getTime();
    this.refreshVipStatus(false);
  }

  onPause() {
    super.onPause();
    this.mLastInactiveTime = new Date().getTime();
    // this.mPlayer.setMute(true);
    this.mPlayer && this.mPlayer.pause();
    if (this.mDelSet != null && this.mDelCb != null) {
      this.mDelCb(this.mDelSet);
    }
    let playbackTime = (new Date().getTime() - this.enterPlayTimer) / 1000;
    let key = this.isCalledForMonitoring ? 'Camera_Monitoring_Time' : 'Camera_CloudStorage_Time';
    TrackUtil.reportResultEvent(key, "Time", playbackTime); // Camera_Playback_Time
  }

  componentDidMount() {
    console.log(TAG,"componentDidMount")
    this.setNavigation(false);
    super.componentDidMount();
    this.mCamL = this.mPlayer.addCameraListener(this);
    this.mPlayer && this.mPlayer.setRate(1.0);
    let winHeight = Dimensions.get('window').height;
    console.log(TAG, 'componentDidMount winHeight', winHeight, Dimensions.get('screen'));
    if (this.props.navigation?.state?.params?.items != null) { // 数据不为空，没有必要刷新数据
      return;
    }
    this.mEvList.switchDay(this.mDefaultLoaderArg?.startDate);// 触发数据刷新，更新内部的startDay.  eventlist里保存的startTime修改了
    setTimeout(() => { // 刷新数据，调用原来切换日期的逻辑
      this.setState({ startDate: this.mDefaultLoaderArg?.startDate });
    });
  }


  componentWillUnmount() {
    console.log(TAG,"componentWillUnmount")
    super.componentWillUnmount();
    if (this.mPlayer) {
      this.mPlayer.stop();
    }
    if (this.isShareDownloading) {
      this.mLoader.cancelDownload(this.state.item, null);
    }
    this._clearTimeout(this.mCorrectAndroidPadSizeTimer);
    this._clearTimeout(this.iosCanntPlayTimer);
    this.mCamL.remove();
    this.mDldL.remove();
    this.toPortrait();
    this.setState = () => false;
  }

  // use handle P2PAlarmPlayer event
  onInfo(aType, aDat) {
    // console.log(this.tag, "type", aType, "aDat", aDat);
    let isAlarmVideo = false;
    switch (aType) {
      case Info.Load:
        this.onLoad(aDat);
        break;
      case Info.Progress:
        this.onProgress(aDat);
        break;
      case Info.End:
        this.onEnd();
        break;

      case Info.ViewUpdate:
        this.setState({});
        break;
      case Info.SeekComplete:
        this.onSeek(aDat);
        break;
      case Info.PlayState:
        this.onPlayStateChange(aDat);
        break;
      default:
        break;

    }
  }


  onDldProgress(aProgress,  taskBusy = false) {
    console.log(this.tag, "onDldProgress", aProgress, "for", this.mDldInfo["path"], "name", this.mDldInfo["name"]);
    if (this.mDldInfo["path"]) {
      switch (aProgress) {
        case DldStatus.Complete:
          if (this.mDldInfo["share"]) {
            this.isShareDownloading = false;
            this.playToolbar &&this.playToolbar.disableFullscreenButton(false);
            if (this.mActive) {
              if (Platform.OS == 'android' && this.state.fullScreen) {
                this.toPortrait();
                this.playToolbar && this.playToolbar.updateOrientation(false);
                setTimeout(() => {
                  Host.ui.openSystemShareWindow(this.mDldInfo["path"]);
                  TrackUtil.newOneTrack(['share'], { ...AlarmPlayerCtl.bShare, share_type: 'video', share_result: '开始分享' });
                  this.mDldInfo = {};
                }, 5);
              } else {
                Host.ui.openSystemShareWindow(this.mDldInfo["path"]);
                TrackUtil.newOneTrack(['share'], { ...AlarmPlayerCtl.bShare, share_type: 'video', share_result: '开始分享' });
                this.mDldInfo = {};
              }
            }
          } else {
            AlbumHelper.saveToAlbum(this.mDldInfo["name"], true)
              .then((result) => {
                Toast.success('save_success');
              })
              .catch((err) => {
                Toast.success('save_faild');
                console.log(err);
              });
            this.mDldInfo = {};
          }
          break;
        case DldStatus.Err:
          this.mDldInfo = {};
          if (taskBusy) {
            Toast.fail('task_busy_retry_later');
          } else {
            Toast.fail('camera_play_error_file');
          }
          this.isShareDownloading = false;
          this.playToolbar && this.playToolbar.disableFullscreenButton(false);
          break;
        default:
          if (typeof (aProgress) == 'object' && aProgress.detail == 100) {
            this.isShareDownloading = false;
            this.playToolbar && this.playToolbar.disableFullscreenButton(false);
          }
          break;
      }
    }
  }

  onDldCancelResult(fileId, state) {
    console.log('sharedownload is canceled', fileId, state);
  }

  startPlayer(aItm, autoStart = false) {
    if (!aItm) return;
    if (!this.statButtonsExposeDone && this.statButtonsExposeDone != undefined) {
      // 播放器按钮曝光打点
      this.statButtonsExposeDone = true;
      let en = ['expose'];
      TrackUtil.newOneTrack(en, AlarmPlayerCtl.bPlay);
      !this.isVip && TrackUtil.newOneTrack(en, AlarmPlayerCtl.bShare);
      TrackUtil.newOneTrack(en, AlarmPlayerCtl.bDownload);
      TrackUtil.newOneTrack(en, AlarmPlayerCtl.bDelete);
      TrackUtil.newOneTrack(en, AlarmPlayerCtl.bAudio);
      TrackUtil.newOneTrack(en, AlarmPlayerCtl.bFullScr);
    }
    let logstr = `StartPlayer: item fileid ${ aItm.fileId }, createTime: ${ aItm?.createTime }, time: ${ aItm?.eventTime }, offset: ${ aItm?.offset }`;
    Service.smarthome.reportLog(Device.model, logstr);
    this.setState({ showBackOnly: false, mEmptyDes: null, mNoVideoEvent: null });
    this.mLoader.eventRead(aItm);
    let mLstType = this.listType();
    LogUtil.logOnAll(`AlarmVideoUI, startPlayer, item: ${ JSON.stringify({ fileId: aItm?.fileId, offset: aItm?.offset }) }`);
    if (mLstType === "list_vip") {
      this.nextEv = null;
      if (this.mLstItm != aItm) {
        this.mCurTime = aItm.offset * 3 + 0.1;
        this.mLstItm = aItm;
        this.mNewPlay = true;
      } else if (this.mLstItm.fileId == aItm.fileId && this.mLstItm.offset != aItm.offset) {
        this.mPlayer.seek(aItm.offset * 3 + 0.1);
        return;
      }
    } else {
      if (this.mLstItm != aItm) {
        let offset = aItm?.offset != undefined ? aItm.offset : 0;
        this.mCurTime = offset * 3 + 0.1;
        this.mLstItm = aItm;
        this.mNewPlay = true;
      }
    }


    if (this.playToolbar != null) {
      this.playToolbar.updateCurTime(Math.max(this.mCurTime, 0));
    }
    console.log("startplay, isVip? ", this.isVip);

    this.mPlayer.start(aItm, false)
      .then(() => {
        if (autoStart) {
          this.updatePlayState(true);
        } else {
          this.updatePlayState(this.state.isPlaying);
        }

      })
      .catch((aErr) => {
        console.log(this.tag, "play catch error", aErr);
      });
    this.getFaceUrl(aItm);
  }

  getFaceUrl(aItm) {
    if (aItm?.faceInfo != null) {
      if (aItm.faceInfo.faceUrl != null) {
        this.setState({ faceUrl: aItm.faceInfo.faceUrl });
      } else {
        Util.getFaceImgUrl(aItm.faceInfo.faceId)
          .then((aRet) => {
            console.log(this.tag, "getFaceImgUrl", aRet);
            aItm.faceInfo.faceUrl = aRet;
            this.setState({ faceUrl: aRet });
          })
          .catch((aErr) => {

            console.log(this.tag, "getFaceImgUrl aErr", aErr);
          });
      }
    }
  }

  faceComment(item, aType) {
    if (aType == Event.KnownFace) {
      console.log('click修改备注');
    } else {
      console.log('click 添加备注');
    }
    this.updatePlayItem(item, aType);
    this.changed = false;
    this.mItemForFaceComment = item;
    if (aType == Event.KnownFace) {
      this.setState({ commentDlg: true, defVal: item.faceInfo.name ? item.faceInfo.name : null, commentErr: null });
    } else {
      this.setState({ commentDlg: true, commentErr: null });
    }
    TrackUtil.reportClickEvent('Monitoring_mark_ClickNum');
  }

  updatePlayItem(item) {
    let showImg = item?.isShowImg != null ? item.isShowImg : true;
    if (!showImg) { // 无视频事件
      if (this.mPlayer) {
        this.mPlayer.stop();
      }
      this.setState({item: null, mEmptyDes: null, mNoVideoEvent: LocalizedStrings['kanjia_novideo_tips'], loadingStr: null, showBackOnly: true });
      Toast.fail('camera.alarm.cloud.tip.fullvideo.not.vip');
      return;
    } 
    let isSameFile = false;
    if (this.state?.item?.fileId == item.fileId) {
      isSameFile = true;
    }
    this.setState({ item: item, showBackOnly: false, mNoVideoEvent: null, mEmptyDes: null }, () => {
      if (this.listType() == "list_vip" && this.state.isPlaying && isSameFile) {
        this.mNewEvent = true;
        this.mPlayer.seek(item.offset * 3 + 0.1);
        this.getFaceUrl(item);
      } else {
        this.setState({
          loadingStr: LocalizedStrings['camera_loading'],
        });
        TrackUtil.newOneTrack(['play'], { ...AlarmPlayerCtl.bPlay, play_result: 'prepare', control_result: 'CONTROL_START' });
        TrackUtil.newOneTrack(['play'], { ...AlarmPlayerCtl.bPlay, play_result: 'playing' });
        this.startPlayer(item, true);
      }
    });
    this.mLoader.eventRead(item);
    this.isCalledForMonitoring ? TrackUtil.reportClickEvent('Monitoring_Video_ClickNum') : TrackUtil.reportClickEvent('CloudStorage_VideoPlay_ClickNum');
  }

  updatePlayState(aPlaying) {
    console.log(this.tag, "updatePlayState", aPlaying);
    this.setState({ isPlaying: aPlaying });
    if (aPlaying) {
      this.mPlayer.resume();
    } else {
      this.mPlayer.pause();
    }
    if (this.playToolbar) {
      this.playToolbar.updatePlayState(aPlaying);
    }
  }

  doDld(aForShare) {
    this.checkStoragePerm()
      .then(() => {
        console.log("doDld",aForShare,this.isShareDownloading);
        if (aForShare && this.isShareDownloading) {
          Toast.loading('c_setting');
          return;
        }
        let basePath = Host.file.storageBasePath;
        let name = AlbumHelper.getDownloadTargetPathName(true);
        let path = `${basePath}/${name}`;
        this.mDldInfo["name"] = name;
        this.mDldInfo["path"] = path;
        this.mDldInfo["share"] = aForShare;
        if (aForShare) {
          Toast.loading('c_setting');
          // Stat.reportEvent(StatEV.SHARE, null);
          console.log("======================doDld",aForShare,this.isShareDownloading);
          this.isShareDownloading = true;
          this.mLoader.download(this.state.item, path, this);

          this.playToolbar && this.playToolbar.disableFullscreenButton(true);
          console.log('disableFullscreenButton1',this.playToolbar.disableFullscreenButton);
          
          this.isCalledForMonitoring && TrackUtil.reportClickEvent('Monitoring_Share_ClickNum');
          TrackUtil.newOneTrack(['click'], AlarmPlayerCtl.bShare);
        } else {
          // Toast.loading('c_download');
          // Stat.reportEvent(StatEV.DOWNLOAD, null);
          this.setState({ showDownloadHint: true });
          setTimeout(() => {
            this.setState({ showDownloadHint: false });
          }, 5000);
          this.addInfoForPush();
          DldMgr.addDld([this.state.item], this.mLoader);
          this.isCalledForMonitoring ? TrackUtil.reportClickEvent('Monitoring_Save_ClickNum') : TrackUtil.reportClickEvent('Storage_CloudStorage_Download');
          TrackUtil.newOneTrack(['click'], AlarmPlayerCtl.bDownload);
        }
      })
      .catch((err) => {
        console.log(this.tag, err);
        Toast.success(err);
      });
  }

  addInfoForPush() {
    if (this.props.navigation.state.params && this.props.navigation.state.params.lstType == 'push') {
      let items = this.mEvList.getEvents()[0].data;
      for (let key in items) {
        if (items[key].fileId == this.state?.item?.fileId && items[key].offset == this.state.item.offset) {
          this.state.item = items[key];
          break;
        }
      }
    }
  }

  doBack() {
    if (this.state.fullScreen) {
      this.toPortrait();
      this.setNavigation(false);
    } else {
      this.pushBack();
      this.naviBack();
    }
  }
  doSnapShot() {
    this.checkStoragePerm()
      .then(() => {
        if (this.mPlayer.getVideo()) {
          clearTimeout(this.snapshotTimeout);
          this.setState({ screenshotVisiblity: false, screenshotPath: null });
          AlbumHelperCard.reactNativeSnapShot(this.mPlayer.getVideo())
            .then((path) => {
              console.log("landing14");
              console.log("landing snapshot ", path);
              this.setState({ screenshotVisiblity: true, screenshotPath: path });// show snapshotview
              clearTimeout(this.snapshotTimeout); 
              this.snapshotTimeout = setTimeout(() => {
                this.setState({ screenshotVisiblity: false, screenshotPath: null });
              }, 3000);
            })
            .catch((error) => {
              console.log("landing15");
              console.log(JSON.stringify(error));
              Toast.success("action_failed");
            });
        } else {
          console.log("landing16");
          Toast.success("action_failed");
        }

      })
      .catch((err) => {
        console.log(this.tag, err);
        Toast.success(err);
      });
  }

  onProgress = (data) => {
    if (!this.mNewPlay && !this.mNewEvent && data.currentTime > this.mCurTime) {
      if (this.listType() == "list_vip") {
        let event = this.mEvList.getAdjacentEventForMarkListitem(this.state.item);
        if (event !== this.nextEv) {
          this.nextEv && console.log("next event: ", this.nextEv.offset);
          this.nextEv = event;
        }

        if (this.nextEv && data.currentTime > (this.nextEv.offset * 3 + 0.1)) {
          this.mLstItm = this.state.item;
          this.setState({ item: this.nextEv });
          this.getFaceUrl(this.nextEv);
        }
      }

      this.mCurTime = data.currentTime;
      if (this.playToolbar && !this.mSeeking) {
        if (data.hasOwnProperty("duration")) {
          this.duration = Math.round(data["duration"]);
        }
        this.playToolbar.updateCurTime(this.mCurTime, this.duration);
        this.setState({});
      }
    } else {
      console.log(this.tag, "play progress exp");
    }

  }

  onEnd = () => {
    this.mCurTime = 0;
    if (this.playToolbar) {
      this.playToolbar.updateCurTime(-1);
    }
    this.updatePlayState(false);
    // console.log(TAG, "onEnd");
    // use timeout to prevent ios repeat play
    setTimeout(() => {
      this.mPlayer.seek(0);
      TrackUtil.newOneTrack(['play'], { ...AlarmPlayerCtl.bPlay, play_result: 'stop' });
    }, 10);
  }

  onLoad = (info) => {
    if (this.mNewPlay) {
      this.mNewPlay = false;
    }
    this.duration = Math.round(info.duration);
    if (this.playToolbar) {
      this.playToolbar.updateDuration(this.duration);
      this.playToolbar.updateCurTime(this.mCurTime);
    }
    if (info?.currentTime != this.mCurTime) {
      this.mPlayer.seek(this.mCurTime);
    }
    this.setState({ loadingStr: null });

  }

  onError = (err) => {
    console.log(this.tag, "onerror", err);
    this.setState({ loadingStr: LocalizedStrings[err] });
  }

  onBuffer = () => {

  }

  onPress = (mShowBackOnly = false) => {
    this.setState({ hiddenBars: !this.state.hiddenBars, showBackOnly: mShowBackOnly });
    this.hidePlayToolBar(this.state.hiddenBars);

  }

  hidePlayToolBar(flag) {
    if (flag) {
      if (this.mPlayToolBarsTimer) {
        clearTimeout(this.mPlayToolBarsTimer);
      }
      this.mPlayToolBarsTimer = setTimeout(() => {
        this.setState({ hiddenBars: true });
      }, 5000);
    }
  }

  onPlay(aPlaying) {
    this.mShowBuyCloudTip = false;
    this.updatePlayState(aPlaying);
    this.hidePlayToolBar(true);
    TrackUtil.newOneTrack(['click'], AlarmPlayerCtl.bPlay);
    TrackUtil.newOneTrack(['control'], { ...AlarmPlayerCtl.bPlay, control_result: 'CONTROL_START' });
    TrackUtil.newOneTrack(['play'], { ...AlarmPlayerCtl.bPlay, play_result: aPlaying ? 'playing' : 'pause' });
  }

  onSeek = (aData) => {
    this.mSeeking = false;
    this.mCurTime = aData.currentTime;
    if (this.listType() == "list_vip" && !this.mNewEvent) {
      let item = this.mEvList.getCurrentEventForMarkListitemByTime(this.state.item, this.mCurTime);
      if (item) {
        this.mLstItm = this.state.item;
        this.setState({ item: item });
        this.getFaceUrl(item);
      }
    }
    this.mNewEvent = false;
    this.updatePlayState(this.state.isPlaying);
  }

  onPlayStateChange = (playing) => {
    if (this.playToolbar) {
      this.playToolbar.setPlay(playing);
    }
  }

  onSeeked = (aPos) => {
    let target = this.duration * aPos;
    console.log(this.tag, "seek to ", aPos, "=>", target);
    this.mSeeking = true;
    this.mPlayer.seek(target);
  }

  onSlidingStart = () => {
    this.hidePlayToolBar(true);
  }

  mutePressed = (isMute) => {
    this.mPlayer.setMute(isMute);
    this.setState({
      isMute: isMute
    });
    CameraConfig.setUnitMute(isMute);
    if (!isMute) {
      this.isCalledForMonitoring ? TrackUtil.reportClickEvent('Monitoring_OpenVolume_ClickNum') : TrackUtil.reportClickEvent('CloudStorage_OpenVolume_ClickNum');
    }
    this.hidePlayToolBar(true);
    TrackUtil.newOneTrack(['click', 'control'], { ...AlarmPlayerCtl.bAudio, control_result: 'CONTROL_START' });
  }

  fastPlayProessed = (speed) => {
    let r = speed ? 2.0 : 1.0;
    let mute = speed;
    if (mute) {
      this.mPlayer.setMute(mute);
      this.playToolbar.setMute(mute);
    } else {
      this.mPlayer.setMute(CameraConfig.getUnitMute());
      this.playToolbar.setMute(CameraConfig.getUnitMute());
    }
    this.mPlayer.setRate(r);
    this.setState({ rate: r, fastPlay: speed, isMute: speed ? true : CameraConfig.getUnitMute() });
    this.hidePlayToolBar(true);
  }

  fullscreenPressed = (isFullscreen) => {
    LogUtil.logOnAll("AlarmVideoUI", "onPress fullscreen button , try change to fullscreen:" + isFullscreen);
    if (isFullscreen) {
      this.mPxDpScale = this.state.screenSize.scale || 1.83;
      this.toLandscape();
      TrackUtil.reportClickEvent('Monitoring_FullScreen_ClickNum');
    } else {
      this.toPortrait();
    }
    this.setNavigation(isFullscreen);
    TrackUtil.newOneTrack(['click', 'control'], { ...AlarmPlayerCtl.bFullScr, control_result: isFullscreen ? 'Full' : 'Nomal' });
  }

  listType() {
    let mLstType = this.props.navigation.state.params.lstType;
    // console.log("========>error: ", this.mABType, this.props.navigation.state.params.item);
    if (this.mABType !== ABTest.Types.A) { // 合并看家列表和播放器后都是用list_vip
      mLstType = "list_vip";
    } else {
      if (mLstType == 'push') {
        mLstType = (!this.props.navigation.state.params.item?.isAlarm ? "list_vip" : "list");
      }
    }
    return mLstType;
  }

  deletePressed() {
    let item = this.state.item;
    // Stat.reportEvent(StatEV.DELETE, null);
    TrackUtil.reportClickEvent('Monitoring_Delete_ClickNum');
    this.mLoader.delete([item])
      .then((aRet) => {
        try {
          if (!this.mDelSet) {
            this.mDelSet = new Set();
          }
          this.mDelSet.add(item.fileId);
          console.log(this.tag, "delete  complete", aRet);
          let newPlay = this.mEvList.getAdjacentEvent(item);
          if (this.listType() == 'list_vip' && this.mABType == ABTest.Types.A) {
            newPlay = null; // 删除了文件，所有事件都没有了
            // this.toPortrait();
          }
          Toast.show("delete_success");

          this.mEvList.removeEvents((aItm) => { return aItm.fileId != item.fileId; });
          if (newPlay != null && newPlay.fileId != item.fileId && newPlay?.offset != '-888') { // 如果选到的是banner（offset = ‘-888’），说明已经删除完了，退出
            this.setState({
              item: newPlay
            });
            this.startPlayer(newPlay);
            let logstr = `deleted item fileid ${ item.fileId }, newPlay fileid ${ newPlay.fileId }`;
            Service.smarthome.reportLog(Device.model, logstr);
          } else {
            this.toPortrait();
            this.pushBack();
            this.props.navigation.goBack();
          }
        } catch (err) {
          LogUtil.logOnAll("AlarmVideoUI delete file err=", err);
          Toast.show("delete_success");
          let newPlay = this.mEvList.getAdjacentEvent(item);
          if (this.listType() == 'list_vip'  && this.mABType == ABTest.Types.A) {
            newPlay = null; // 删除了文件，所有事件都没有了
            this.toPortrait();
          }
        }
      })
      .catch((aErr) => {
        console.log(this.tag, "delete", aErr);
        Toast.fail("delete_failed");
      });
  }

  naviBack() {
    this.mPauseL();
    this.mRestorePreOrientation();
    if (this.props.navigation.state.params.shouldPopTopWhenBack) {
      this.props.navigation.popToTop(); // 弹出到栈顶。
    } else if (this.props.navigation.state.params?.pushType == "startup") {
      Package.exit();
      this.onPause();
      return true;
    } else {
      this.props.navigation.goBack();
    }
  }

  onBack() {
    if (this.props.navigation.state.params.shouldPopTopWhenBack) {
      this.naviBack();
      return true; // 弹出到栈顶。
    } 
    let ret = false;
    if (this.state.fullScreen) {
      this.fullscreenPressed(false);
      this.playToolbar && this.playToolbar.updateOrientation(false);
      ret = true;
    }
    return ret;
  }

  pushBack() {
    if (this.props.navigation.state.params && this.props.navigation.state.params.lstType == 'push' && this.props.navigation.state.params.pushType == 'startup') {
      this.onPause();
      Package.exit();
    }
  }

  renderErrorButton(info) {
    if (!this.mIsInternationalServer) {
      return (
        <View style={{ flex: 1, marginLeft: 10, justifyContent: "flex-end", backgroundColor: "#00000000", flexDirection: "row" }}>
          <TouchableOpacity style={{ backgroundColor: "#F2F2F2", borderRadius: 17, minHeight: 34, padding: 10, }}
            onPress={() => {
              this.mReportType = 'face';
              if (info === 'faceInfo' ) {
                this.mReportType = 'face';
              } else if (info === 'peopleMotion' ) {
                this.mReportType = 'PeopleMotion';
              } else if ( info === 'Pet' ) {
                this.mReportType = 'Pet';
              }
              console.log('renderErrorButton', this.mReportType)
              this.setState({ errReportPermissionVisible: true });
            }}
          >
            <Text style={[BaseStyles.text12]}>{LocalizedStrings["cloud_face_rec_error"]}</Text>
          </TouchableOpacity>
        </View>);
    } else {
      return  null;
    }
  }
  render() {
    let faceInfo = this.state?.item?.faceInfo;
    // let faceInfo = this.fromFaceManager ? this.props.navigation.state.params.item.faceInfo : this.state?.item?.faceInfo;
    // console.log("this.state.item", this.state.item);
    // console.log("this.props.navigation.state.params.item",this.props.navigation.state.params.item)
    let peopleMotion = this.state?.item?.type && this.state?.item?.type.indexOf('PeopleMotion') != -1;
    let Pet = this.state?.item?.type && this.state?.item?.type.indexOf('Pet') != -1;
    // console.log("peopleMotion",peopleMotion,"Pet",Pet);
    let mPlayerToolBarPaddingLeft = (Platform.OS == 'android' && this.state.fullScreen) ? 20 : 0; // Host.displayCutoutTop 不准确

    let mCtnStl = this.state.fullScreen ? styles.fullScreenContainer : styles.container;
    let mHeaderHeigth = this.state.fullScreen || Platform.OS == 'android' || this.mABType == ABTest.Types.A ? 0 : StatusBarUtil._getInset("top");

    if (!this.statFixBarButtonsExposeDone && this.statFixBarButtonsExposeDone != undefined  && this.mABType == ABTest.Types.B) {
      // 日历过滤器等曝光
      this.statFixBarButtonsExposeDone = true;
      let en = ['expose'];
      TrackUtil.newOneTrack(en, AlarmPlayerCtl.bFilter);
      let date = dayjs(this.mSltDate).format("YYYY-MM-DD");
      TrackUtil.newOneTrack(en, { 'item_type': 'button', 'item_name': date });
    }
    return (
      <View style={[BaseStyles.pageRoot, { flexDirection: 'column', paddingTop: this.state.fullScreen ? 0 : StatusBar.currentHeight, backgroundColor: this.state.fullScreen ? "black" : "white" }]}>
        {this.mABType !== ABTest.Types.A ? this._renderTitleView() : null}
        {this.mABType == ABTest.Types.A ? this.renderHeader() : null}
        {this.renderContent(mCtnStl, mHeaderHeigth, faceInfo, peopleMotion, Pet, mPlayerToolBarPaddingLeft)}
      </View>
    );
  }

  renderContent(mCtnStl, mHeaderHeigth, faceInfo, peopleMotion, Pet, mPlayerToolBarPaddingLeft) {
    let mLstType = this.listType();
     
    let offset = (this.mEvList && this.mEvList.constructor.name == "VipEventSectionList" && mLstType == "list_vip") ? this.mEvList.getPlayingOffset(this.state.item) 
      : (this.state.item?.offset != undefined ? this.state.item.offset : -1);
    let mPId = offset != -1 ? `${this.state?.item?.fileId}_${offset}` : `${this.state?.item?.fileId}`; // grid and list use fileId only

    let pTypes = Util.getFiltedEvents(this.state?.item?.type);
    if (this.playToolbar) {
      this.playToolbar.setMute(this.state.fastPlay ? true : CameraConfig.getUnitMute());
    }
    let mListBgStyle = this.mABType == ABTest.Types.A ? { marginTop: 20 } : { marginTop: 0, backgroundColor: Util.isDark() ? "#000000" : "#F6F6F6" };

    let showShare = this.state?.item?.isAlarm && Device.permitLevel !== 36;
    return (
      <View style={[mCtnStl, { marginTop: mHeaderHeigth  }]}>
        <View style={this.state.fullScreen ? styles.fullscreenPlayer : styles.player}>

          {this.mPlayer.getVideoView()}
          <TouchableOpacity 
            style={{ width: "100%", height: "100%", backgroundColor: "#00000000", position: "absolute", zIndex: 0 }}
            onPress={() => { this.onPress(); }}
            accessible={true}
            accessibilityLabel={DescriptionConstants.kj_2_37}
          >
            
          </TouchableOpacity>

          {
            this.state.loadingStr != null ?
              <Text style={{ color: 'white', zIndex: 99, position: "absolute", alignSelf: "center", top: "50%", display: 'flex' }}>
                {this.state.loadingStr}</Text>
              : null
          }

          {
            !this.state.hiddenBars &&
            <LinearGradient colors={['#00000077', '#00000000']} style={{ position: 'absolute', height: this.state.fullScreen ? Host.isPad ? 110 : 80 : 50, width: "100%", zIndex: 100 }}>
              {this.state.fullScreen && <SafeAreaView style={{ flex: 1 }} />}
              <View style={{ width: "100%", flexDirection: "row", justifyContent: "space-between", paddingRight: 10, marginTop: this.state.fullScreen ? (Host.isPad) ? 50 : 20 : 10 }}>
                <TouchableOpacity
                  onPress={() => {
                    if (this.state.fullScreen) {
                      this.onBack();
                    } else {
                      this.pushBack();
                      this.naviBack();
                    }
                  }}
                  accessibilityLabel={!this.state.fullScreen ? DescriptionConstants.yc_1 : DescriptionConstants.yc_12}

                >
                  <Image
                    style={{ width: 45, height: 45, marginLeft: (Platform.OS == 'ios' || !this.state.fullScreen)   ? 0 : this.displayCutoutTop, display: (this.state.fullScreen || this.mABType != ABTest.Types.A) ? "flex" : "none" }}

                    source={require("../../Resources/Images/icon_back_black_nor_dark.png")}
                  />
                </TouchableOpacity>
                { !this.state.showBackOnly ? <OperateToolbar
                  showSnapShot={this.showSnapShot}
                  showBack={!this.state.fullScreen} // 控件中的back已经不再使用，被上面的back替代
                  showShare={showShare}
                  backProcessed={() => this.doBack()}
                  snapshotPressed={() => this.doSnapShot()}
                  downloadPressed={() => { this._showConfimTips(true)}}
                  sharePressed={() => this.doDld(true)}
                  deletePressed={() => { this._showConfimTips(false) }} /> : null }
              </View>
            </LinearGradient>
          }
          {!this.state.screenshotVisiblity ? null : this._renderSnapshotView()}
          {
            !this.state.hiddenBars && <LinearGradient colors={['#00000000', '#00000077']} style={[styles.playerToolbar, { height: this.state.fullScreen ? 50 : 40, paddingLeft: mPlayerToolBarPaddingLeft }]}>
              <PlayerToolbar
                ref={(ref) => { this.playToolbar = ref; }}
                duration={this.duration}
                isPlaying={this.state.isPlaying}
                isMute={this.state.fastPlay ? true : CameraConfig.getUnitMute()}
                startTime={Math.max(0, this.mCurTime)}
                isFullscreen={this.state.fullScreen}
                playPressed={(aPlaying) => this.onPlay(aPlaying)}
                mutePressed={this.mutePressed}
                fastPlayPressed={this.fastPlayProessed}
                onSeeked={this.onSeeked}
                fullscreenPressed={this.fullscreenPressed}
                isFastPlay={this.state.fastPlay}
                onSlidingStart={this.onSlidingStart}
              />
            </LinearGradient>
          }
          {
            this.state.mNoVideoEvent != null ?
              <View style={{ flexDirection: "column", width: "100%", height: '100%', display: "flex", alignItems: "center", zIndex: 99, position: "absolute", alignSelf: "center", backgroundColor: 'black' }}>
                <TouchableOpacity
                  style={{ width: "100%", height: '100%' }}
                  onPress={() => {
                    this.onPress(true);
                  }}>
                  <Image
                    style={{ alignSelf: "center", width: 56, height: 56, marginTop: '20%' }}
                    source={ require("../../resources2/images/icon_ev_empty_w.png") }/>
                  <Text style={[BaseStyles.text13, { color: '#999999', alignSelf: "center", display: 'flex' }]}>
                    {this.state.mNoVideoEvent}</Text>
                </TouchableOpacity>
              </View> : null
          }
        </View>

        { (this.mABType == ABTest.Types.A) && !this.state.fullScreen && pTypes.includes(Event.KnownFace) && !this.mIsInternationalServer ? this._renderCommentLine(Event.KnownFace, this.state?.item?.faceInfo) : null }
        { (this.mABType == ABTest.Types.A) && !this.state.fullScreen && !this.mIsInternationalServer && pTypes.includes(Event.PeopleMotion) ? this._renderCommentLine(Event.PeopleMotion) : null }
        { (this.mABType == ABTest.Types.A) && !this.state.fullScreen && pTypes.includes(Event.Pet) && !this.mIsInternationalServer ? this._renderCommentLine(Event.Pet) : null }
        { (this.mABType !== ABTest.Types.A) && !this.state.fullScreen ? this._renderEventInfoHeader(pTypes) : null /* 新样式显示事件信息和过滤器等 */}
        { (this.mABType == ABTest.Types.A || this.mABType == ABTest.Types.B) && !this.state.fullScreen ? this._renderBuyCloudNew() : null /* 老样式和合并吸顶 */}
        {
          mLstType === "list" ?
            <EventSectionList
              ref={(ref) => { this.mEvList = ref; }}
              style={mListBgStyle}
              contentContainerStyle={{ paddingHorizontal: 12 }}
              events={this.props.navigation.state.params.items}
              loader={this.mLoader}
              loaderArgs={{ startDate: this.state.startDate, filter: this.state.filter, nextDate: this.mDefaultLoaderArg?.nextDate }}
              onEventPress={(aItm) => { this.updatePlayItem(aItm); }}
              playingId={mPId}
              onGetDataDone={this.mGetDataDone}
              dataFilter={this.props.navigation.state.params.dataFilter}
              type={CldDldTypes.Events}
              // postNoFreeSVL={CameraConfig.isPostNoFreeSVL()}
              // noFreeSVLEndTime={CameraConfig.noFreeSVLCfg().endTime}
              abType={this.mABType}
              enterTime={this.props.navigation.state.params.enterTime}
            />
            :
            (
              mLstType === "list_vip" ?
                <VipEventSectionList
                  ref={(ref) => { this.mEvList = ref; }}
                  style={mListBgStyle}
                  contentContainerStyle={{ paddingHorizontal: 12 }}
                  loader={this.mLoader}
                  loaderArgs={{ startDate: this.state.startDate, filter: this.state.filter, nextDate: this.mDefaultLoaderArg?.nextDate }}
                  onEventPress={(aItm) => { this.updatePlayItem(aItm); }}
                  onFaceButtonPress={(aItm, aType) => { this.faceComment(aItm, aType); }}
                  playingId={mPId}
                  onGetDataDone={this.mGetDataDone}
                  dataFilter={this.props.navigation.state.params.dataFilter}
                  event={this.props.navigation.state.params.item}
                  type={CldDldTypes.Events}
                  // postNoFreeSVL={CameraConfig.isPostNoFreeSVL()}
                  // noFreeSVLEndTime={CameraConfig.noFreeSVLCfg().endTime}
                  abType={this.mABType}
                  emptyDes={this.state.mEmptyDes}
                  showCloudTips={this.mABType == ABTest.Types.C && this.state.showBuyCloudVideoTip}
                  onRCloudTips={this._renderBuyCloudNew}
                  enterTime={this.props.navigation.state.params.enterTime}
                  withDailyStoryButton={this.mSupportDailyStory && !this.state.fullScreen}

                />
                :
                <EventGrid
                  style={mListBgStyle}
                  ref={(ref) => { this.mEvList = ref; }}
                  events={this.props.navigation.state.params.items}
                  secHeaderBg={BaseStyles.mainBg.backgroundColor}
                  loader={this.mLoader}
                  loaderArgs={{ startDate: this.state.startDate, filter: this.state.filter, nextDate: this.mDefaultLoaderArg?.nextDate }}
                  onEventPress={(aItm) => { this.updatePlayItem(aItm); }}
                  playingId={mPId}
                  onGetDataDone={this.mGetDataDone}
                  dataFilter={this.props.navigation.state.params.dataFilter}
                  isFullscreen={this.state.fullScreen}
                  abType={ABTest.Types.A} // 云存播放使用播放页面的老逻辑，也就是DFT， A
                  enterTime={this.props.navigation.state.params.enterTime}
                />
            )
        }
        { this.mSupportDailyStory && this.mABType != ABTest.Types.A && !this.state.fullScreen && !Device.isReadonlyShared ?
          <SafeAreaView style={{bottom: 0, backgroundColor: '#F7F7F7', width: '100%', position: 'absolute'}}>
            <TouchableOpacity style={{ height: 46, marginTop: 10, width: "90%", backgroundColor: "#32BAC0", borderRadius: 23, alignSelf: 'center', marginBottom: Util.getBottomMarginWithoutSafeArea() }}
              onPress={() => {
                TrackUtil.reportClickEvent('Monitoring_DailyStory_ClickNum');
                let goUseDailyStory = CameraConfig.supportNonVipDailyStory(Device.model) ? true : (this.isVip || this.isInCloudWindow);
                if (goUseDailyStory && CameraConfig.dailyStorySwitch) { // inexpireWindow== closeWindow= true 代表已经彻底过期了。
                  console.log("是否开启每日故事：", this.state.dailyStorySwitch);
                  this.props.navigation.navigate('DailyStoryList');
                } else {
                  // console.log("是不是vip：",this.state.isVip)
                  // this.props.navigation.navigate('DailyStoryList');
                  this.props.navigation.navigate('DailyStoryFirstEnter');
                }
              }}>
              <Text style={{ textAlign: "center", fontSize: 15, lineHeight:46, color: "#ffffff", fontWeight: 'bold' }}>
                {LocalizedStrings["ss_daily_story"]}
              </Text>
            </TouchableOpacity>
          </SafeAreaView>
          : null}
        <CoverLayer CoverLayerState={() => {}}  ref={(ref) => this.coverLayer = ref} />
        <MessageDialog
          visible={this.state.deleteVisible }
          canDismiss={false}
          modalStyle={{ width: "100%" }}
          message={this.state.mDlgTips}
          messageStyle={[BaseStyles.text17, { alignSelf: 'center', color: '#333333', fontWeight: '400'}]}
          onDismiss={() => {
            this.setState({ deleteVisible: false });
          }}
          buttons={[
            {
              text: LocalizedStrings["action_cancle"],
              callback: (_) => this.setState({ deleteVisible: false })
              // ignore
            },
            {
              text: LocalizedStrings["delete_confirm"],
              callback: (_) => {
                this.setState({ deleteVisible: false });
                this.deletePressed();
              }
              // ignore
            }
          ]}
        />

        <MessageDialog
          visible={this.state.downloadVisible }
          canDismiss={false}
          message={this.state.mDlgTips}
          messageStyle={[BaseStyles.text17, { alignSelf: 'center', color: '#333333', fontWeight: '400'}]}
          onDismiss={() => {
            this.setState({ downloadVisible: false });
          }}
          buttons={[
            {
              text: LocalizedStrings["action_cancle"],
              callback: (_) => this.setState({ downloadVisible: false })
              // ignore
            },
            {
              text: LocalizedStrings["f_download"],
              callback: (_) => {
                this.setState({ downloadVisible: false });
                this.doDld(false);
              }
              // ignore
            }
          ]}
        />

        <MessageDialog
          visible={this.state.errReportPermissionVisible}
          canDismiss={false}
          title={LocalizedStrings["cloud_face_rec_error"]}
          message={LocalizedStrings["mydevice.camera.motion.report.permission.no.policy"]}
          messageStyle={[BaseStyles.text17, { alignSelf: 'center', color: '#333333', fontWeight: '400', lineHeight: 23}]}
          onDismiss={() => {
            this.setState({ errReportPermissionVisible: false });
          }}
          buttons={[
            {
              text: LocalizedStrings["btn_cancel"],
              callback: (e) => {
                console.log('onCancel err report permission', e);
                this.setState({ errReportPermissionVisible: false });
              }
            },
            {
              text: LocalizedStrings["btn_confirm"],
              callback: (e) => {
                console.log('onConfirm err report permission', e, this.mReportType);
                this.setState({ errReportPermissionVisible: false });
                Util.reportFaceRecError(this.state.item, this.mReportType)
                  .then((aRet) => {
                    if (aRet.code == 0) {
                      Toast.success("cloud_face_feed_thx");
                      return;
                    }
                    console.log(this.tag, "reportFaceRecError", aRet);
                    Toast.fail("action_failed");
                  })
                  .catch((aErr) => {
                    console.log(this.tag, "reportFaceRecError err", aErr, this.mReportType);
                    Toast.fail("action_failed");
                  });
              }
            }
          ]}
        />
        {this.renderEvFilterDlg()}
        {this._renderCommentsDialog()}
        {this.renderCommentDlg(faceInfo)}
        {this._renderDownloadHint()}
      </View>
    );
  }
  _renderTitleView() {
    if (this.state.fullScreen) {
      return null;
    }

    let statusBarHeight = StatusBarUtil._getInset("top");

    let gradientColors;
    gradientColors = ['white', 'white'];

    let bgColor = "transparent";
    let showGradient = true;
    if (this.isPageForeGround) {
      StatusBar.setBarStyle('light-content');
    }

    let titleBarStyle = {
      display: "flex",
      position: "absolute",
      top: 0,
      height: statusBarHeight,
      width: "100%",
      flexDirection: "column",
      backgroundColor: bgColor,
      zIndex: 1,
      alignItems: "center"
    };

    const gradientStyle = {
      position: "absolute",
      top: 0,
      width: "100%",
      height: "100%"
    };
    return (
      <View style={titleBarStyle}>
        {showGradient ? <LinearGradient colors={gradientColors} style={gradientStyle} /> : null}
      </View>
    );
  }
  _showConfimTips(isDownload) {
    // 下载一个事件视频时直接下载，所以非云存看家和一个事件的看家视频直接下载，多个事件的则提示
    // 删除一个事件视频提示‘确认删除’， 多个事件提示多个事件删除
    if (this.mABType == ABTest.Types.A) {
      if (isDownload) {
        this.doDld(false);
      } else {
        this.setState({ deleteVisible: true, mDlgTips: LocalizedStrings['delete_title'] });
      }
    } else {
      let mDlgTips = isDownload ? LocalizedStrings['kanjia_download_tips_single'] : LocalizedStrings['kanjia_delete_tips_single'];
      let elist = this.mEvList.getEventListOfCurentFile(this.state?.item?.fileId);
      let count = elist.length;
      if (count > 1) {
        let mStr = isDownload ? LocalizedStrings['kanjia_download_tips'] : LocalizedStrings['kanjia_delete_tips'];
        mDlgTips = Util.fmtStr(mStr, count);
      } else {
        if (isDownload) {
          this.doDld(false);
          return;
        } 
      }
  
      if (isDownload) {
        this.setState({ downloadVisible: true, mDlgTips });
      } else {
        this.setState({ deleteVisible: true, mDlgTips });
      }
    }
    TrackUtil.newOneTrack(['click'], AlarmPlayerCtl.bDelete);

  }

  _getCommentCounts(pTypes) {
    let count = 0;
    if (pTypes.includes(Event.PeopleMotion)) count++;
    if (pTypes.includes(Event.KnownFace)) count++;
    if (pTypes.includes(Event.Pet)) count++;
    return count;
  }
  // render calender, event filter and comments line entries
  _renderEventInfoHeader(pTypes) {
    let mShowComments = (pTypes.includes(Event.KnownFace) || pTypes.includes(Event.PeopleMotion) || pTypes.includes(Event.Pet)) && !this.mIsInternationalServer;
    let isDark = Util.isDark();
    let imageName = isDark ? require("../../resources2/images/icon_ev_all_events_dark.png") : require("../../resources2/images/icon_ev_all_events_light.png");
    if (this.state.filter == Event.Default) {
      imageName = isDark ? require("../../resources2/images/icon_ev_all_events_dark.png") : require("../../resources2/images/icon_ev_all_events_light.png");
    } else if (this.state.filter == Event.BabyCry) {
      imageName = isDark ? require("../../resources2/images/icon_ev_baby_cry_dark.png") : require("../../resources2/images/icon_ev_baby_cry_light.png");
    } else if (this.state.filter == Event.KnownFace) {
      imageName = isDark ? require("../../resources2/images/icon_ev_face_dark.png") : require("../../resources2/images/icon_ev_face_light.png");
    } else if (this.state.filter == Event.PeopleMotion) {
      imageName = isDark ? require("../../resources2/images/icon_ev_people_move_dark.png") : require("../../resources2/images/icon_ev_people_move_light.png");
    } else if (this.state.filter == Event.AI) {
      imageName = isDark ? require("../../resources2/images/icon_ev_ai_dark.png") : require("../../resources2/images/icon_ev_ai_light.png");
    } else if (this.state.filter == Event.Pet) {
      imageName = isDark ? require("../../resources2/images/icon_ev_pet_dark.png") : require("../../resources2/images/icon_ev_pet_light.png");
    } else if (this.state.filter == Event.ObjectMotion) {
      imageName = isDark ? require("../../resources2/images/icon_ev_area_move_dark.png") : require("../../resources2/images/icon_ev_area_move_light.png");
    } else if (this.state.filter == Event.LouderSound) {
      imageName = isDark ? require("../../resources2/images/icon_ev_high_volume_dark.png") : require("../../resources2/images/icon_ev_high_volume_light.png");
    } else if (this.state.filter == Event.CameraCalling) {
      imageName = isDark ? require("../../resources2/images/icon_ev_voice_video_call_dark.png") : require("../../resources2/images/icon_ev_voice_video_call_light.png");
    }
    let bgColor = isDark ? "xm#333333" : "#FFFFFF";
    let imgStyle = { width: 26, height: 26, borderRadius: 13, marginLeft: 2};
    let imgbgStyle = { width: 30, height: 30, borderRadius: 15, backgroundColor: isDark ? "xm#333333" : "#FFFFFF", justifyContent: 'center' };
    let count = this._getCommentCounts(pTypes);
    let commentsStyleBase = { justifyContent: "flex-end", height: 30, borderRadius: 15, alignItems: "center", width: 'auto' };
    let commentsStyle = count > 1 ? [commentsStyleBase, {backgroundColor: isDark ? "xm#333333" : "#FFFFFF"}] : commentsStyleBase;

    return (
      <View style={[BaseStyles.row, { backgroundColor: isDark ? "#000000" : "#F6F6F6", height: 64, paddingLeft: 24, paddingRight: 24 }]}>
        <TouchableOpacity
          style={[BaseStyles.row, { alignItems: "center", width: "auto" }]}
          onPress={() => {
            this.mShowPopupView(); // 显示日历
            let date = dayjs(this.mSltDate).format('YYYY-MM-DD');
            TrackUtil.newOneTrack(['click'], { 'item_type': 'button', 'item_name': date });
          }}>
          <Text
            style={[BaseStyles.text12, { fontWeight: "bold", paddingLeft: 3, color: "#8C93B0" }]}>{this.state.selectDateStr}</Text>
          <Image
            style={BaseStyles.icon20} source={require("../../resources2/images/icon_ev_sel.png")} />
        </TouchableOpacity>
        

        <View style={{ flex: 1, justifyContent: "flex-end", backgroundColor: "#00000000", flexDirection: "row" }}>
          { mShowComments ?
            <TouchableOpacity
              style={[BaseStyles.row, commentsStyle]}
              onPress={() => {
                this.commentsItem = this.state.item;
                this.setState({ showCommentsDlg: true }); // 显示备注弹窗
              }}>
              { pTypes.includes(Event.KnownFace) ? <View style={[imgbgStyle, { marginRight: -10, zIndex: 100 }]}>
                <Image style={imgStyle } source={this.state.faceUrl}></Image></View> : null}
              { pTypes.includes(Event.PeopleMotion) ? <View style={[imgbgStyle, { marginRight: -10, zIndex: 99 }]}>
                <Image style={imgStyle } source={Util.getIconFromType(Event.PeopleMotion)}></Image></View> : null}
              { pTypes.includes(Event.Pet) ? <View style={[imgbgStyle, { zIndex: 98 }]}>
                <Image style={imgStyle } source={Util.getIconFromType(Event.Pet)}></Image></View> : null}
            </TouchableOpacity> : null
          }

          <TouchableOpacity style={{ backgroundColor: bgColor, borderRadius: 15, width: 45, height: 30, alignItems: "center", alignSelf: 'center', justifyContent:'center', marginLeft: 30 }}
            onPress={() => {
              this.setState({ showEvFilter: true }); // 显示事件过滤弹窗
              TrackUtil.newOneTrack(['click'], AlarmPlayerCtl.bFilter);
            }}
          >
            <Image style={BaseStyles.icon20} source={imageName} />
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  _renderCommentsDialog() {
    // let pTypes = Util.getFiltedEvents(this.state?.item?.type);
    let pTypes = Util.getFiltedEvents(this.commentsItem?.type);
    return (
      <AbstractDialog
        visible={this.state.showCommentsDlg}
        // title={'识别报错'}
        // subtitle={testTitle}
        showTitle={false}
        showSubtitle={false}
        useNewTheme={true}
        onDismiss={(_) => { this.setState({ showCommentsDlg: false })}}
        buttons={[
          {
            text: LocalizedStrings['action_cancle'],
            colorType: "grayLayerBlack",
            callback: (_) => {
              this.setState({ showCommentsDlg: false });
            }
          }
        ]}>
        <View style={[styles.container, { marginTop: 20 }]} >
          { !this.state.fullScreen && pTypes.includes(Event.KnownFace) ? this._renderCommentLine(Event.KnownFace, this.commentsItem?.faceInfo) : null }
          { !this.state.fullScreen && !this.mIsInternationalServer && pTypes.includes(Event.PeopleMotion) ? this._renderCommentLine(Event.PeopleMotion) : null }
          { !this.state.fullScreen && pTypes.includes(Event.Pet) ? this._renderCommentLine(Event.Pet) : null }
        </View>
      </AbstractDialog>
    )
  }
  _renderCommentLine(eType, faceInfo = null) {
    let commentStr;
    if (eType == Event.KnownFace) {
      commentStr  = faceInfo && (faceInfo.name || !Device.isOwner) ? Util.getDescFromType(eType, faceInfo) : LocalizedStrings["add_notes"];
    }
    return (
      <View style={[BaseStyles.row, { backgroundColor: "white", height: 74, paddingLeft: 24, paddingRight: 24 }]}>
        {(faceInfo != null) ?
          <TouchableOpacity style={[BaseStyles.row, { alignItems: "center", width: "auto" }]}
            onPress={() => {
              if (Device.isOwner) {
                // this.naviTo(AllNativePage.FaceRec);
                this.changed = false;
                this.mItemForFaceComment = this.commentsItem != null ? this.commentsItem : this.state.item; 
                this.setState({ commentDlg: true, defVal: faceInfo.name ? faceInfo.name : "", commentErr: null, showCommentsDlg: false });
              }
              TrackUtil.reportClickEvent('Monitoring_mark_ClickNum');
            }}>
            <Image style={{ width: 36, height: 36, borderRadius: 18 }} source={this.state.faceUrl}></Image>
            {/* 有名字的不需要+   非设备主人的不需要+*/}
            {faceInfo.name ? null : (Device.isOwner ? <Image style={{ width: 19, height: 19, position: "relative", left: -8, top: -10 }}
              source={Util.isDark() ? require("../../resources2/images/icon_face_comment_dark.png") : require("../../resources2/images/icon_face_comment.png")} /> : null)}
            <View style={{ flexDirection: 'column', alignItems: "flex-start", justifyContent: "center" }}>
              {/* 有名字的 直接输出desc即可   没有名字的共享设备提示有人脸出现   没名字的非共享设备提示添加备注+*/}
              {<Text style={[BaseStyles.text15, { fontWeight: "bold", paddingLeft: 5 }]}>{commentStr}</Text>}
              {/* 设备主人 & 有名字才提示修改备注+*/}
              {Device.isOwner && faceInfo.name ? <Text style={[BaseStyles.text12, { paddingLeft: 5 }]}>{LocalizedStrings["modify_notes"]}</Text> : null}
            </View>
          </TouchableOpacity>
          :
          this._renderCommentsLineRight(eType)
        }
        <View style={{ flex: 1, marginLeft: 12, justifyContent: "flex-end", backgroundColor: "#00000000", flexDirection: "row" }}>
          <TouchableOpacity style={{ backgroundColor: this.mABType != ABTest.Types.A ? (Util.isDark() ? "#25A9AF32" : "#32BAC019") : "#F2F2F2", borderRadius: 17, minHeight: 34, paddingHorizontal: 20, justifyContent: 'center'}}
            onPress={() => {
              this.mReportType = eType;
              this.setState({ errReportPermissionVisible: true, showCommentsDlg: false });
            }}
          >
            <Text style={[BaseStyles.text13, { fontWeight: "bold", color: this.mABType != ABTest.Types.A ? (Util.isDark() ? "#25A9AF" : "#32BAC0") : 'black' }]}>{LocalizedStrings["cloud_face_rec_error"]}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  _renderCommentsLineRight(eType) {
    let icon = Util.getIconFromType(eType);

    return (
      <TouchableOpacity
        style={[BaseStyles.row, { alignItems: "center", width: "auto" }]}
        onPress={() => {
        }}>
        <Image
          style={BaseStyles.icon30} source={icon} />
        <Text
          style={[BaseStyles.text15, { fontWeight: "bold", paddingLeft: 15 }]}>{Util.getDescFromType(eType)}</Text>
      </TouchableOpacity>
    );
  }

  _renderBuyCloudNew = ()=> {
    let PlatoTips = PlatoTipsOn();
    let tipsText, tipsUrl, backgroundColor, channel, tipsTextColor, backIcon;
    if (PlatoTips) {
      if (!this.state.showBuyCloudVideoTip || !this.cloudTipsItem || !CloudAdvertising.validateAd(this.isVip, this.cloudTipsItem)) {
        return null;
      }
      if (!this.statCloudTipsExposeDone && this.statCloudTipsExposeDone != undefined) {
        this.statCloudTipsExposeDone = true;

        TrackUtil.oneTrackReport('Monitoring_player_Tips_Expose', [this.isInCloudWindow ? 1 : 0, this.ref_tip]);
        TrackUtil.newOneTrack(['expose'], { item_type: 'banner', item_name: 'cloud_banner' });
        TrackUtil.reportExposeMapEvent('Monitoring_CloudTips_Show');
      }
      let tipType = this.cloudTipsItem?.vipStatus; // 1: 云存未开通 2: 云存生效中 3: 云存临期  4: 云存过期
      tipsText = this.cloudTipsItem?.title;
      tipsUrl = this.cloudTipsItem?.targetUrl;
      backgroundColor = tipType == 1 ? (Util.isDark() ? "#25A9AF32" : '#32BAC019') : (Util.isDark() ? "#DB8E0D32" : '#F5A62319');
      channel = "videodetails_button";
      tipsTextColor = tipType == 1 ? (Util.isDark() ? "#25A9AF" : "#32BAC0") : (Util.isDark() ? "#DB8E0D" : "#F5A623");
      let iconForNewUser = require('../../Resources/Images/coud_banner_player_tip_expired.png'); // 绿色
      let iconForRenewUser = Util.isDark ? require('../../Resources/Images/coud_banner_player_tip_inwindow_dark.png') : require('../../Resources/Images/coud_banner_player_tip_inwindow.png');
      backIcon = tipType == 1 ? iconForNewUser : iconForRenewUser;
    } else {
      if (!this.state.showBuyCloudVideoTip) {
        return null;
      }
      if (!this.statCloudTipsExposeDone && this.statCloudTipsExposeDone != undefined) {
        this.statCloudTipsExposeDone = true;
        TrackUtil.oneTrackReport('Monitoring_player_Tips_Expose', [this.isInCloudWindow ? 1 : 0, this.ref_tip]);
        TrackUtil.newOneTrack(['expose'], { item_type: 'banner', item_name: 'cloud_banner' });
      }
      backgroundColor = Util.isDark() ? "#25A9AF32" : '#32BAC019';
      channel = "videodetails_button";
      // tipsText = this.isEuropeServer ? LocalizedStrings['eu_camera.alarm.cloud.tip.fullvideo.not.vip_new'] : LocalizedStrings['camera.alarm.cloud.tip.fullvideo.not.vip_new'];
      tipsText = LocalizedStrings['camera.alarm.cloud.tip.fullvideo.not.vip_new'];
      if (this.freeHomSurStatus && (this.freeHomeSurExpireTime == -2 || this.freeHomeSurExpireTime == -1)) {
        tipsText = this.isEuropeServer ? LocalizedStrings['eu_camera.alarm.cloud.tip.fullvideo.not.vip'] : LocalizedStrings['camera.alarm.cloud.tip.fullvideo.not.vip'];
      }
      tipsTextColor = Util.isDark() ? "#25A9AF" : "#32BAC0";
  
      backIcon = require('../../Resources/Images/coud_banner_player_tip_expired.png');
      if (this.isInCloudWindow) {
        tipsText = this.isEuropeServer ? LocalizedStrings['eu_c_cloudvip_end_tip'] : LocalizedStrings['c_cloudvip_end_tip'];
        tipsTextColor = Util.isDark() ? "#DB8E0D" :"#F5A623";
        channel = "videodetails_button_expire";
        backgroundColor = Util.isDark() ? "#DB8E0D32" : '#F5A62319';
        backIcon = require('../../Resources/Images/coud_banner_player_tip_inwindow.png');
      }
    }


    let style = { flexDirection: "row", flexWrap: 'nowrap', alignItems: "center", justifyContent: "space-between", backgroundColor: backgroundColor, paddingLeft: 16, paddingRight: 30, width: "100%", height: 54, borderRadius: 16 };
    let mBgStyleBase = { marginTop: this.mABType == ABTest.Types.A ? 15 : 0, alignItems: "center", justifyContent: "center", marginHorizontal: 15 };
    let mBgStyle = this.mABType == ABTest.Types.A ? [mBgStyleBase, { marginBottom: -15 }] : [mBgStyleBase, { paddingBottom: 10, backgroundColor: Util.isDark() ? "#000000" : "#F6F6F6" }];
    return (
      <View style={mBgStyle}>
        <TouchableOpacity
          style={style}
          onPress={() => {
            TrackUtil.reportClickEvent('Monitoring_WholeVideo_ClickNum', true, [this.isInCloudWindow ? 1 : 0, this.ref_tip]);
            TrackUtil.newOneTrack(['click'], { item_type: 'banner', item_name: 'cloud_banner' });
            if (!Device.isOwner) {
              Toast.success("share_user_permission_hint");
              return;
            }
            this.toPortrait();
            Service.miotcamera.showCloudStorage(true, true, Device.deviceID, tipsUrl ? tipsUrl : "", true, { channel: channel });
            CameraConfig.isToUpdateVipStatue = true;
            this.setState({ showBuyCloudVideoTip: false });
          }
          }
          ref={(ref) => this.mCloudBuyTip = ref}
          onLayout={(e) => {
            this.mCloudBuyTipWidth = e.nativeEvent.layout.width;
          }}
          // accessibilityLabel={!this.state.fullScreen ? DescriptionConstants.kj_2_5 : DescriptionConstants.kj_2_30}
        >
          <Text numberOfLines={3}
            style={[BaseStyles.text14, { paddingRight: 0, color: tipsTextColor, textAlign: "left", textAlignVertical: 'center', width: '100%' }]}>{tipsText}</Text>
          <ImageButton
            style={{ width: 22, height: 22 }}
            source={backIcon}
          />
        </TouchableOpacity>
      </View>
    );
  }

  _getFullscreenVideoSize() {
    // 全屏时的宽高计算
    let { width, height } = Dimensions.get('screen');
    let wIn = Math.max(width, height);
    let hIn = Math.min(width, height);
    let mW = wIn;
    let mH = hIn;
    if (wIn / hIn > 16 / 9) {
      mW = hIn * 16 / 9;
    } else if (wIn / hIn < 16 / 9) {
      mH = wIn * 9 / 16;
    }
    return { mW, mH };
  }
  _renderDownloadHint() {
    if (!this.state.showDownloadHint) {
      return null;
    }
    let { mW, mH } = this._getFullscreenVideoSize();
    return (
      <View style={{ position: "absolute", bottom: 0, height: 92, width: "100%", display: "flex", alignItems: "center" }}>
        <View style={{ height: '100%', width: this.state.fullScreen ? mW : "100%", justifyContent: "center", backgroundColor: Util.isDark() ? "xm#1A1A1A" : "xm#FFFFFF",
          borderTopLeftRadius: this.state.fullScreen ? 20 : 0, borderTopRightRadius: this.state.fullScreen ? 20 : 0, borderTopWidth: 1,
          borderTopColor: Util.isDark() ? "xm#000000" : "xm#F6F6F6", alignItems: "center" }}>
          <TouchableOpacity style={{ width: "85%", height: "39%", display: "flex", justifyContent: 'space-between', flexDirection: 'row' }}
            onPress={() => {
              this.props.navigation.navigate("DldPage", { preOri: this.state.fullScreen ? "landscape" : "portrait" });
            }}>
            <Text numberOfLines={2}
              style={[BaseStyles.text16, { color: Util.isDark() ? "#FFFFFFCD" : "#000000CD", textAlignVertical: 'center', alignSelf: 'center' }]}>
              {LocalizedStrings["download_hint"]}
            </Text>
            <Image style={{ width: 20, height: 20, display: "flex", alignSelf: 'center' }}
              source={Util.isDark() ? require('../../Resources/Images/icon_download_kanjia_dark.png') : require('../../Resources/Images/icon_download_kanjia.png')}
            />
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  _renderSnapshotView() {
    let snapshotButton;

    let sWidth = 90;
    let sHeight = 55;
    let sPadding = 20;

    if (this.state.fullScreen) {

      let left = StatusBarUtil._getInset("top")
      snapshotButton = {
        position: "absolute",
        top: Host.isPad ? 90 : 70,
        left: left,
        width: sWidth,
        height: sHeight,
        borderRadius: 4,
        borderWidth: 1.5,
        borderColor: "xm#ffffff"
      };
      if (Host.isPad) {
        snapshotButton.top = "50%";
        snapshotButton.marginTop = -1 * sHeight / 2;
      }
    } else {
      snapshotButton = {
        position: "absolute",
        bottom: SCREEN_WIDTH * 9 / 16 - sPadding - sHeight - 30,
        left: sPadding,
        width: sWidth,
        height: sHeight,
        borderRadius: 4,
        borderWidth: 1.5,
        borderColor: "xm#ffffff"
      };
    }

    return (
      <View style={snapshotButton} >
        <ImageButton
          accessibilityLabel={!this.state.fullScreen ? DescriptionConstants.kj_2_19 : DescriptionConstants.kj_2_25}
          style={{ width: "100%", height: "100%", borderRadius: 4 }}
          source={(this.state.screenshotPath == "") ? null : ({ uri: `file://${Host.file.storageBasePath}/${this.state.screenshotPath}` })}
          onPress={() => {
            clearTimeout(this.snapshotTimeout);
            this.setState({ screenshotVisiblity: false, screenshotPath: "" });// 点击后就消失。
            this.showLastImage();
          }}


        />

      </View>
    );
  }

  showLastImage() {
    this.props.navigation.navigate("AlbumPhotoViewPage", { preOri: this.state.fullScreen ? "landscape" : "portrait", routerName:"AlarmVideoUI" });
  }

  renderCommentDlg(aFaceInf) {
    if (!this.state.commentDlg) {
      return null;
    }
    return (
      <InputDlgEx
        visible={this.state.commentDlg}
        icon={this.state.faceUrl}
        listData={this.mAllFigureInf}
        onPressed={(aDat) => {
          this.setState({ defVal: aDat.name, commentErr: null });
        }}
        title={LocalizedStrings["cloud_comment_dlg_title"]}
        onDismiss={(_) => {
          this.renameItem = null;
          this.setState({ commentDlg: false, isRename: false, commentErr: null });
          this.mItemForFaceComment = null;
        }}
        inputWarnText={this.state.commentErr}
        noInputDisButton={ true }
        inputs={[{
          onChangeText: (text) => {
            this.changed = true;
            let isEmoji = Util.containsEmoji(text);
            let length = text.length; 
            if (isEmoji) {
              this.setState({ commentErr: LocalizedStrings["Special_symbol_input_not_supported_temporarily"] }); 
            } else if (length > 8) {
              this.setState({ commentErr: LocalizedStrings["input_name_too_long"]?.replace("6", "8")});
            } else if (length <= 0) {
              this.setState({ commentErr: LocalizedStrings["add_feature_empty_tips"] });
            } else {
              this.setState({ commentErr: null });
            }
          },
          textInputProps: {
            returnKeyType: "done",
            autoFocus: Platform.OS == 'ios' ? Util.isHeightPt() : true
          },
          defaultValue: this.state.defVal,
          type: 'DELETE',
          isCorrect: this.state.commentErr == null
        }]}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: (_) => {
              this.renameItem = null;
              this.setState({ commentDlg: false, isRename: false, commentErr: null });
              this.mItemForFaceComment = null;
            }
            // ignore
          },
          {
            text: LocalizedStrings["csps_right"],
            disabled: this.state.commentErr != null || this.state.uploadingFace,
            callback: (result) => {
              let text = result.textInputArray[0].trim();
              console.log(this.tag, "input changed", text, text.length);
              if (!this.mItemForFaceComment?.faceInfo) {
                this.setState({ isPlaying: true, commentDlg: false, isRename: false, commentErr: null });
                return;
              }
              if (text.length == 0 && this.state.defVal?.length > 0 && !this.changed) {
                text = this.state.defVal;
              }
              console.log(this.tag, "input changed2", text, text.length);
              // 大于8个字符不能输入
              if (text.length > 8) {
                return;
              }
              if (text.length > 0 && !Util.containsEmoji(text)) {
                let cmd = null;
                let faceInfo = this.mItemForFaceComment?.faceInfo;
                if (faceInfo?.name) {
                  cmd = Util.modifyFaceComment(faceInfo.figureId, faceInfo.faceId, text);
                } else {
                  cmd = Util.commentFace(text, faceInfo.faceId);
                }

                if (cmd) {
                  if (this.state.uploadingFace) {
                    return;
                  }
                  this.setState({ uploadingFace: true });
                  cmd.then((aRet) => {
                    this.mEvList.updateAllItems();
                    this.refreshFigureInfo(true);
                    if (this.mItemForFaceComment?.offset == this.state.item?.offset) {
                      this.state.item.faceInfo.name = text;
                      if (aRet?.data?.figureId) {
                        this.state.item.faceInfo.figureId = aRet.data.figureId;
                      }
                      this.state.item.desc = Util.fmtStr(LocalizedStrings['alarm_event_face_face'], text);
                    } else {
                      faceInfo.name = text;
                      if (aRet?.data?.figureId) {
                        faceInfo.figureId = aRet.data.figureId;
                      }
                      this.mItemForFaceComment.desc = Util.fmtStr(LocalizedStrings['alarm_event_face_face'], text);
                    }
                    this.mFaceUpdateInfoCb && this.mFaceUpdateInfoCb();
                    this.mPlayer.resume();
                    this.setState({ isPlaying: true, commentDlg: false });
                    this.mItemForFaceComment = null;
                    console.log(this.tag, "comment success", aRet);
                    this.setState({ uploadingFace: false });
                  })
                    .catch((aErr) => {
                      this.setState({ uploadingFace: false });
                      this.setState({ commentDlg: false });
                      this.mItemForFaceComment = null;
                      let errCode = aErr.code;
                      // 400302 人物上限
                      let errMap = { 400302: "figure_max_tips", 400305: "face_max_tips" };
                      let err = errMap[errCode] || "action_failed";
                      Toast.fail(err, false, true);
                      console.log(this.tag, "comment failed", aErr);
                    });
                } else {
                  console.log(this.tag, "nothing changed");
                  this.renameItem = null;
                  this.setState({ commentDlg: false, isRename: false });
                  this.mItemForFaceComment = null;
                }
              } else {
                // Alert.alert(
                //   LocalizedStrings["lowpower_leave_msg_cant_null"],
                //   null,
                //   [{ text: LocalizedStrings["owldiff_wifi_ok"] }]
                // );
                if (Util.containsEmoji(text)) {
                  this.setState({ commentErr: LocalizedStrings["Special_symbol_input_not_supported_temporarily"] });
                }
                else {
                  this.setState({ commentErr: LocalizedStrings["cloud_comment_null"] });
                }
              }
            }
          }
        ]}
      />);
  }


  renderHeader() {
    if (this.state.fullScreen) {
      return null;
    }

    return (
      <View style={[BaseStyles.row, {
        height: 50, paddingLeft: 12, paddingRight: 52, // 12+40
        marginTop: Platform.OS == 'android' ? 0 : StatusBarUtil._getInset("top"),
        backgroundColor: "white",
        width: "100%"
      }]}>

        <ImageButton
          style={BaseStyles.icon40}
          source={Util.isDark() ? require("../../Resources/Images/icon_back_black_nor_dark.png") : require("../../Resources/Images/icon_back_black.png")}
          onPress={() => {
            this.pushBack();
            this.naviBack();
          }}
          accessibilityLabel={DescriptionConstants.yc_1}

        />
        <Text style={[BaseStyles.text18, { fontWeight: "bold" }]}>{LocalizedStrings["video_details"]}</Text>
        <View></View>
      </View>
    );
  }


  restoreOri() {
    console.log(this.tag, "restoreOri", this.mOri);
    if ("PORTRAIT" === this.mOri) {
      this.toPortrait();
    } else if ("LANDSCAPE" === this.mOri) {
      this.toLandscape();
    }
  }

  toPortrait() {
    console.log(this.tag, "toPortrait");
    StatusBar.setHidden(false);
    this.mOri = "PORTRAIT";
    CameraConfig.lockToPortrait();
    // Service.miotcamera.enterFullscreenForPad(false);
    this.props.navigation.setParams({ isFullScreen: false });
  }

  toLandscape() {
    console.log(this.tag, "toLandscape");
    this.mOri = "LANDSCAPE";
    StatusBar.setHidden(true);
    if (Platform.OS === "android") {
      Orientation.lockToLandscapeLeft();
      Service.miotcamera.enterFullscreenForPad(true);
    } else {
      if (Platform.OS == 'ios' && Host.isPad) {
        Service.miotcamera.enterFullscreenForPad(true);
      } else {
        Orientation.lockToLandscapeRight();
      }
    }
  }

  mOriL = (orientation) => {
    console.log(this.tag, `device orientation changed :${ orientation } want ${ this.mOri } active ${ this.mActive } fullscreen? ${ this.state.fullScreen }`);
    if ((Platform.OS == "ios" || (Host.isAndroid && Host.isPad)) && ((this.mOri === 'LANDSCAPE' && this.state.fullScreen) || (this.mOri === 'PORTRAIT' && !this.state.fullScreen))) {
      return;
    }
    if (this.mOri === orientation) {
      LogUtil.logOnAll(this.tag, "mOriL fullScreen? ", "LANDSCAPE" == this.mOri ? "landcape" : "portrait");
      if (orientation === 'LANDSCAPE') {
        // do something with landscape layout
        this.setState({ fullScreen: true });
        if (Host.isPad) {
          Service.miotcamera.enterFullscreenForPad(true);
        }
      } else {
        // do something with portrait layout
        this.setState({ fullScreen: false });
        if (Host.isPad) {
          Service.miotcamera.enterFullscreenForPad(false);
        }
      }
    } else {
      // ios need restore for next lock
      if (!this.isAndroid()) {
        this.restoreOri();
      }
    }
  };

  mGetDataDone = (count, allItems = -1) => {
    let type = this.props.navigation.state.params.lstType;
    if (count > 0 && !allItems[0].data[0]?.cached) {
      let items = allItems[0].data;
      // 对abtest b and c, 如果没有播放中的视频，或者是刚刚切换了日期，就开始播放第一个视频事件
      if (this.mABType != ABTest.Types.A) {
        if (count > 0 && (!this.state.item || this.playTheFirstEvent) && items[0]?.cached != true) {
          this.playTheFirstEvent = false;
          let itm = items[0];
          if (itm?.isNoSVLTips || itm?.isCloudTips) {
            items = allItems[1].data;
            itm = items[0];
          }
          if (!itm?.isShowImg) {
            if (this.mPlayer) {
              this.mPlayer.stop();
            }
            this.setState({ item: null, mEmptyDes: null, mNoVideoEvent: LocalizedStrings['kanjia_novideo_tips'], loadingStr: null, showBackOnly: true });
            return;
          } else {
            this.setState({ item: itm });
            this.startPlayer(itm);
          }
        }
      }
    } else {
      if (this.mPlayer) {
        this.mPlayer.stop();
      }
      this.setState({ item: null, mEmptyDes: LocalizedStrings['kanjia_nonotification_tips'], mNoVideoEvent: LocalizedStrings['kanjia_novideo_tips'], loadingStr: null, showBackOnly: true });
    }

    if (count > 0 && this.props.navigation.state.params.lstType == 'push') {
      let item = this.mEvList.findEvent(this.state.item);
      if (item) {
        this.setState({ item: item });
      }
      if (this.listType() == "list_vip") {
        this.getFaceUrl(item);
      }
    }

    this.mLoader.getVideoDates(CldDldTypes.Events).then((dates) => { // 云存和看家目前返回null
      if (dates != null && dates.length > 0) {
        this.mDatesWithData = dates;
      }
    }).catch((err) => {
      console.log(`getVideoDates err: ${ err }`);
    });
  }
  isEmojiCharacterV2(codePoint) {
    return !((codePoint == 0x0) ||
      (codePoint == 0x9) ||
      (codePoint == 0xA) ||
      (codePoint == 0xD) ||
      ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) ||
      ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) ||
      ((codePoint >= 0x10000))) ||
      (codePoint == 0xa9 || codePoint == 0xae || codePoint == 0x2122 ||
        codePoint == 0x3030 || (codePoint >= 0x25b6 && codePoint <= 0x27bf) ||
        codePoint == 0x2328 || (codePoint >= 0x23e9 && codePoint <= 0x23fa))
      || ((codePoint >= 0x1F000 && codePoint <= 0x1FFFF))
      || ((codePoint >= 0x2702) && (codePoint <= 0x27B0))
      || ((codePoint >= 0x1F601) && (codePoint <= 0x1F64F));
  }
  containsEmoji(str) {
    let length = str.length;
    for (let i = 0; i < length; ++i) {
      let c = str.charCodeAt(i);
      if (this.isEmojiCharacterV2(c)) {
        return true;
      }
    }
    return false;
  }


  mShowPopupView = () => {
    this.coverLayer.showWithContent(
      () => {
        // let nowDate = new Date();
        console.log('calendar input: ', this.state.mDate);
        let mWidth = Math.min(Dimensions.get("window").height, Dimensions.get("window").width);
        return (
          <View style={{ height: 530, width: mWidth }}>
            <Calendar
              ref={(hdl) => this.mCalendar = hdl}
              visible={this.showCalendar}
              y={this.state.mDate.getFullYear()}
              m={this.state.mDate.getMonth() + 1}
              d={this.state.mDate.getDate()}
              interval={this.rollingInterval} // ???? 需要通过接口获取有效日期范围，并填充到dates里面去
              width={mWidth}
              onDateChanged={this.mSwitchOneDay}
              onCancel={() => { this.coverLayer.hide(); }}
              onAllVideo={this.mSwitchAllVideo}
              dates={this.mDatesWithData ?? null}  // 有内容的日期
            >
            </Calendar>
          </View>
        );
      },
      () => this.coverLayer.hide(),
      CoverLayer.popupMode.bottom
    );
  }


  mSwitchAllVideo = () => {
    this.mSwitchOneDay(null, true);
  }
  mSwitchOneDay = (items, isToday = false) => {
    console.log(`get selected: ${ items }, isToday? ${ isToday }`);
    this.mEvList.scrollTo({ animated: true,
      itemIndex: 0,
      viewOffset: 0,
      sectionIndex: 0 });
    this.mFirstTimeAfterSelectNewDate = true;
    let sltDate = new Date();
    if (!isToday) {
      sltDate = new Date(Date.parse(`${ items[0] }/${ items[1] }/${ items[4] }`));
    }
    this.mSltDay = true;
    this.mSltDayMore = false;
    this.mEvList.mSwitchSltDayMore(this.mSltDayMore);
    this.mSltDate = sltDate;
    this.setState({ mDate: sltDate, showLoading: true, selectDateStr: this.formatDateDes(this.mSltDate) });

    this.mCalendar.setDate(sltDate);
    this.mEvList.switchDay(sltDate, this.mSltDay);// remove all exist and refresh
    this.coverLayer.hide();

    this.playTheFirstEvent = true; // 切换日期后播放第一个视频
  }

  formatDateDes(date) {
    return this.addDateDes(dayjs(date), dayjs(date).format(LocalizedStrings["mmdd"]));
  }
  addDateDes(date, dStr) {
    if (Util.isToday(date)) {
      dStr = `${ dStr } | ${ LocalizedStrings['today'] }`;
    } else if (Util.isYestoday(date)) {
      dStr = `${ dStr } | ${ LocalizedStrings['yestoday'] }`;
    }
    return dStr;
  }

  renderEvFilterDlg() {
    if (this.state.showEvFilter && this.mEvFilter) {
      let idx = 0;
      let options = this.mEvFilter.map((itm) => { return { title: itm.name, icon: Util.getIconFromType(itm.key), index: idx++, key: itm.key }; });
      let selected = 0;
      for (let i = 0; i < this.mEvFilter.length; i++) {
        if (this.mEvFilter[i].key == this.state.filter) {
          selected = i;
        }
      }

      return (<ChoiceDlgWithIconSel
        animationType={'slide'}
        accessible={true}
        type={ChoiceDialog.TYPE.SINGLE}
        useNewType={true}
        showTitle={false}
        showSubtitle={false}

        visible={this.state.showEvFilter}
        selectedIndexArray={[selected]}
        options={options}
        onDismiss={() => {
          this.setState({ showEvFilter: false });
        }}
        dialogStyle={{ unlimitedHeightEnable: true, titleStyle: {
          marginTop: Platform.OS == 'ios' ? 15 : 5
        } } }
        onSelectEx={(aSel) => {
          console.log(this.tag, "sel", aSel, this.mEvFilter[aSel[0]]);
          this.setState({ filter: this.mEvFilter[aSel[0]].key, showEvFilter: false });
          this.playTheFirstEvent = true;
          let sValue = 1;
          switch (this.mEvFilter[aSel[0]].key) {
            case Event.KnownFace:
              sValue = 5;
              break;
            case Event.PeopleMotion:
              sValue = 3;
              break;
            case Event.BabyCry:
              sValue = 4;
              break;
            case Event.AI:
              sValue = 6;
              break;
            case Event.ObjectMotion:
              sValue = 2;
              break;
            default:
              sValue = 1;
          }
          console.log(this.tag, 'Monitoring_Motion_Status', sValue);
          TrackUtil.reportResultEvent('Monitoring_Motion_Status', 'type', sValue);
          TrackUtil.newOneTrack(['click'], { item_type: 'button', item_position: aSel[0], item_name: this.mEvFilter[aSel[0]].key });
          this.statEvFilterDialogExopseDone = false;
        }}
        buttons={
          [
            {
              text: LocalizedStrings['action_cancle'],
              callback: () => {
                this.setState({ showEvFilter: false });
                this.statEvFilterDialogExopseDone = false;
              },
              colorType: "grayLayerBlack"
            }
          ]
        }
      />);
    }
  }

  // 通过后端获取开关信息
  _getSetting() {
    AlarmUtil.getAlarmConfig().then((res) => {
      if (res.code != 0) {
        console.log("getdailyStorySwitch:", JSON.stringify(-1));
        Toast.fail('c_get_fail');
        return;
      }
      CameraConfig.dailyStorySwitch = res.data.dailyStorySwitch;
      this.setState({
        dailyStorySwitch: res.data.dailyStorySwitch // 没有实际使用，只是为了刷新页面
      });
      console.log('先看看开关的状态:', res.data.dailyStorySwitch); // 开关为true
    }).catch((err) => {
      console.log("getdailyStorySwitch:", JSON.stringify(err));
      Toast.fail('c_get_fail', err);
    });
  }

  async _getCloudTips() { // 从plato获取tips
    let targetItem = await CloudAdvertising.getSvlPageTips();
    if (targetItem) {
      if (!this.cloudTipsItem || targetItem.shortKey != this.cloudTipsItem.shortKey) {
        this.cloudTipsItem = targetItem;
        LogUtil.logOnAll('AlarmVideoUI', 'platoTips', `${ JSON.stringify(targetItem) }`);
        this.setState({ showBuyCloudVideoTip: true });
      }
    } else {
      this.cloudTipsItem = null;
      this.setState({ showBuyCloudVideoTip: false });
    }
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BaseStyles.mainBg.backgroundColor
  },
  fullScreenContainer: {
    width: '100%',
    height: '100%',
    backgroundColor: 'black'
  },

  player: {
    width: "100%",
    height: 9 * SCREEN_WIDTH / 16,
    backgroundColor: 'black'
  },
  fullscreenPlayer: {
    width: "100%",
    height: "100%"
  },

  playerToolbar: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    paddingHorizontal: 5
  },
  desc: {
    height: 50,
    justifyContent: 'center',
    paddingHorizontal: 20,
    backgroundColor: 'white',
    marginBottom: 10
  }
});
