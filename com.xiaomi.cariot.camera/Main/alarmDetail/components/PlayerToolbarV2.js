import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  Image,
  TouchableOpacity
} from 'react-native';
import Slider from "react-native-slider";
import Util from "../../util2/Util";
import ImageButton from "miot/ui/ImageButton";
import { DescriptionConstants } from '../../Constants';
import TrackUtil from '../../util/TrackUtil';
import {dynamicColor} from 'miot/ui/Style';
import MHLottieSpeedToolButton, {
  MHLottieSpeedToolBtnDisplayState
} from "../../ui/animation/lottie-view/MHLottieSpeedToolButton";
import M<PERSON>ottieAudioToolButton, {
  MHLottieAudioToolBtnDisplayState
} from "../../ui/animation/lottie-view/MHLottieAudioToolButton";
import MHLottieFullScreenToolButton, {
  MHLottieFullScreenToolBtnDisplayState
} from "../../ui/animation/lottie-view/MHLottieFullScreenToolButton";

const TAG = "AlarmPlayerToolBar";
const SliderMin = 1;
const SliderMax = 100;
export default class PlayerToolbarV2 extends React.Component {
  constructor(props) {
    super(props);

    let dur = this.props.duration;
    let cur = this.props.startTime;
    let isMuteValue = this.props.isMute == null ? true : this.props.isMute;
    let percent = cur / Math.max(1, dur);
    this.state = {
      isPlaying: this.props.isPlaying,
      isMute: isMuteValue,
      isFullscreen: this.props.isFullscreen,
      curTime: cur,
      progress: percent * (SliderMax - SliderMin) + SliderMin,
      duration: dur
    };
    this.mSeek = -1;
    this.showSnapShot = Util.isShowSnapShop();
  }

  componentDidUpdate() {
    if (this.props.isMute != this.state.isMute) {
      console.log("componentDidUpdate-=-=-=-", this.state.isMute);
      let isMuteValue = this.props.isMute == null ? true : this.props.isMute;
      this.setState({ isMute: isMuteValue });
    }
  }

  seek(pos) {
    this.setState({ progress: pos });
  }

  updateOrientation(isFullscreen) {
    this.setState({isFullscreen: isFullscreen});
  }
  handlePlayPressed() {
    this.setState({
      isPlaying: !this.state.isPlaying
    });
    if (this.props.playPressed) {
      this.props.playPressed(!this.state.isPlaying);
    }
  }

  handleMutePressed() {
    if (!this.state.isMute) {
      TrackUtil.reportClickEvent("Playback_OpenVolume_ClickNum");
    }
    if (this.props.isFastPlay) {
      return;
    }
    
    this.setState({
      isMute: !this.state.isMute
    });
    if (this.props.mutePressed) {
      this.props.mutePressed(!this.state.isMute);
    }
  }

  handleFastPlayProcessed() {
    if (this.props.fastPlayPressed) {
      this.props.fastPlayPressed(!this.props.isFastPlay);
    }

  }
  handleFullscreenPressed() {
    this.setState({
      isFullscreen: !this.state.isFullscreen
    });
    if (this.props.fullscreenPressed) {
      this.props.fullscreenPressed(!this.state.isFullscreen);
    }
  }

  updateDuration(duration) {
    this.setState({ duration: duration });
  }

  updatePlayState(aPlaying) {
    this.setState({ isPlaying: aPlaying });
  }

  updateCurTime(curTime, newDuration = -1) {
    if (newDuration != -1 && newDuration != this.state.duration) {
      this.setState({ duration: newDuration });
    }
    if (curTime < 0) {
      this.setState({
        isPlaying: false
      });
    }
    if (this.mSeek < 0) {
      if (curTime < 0) {
        this.setState({
          progress: 0,
          curTime: 0
        });
      } else {
        if (this.state.duration > 0) {
          let percent = Math.min(1.0, curTime / Math.max(1, this.state.duration));
          // console.log(TAG, "updateCurTime", percent, ":",curTime, "~",this.state.duration);
          this.setState({
            progress: percent * (SliderMax - SliderMin) + SliderMin,
            curTime: curTime
          });
        } else {
          console.log(TAG, "ignore time update when loading not finish");
        }
      }
    }
  }

  textFromDuration(duration) {
    let mStr = "00";
    let sStr = "00";
    if (duration) {
      mStr = Util.zeroPad(Math.floor(duration / 60), 10);
      sStr = Util.zeroPad(Math.floor(duration % 60), 10);
    }
    return `${ mStr }:${ sStr }`;
  }

  handleSeekComplete() {
    if (this.mSeek !== -1) {
      this.props.onSeeked((this.mSeek - SliderMin) / (SliderMax - SliderMin));
      this.mSeek = -1;
    }
  }

  handleSeek(aPos) {
    this.mSeek = aPos;
    let ct = this.state.duration * (this.mSeek - SliderMin) / (SliderMax - SliderMin);
    this.setState({ curTime: ct });

  }

  setMute(mute = true) {
    this.setState({ isMute: mute });
  }

  setPlay(playing) {
    this.setState({
      isPlaying: playing
    });
  }

  render() {
    // console.log(TAG, "render this.state.isPlaying", this.state.isPlaying, "this.props.startTime", this.props.startTime);
    let muteSource = require('../../../resources2/images/icon_volume_mute_w.png');
    let playSource = this.state.isPlaying ? require('../../../resources2/images/icon_camera_pause.png') : require('../../../resources2/images/icon_camera_play.png');
    if (!this.state.isMute) {
      muteSource = require('../../../resources2/images/icon_camera_unmute_playback.png');
    }
    let fastPlaySource = this.props.isFastPlay ? require('../../../Resources/Images/X2_nor.png') : require('../../../Resources/Images/X1_nor.png');
    let cur = this.state.curTime;
    let progress = this.state.progress;
    if (this.state.duration == 0) {
      cur = 0;
      progress = 0;
    }
    let curTime = this.textFromDuration(cur);
    let duration = this.textFromDuration(this.state.duration);
    let speedDisplayState = this.props.isFastPlay ? MHLottieSpeedToolBtnDisplayState.X2:MHLottieSpeedToolBtnDisplayState.X1;
    // switch (this.state.speed) {
    //   case 1:
    //     speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
    //     break;
    //   case 2:
    //     speedDisplayState = MHLottieSpeedToolBtnDisplayState.X2;
    //     break;
    //   case 4:
    //     speedDisplayState = MHLottieSpeedToolBtnDisplayState.X4;
    //     break;
    //   case 16:
    //     speedDisplayState = MHLottieSpeedToolBtnDisplayState.X16;
    //     break;
    //   default:
    //     speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
    //     break;
    // }
    return (
      <View style={[styles.container,this.state.isFullscreen?{marginHorizontal: 24, paddingBottom: 10}:null]}>
        <TouchableOpacity
          accessibilityLabel={this.state.isPlaying ? DescriptionConstants.yc_5 : DescriptionConstants.yc_16}
          style={styles.playButton} onPress={() => this.handlePlayPressed()}
          accessible={true}
          accessibilityState={{
            selected:this.state.isPlaying
          }}
          testID={this.state.isPlaying ?'1':'0'}
          >
          <Image
            source={playSource}
            style={styles.img}
          >
          </Image>
        </TouchableOpacity>
        <Text
          style={styles.timeLabel}
          accessibilityLabel={DescriptionConstants.yc_6 + "00:" + curTime}
        >
          {curTime}
        </Text>
        <Slider
          disabled={!(this.state.duration > 0)}
          trackStyle={{ height: 1 }}
          style={{ flexGrow: 1, height: 20, marginLeft: 3, marginRight: 3 }}
          maximumValue={SliderMax}
          minimumValue={SliderMin}
          step={1}
          onSlidingComplete={() => this.handleSeekComplete()}
          onValueChange={(aPos) => this.handleSeek(aPos)}
          minimumTrackTintColor={"#32BAC0"}
          maximumTrackTintColor={"rgba(255,255,255,0.2)"}
          value={progress > SliderMax ? SliderMax : progress}
          thumbTintColor={this.state.duration > 0 ? dynamicColor('#fff','#fff') : "gray"}
          accessible={true}
          accessibilityLabel={!this.state.isFullscreen ? DescriptionConstants.yc_7 : DescriptionConstants.yc_18}
         
        />
        <Text
          style={styles.timeLabel}
          accessibilityLabel={ DescriptionConstants.yc_8 + "00:" + duration}

        >
          {duration}
        </Text>
        {this.state.isFullscreen || !this.showSnapShot ? null : <TouchableOpacity
          style={styles.playButton} onPress={() => {
            this.props.snapShotPressed();
          }}
          accessible={true}>
          <Image
            source={require("../../../Resources/Images/icon_snap_short_toolbar.png")}
            style={styles.img}
          >
          </Image>
        </TouchableOpacity>}
        {this.state.isFullscreen?null:<MHLottieSpeedToolButton
          style={styles.img}

          onPress={() => {
            this.handleFastPlayProcessed()
          }}

          displayState={speedDisplayState}
          landscape={true}

          disabled={this.state.isRecording}
          accessible={true}
          accessibilityLabel={DescriptionConstants.rp_25.replace('1',this.state.speed)}
          accessibilityState={{
            disabled:this.state.isRecording
          }}
          testId={speedDisplayState}
        /> }

        {this.state.isFullscreen?null:<MHLottieAudioToolButton
          style={styles.img}

          onPress={() => {
            this.handleMutePressed()
          }}

          displayState={this.state.isMute ? MHLottieAudioToolBtnDisplayState.MUTED : MHLottieAudioToolBtnDisplayState.NORMAL}
          landscape={this.state.fullScreen}
          disabled={this.props.isFastPlay}
          accessibilityLabel={this.state.isMute ? DescriptionConstants.rp_3 : DescriptionConstants.rp_24}

        /> }

        {this.state.isFullscreen?null:<MHLottieFullScreenToolButton
          style={styles.img}

          onPress={() => {
            this.handleFullscreenPressed();
          }}

          displayState={MHLottieFullScreenToolBtnDisplayState.NORMAL}
          accessibilityLabel={DescriptionConstants.rp_4}

        /> }


      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  playButton: {
    width: 40,
    height: 40,
    alignItems: 'center'
  },
  timeLabel: {
    color: 'white',
    // width: 50
  },
  muteButton: {
    width: 40,
    height: 40
  },
  fullScreenButton: {
    width: 40,
    height: 40
  },
  img: {
    width: 40,
    height: 40
  },

});
