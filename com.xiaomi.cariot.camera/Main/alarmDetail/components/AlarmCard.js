import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  Image,
  TouchableOpacity
} from 'react-native';

export default class EventCard extends React.Component {

  handleCardPressed(item) {
    if (this.props.cardPressed) {
      this.props.cardPressed(item);
    }
  }

  render() {
    let imgStoreUrl = this.props.item.imgStoreUrl;
    let imgSource = imgStoreUrl == null ? null : { uri: `file://${ imgStoreUrl }` };
    let isHightlight = this.props.item.selected;
    let iconSource = null;
    if (this.props.item.type == "Bell") {
      iconSource = require("../../../../resources/images/icon_alarm_bell.png");
    } else if (this.props.item.type == "Pass:Stay") {
      iconSource = require("../../../../resources/images/icon_alarm_stay.png");
    }
    return (
      <TouchableOpacity onPress = {() => { this.handleCardPressed(this.props.item); }}>
        <View style = {styles.container}>
          <Image style = {isHightlight ? [styles.hightlightImgView, { backgroundColor: "#EEEEEE" }] : [styles.imgView, { backgroundColor: "#EEEEEE" }]} source = {imgSource}/>
          <View style = {{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style = {styles.timeLabel}>{this.props.item.eventTime}</Text>
            {iconSource ? <Image style = {styles.icon} source = {iconSource}></Image> : null}
          </View>
        </View>
      </TouchableOpacity>
    );
  }
}

var styles = StyleSheet.create({
  container: {
    height: 100,
    width: 130,
    paddingRight: 10,
    alignItems: 'center'
  },
  imgView: {
    width: 120,
    height: 70,
    borderRadius: 5
  },
  hightlightImgView: {
    width: 120,
    height: 70,
    borderRadius: 5,
    borderWidth: 2,
    borderColor: '#5db8be'
  },
  timeLabel: {
    marginVertical: 10,
    fontSize: 15,
    color: 'gray'
  },
  icon: {
    width: 20,
    height: 20
  }
});
