import React from 'react';
import {
  StyleSheet,
  View,
  Image,
  TouchableOpacity
} from 'react-native';
import { <PERSON>ce, Host } from 'miot';
import ImageButton from "miot/ui/ImageButton";
import { DescriptionConstants } from '../../Constants';

export default class PlayerToolbar extends React.Component {
  constructor(props) {
    super(props);
  }
  componentDidMount() {
  }
  handleBackProcessed() {
    if (this.props.backProcessed) {
      this.props.backProcessed();
    }
  }
  handleSnapShotPressed() {
    if (this.props.snapshotPressed) {
      this.props.snapshotPressed();
    }
  }

  handleDownloadPressed() {
    if (this.props.downloadPressed) {
      this.props.downloadPressed();
    }
  }

  handleSharePressed() {
    if (this.props.sharePressed) {
      this.props.sharePressed();
    }
  }

  handleDeletePressed() {
    if (this.props.deletePressed) {
      this.props.deletePressed();
    }
  }

  render() {
    return (
      <View style={styles.container}>
        {/* { this.props.showBack ?
          <View style={styles.container1}>
            <TouchableOpacity
              style={styles.button}
              onPress={() => this.handleBackProcessed()}
              accessiilityLabel={!this.props.showBack ? DescriptionConstants.yc_1 : DescriptionConstants.yc_12}

            >
              <Image
                source={require('../../../Resources/Images/icon_cloud_play_back.png')}

              >

              </Image>
            </TouchableOpacity>
          </View> : null
        } */}

        <View style={styles.container2}>
          {
            !Device.isReadonlyShared && this.props.showSnapShot ?
              <TouchableOpacity
                onPress={() => this.handleSnapShotPressed()}
                accessibilityLabel={!this.props.showBack ? DescriptionConstants.yc_4 : DescriptionConstants.yc_15}
              >
                <Image
                  style={styles.button}
                  source={require('../../../Resources/Images/cloud_play_snapshot_normal.png')}
                ></Image>
              </TouchableOpacity>
              : null
          }
          {
            this.props.showShare ?
              <TouchableOpacity
                style={styles.button}
                onPress={() => this.handleSharePressed()}
                accessibilityLabel={ DescriptionConstants.lc_13 }
              >
                <Image
                  style={{width: 25, height:25}}
                  source={require('../../../resources2/images/icon_record_share_w.png')}
                />
              </TouchableOpacity> : null
          }
          {
            Device.isReadonlyShared ? null :
              <TouchableOpacity
                onPress={() => this.handleDownloadPressed()}
                accessibilityLabel={!this.props.showBack ? DescriptionConstants.yc_3 : DescriptionConstants.yc_14}

              >
                <Image
                  style={{ width: 28, height: 28 }}
                  source={require('../../../resources2/images/icon_record_download_w.png')}
                ></Image>
              </TouchableOpacity>
          }
          {
            Device.isReadonlyShared ? null :
              <TouchableOpacity
                style={styles.button}
                onPress={() => this.handleDeletePressed()}
                accessibilityLabel={!this.props.showBack ? DescriptionConstants.yc_2 : DescriptionConstants.yc_13}
              >
                <Image
                  style={{ width: 28, height: 28 }}
                  source={require('../../../resources2/images/icon_record_delete_w.png')}
                >
                </Image>
              </TouchableOpacity>
          }
        </View>
      </View>

    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center'
  },
  container1: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center'
  },
  container2: {
    flex: 2,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center'
  },
  button: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center'
  }
});