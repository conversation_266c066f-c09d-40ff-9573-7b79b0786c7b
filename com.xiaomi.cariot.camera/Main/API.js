import { Host, Service, Device } from 'miot';

export class _API {
  constructor() {
  }

  get _generalParams() {
    return {
      did: Device.deviceID,
      region: Host.locale.language.includes("en") ? "US" : "CN"
    };
  }

  _request(api, subDemain, post, params) {
    let startTime = new Date().getTime();
    let combinesParams = Object.assign({}, params, this._generalParams);
    return new Promise((resolve, reject) => {
      Service.callSmartHomeCameraAPI(api, subDemain, post, combinesParams)
        .then((res) => {
          let endTime = new Date().getTime();
          Service.smarthome.reportLog(Device.model, `API method:${ api } param:${ JSON.stringify(params) } cost time:${ endTime - startTime }`);
          resolve(typeof res === 'string' ? JSON.parse(res) : res);
        })
        .catch((e) => {
          let endTime = new Date().getTime();
          Service.smarthome.reportLog(Device.model, `API method:${ api } param:${ JSON.stringify(params) } result error:${ JSON.stringify(e) } cost time:${ endTime - startTime }`);
          console.log('❌request failed', post, api, subDemain, combinesParams, JSON.stringify(e));
          reject(e);
        });
    });
  }

  _request_ios(api, params) {
    let startTime = new Date().getTime();
    return new Promise((resolve, reject) => {
      Service.callSmartHomeAPI(api, params)
        .then((res) => {
          let endTime = new Date().getTime();
          Service.smarthome.reportLog(Device.model, `API method:${ api } param:${ JSON.stringify(params) } cost time:${ endTime - startTime }`);
          resolve(typeof res === 'string' ? JSON.parse(res) : res);
        })
        .catch((e) => {
          let endTime = new Date().getTime();
          Service.smarthome.reportLog(Device.model, `API method:${ api } param:${ JSON.stringify(params) } result error:${ JSON.stringify(e) } cost time:${ endTime - startTime }`);
          console.log('❌request failed', true, api, params, JSON.stringify(e));
          reject(e);
        });
    });
  }

  _requestString(api, subDemain, post, params) {
    if (Host.isIOS) {
      return this._request_ios(api, params);
    }
    let startTime = new Date().getTime();
    let combinesParams = JSON.stringify(params);
    return new Promise((resolve, reject) => {
      Service.callSmartHomeCameraAPIWithStringParam(api, subDemain, post, combinesParams)
        .then((res) => {
          let endTime = new Date().getTime();
          Service.smarthome.reportLog(Device.model, `API method:${ api } param:${ JSON.stringify(params) } cost time:${ endTime - startTime }`);
          resolve(typeof res === 'string' ? JSON.parse(res) : res);
        })
        .catch((e) => {
          let endTime = new Date().getTime();
          Service.smarthome.reportLog(Device.model, `API method:${ api } param:${ JSON.stringify(params) } result error:${ JSON.stringify(e) } cost time:${ endTime - startTime }`);
          console.log('❌request failed', post, api, subDemain, combinesParams, JSON.stringify(e));
          reject(e);
        });
    });
  }

  /**
   * @param {string} api 接口地址
   * @param {string} subDomain subDomain
   * @param {object} params 传入参数
   */
  get(api, subDemain, params = {}) {
    return this._request(api, subDemain, false, params);
  }

  /**
   * @param {string} api 接口地址
   * @param {string} subDomain subDomain
   * @param {object} params 传入参数
   */
  post(api, subDemain, params) {
    return this._request(api, subDemain, true, params);
  }

  /**
   * @param {string} api 接口地址
   * @param {string} subDomain subDomain
   * @param {object} params 传入参数
   */
  postString(api, subDemain, params) {
    return this._requestString(api, subDemain, true, params);
  }
}

const API = new _API();
export default API;
