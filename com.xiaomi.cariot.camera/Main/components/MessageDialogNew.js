import { AbstractDialog } from "mhui-rn";
import { Styles } from "miot/resources";
import { dynamicColor } from "miot/ui";
import {
  AccessibilityRoles,
  getAccessibilityConfig,
} from "miot/utils/accessibility-helper";
import React from "react";
import { StyleSheet, Text, View } from "react-native";
import MultiLineButton from "./MultiLineButton";
import { FontSecondary } from "miot/utils/fonts";

export default function MessageDialogNew(props) {
  const buttons = props.buttons;
  const renderButtonOne = () => {
    return <MultiLineButton title={buttons[0].text} sizeLevel="medium" onPress={buttons[0].callback} colorType={"blueLayerWhite"} />;
  };
  const renderButtonTwo = () => {
    return (
      <>
        <MultiLineButton style={{ marginRight: 8 }} title={buttons[0].text} sizeLevel="medium" onPress={buttons[0].callback}/>
        <MultiLineButton
          style={{ marginLeft: 8 }}
          title={buttons[1].text}
          sizeLevel="medium"
          numberOfLines={2}
          colorType={"blueLayerWhite"}
          onPress={buttons[1].callback}
        />
      </>
    );
  };
  return (
    <AbstractDialog
      useNewTheme
      visible={props.visible}
      title={props.title}
      onDismiss={props.onDismiss}
      buttons={[]}
    >
      <View style={[styles.dialogContainer, { paddingTop: 17 }]}>
        <Text
          numberOfLines={15}
          allowFontScaling={false}
          style={[
            styles.message,
            {
              color: dynamicColor("#333333", "#cccccc")
            }
          ]}
          {...getAccessibilityConfig({
            accessible: false,
            accessibilityRole: AccessibilityRoles.text
          })}
        >
          {props.message || ""}
        </Text>
      </View>
      <View style={[Styles.dialog.buttons, { paddingHorizontal: 40 }]}>
        {buttons.length == 1 ? renderButtonOne() : renderButtonTwo()}
      </View>
    </AbstractDialog>
  );
}
const styles = StyleSheet.create({
  dialogContainer: {
    paddingHorizontal: 40,
    paddingVertical: 28,
    borderTopEndRadius: 20,
    borderTopStartRadius: 20
  },
  message: {
    fontSize: 16,
    lineHeight: 22,
    ...FontSecondary
  }
});