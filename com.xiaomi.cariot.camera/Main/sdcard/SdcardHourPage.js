'use strict';

import React from 'react';
import { ActivityIndicator, BackHandler, View, Text, Image, StyleSheet, FlatList, Dimensions, TouchableOpacity, Platform, TouchableWithoutFeedback, PermissionsAndroid, NativeModules ,SafeAreaView} from 'react-native';

import { Device, Host, System } from 'miot';

import { MessageDialog } from "mhui-rn";

import Toast from '../components/Toast';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import MultiSelectableNavigationBar from '../ui/MultiSelectableNavigationBar';

import SdFileManager, { DownloadVideoState } from './util/SdFileManager';
import EventTypeConfig, { EVENT_TYPE } from './util/EventTypeConfig';
import CameraConfig, { IMG_DARKMODE_TINT } from '../util/CameraConfig';
import VersionUtil from '../util/VersionUtil';
import { array } from 'prop-types';
import CancelableProgressDialog from '../ui/CancelableProgressDialog';
import AlbumHelper from '../util/AlbumHelper';
import TrackUtil from '../util/TrackUtil';
import DldMgr from '../framework/DldMgr';
import SdcardEventLoader from '../framework/sdcard/SdcardEventLoader';
import resolve from 'miot/native/common/node/resolve';
import { DldStatus } from '../framework/EventLoaderInf';
import { CardWidth, CardHeight } from '../widget/EventGridCard';
// 这里是sdcard再次跳进去时间那里
import CommonMsgDialog from '../ui/CommonMsgDialog';
import { DarkMode } from 'miot/Device';
import Util from "../util2/Util";
import { DescriptionConstants } from '../Constants';
import { NavigationBar } from 'mhui-rn';
import dayjs from 'dayjs';
import CameraPlayer from '../util/CameraPlayer';
import LogUtil from "../util/LogUtil";
const TAG = "SdcardHourPage";
export default class SdcardHourPage extends React.Component {

  // static navigationOptions = ({ navigation }) => {
  //   let { titleProps } = navigation.state.params || {};
  //   // if (!titleProps) return { header: null };
  //   if (titleProps == null) {
  //     titleProps = {
  //       title: LocalizedStrings["all_playback_video"],
  //       isSelectAll: false,
  //       isSelectMode: false,
  //       disableSelect: true,
  //       onEnterSelectMode: () => {
  //         // this.setNavigation(false, true, false);
  //         // this.setState({ isSelectMode: true });
  //       },
  //       onExitSelectMode: () => {
  //         // this.onSelectAllChanged(false);// 将isSelected重置
  //         // this.setNavigation(false, false, false);
  //         // this.setState({ isSelectMode: false });
  //       },
  //       onSelectAll: () => {
  //         // this.onSelectAllChanged(true);
  //       },
  //       onBackPress: () => {
  //         navigation.goBack();
  //       },
  //       onUnselectAll: () => {
  //         // this.onSelectAllChanged(false);
  //       }
  //     };
  //   }

  //   return {
  //     header:
  //       <MultiSelectableNavigationBar
  //         {...titleProps}
  //       />
  //   };
  // };

  state = {
    isSelectMode: false,
    index: 0,
    isEmpty: true,
    sdcardFiles: [],
    dialogVisible: false,
    eventTypes: [],
    eventType: EventTypeConfig.SD_DEFAULT,
    isShowingSelector: false,
    showDownloadHint: false,
    showLoading: true,
    permissionRequestState: 0,
    isDelete: false,
    showPermissionDialog: false,
    isInSameLAN: true,  // 是否在同一局域网
    showNetworkDialog: false  // 是否显示网络提示弹窗
  };

  constructor(props) {
    super(props);
    this.dateTime = new Date();
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.isPageForeGround = true;
        this.setNavigation(false, false, false);
        this.setState({ index: 1 });

        this.dayStartTime = this.props.navigation.getParam("dayBeginTime");
        this.hourStartTime = this.props.navigation.getParam("hourBeginTime");

        this.hour = this.props.navigation.getParam("hour");
        this.tag = this.props.navigation.getParam("tag");// 年月日的形式
        this.deleteCb = this.props.navigation.getParam("deleteCb");

        this.dateTime.setTime(this.dayStartTime);
        let hour = this.hour;

        let monthDayStr = dayjs.unix(this.dayStartTime/1000).format(LocalizedStrings["mmdd"]);
        let hourStr = `${hour > 9 ? `${hour}` : `0${hour}`}:00`;
        this.title = monthDayStr + hourStr;

        this.updateSdcardFiles();

        self.enterPlaybackTime = new Date().getTime();
        // 视频下载回调。
        this.sdcardVideoDownload = DldMgr.addListener((status) => {
          console.log("download status:test", status);
          this.videoFileListener();
        });
        this.fileListListener = SdcardEventLoader.getInstance().addListener(() => {
          // this._bindFilesHandler();
          this._onGetData();
        });
        
        // 检查是否在同一局域网
        this.checkLocalNetwork();
      }
    );

    this.didBlurListener = this.props.navigation.addListener(
      'didBlur',
      () => {
        console.log("did blur");


        this.isPageForeGround = false;
        this.sdcardVideoDownload && this.sdcardVideoDownload.remove();
        this.sdcardVideoDownload = null;
        this.fileListListener && this.fileListListener.remove();
        this.fileListListener = null;
        
        // 移除网络监听
        this.networkStateListener && this.networkStateListener.remove();
        this.networkStateListener = null;

        let playbackTime = (new Date().getTime() - self.enterPlaybackTime) / 1000;
        TrackUtil.reportResultEvent("Camera_Playback_Time", "Time", playbackTime); // Camera_Playback_Time
      }
    );

    this.isSupportDownload = CameraConfig.shouldDownloadSdcardFile(Device.model);

    this.downloadingVideoTimestamps = [];
    this.shouldDownload = false;
    this.isSupportNewStorage = CameraConfig.shouldDisplayNewStorageManage(Device.model);

    if (this.isSupportNewStorage) {
      this.imgContainerStl = { width: CardWidth, height: CardHeight, marginLeft: 5, marginRight: 5 };
      this.imgStl = { width: CardWidth, height: CardHeight - 38 };
    } else {
      this.imgContainerStl = { width: CardWidth, height: 95, marginLeft: 3.5, marginRight: 3.5 };
      this.imgStl = { width: CardWidth, height: 65 };
    }
    this.selectCount = 0;
  }

  componentWillUnmount() {
    this.didFocusListener.remove();// 避免内存泄漏
    this.didBlurListener.remove();
    this.sdcardVideoDownload && this.sdcardVideoDownload.remove();
    this.sdcardVideoDownload = null;
    this.fileListListener && this.fileListListener.remove();
    this.fileListListener = null;

    this.timeoutLocalPing && clearTimeout(this.timeoutLocalPing);

    // 移除网络监听
    this.networkStateListener && this.networkStateListener.remove();
    this.networkStateListener = null;
    
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.setState = () => false;
  }
  
  // 检查是否在同一局域网
  checkLocalNetwork() {
    console.log("检查局域网状态");
    // 设置超时，防止 localPing 在非局域网内执行时间过长
    this.timeoutLocalPing && clearTimeout(this.timeoutLocalPing);
    this.timeoutLocalPing = setTimeout(() => {
      CameraConfig.isLocalNetwork = false;
      LogUtil.logOnAll(TAG, "checkLocalNetwork", this.state.isInSameLAN);
      if (this.state.isInSameLAN) {
        this.setState({ 
          isInSameLAN: false,
          showNetworkDialog: true
        });
      }
    }, 3000);
    
    Device.getDeviceWifi().localPing().then((response) => {
      console.log("localPing 结果:", response);
      this.timeoutLocalPing && clearTimeout(this.timeoutLocalPing);
      CameraConfig.isLocalNetwork = response;
      if (!response && this.isPageForeGround) {
        // 不在同一局域网，显示提示弹窗并暂停播放
        this.setState({
          isInSameLAN: false,
          showNetworkDialog: true
        });
      } else if (response && !this.state.isInSameLAN) {
        // 恢复到同一局域网
        this.setState({
          isInSameLAN: true,
          showNetworkDialog: false
        });
      }
    }).catch((err) => {
      console.log("localPing 错误", err);
      this.timeoutLocalPing && clearTimeout(this.timeoutLocalPing);
      if (this.isPageForeGround) {
        CameraConfig.isLocalNetwork = false;
        // 发生错误，认为不在同一局域网，显示提示弹窗
        this.setState({ 
          isInSameLAN: false,
          showNetworkDialog: true
        });
      }
    });
  }

  setNavigation(isSelectAll, isSelectMode, isDisableSelect) {
    console.log("cheng" + "setNavigation");
    if (Device.isReadonlyShared) {
      isDisableSelect = true;
    }
    let title = isSelectMode ? (this.selectCount <= 0 ? LocalizedStrings["action_select"] : LocalizedStrings["selected_count"].replace("%1$d", this.selectCount)) : this.title;

    this.props.navigation.setParams({

      title: title,

      left: [
        {
          key: isSelectMode ? NavigationBar.ICON.CLOSE : NavigationBar.ICON.BACK,
          onPress: () => {
            if (this.state.isSelectMode) {// 如果已经是选择模式，点左边 应该退出选择模式
              this.onSelectAllChanged(false);// 将isSelected重置
              this.setNavigation(false, false, false);
              this.setState({ isSelectMode: false });
            } else { // 不是选择模式 就退出吧
              this.props.navigation.goBack();
            }
          }
        }
      ],
      right: !isDisableSelect ?
        [

          {
            key: !isSelectMode ? NavigationBar.ICON.EDIT : (isSelectAll ? NavigationBar.ICON.SELECTED_ALL : NavigationBar.ICON.SELECT_ALL),
            onPress: () => {
              if (!this.state.isSelectMode) { //进入选择模式
                if (this.state.isShowingSelector) {
                  this.setState({ isShowingSelector: false });
                }
                this.setNavigation(false, true, false);
                this.setState({ isSelectMode: true });
              } else if (this.state.isSelectAll) { //退出全选模式
                this.onSelectAllChanged(false);
              } else { //进入全选模式
                // 就选择50个，产品们脑子有坑
                let selectedCount = 0;
                for (let file of this.state.sdcardFiles) {
                  if (file.isSelected) {
                    selectedCount++;
                  }
                }
                let allCount = this.state.sdcardFiles.length;
                let sdcardCopy = this.state.sdcardFiles;
                if (allCount > 50) {
                  let diff = 50 - selectedCount;
                  for (let i = 0; i < sdcardCopy.length && diff > 0; i++) {
                    if (!sdcardCopy[i].isSelected) {
                      diff--;
                      sdcardCopy[i].isSelected = true;
                    }
                  }
                  Toast.show("max_select_noti");
                  this.selectCount = 50;
                  this.setState({ sdcardFiles: this.state.sdcardFiles, isSelectAll: true });
                  this.setNavigation(true, this.state.isSelectMode, this.state.isEmpty);
                  return;
                }
                this.onSelectAllChanged(!this.state.isSelectAll);
              }
            }
          }
        ] : [],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });

  }

  videoFileListener = (timestamp, state) => {
  }

  // 渲染网络提示弹窗
  _renderNetworkDialog() {
    return (
      <MessageDialog
        message={LocalizedStrings["lan_is_different"]}
        buttons={[
          {
            text: LocalizedStrings["offline_divice_ok"],
            colorType: "grayLayerBlack",
            callback: () => {
              this.setState({ showNetworkDialog: false });
              this.props.navigation.goBack();
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showNetworkDialog: false });
          this.props.navigation.goBack();
        }}
        cancelable={true}
        visible={this.state.showNetworkDialog}
      />
    );
  }
  
  render() {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: "#ffffff" }}>
        
        {this._renderEventTypeView()}
        {this._renderBodySection()}
        {this._renderBottomSelectView()}
        {this._renderDialog()}
        {this._renderPermissionDialog()}
        {this._renderLoadingDialog()}
        {this._renderNetworkDialog()}
      </SafeAreaView>
    );
  }

  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 麦克风
    // 
    let message = "";
    if (this.state.permissionRequestState == 0) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_microphone"]);
    }
    return (
      <MessageDialog
        title={LocalizedStrings["tips"]}
        message={message}
        messageStyle={{
          fontSize: 14,
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );

  }

  _renderLoadingDialog() {
    return (
      <CancelableProgressDialog
        ref={(ref) => {
          this.cancelableProgressDialog = ref;
        }}
        visible={this.state.loading}
        onCancelPress={() => {
          this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
          this.shouldDownload = false;
        }}
        cancelText={LocalizedStrings["action_cancel"]}
        // loadingText={this.state.isDelete ? LocalizedStrings["c_setting"] : LocalizedStrings["action_downloading"]}
        loadingText={LocalizedStrings["c_setting"]}
        disableOutsideCancel={true}
      />
    );
  }

  _renderBodySection() {

    return (
      <View style={{ width: "100%", flex: 1 }}>
        {this._renderDayFiles()}

        {this._renderEmptyLayout()}
        {this._renderSelectorView()}
        {this._renderLoadingView()}


        {this._renderDownloadHint()}
      </View>
    );
  }

  _renderDownloadHint() {
    if (!this.state.showDownloadHint) {
      return null;
    }
    return (
      <TouchableOpacity style={{ position: "absolute", bottom: 0, minHeight: 40,width: "100%", backgroundColor: "#32BAC0", display: "flex", justifyContent: "center", alignItems: "center", paddingHorizontal: 10 }}
        onPress={() => {
          this.props.navigation.navigate("DldPage");
        }}
      >
        <Text
          style={{ color: "#ffffff", fontSize: 13, paddingHorizontal: 10 }}
        >
          {LocalizedStrings["download_hint"]}
        </Text>
      </TouchableOpacity>
    );
  }

  _renderSelectorView() {
    if (!this.state.isShowingSelector) {
      return;
    }
    return (
      <View style={{ position: "absolute", width: "100%", height: "100%" }}>


        
        <TouchableOpacity style={{ position: "absolute", width: "100%", height: "100%", backgroundColor: "#0000007f" }}
          onPress={() => {
            this.setState({ isShowingSelector: false });
          }}
        >
          {/* 这里是全部事件下面的 */}


        </TouchableOpacity>

        <FlatList
          style={{ position: "absolute", width: "100%" }}
          data={this.state.eventTypes}
          renderItem={({ item, index }) => this._renderSelectView(item, index)}
          keyExtractor={(item, index) => index}
          numColumns={2}
          scrollEnabled={false}
        ></FlatList>
      </View>


    );

  }

  _renderSelectView(item, index) {
    let screenWidth = Dimensions.get('window').width;
    let containerWidth = (screenWidth) / 2;
    return (

      <View style={{ width: containerWidth, height: 52, padding: 5, display: "flex", flexDirection: "row", justifyContent: "center", alignItems: "center", backgroundColor: "#ffffff" }}>

        <TouchableOpacity style={[{ height: 43, borderRadius: 5, flexGrow: 1, justifyContent: "center", alignItems: "center" }, (item.eventDesc == null || item.eventDesc == "") ? null : { backgroundColor: "#f6f6f6" }]}
          onPress={() => {
            if (item.eventDesc == null || item.eventDesc == "") {
              this.setState({ isShowingSelector: false });
              return;
            }
            this.setState(() => { return { isShowingSelector: false, eventType: item.eventType }; },
              () => {
                this._onGetData();
              });
          }}
          activeOpacity={1}
          accessible={true}
        >
          <Text style={{ fontSize: 13, color: "black" }}>
            {item.eventDesc}
          </Text>
        </TouchableOpacity>
      </View>

    );

  }

  _renderDialog() {
    let title = this.state.isDelete ? LocalizedStrings["delete_title"] : LocalizedStrings["save_title"];
    let btn = this.state.isDelete ? LocalizedStrings["delete_confirm"] : LocalizedStrings["action_confirm"];
    return (
      <MessageDialog 
        message={title}
        // title={LocalizedStrings["tips"]}
        messageStyle={
          {
            textAlign: 'center'
          }
        }
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              console.log('onCancel');
              this.setState({ dialogVisible: false, isDelete: false });
            }
          },
          {
            text: btn,
            callback: () => {
              if (this.state.isDelete) {
                this.onConfirmDelete();
              } else {
                this.onConfirmSave();
              }
              this.setState({ dialogVisible: false, isDelete: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ dialogVisible: false });
        }}
        cancelable={true}
        visible={this.state.dialogVisible} />
    );
  }

  _renderEventTypeView() {
    if (!CameraConfig.shouldDisplayEventType(Device.model)) {
      return null;
    }

    let eventType = this.state.eventType;
    let isShowingSelector = this.state.isShowingSelector;
    let color = isShowingSelector ? "#32BAC0" : "#000000";
    let drawable = this.state.isShowingSelector ? require("../../Resources/Images/icon_select_up_sd.png") : require("../../Resources/Images/icon_select_down_sd.png");
    let desc = "";
    if (eventType == EventTypeConfig.SD_DEFAULT) {
      desc = "event_type_all";// all
    } else if (eventType == EventTypeConfig.SD_FACE) {
      desc = "face_desc";
    } else if (eventType == EventTypeConfig.SD_OBJECT_MOTION) {
      desc = "object_move_desc";
    } else if (eventType == EventTypeConfig.SD_PEOPLE_MOTION) {
      desc = "people_move_desc";
    } else if (eventType == EventTypeConfig.SD_CAT) {
      desc = "pet_desc";// 宠物
    } else if (eventType == EventTypeConfig.SD_BABY_CRY) {
      desc = "baby_cry_desc";
    } else if (eventType == EventTypeConfig.SD_CAMERA_CALLING) {
      desc = CameraConfig.isSupportVideoCall(Device.model) ? 'voice_video_call' : "one_key_call_event_name";
    } else if (eventType == EventTypeConfig.SD_LOUDER_SOUND) {
      desc = "loud_desc";
    } else if (eventType == EventTypeConfig.SD_CHILD) {
      desc = "child_desc";
    } else if (eventType == EventTypeConfig.SD_IGNORE_EVENT) {
      desc = "ignore_event";
    }

    return (
      <TouchableOpacity style={[{ width: "100%", height: 45, flexDirection: "row", display: "flex", justifyContent: "center", alignItems: "center" }, this.state.isShowingSelector ? { backgroundColor: "#f6f6f6" } : { borderColor: "#00000055", borderTopWidth: 0.5, borderBottomWidth: 0.5 }]}
        onPress={() => {
          if (this.state.isSelectMode) { // 选择模式的时候点击该按钮不响应点击
            return;
          }
          this.setState({ isShowingSelector: !isShowingSelector });
        }}
        accessibilityState={{
          selected:this.state.isShowingSelector
        }}
        accessible={true}

      >
        <Text style={{ color: color, fontSize: 14 }}>
          {LocalizedStrings[desc]}
        </Text>

        <Image
          style={{ width: 20, height: 20 }}
          source={drawable}
        >

        </Image>
      </TouchableOpacity>
    );
  }

  _renderBottomSelectView() {
    if (!this.state.isSelectMode) {
      return;
    }

    return (
      <View style={{ width: "100%", height: 69, borderTopColor: "#e5e5e5", borderTopWidth: 1, display: "flex", flexDirection: "row", justifyContent: "center", alignItems: "center" }}>
        <TouchableOpacity
          style={{ width: 90, display: "flex", alignItems: "center" }}
          onPress={() => { this.onPressSave(); }}
          accessibilityLabel={DescriptionConstants.hk_2_9}
        >
          <Image
            style={{ width: 25, height: 25 }}
            source={Util.isDark() ? require("../../resources2/images/icon_videorecord_download_w.png") : require("../../resources2/images/icon_videorecord_download_b.png")} />
          <Text
            style={{ color: "#000000", fontSize: 11 }}
          >
            {LocalizedStrings["save_files"]}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{ marginLeft: 30, width: 90, display: "flex", alignItems: "center" }}
          onPress={() => { this.onPressDelete(); }}
          accessibilityLabel={DescriptionConstants.hk_2_10}
        >
          <Image
            style={{ width: 25, height: 25 }}
            source={Util.isDark() ? require("../../resources2/images/icon_videorecord_delete_w.png") : require("../../resources2/images/icon_videorecord_delete_b.png")} />
          <Text
            style={{ color: "#000000", fontSize: 11 }}
          >
            {LocalizedStrings["delete_files"]}
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  _renderDayFiles() {
    if (this.state.isEmpty) {
      return null;
    }
    return (
      <View style={{ width: "100%", flex: 1 }}>
        <FlatList
          style={{ paddingTop: 22, flex: 1, paddingHorizontal: 16, borderTopColor: "#e5e5e5", borderTopWidth: 0.5, width: "100%" }}
          data={this.state.sdcardFiles}
          renderItem={({ item, index }) => this._renderDayFile(item, index)}
          numColumns={3}
          ListFooterComponent={<View style={{ height: 20 }}></View>}
          keyExtractor={(item, index) => index}
        />
      </View>

    );

  }

  _renderDayFile(item, index) {
    let marginHorizontal = 3.5;
    let screenWidth = self.windowWidth;
    let containerWidth = (screenWidth - 33 - marginHorizontal * 6) / 3;
    this.dateTime.setTime(item.startTime);
    let hour = this.dateTime.getHours();
    let minutes = this.dateTime.getMinutes();
    this.dateTime.setTime(item.endTime);
    let endHour = this.dateTime.getHours();
    let endMminutes = this.dateTime.getMinutes();
    let startTime = `${hour > 9 ? `${hour}` : `0${hour}`}:${minutes > 9 ? `${minutes}` : `0${minutes}`}`;
    let endTime = `${endHour > 9 ? `${endHour}` : `0${endHour}`}:${endMminutes > 9 ? `${endMminutes}` : `0${endMminutes}`}`;
    let path = SdFileManager.getInstance().getImageFilePath(item.startTime);
    let source = (path == null) ? null : ({ uri: `file://${Host.file.storageBasePath}${path}` });
    return (
      <TouchableOpacity
        style={[{ width: containerWidth, height: 95, paddingBottom: 10, marginLeft: 3.5, marginRight: 3.5 }, this.imgContainerStl]}
        onPress={() => this._onPressVideoFileView(index)}
      >

        <View style={[{ width: "100%", height: 60, marginBottom: 3, position: "relative", borderRadius: 5, resizeMode: "stretch", backgroundColor: "#EEEEEE" }, this.imgStl]} >
          <Image style={{ width: "100%", height: "100%", borderRadius: 5, resizeMode: "stretch" }}
            source={source}
            accessibilityLabel={DescriptionConstants.hk_2_6}
          >
          </Image>
          {/*<Image*/}
          {/*  source={require("../../resources2/images/shelter.png")}*/}
          {/*  style={[styles.shelter]} />*/}
          {
            this.state.isSelectMode ?
              <Image
                accessibilityLabel={item.isSelected ? DescriptionConstants.selected: DescriptionConstants.unSelected}
                style={{ width: 20, height: 20, position: "absolute", bottom: 4, right: 4 }}
                source={item.isSelected ? require("../../Resources/Images/icon_selected.png") : require("../../Resources/Images/icon_unselected.png")}
              /> :
              null
          }
          {
            item.save == 1 ?
              <Image
                style={{ width: 16, height: 16, position: "absolute", bottom: 4, left: 4 }}
                source={require("../../Resources/Images/icon_lock.png")}
              />
              :
              null
          }
        </View>
        <View style={{ display: "flex", flexDirection: "row", alignItems: "center" }}
          accessibilityLabel={ DescriptionConstants.rp_67 + `${startTime}` + DescriptionConstants.rp_66 +`${ endTime }` }
        >
          {this._renderFileFlagIcons(item)}

          <Text style={{ fontSize: 11, color: "#000000" }}
          >
            {` ${startTime}-${endTime} `}

          </Text>

        </View>


      </TouchableOpacity>
    );
  }

  _renderFileFlagIcons(item) {
    let eventType = item.eventType;
    if (eventType < EventTypeConfig.SD_OBJECT_MOTION) {
      return;
    }


    let source = null;
    if (eventType == EventTypeConfig.SD_PEOPLE_MOTION) {
      source = require("../../Resources/Images/icon_event_type_people_run.png");
    } else if (eventType == EventTypeConfig.SD_BABY_CRY) {
      source = require("../../Resources/Images/icon_event_type_baby_cry.png");
    } else if (eventType == EventTypeConfig.SD_DOG || eventType == EventTypeConfig.SD_CAT) {
      source = require("../../Resources/Images/icon_event_type_pet.png");
    } else if (eventType == EventTypeConfig.SD_FACE) {
      source = require("../../Resources/Images/icon_event_type_unknown_people.png");
    } else if (eventType == EventTypeConfig.SD_KNOWN_FACE) {
      source = require("../../Resources/Images/icon_event_type_known_people.png");
    } else if (eventType == EventTypeConfig.SD_CAMERA_CALLING) {
      source = require("../../Resources/Images/icon_event_type_camera_calling.png");
    } else if (eventType == EventTypeConfig.SD_LOUDER_SOUND) {
      source = require("../../Resources/Images/icon_event_type_louder.png");
    } else if (eventType == EventTypeConfig.SD_OBJECT_MOTION) {
      source = require("../../Resources/Images/icon_event_type_object_motion.png");
    } else if (eventType == EventTypeConfig.SD_CHILD) {
      source = require("../../Resources/Images/icon_event_type_child.png");
    }
    if (source == null) {
      return null;
    }
    return (
      <Image style={{ width: 14, height: 14, marginLeft: 5 }}
        source={source} />
    );
  }
  _renderLoadingView() {
    if (!this.state.showLoading) {
      return;
    }
    let isDark = DarkMode.getColorScheme() == "dark";
    return (
      <View
        style={{ position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <ActivityIndicator
          style={{ width: 54, height: 54 }}
          color={isDark ? "xm#ffffff" : "xm#000000"}
          size={"large"}

        />
        <Text
          style={{ marginTop: 10, fontSize: 12, color: "#000000" }}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );

  }
  _renderEmptyLayout() {
    if (this.state.isEmpty && !this.state.showLoading) {
      return (
        <View
          style={{ position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
          pointerEvents={"none"}
        >
          <Image
            source={require("../../Resources/Images/icon_camera_empty_files.png")}
            style={{ width: 79, height: 79 }}
          />
          <Text
            accessibilityLabel={DescriptionConstants.hk_2_12}
            style={{ fontSize: 14, color: "#808080" }}
          >
            {LocalizedStrings["no_files"]}
          </Text>
        </View>
      );
    } else {
      return null;
    }

  }

  componentDidMount() {
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    self.windowWidth = Dimensions.get("window").width;
    self.windowHeight = Dimensions.get("window").height;
    if (self.windowHeight < self.windowWidth) {
      let sw = self.windowWidth;
      self.windowWidth = self.windowHeight;
      self.windowHeight = sw;
    }


    let eventTypes = [];
    eventTypes.push({ eventType: EventTypeConfig.SD_DEFAULT, eventDesc: LocalizedStrings["event_type_all"] });

    if (CameraConfig.displayIgnoreEvent(Device.model)) {
      eventTypes.push({ eventType: EventTypeConfig.SD_IGNORE_EVENT, eventDesc: LocalizedStrings["ignore_event"] })
    } else {
      if (CameraConfig.displayChild(Device.model)) {
        eventTypes.push({ eventType: EventTypeConfig.SD_CHILD, eventDesc: LocalizedStrings["child_desc"] });
      }
      if (CameraConfig.displayPetInTimeline(Device.model)) {
        eventTypes.push({ eventType: EventTypeConfig.SD_CAT, eventDesc: LocalizedStrings["pet_desc"] });
      }
    }

    if (CameraConfig.shouldDisplayEventType(Device.model)) {
      this.setState({ eventTypes: eventTypes });
    }
    
    // 添加网络监听
    this.networkStateListener = CameraPlayer.getInstance().addNetworkListener((state) => {
      console.log("network change", state);
      // 网络变化时重新检查局域网状态
      this.checkLocalNetwork();
    });
  }

  // android返回键处理
  onBackHandler = () => {
    if (!this.isPageForeGround) {
      return false;
    }
    if (this.state.isSelectMode) {
      this.onSelectAllChanged(false);
      this.setNavigation(false, false, false);
      this.setState({ isSelectMode: false });
      return true;
    } else {
      return false;// 不接管
    }
  }

  _onPressVideoFileView(index) {
    TrackUtil.reportClickEvent("Playback_VideoList_ClickNum");
    if (!this.state.isSelectMode) {
      // todo 跳到播放页去
      let hour = this.hour;
      let tag = this.tag;
      let startTime = this.state.sdcardFiles[index].startTime;
      this.props.navigation.navigate('SdcardPlayerPage', { tag: tag, hour: hour, startTime: startTime });
    } else {
      let sdcardFile = this.state.sdcardFiles[index];
      let selectedCount = 0;
      for (let file of this.state.sdcardFiles) {
        if (file.isSelected) {
          selectedCount++;
        }
      }
      if (selectedCount >= 50 && !sdcardFile.isSelected) {// 达到了50，再勾选没有勾选的 才会这样子
        Toast.fail('max_select_noti');
        return;
      }
      
      sdcardFile.isSelected = !sdcardFile.isSelected;
      if (sdcardFile.isSelected) {
        selectedCount++;
      } else {
        selectedCount--;
      }
      if (selectedCount == 0) {
        this.onSelectAllChanged(false);
      } else if (selectedCount == this.state.sdcardFiles.length) {
        this.onSelectAllChanged(true);
      } else {
        this.setState({ sdcardFiles: this.state.sdcardFiles });// 刷新页面 状态不要保留在ui控件里
        this.selectCount = selectedCount;
        // this.onSelectAllChanged(false);
        this.setState(() => { return { isSelectAll: false }}, () => { 
          this.setNavigation(this.state.isSelectAll, this.state.isSelectMode, false);
        });
      }

    }
  }


  onSelectAllChanged(isSelectAll) {
    this.selectCount = isSelectAll ? this.state.sdcardFiles.length : 0;
    this.setNavigation(isSelectAll, this.state.isSelectMode, this.state.isEmpty);
    this.setState({ index: isSelectAll ? 0 : 1 });
    for (let timeItem of this.state.sdcardFiles) {
      timeItem.isSelected = isSelectAll ? true : false;
    }
    this.setState({ sdcardFiles: this.state.sdcardFiles, isSelectAll: isSelectAll });
  }

  _onGetData() {
    // 当前选中的是哪个日期
    this.updateSdcardFiles();
  }

  updateSdcardFiles() {
    if (this.tag == null || this.hour < 0) {
      this.setNavigation(false, false, true);// 禁用选择功能
      this.setState({ isEmpty: true, isSelectMode: false });
      this.props.navigation.goBack();
      return;
    }

    let timeItemDays = SdFileManager.getInstance().getTimeItemDays();
    if (timeItemDays == null || timeItemDays.length <= 0) {
      this.setNavigation(false, false, true);// 禁用选择功能
      this.setState({ isEmpty: true, isSelectMode: false, isSelectAll: false });
      if (this.deleteCb) {
        this.deleteCb(this.tag, this.hour);
      }
      this.props.navigation.goBack();
      return;
    }
    let timeItemDay = null;
    for (let item of timeItemDays) {
      if (item.tag == this.tag) {
        timeItemDay = item;
        break;
      }
    }
    if (!this.state.isSelectMode) {
      this.onSelectAllChanged(false);
    }
    if (timeItemDay == null || timeItemDay.timeItemHourList == null || timeItemDay.timeItemHourList.length == 0) {
      this.setNavigation(false, false, true);// 禁用选择功能
      this.setState({ isEmpty: true, isSelectMode: false, isSelectAll: false });
      if (this.deleteCb) {
        this.deleteCb(this.tag, this.hour);
      }
      this.props.navigation.goBack();
      return;
    }

    let hourTimeItem = null;

    for (let timeItem of timeItemDay.timeItemHourList) {
      if (timeItem.hour == this.hour) { // 搞到小时的那个东西 （防止删除了头部元素 timesamp值跟之前保存的timeStart值不一样了）
        hourTimeItem = timeItem;
        break;
      }
    }
    if (hourTimeItem == null || hourTimeItem.timeItemList == null || hourTimeItem.timeItemList.length == 0) {
      this.setNavigation(false, false, true);// 禁用选择功能
      this.setState({ isEmpty: true, isSelectMode: false });
      if (this.deleteCb) {
        this.deleteCb(this.tag, this.hour);
      }
      this.props.navigation.goBack();
      return;
    }

    let timeItemList = [];
    for (let j = 0; j < hourTimeItem.timeItemList.length; j++) {
      let timeItem = hourTimeItem.timeItemList[j];
      for (let timeItemLocal of this.state.sdcardFiles) { // 遍历所有的正在展示的内容
        if (timeItem.startTime === timeItemLocal.startTime) {
          timeItem.isSelected = timeItemLocal.isSelected;
          break;
        }
      }
      if (this.state.eventType == EventTypeConfig.SD_DEFAULT) {
        timeItemList.push(timeItem);
      } else if ((this.state.eventType == EventTypeConfig.SD_CAT || this.state.eventType == EventTypeConfig.SD_DOG) && (timeItem.eventType == EventTypeConfig.SD_CAT || timeItem.eventType == EventTypeConfig.SD_DOG)) {
        timeItemList.push(timeItem);
      } else if (this.state.eventType == EventTypeConfig.SD_FACE && (timeItem.eventType == EventTypeConfig.SD_FACE || timeItem.eventType == EventTypeConfig.SD_KNOWN_FACE)) {
        timeItemList.push(timeItem);
      } else if (this.state.eventType == EventTypeConfig.SD_OBJECT_MOTION && (timeItem.eventType == EventTypeConfig.SD_OBJECT_MOTION)) {
        timeItemList.push(timeItem);
      } else if (this.state.eventType == EventTypeConfig.SD_PEOPLE_MOTION && (timeItem.eventType == EventTypeConfig.SD_PEOPLE_MOTION)) {
        timeItemList.push(timeItem);
      } else if (this.state.eventType == EventTypeConfig.SD_BABY_CRY && (timeItem.eventType == EventTypeConfig.SD_BABY_CRY)) {
        timeItemList.push(timeItem);
      } else if (this.state.eventType == EventTypeConfig.SD_CAMERA_CALLING && (timeItem.eventType == EventTypeConfig.SD_CAMERA_CALLING)) {
        timeItemList.push(timeItem);
      } else if (this.state.eventType == EventTypeConfig.SD_LOUDER_SOUND && (timeItem.eventType == EventTypeConfig.SD_LOUDER_SOUND)) {
        timeItemList.push(timeItem);
      } else if (this.state.eventType == EventTypeConfig.SD_CHILD && timeItem.eventType == EventTypeConfig.SD_CHILD) {
        timeItemList.push(timeItem);
      } else if (this.state.eventType == EventTypeConfig.SD_IGNORE_EVENT && timeItem.eventType == EventTypeConfig.SD_IGNORE_EVENT) {
        timeItemList.push(timeItem);
      }

    }
    if (timeItemList.length == 0) {
      this.setNavigation(false, false, true);// 禁用选择功能
      this.setState({ isEmpty: true, isSelectMode: false });
      return;
    }
    if (!this.state.isSelectMode) {
      this.onSelectAllChanged(false);// 去除全选
    }
    this.setNavigation(this.state.isSelectAll, this.state.isSelectMode, false);// 禁用选择功能
    // 倒序显示
    timeItemList.sort((a, b) => { return b.startTime - a.startTime; });
    this.setState({ sdcardFiles: timeItemList, isEmpty: false, showLoading:false, isSelectMode: this.state.isSelectMode });// 这里是有数据的  所以不用管isEmpty了。
    // 开始下载图片

    let timestampList = [];
    for (let sdcardFile of timeItemList) {
      let startTime = sdcardFile.startTime;
      let path = SdFileManager.getInstance().getImageFilePath(startTime);
      if (path == null) {
        timestampList.push(startTime);
      }
    }
    if (timestampList.length > 0) {
      this.downloadThumbs(timestampList);
    }
  }

  async downloadThumbs(timestampList) {
    if (timestampList.length > 0) {
      let lastNotifyTime = Date.now();
      for (let i = 0; i < timestampList.length; i++) {
        try {
          await SdcardEventLoader.getInstance().getThumb({ imgStoreId: timestampList[i] });
          if (Date.now() - lastNotifyTime < 1000) {
            continue;
          }
          lastNotifyTime = Date.now();
          // if (i % 5 == 0) { // 下载成功 通知一下。
            this.onReceiveFile(timestampList[i]);// 通知刷新咯。
          // }
        } catch (err) {

        }
      }
      this.onReceiveFile(timestampList[0]);// 下载完毕后，通知刷新。
    }
  }


  onReceiveFile = (timestamp, status) => {
    // checkTimestampRange
    let files = this.state.sdcardFiles;
    if (files == null || files.length == 0) {
      return;
    }
    // if (files[0].startTime <= timestamp && files[files.length - 1].startTime >= timestamp) { // 只有在这中间的才刷新
    //   this.setState({ index: (this.state.index > 100 ? 0 : this.state.index + 1) });// 刷新页面
    // }

    if (files[0].startTime >= timestamp && files[files.length - 1].startTime <= timestamp) { // 只有在这中间的才刷新
      this.setState({ index: (this.state.index > 100 ? 0 : this.state.index + 1) });// 刷新页面
    }
  }

  onPressSave() {
    console.log("onpresssave");
    let timeItems = [];
    for (let timeItem of this.state.sdcardFiles) { // 遍历所有的正在展示的内容
      if (timeItem.isSelected) {
        timeItems.push(timeItem);
      }
    }

    if (timeItems == null || timeItems.length == 0) {
      Toast.success("bottom_action_tip");

      console.log("select none return");
      return;
    }
    if (timeItems.length > 50) {
      Toast.fail("max_select_noti");
      return;
    }

    if (this.isSupportDownload) {
      console.log("start download file");
      if (!CameraConfig.isShowDownloadHint) {
        CameraConfig.isShowDownloadHint = true;
        Toast.success("playback_download_hint");
      }
      this._startDownload(timeItems);
      return;
    }
    for (let i = timeItems.length - 1; i >= 0; i--) {
      if (timeItems[i].save == 1) {
        timeItems.splice(i, 1);
      }
    }
    if (timeItems.length == 0) {
      Toast.success("bottom_save_tip");
      return;
    }
    this.setState({ dialogVisible: true, isDelete: false });

  }

  _startDownload(selectedTimeItems) {
    TrackUtil.reportClickEvent("Storage_MemoryCard_Download");
    this.setState((state) => {
      return {
        isSelectMode: false
      };
    }, () => { // 数据塞给listview了，渲染还需要时间吧，延迟一会再执行scroll
      this.onSelectAllChanged(false);
    });

    if (Platform.OS === "android") {
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          console.log("download file: granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this._realStartDownloadFiles(selectedTimeItems);
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            // Toast.success("camera_no_write_permission");
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
          console.log("permission failed");

          Toast.success("action_failed");
        });
    } else {
      // no ios's photos const use hardcode
      System.permission.request("photos").then((res) => {
        this._realStartDownloadFiles(selectedTimeItems);
      }).catch((error) => {
        // Toast.success("camera_no_write_permission");
        this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
      });
    }

  }

  _realStartDownloadFiles(timeItems) {
    // 这里先判断文件是否已经下载过了

    // then add into file

    // show loading view
    // this.setState({ loading: true });

    if (this.isSupportNewStorage) {
      let times = timeItems.map((item) => {
        let startTime = item.startTime;
        let localUrls = SdcardEventLoader.getInstance().getThumbUrlByImgStoreId(startTime);
        let path = localUrls.thumbUrl;
        let source = (path == null) ? null : (`${ Host.file.storageBasePath }${ path }`);
        return { duration: 60, fileId: startTime, mediaType: "sdcard", createTime: startTime, imgStoreUrl: source, videoUrl: localUrls.videoUrl, playCfg: { loader: SdcardEventLoader.getInstance() } };// 缩略图，下载路径，创建时间，fileId
      });
      this.setState({ showDownloadHint: true });
      setTimeout(() => {
        this.setState({ showDownloadHint: false });
      }, 5000);
      DldMgr.addDld(times, SdcardEventLoader.getInstance());
    } else {
      console.log("download file real begin");

      this.cancelableProgressDialog && this.cancelableProgressDialog.show();
      let times = timeItems.map((item) => {
        return item.startTime;
      });
      this.downloadingVideoTimestamps = times;
      this.shouldDownload = true;
      this.tryDownloadWithDirectWay();
    }

  }


  async tryDownloadWithDirectWay() {

    for (let i = 0; i < this.downloadingVideoTimestamps.length && this.shouldDownload; i++) {
      let timestamp = this.downloadingVideoTimestamps[i];
      try {
        await new Promise((resolve, reject) => {
          SdcardEventLoader.getInstance()
            .download({ fileId: timestamp }, null, {
              onDldProgress: (state) => {
                if (state == DldStatus.Complete) {
                  resolve();
                } else if (state == DldStatus.Err) {
                  reject();
                }
              }
            });
        });
        let { videoUrl } = SdcardEventLoader.getInstance().getThumbUrlByImgStoreId(timestamp);
        if (!this.shouldDownload) {
          break;
        }
        await AlbumHelper.saveToAlbum(videoUrl, true);

      } catch (err) {
        console.log(`download video and save error:${timestamp}`);
        Toast.fail("save_faild", err);
        this.cancelableProgressDialog.hide();
        this.shouldDownload = false;
        break;
      }

    }
    if (this.shouldDownload) {
      Toast.success("save_success");
      this.shouldDownload = false;
    }
    this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
  }

  onConfirmDelete() {
    let timeItems = [];
    for (let timeItem of this.state.sdcardFiles) { // 遍历所有的正在展示的内容
      if (timeItem.isSelected) {
        timeItems.push(timeItem);
      }
    }
    this.onSelectAllChanged(false);//退出全选
    this.setState({
      isSelectMode: false, isDelete: true
    });
    this.cancelableProgressDialog && this.cancelableProgressDialog.show();
    SdFileManager.getInstance().startDeleteFiles(timeItems)
      .then(() => {

        this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
        this._onGetData();
        Toast.success("delete_success");
        this.setState({
          isDelete: false
        });
      })
      .catch((err) => {
        this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
        Toast.fail("delete_failed", err);
        this.setState({
          isDelete: false
        });
      });

  }

  onPressDelete() {
    let timeItems = [];
    for (let timeItem of this.state.sdcardFiles) { // 遍历所有的正在展示的内容
      if (timeItem.isSelected) {
        timeItems.push(timeItem);
      }
    }
    if (timeItems == null || timeItems.length == 0) {
      Toast.success("bottom_action_tip");
      return;
    }
    if (timeItems.length > 50) {
      Toast.fail("max_select_noti");
      return;
    }
    this.setState({ dialogVisible: true, isDelete: true });
  }

  onConfirmSave() {
    let timeItems = [];
    for (let timeItem of this.state.sdcardFiles) { // 遍历所有的正在展示的内容
      if (timeItem.isSelected) {
        timeItems.push(timeItem);
      }
    }
    if (timeItems == null || timeItems.length == 0) {
      return;
    }
    for (let i = timeItems.length - 1; i >= 0; i--) {
      if (timeItems[i].save == 1) {
        timeItems.splice(i, 1);
      }
    }
    if (timeItems.length == 0) {
      return;
    }
    SdFileManager.getInstance().startSaveFiles(timeItems)
      .then(() => {

        this.setState((state) => {
          return {
            isSelectMode: false
          };
        }, () => {
          this.onSelectAllChanged(false);
          this._onGetData();
        });
        Toast.success("save_success");
      })
      .catch((err) => {
        Toast.fail("save_faild", err);
      });
  }
}

export const styles = StyleSheet.create({
  container: {
    backgroundColor: "#ffffff",
    width: '100%',
    height: '100%',
    display: "flex",
    flexWrap: "nowrap"
  },
  shelter: {
    height:40,
    width:'100%',
    position: 'absolute',
    bottom: 0,
    borderBottomLeftRadius: 9,
    borderBottomRightRadius: 9
  },
});
