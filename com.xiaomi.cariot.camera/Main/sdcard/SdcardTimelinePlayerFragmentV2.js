'use strict';

import React from 'react';
import { isIphoneX, getStatusBarHeight } from 'react-native-iphone-x-helper';
import {
  ScrollView,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  BackHandler,
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  PermissionsAndroid,
  Platform,
  DeviceEventEmitter,
  Dimensions,
  NativeModules,
  PanResponder,
  TouchableHighlight,
  Modal
} from 'react-native';

import LinearGradient from 'react-native-linear-gradient';
import { Device, Service, PackageEvent, Host, System, DarkMode } from 'miot';
import AlbumHelper from "../util/AlbumHelper";

import ImageButton from "miot/ui/ImageButton";

import Toast from '../components/Toast';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';

import SdFileManager from './util/SdFileManager';

import CameraRenderView from 'miot/ui/CameraRenderView';
import { MISSSampleRate, MISSDataBits, MISSAudioChannel } from 'miot/ui/CameraRenderView';
import CameraPlayer from '../util/CameraPlayer';
import { MISSCommand, MISSError } from "miot/service/miotcamera";
import Orientation from 'react-native-orientation';

import StorageKeys from '../StorageKeys';
import ScaleableTimelineView from '../ui/ScaleableTimelineView';
import VersionUtil from '../util/VersionUtil';
import CameraConfig from '../util/CameraConfig';
import TimeScaleView from '../ui/TimeScaleView';
import CenterTimeView from '../ui/CenterTImeView';
import TimeScaleView2_1 from '../ui/TimeScaleView2_1';
import TimeScaleView3, { SCROLLSTATE } from '../ui/TimeScaleView3';
import DeviceOfflineDialog from '../ui/DeviceOfflineDialog';
import NoNetworkDialog from '../ui/NoNetworkDialog';
import { EVENT_TYPE, EVENT_TYPE_COLOR } from './util/EventTypeConfig';
import StackNavigationInstance, { SD_CLOUD_STACK_NAVIGATION_ONBACK, SD_CLOUD_STACK_NAVIGATION_ONPAUSE, SD_CLOUD_STACK_NAVIGATION_ONRESUME } from '../StackNavigationInstance';
import th from 'miot/resources/strings/th';
import { AbstractDialog, ChoiceDialog } from 'mhui-rn';
import { MessageDialog } from "mhui-rn";
import RectAngleView from '../ui/RectAngleView';
import { PixelRatio } from 'react-native';
import LoadingView from '../ui/LoadingView';
import SdcardEventLoader from '../framework/sdcard/SdcardEventLoader';
import CommonMsgDialog from '../ui/CommonMsgDialog';

import MHLottieSnapToolButton, { MHLottieSnapToolBtnDisplayState } from '../ui/animation/lottie-view/MHLottieSnapToolButton';
import MHLottieRecordToolButton, { MHLottieRecordToolBtnDisplayState } from '../ui/animation/lottie-view/MHLottieRecordToolButton';
import MHLottieSpeedToolButton, { MHLottieSpeedToolBtnDisplayState } from '../ui/animation/lottie-view/MHLottieSpeedToolButton';
import MHLottieAudioToolButton, { MHLottieAudioToolBtnDisplayState } from '../ui/animation/lottie-view/MHLottieAudioToolButton';
import MHLottieFullScreenToolButton, { MHLottieFullScreenToolBtnDisplayState } from '../ui/animation/lottie-view/MHLottieFullScreenToolButton';

import MHLottieSnapLandscapeButton, { MHLottieSnapLandscapeBtnDisplayState } from '../ui/animation/lottie-view/MHLottieSnapLandscapeButton';
import MHLottieRecordLandscapeButton, { MHLottieRecordLandscapeBtnDisplayState } from '../ui/animation/lottie-view/MHLottieRecordLandscapeButton';
import OfflineHelper from '../util/OfflineHelper';
import RPC from '../util/RPC';
import StatusBarUtil from '../util/StatusBarUtil';
import { log } from 'miot/utils/fns';

import { DescriptionConstants } from '../Constants';
import SpecUtil from '../util/SpecUtil';
import TrackUtil from '../util/TrackUtil';
import LogUtil from '../util/LogUtil';
import Util, { EvArray, EvMap } from '../util2/Util';
import DldMgr from '../framework/DldMgr';
import { BaseStyles } from '../BasePage';
import dayjs from 'dayjs';
import Calendar from '../widget/Calendar';
import CoverLayer from '../widget/CoverLayer';
import { Event } from '../config/base/CfgConst';
import SdcardCloudTimelinePageV2 from './SdcardCloudTimelinePageV2';
import AlarmUtilV2 from "../util/AlarmUtilV2";
import SDCardSetting from '../setting/SDCardSetting';
import NoSdcardPage from '../setting/NoSdcardPage';
import { handlerOnceTapWithToast } from "../util/HandlerOnceTap";

const kIsCN = Util.isLanguageCN();
const winHeight = Dimensions.get('window').height;
let isDark = DarkMode.getColorScheme() == "dark";



const timelinePlaybackEndListenerName = "onTimelinePlaybackEnd";
const kRecordTimeCallbackName = "recordTimeCallback";
const TAG = "SdcardTimelinePlayerFragmentV2";
const kWindowHeight = Math.max(Dimensions.get("window").height, Dimensions.get("window").width);
const kWindowWidth = Math.min(Dimensions.get("window").height, Dimensions.get("window").width);
// const portraitVideoHeight = Number.parseInt(kWindowWidth / 1.78);
const portraitVideoHeight = kWindowWidth * 9 / 16;
const TW_WIDTH = 390;
const TW_HEIGHT = 844;
const TWP_WIDTH = 428;
const TWP_HEIGHT = 926;
const isIphone12 = (() => {
  if (Platform.OS === 'web') return false;
  return (
    Platform.OS === 'ios' &&
    ((TW_HEIGHT === kWindowHeight && TW_WIDTH === kWindowWidth))

  )
})()

const isIphone12P = (() => {
  if (Platform.OS === 'web') return false;
  return (
    Platform.OS === 'ios' &&
    ((TWP_HEIGHT === kWindowHeight && TWP_WIDTH === kWindowWidth))

  )
})()
export default class SdcardTimelinePlayerFragmentV2 extends React.Component {

  static navigationOptions = ({ navigation }) => {
    // if (true) {//不要导航条
    //   return null;
    // }

    let tabBarVisible = true;
    let param = navigation.state.params || {};
    if (param.isFullScreen) {
      tabBarVisible = false;
    }
    return {
      tabBarVisible
    };
  }

  state = {
    showEvFilter: false,
    curDate: new Date(), // 当前选中的日期
    isEmpty: true,
    sdcardFiles: [],
    showPlayToolBar: false,
    touchMovement: false,
    sdcardCode: -1,
    pstate: 0,
    error: 0,

    fullScreen: false,
    isMute: CameraConfig.getUnitMute(),
    isSleep: false,
    resolution: 0,
    speed: 1, // 倍速
    isPlaying: true,
    isRecording: false,

    screenshotVisiblity: false, // 截图是否可见

    showErrorView: false,
    showHighTemperatureView: CameraConfig.isDeviceTemperatureHigh,
    showLoadingView: false,
    showPoweroffView: false,
    showPauseView: false,
    showFastSpeedView: false,
    errTextString: "", // 错误提示文案

    screenshotPath: null,
    recordTimeSeconds: 0,

    // eventTypeFlags: (EVENT_TYPE.Default | EVENT_TYPE.Pet | EVENT_TYPE.ChildDetected),
    eventTypeFlags: (EVENT_TYPE.Default | EVENT_TYPE.IgnoreEvent),

    videoViewWidth: 0,
    videoViewHeight: 0,

    dialogVisibility: false,
    angle: 51,
    elevation: 51,
    videoScale: 1.0,
    angleViewShowScale: false,
    showCameraAngleView: false,

    permissionRequestState: 0,
    showPermissionDialog: false,
    lastOfflineTime: "",
    showMoreDlg: false,
    moreIconY: -1,
  };


  constructor(props) {
    super(props);
    this.isSupportPhysicalCover = CameraConfig.isSupportPhysicalCover(Device.model);
    this.timeIndicatorView = null;
    this.dateTime = new Date();
    this.isFirstReceiveFiles = true;
    this.isUserPause = false;
    this.isPlayEnd = false; //是否播放到末尾
    this.isPluginForeGround = true;// sdcard页面不存在跳到其他页面的情况
    this.isPageForeGround = true;
    this.isAppForeground = true;

    this.isParentPageBackground = false;
    this.firstInPage = true;
    //单独判断是否是V1 V3
    this.isCameraV1orV3 = CameraConfig.isCameraV1orV3(Device.model);
    this.moreIconY = -1;
    this.isVip = false;
    this.isFirstIn = true;
    this.enterSDPageTime = 0; // 记录进入SD卡界面的时间
    // see https://blog.csdn.net/u011068702/article/details/83218639
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来 这里监听的是tabNavigation的事件，不是stackNavigation的事件。
      'didFocus',
      () => {
        console.log("will focus");

        this.isPageForeGround = true;
        this.isPluginForeGround = true;
        this.isAppForeground = true;
        this.restoreOri();
        // 解决看家列表选择其他日期，播放后，点击更多视频，日期没切换到视频播放的日期
        if (SdcardCloudTimelinePageV2.toSDCardPageTime > 0) {
          this.setState({ curDate: new Date(SdcardCloudTimelinePageV2.toSDCardPageTime) });
        }
        this._onResume();
        if (this.isFirstIn) {
          this.isFirstIn = false;
        }
      }
    );
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'willBlur',
      () => {
        console.log(`${TAG} did blur`);

        this.isPageForeGround = false;
        this._onPause();
        this.cameraRenderView && this.cameraRenderView.hidesSurfaceView();
      }
    );


    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      LogUtil.logOnAll(TAG, "packageEvent.didResume: isPluginForeground:" + this.isPluginForeGround + " isPageForeround:" + this.isPageForeGround);
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }

      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.isAppForeground = true;// rnactivity调用了onresume
      this.restoreOri();
      this._onResume();
    });

    this.willPauseListener = PackageEvent.packageWillPause.addListener(() => {
      LogUtil.logOnAll(TAG, "packageEvent.didPause: isPluginForeground:" + this.isPluginForeGround + " isPageForeround:" + this.isPageForeGround);
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.isAppForeground = false;// rnactivity调用了onresume
      this._onPause();
    });

    if (Platform.OS == "ios") {
      this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {
        LogUtil.logOnAll(TAG, "packageEvent.willAppear: isPluginForeground:" + this.isPluginForeGround + "  isPageForeground:" + this.isPageForeGround);
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.isPluginForeGround = true;// rnactivity调用了onresume
        this._onResume();
      });

      this.willDisappearListener = PackageEvent.packageViewWillDisappearIOS.addListener(() => {

        LogUtil.logOnAll(TAG, "packageEvent.willDisappear: isPluginForeground:" + this.isPluginForeGround + "  isPageForeground:" + this.isPageForeGround);
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.toPortrait();// ios强制切换到竖屏去
        setTimeout(() => {
          this.isPluginForeGround = false;// rnactivity调用了onpause
          // console.log('testaaa', 'willDisappearListener', 'page: ', this.isPageForeGround, ' plugin: ', this.isPluginForeGround);
          this._onPause();

        }, 500);

      });
    } else {
      if (PackageEvent.packageWillStopAndroid) {
        this.willStopListener = PackageEvent.packageWillStopAndroid.addListener(() => {

          // this.cameraRenderView && this.cameraRenderView.hidesSurfaceView();

        });
      }

    }


    this.currentNetworkState = -1;
    this.isChangingNetwork = false;

    this.toStartTime = 0;// 暂存要播放的view的时间戳

    this.startTime = 0;
    this.endTime = 0;
    this.offset = 0;
    this.sessionId = 0;

    this.lastTimeItemEndTime = 0;// 从rdt捞过来的数据 的最后一个item的endtime

    this.isSetPlayTime = false;// 是否正在设置播放时间
    this.setPlayTimeMillis = 0;// 记录本次设置playback命令的时间
    this.lastTimestamp = 0;// 记录上次返回回调的时间戳
    // todo notify native side  whether in continue playback mode

    this.timelinePlaybackEndListener = DeviceEventEmitter.addListener(timelinePlaybackEndListenerName, () => {
      // 走到这里说明连续回放结束了
      console.log("时间轴模式下，从点播切换成了直播 要暂停camera");
      this.toSdcardEnd();// 停止播放
    });

    this.timelineView = null;
    this.cameraRenderView = null;
    this.scrollTimeout = null;
    this.scrollTimeout1 = null;
    this.isConnecting = false;
    this.mOri = "PORTRAIT";
    this.connRetry = 2;

    this.stackNavigationOnPauseListener = null;
    this.stackNavigationOnResumeListener = null;

    if (this.stackNavigationOnPauseListener == null) {
      this.stackNavigationOnPauseListener = DeviceEventEmitter.addListener(SD_CLOUD_STACK_NAVIGATION_ONPAUSE, () => {
        LogUtil.logOnAll(TAG, "sdcard timeline page receive parent onpause event:  ispageForeground:" + this.isPageForeGround);
        // 父类收到了pause事件，传给了子控件。
        if (!this.isPageForeGround) { // 只有页面处于前台的时候，这个属性才奏效
          return;
        }
        this.cameraRenderView && this.cameraRenderView.hidesSurfaceView();
        this.isParentPageBackground = true;
        this.isPageForeGround = false;
        console.log(TAG, "receive parents' onPause");
        this._onPause();// 暂停咯
      });
    }
    if (this.stackNavigationOnResumeListener == null) {
      this.stackNavigationOnResumeListener = DeviceEventEmitter.addListener(SD_CLOUD_STACK_NAVIGATION_ONRESUME, () => {
        LogUtil.logOnAll(TAG, "sdcard timeline page receive parent onresume event:  isParentPageBackground:" + this.isParentPageBackground);

        if (!this.isParentPageBackground) {
          return;
        }
        this.restoreOri();
        this.isParentPageBackground = false;
        this.isPageForeGround = true;
        this.isPluginForeGround = true;
        this.isAppForeground = true;
        this._onResume();
      });
    }

    if (this.stackNavigationOnbackListener == null) {
      this.stackNavigationOnbackListener = DeviceEventEmitter.addListener(SD_CLOUD_STACK_NAVIGATION_ONBACK, () => {
        this.cameraRenderView && this.cameraRenderView.hidesSurfaceView();

        this.isParentPageBackground = true;
        this.isPageForeGround = false;
        console.log(TAG, "receive parents' onBack");
        this._onPause();// 暂停咯
      });
    }

    this.selectedIndexArray = [0];
    this.displayCutoutTop = 0;
    Host.getPhoneScreenInfo()
      .then((result) => {
        this.displayCutoutTop = PixelRatio.roundToNearestPixel(result.displayCutoutTop / PixelRatio.get() || 0);
        if (isNaN(this.displayCutoutTop)) {
          this.displayCutoutTop = 0;
        }
        console.log(TAG, "result:", result);
      })
      .catch((error) => {

      });
    this.lastTimeItemStartTime = 0;
    this.timestampRequestSend = false;
    this.ignoreDataWarning = false;

    this.isInternationalServer = CameraConfig.getInternationalServerStatus();
    this.mLastScreenSize = Dimensions.get('window');
    Dimensions.addEventListener("change", this.dimensionListener);
    this.storedEvSelMap = {};
    this.EvSelMap = {};
    this.mEvFilter = [];
    this.initEvFilter();
    if (SdcardCloudTimelinePageV2.fromPush) {
      CameraPlayer.getInstance().getSdcardStatus().then((res) => {
        this.setState({ sdcardCode: res.sdcardCode });
        LogUtil.logOnAll(TAG, "getSdcardStatus res=", JSON.stringify(res));
      }).catch((err) => {
        this.setState({ sdcardCode: 0 });
        LogUtil.logOnAll(TAG, "getSdcardStatus err=", JSON.stringify(err));
      });
    }
  }

  initEvFilter() {
    let supportEvents = CameraConfig.pgSupportEvents();
    let list = EvArray.filter((item) => {
      if (item.key == Event.Default) return null;

      if (!supportEvents.includes(item.key)) {
        return null;
      }

      // if (item.key == Event.CameraCalling && !CameraConfig.displayCameraCallingTimeline(Device.model)) return null;
      //
      // if (item.key == Event.Pet && !CameraConfig.displayPetInTimeline(Device.model)) return null;
      // if (item.key == Event.Face && !CameraConfig.displayFaceInTimeline(Device.model)) return null;
      // if (item.key == Event.AI) return null;

      this.storedEvSelMap[item.key] = true;
      this.EvSelMap[item.key] = true;
      return item;
    });

    this.mEvFilter = list;
    // AlarmUtilV2.getALLAISettings().then(() => {
    //   let closedItems = [];
    //   list = list.filter((item, index) => {
    //     if (!AlarmUtilV2.AI_EVENT_SETTING_MAP[item.key]) {
    //       console.log("close AI->",item.key)
    //       closedItems.push(item);
    //       return false;
    //     }
    //     return true;
    //   });
    //   list.push(...closedItems);
    //   this.mEvFilter = list;
    // }).catch(() => {
    //   this.mEvFilter = list;
    // });
  }

  dimensionListener = (args) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    if (Platform.OS === "ios") {
      if (args && args.screen && args.window) {
        if (args.screen.width !== args.window.width || args.screen.height !== args.window.height || this.mLastScreenSize.width != Dimensions.get('screen').width) {
          // this.setState({ screenSize: Dimensions.get('screen') });
          this.mLastScreenSize = Dimensions.get('screen');
          console.log('Dimensions changed========4');
          Dimensions.set({ "window": args.screen });
          this.setState({});
        }
      }
    }
  }

  _onResume() {
    if (!this.isAppForeground || !this.isPluginForeGround || !this.isPageForeGround) {
      return;
    }
    CameraPlayer.getInstance().bindConnectionCallback(this._connectionHandler);
    CameraPlayer.getInstance().bindP2pCommandCallback(this._ServerRespHandler);
    CameraPlayer.getInstance().bindNetworkInfoCallback(this._networkChangeHandler);
    CameraPlayer.getInstance().bindPauseAllCallback(() => { this._stopAll(false, false) });
    DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');
    CameraPlayer.getInstance().bindPowerOffCallback(this._powerOffHandler);
    this.sdcardFilesListner = SdcardEventLoader.getInstance().addListener(() => {
      this.onGetFiles();// 刷新数据。
    });

    // // 如果已经播放完成了 或者是播放error  或者是用户手动暂停的
    // if (this.state.showErrorView) {
    //   return;
    // }
    if (this.state.showPoweroffView) {
      return;
    }
    if (this.isUserPause) {
      return;
    }
    // 重新进来 要绑定一遍这些事件。
    if (!this.isFirstIn) {
      this.onGetFiles();// 从其他页面回来 要刷新一遍数据，避免出现其他页面删了  这个页面还没有同步数据的情况。
    }

    Service.miotcamera.bindTimelinePlaybackEndListener(timelinePlaybackEndListenerName);


    this.queryNetworkJob();

    this.enterSDPageTime = new Date().getTime(); // 获取进入界面的时间
  }

  _onPause(ignoreState = false) {
    if (this.cameraRenderView != null) {
      this.cameraRenderView.stopRender();// stopRender
    }
    CameraPlayer.getInstance().bindConnectionCallback(null);
    CameraPlayer.getInstance().bindP2pCommandCallback(null);
    CameraPlayer.getInstance().bindPowerOffCallback(null);
    CameraPlayer.getInstance().bindPauseAllCallback(null);

    CameraPlayer.getInstance().bindNetworkInfoCallback(null);

    this.sdcardFilesListner && this.sdcardFilesListner.remove();

    this._stopRecord(ignoreState);

    if (this.state.showErrorView) {
      return;
    }
    if (this.state.showPoweroffView) {
      return;
    }
    console.log(TAG, "onPause");
    this._startPlay(false, ignoreState);
    
    // 埋点--离开页面时上报曝光时间
    if(this.enterSDPageTime > 0){
      let SDPageTime = (new Date().getTime() - this.enterSDPageTime) / 1000;
      TrackUtil.reportResultEvent("Monitoring_CloudStorageTab_Time", "Time", SDPageTime)
      // console.log("===================================埋点--离开SD卡页面时上报曝光时间 did blur=================", SDPageTime)
      this.enterSDPageTime = 0;
    }
  }

  restoreOri() {
    console.log(TAG, "restoreOri");
    if ("PORTRAIT" === this.mOri) {
      this.toPortrait();
    } else {
      this.toLandscape();
    }
  }


  toPortrait(ignoreState = false) {
    if (Platform.constants.Version == 26) {
      return;
    }
    StatusBar.setHidden(false);
    console.log(TAG, "toPortrait");
    this.mOri = "PORTRAIT";
    CameraConfig.lockToPortrait();
    if (ignoreState) {
      return;
    }
    this._setNavigation(false);
  }

  toLandscape() {
    if (Platform.constants.Version == 26) {
      return;
    }
    console.log(TAG, "toLandscape");
    this.mOri = "LANDSCAPE";
    if (Platform.OS === "android") {
      Orientation.lockToLandscape();
    } else {
      Orientation.lockToLandscapeRight();
    }
    if (Host.isPad) {
      Service.miotcamera.enterFullscreenForPad(true);
    }

    StatusBar.setHidden(true);

    this._setNavigation(true);
  }
  componentWillMount() {
    let maxSpeed = 16;
    this.panResponder = PanResponder.create({
      onStartShouldSetPanResponder: () => true, // 刚开始的时候
      onMoveShouldSetPanResponder: () => true,
      onShouldBlockNativeResponder: () => true,
      onPanResponderTerminationRequest: () => true, // 不允许其他人抢占。
      onPanResponderGrant: (evt) => {
        console.log("开始触摸");
        // 开始按下视频区域的时间
        // 已经在最高倍速，则不在进行长按快进播放
        if (this.state.speed != maxSpeed) {
          // if (this.state.speed != maxSpeed && this.state.isPlaying){
          this.panResponderDownTime = new Date().getTime();
          this.long_press_timeout = setTimeout(() => {
            if (!this.isLongVideoPress) {
              this.isLongVideoPress = true;
              // 1、视频区域中部展示快进  2、在当前播放基础上翻倍播放
              console.log("快进播放开始");
              // 埋点 -- 长按加速
              TrackUtil.reportClickEvent("Monitoring_PlayBackTab_LongPress_Status")
              this.oldSpeed = this.selectedIndexArray[0];
              let toSpeed = this.selectedIndexArray[0] < 2 ? this.selectedIndexArray[0] + 1 : this.selectedIndexArray[0];
              this.updateSpeed(toSpeed, false);
              this.setState({ showFastSpeedView: true });
            }
          }, 1000);
        }
      },

      onPanResponderRelease: () => {
        console.log("onPanResponderRelease");
        clearTimeout(this.long_press_timeout);
        if (this.isLongVideoPress) {
          console.log("快进播放结束");
          this.isLongVideoPress = false;
          // 重置播放倍速
          this.updateSpeed(this.oldSpeed, false);
          this.setState({ showFastSpeedView: false });
        } else {
          // this._onVideoClick();
        }
      },

      onPanResponderTerminate: () => {
        console.log("onPanResponderTerminate");
        clearTimeout(this.long_press_timeout);
      }
    });
    this._panResponder = PanResponder.create({
      onStartShouldSetPanResponderCapture: (evt, gestureState) => {
        if (Platform.OS == "ios") {
          this.setState({ touchMovement: true });//手指移动，用来监听IOS
        }
      },
    });
  }
  componentWillUnmount() {
    console.log(TAG, "unmount");

    try {
      this._onPause(true);
    } catch (exception) {
      console.log(TAG, "unmount error", exception);
    }
    this.destroyed = true;

    this.sdcardFilesListner && this.sdcardFilesListner.remove();
    this.stackNavigationOnPauseListener && this.stackNavigationOnPauseListener.remove();
    this.stackNavigationOnResumeListener && this.stackNavigationOnResumeListener.remove();
    this.stackNavigationOnbackListener && this.stackNavigationOnbackListener.remove();

    this.toPortrait(true);
    clearTimeout(this.scrollTimeout);
    clearTimeout(this.scrollTimeout1);

    CameraPlayer.getInstance().bindPlaybackTimestampCallback(null);
    CameraPlayer.getInstance().stopPlaybackTimestampInterval();// 防止崩溃

    Service.miotcamera.bindTimelinePlaybackEndListener(null);

    // SdFileManager.getInstance().bindReceiveFilesListener(null);
    // this._onPause();
    Service.miotcamera.setTimelinePlaybackMode(false);

    this.didResumeListener.remove();
    this.willPauseListener.remove();
    if (this.willAppearListener) {
      this.willAppearListener.remove();
    }
    if (this.willDisappearListener) {
      this.willDisappearListener.remove();
    }
    this.willStopListener && this.willStopListener.remove();
    this.timelinePlaybackEndListener.remove();
    this.didFocusListener.remove();
    this.didBlurListener.remove();
    this.recordListener.remove();
    Orientation.removeOrientationListener(this._orientationListener);
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
    // SdFileManager.getInstance().destroyInstance();
    this.setState = () => false;
  }


  _stopAll(showPauseView = false) {

    this._stopRecord();
    this._toggleAudio(true, false);
    CameraPlayer.getInstance().stopVideoPlay();
    this.cameraRenderView && this.cameraRenderView.stopRender();// stopRender
    this.hideshowPauseViewBgBpsCount = 0;

    this.setState({ showPauseView: showPauseView, showLoadingView: false, showPlayToolBar: false });
  }

  getPowerOffString() {
    return this.isSupportPhysicalCover ? "camera_physical_covered" : "camera_power_off";
  }


  _hidePlayToolBarLater(ignoreFull = false) {

    let tTimer = 5000;
    clearTimeout(this.showPlayToolBarTimer);
    this.showPlayToolBarTimer = setTimeout(() => {
      if (this.timelineViewFull && !this.timelineViewFull.isTimelineIdle() && this.state.fullScreen) {
        this._hidePlayToolBarLater();
        return;
      }
      this.setState({ showPlayToolBar: false, showPauseView: false });
    }, tTimer);
  }

  CoverLayerState(state) {
    console.log("CoverLayerState=", state);
  }

  render() {
    if (SdcardCloudTimelinePageV2.fromPush) {
      let sdcardStatus = this.state.sdcardCode;
      // if (sdcardStatus == -1) {
      //   return <View></View>;
      // } else
      if (sdcardStatus == 0 || sdcardStatus == -1 || sdcardStatus == 2 || sdcardStatus == CameraPlayer.SD_CARD_FILE_ERROR_CODE) { // sdcard状态获取失败  sdcard正常或者满了 10存在异常文件
        return this.renderMain();
      } else if (sdcardStatus == 3 || sdcardStatus == 4 || sdcardStatus == CameraPlayer.SD_CARD_NEED_FORMAT_CODE || sdcardStatus == CameraPlayer.SD_CARD_TOO_SMALL_CODE) { // 格式化中
        return <SDCardSetting key={'SDCardSetting'} navigation={this.props.navigation}/>;
      } else {
        return <NoSdcardPage key={'NoSdcardPage'}/>;
      }
    } else {
      return this.renderMain();
    }
  }

  renderMain() {
    return (

      <View {...this._panResponder.panHandlers} style={styles.container}>
        <SafeAreaView style={{ backgroundColor: "#ffffff" }}></SafeAreaView>
        {this._renderVideoLayout()}
        {this._renderTitleBar()}
        {this._renderTimeLineView()}
        {/* {this._renderEventTypes()} */}
        {/* {this._renderBottomSelectView()} */}

        <DeviceOfflineDialog
          ref={(powerOfflineDialog) => {
            this.powerOfflineDialog = powerOfflineDialog;
          }}
        />
        <NoNetworkDialog
          ref={(noNetworkDialog) => {
            this.noNetworkDialog = noNetworkDialog;
          }}
        />
        {this._renderResolutionDialog()}
        {this._renderPermissionDialog()}
        {this._renderEvFilterDialog()}
        <CoverLayer CoverLayerState={this.CoverLayerState} ref={(ref) => this.coverLayer = ref} />
        {/*<SafeAreaView></SafeAreaView>*/}
      </View>
    );
  }
  doEvFilter() {
    let eventFlags = EVENT_TYPE.Default;
    // 埋点 -- SD卡事件筛选状态
    let motionIdxMap = {
      allEvent: 1, PeopleMotion: 2, Face: 3, EmotionRecognition: 4, FenceIn: 5, FenceOut: 6, BabyCry: 7, PeopleCough: 8, LouderSound: 9, ObjectMotion: 10, AI: 11 
    }
    let motionArr = [];
    this.isAllEvSelect() ? motionArr.push(1) : null;
    Object.keys(this.storedEvSelMap).forEach((item) => {
      if (this.storedEvSelMap[item]) {
        eventFlags = eventFlags | EVENT_TYPE[item];
        motionArr.push(motionIdxMap[item])
      }
    });
    this.setState({ eventTypeFlags: eventFlags });
    TrackUtil.reportResultEvent("Monitoring_PlayBackTab_Motion_Status", "type", motionArr.sort((a, b) => { return a - b }).join(','));
  }

  isAllEvSelect() {
    let flag = true;
    Object.keys(this.EvSelMap).every((item) => {
      if (!this.EvSelMap[item]) {
        flag = false;
        return flag;
      }
      return true;
    });
    return flag;
  }
  _renderEvFilterDialog() {
    let allSelected = this.isAllEvSelect();
    return <AbstractDialog
      visible={this.state.showEvFilter}
      title={LocalizedStrings["ev_filter_title"]}
      style={{ maxHeight: kWindowHeight * 0.85 }}
      showSubtitle={false}
      onDismiss={() => {
        this.EvSelMap = Object.assign({}, this.storedEvSelMap);
        this.setState({ showEvFilter: false });
      }}
      canDismiss={true}
      useNewTheme={true}
      buttons={[
        {
          text: LocalizedStrings["btn_cancel"],
          // titleColor: 'rgba(0, 0, 0, 0.8)',
          // backgroundColor: { bgColorNormal: 'rgba(0, 0, 0, 0.04)', bgColorPressed: "rgba(0, 0, 0, 0.08)" },
          callback: () => {
            this.EvSelMap = Object.assign({}, this.storedEvSelMap);
            this.setState({ showEvFilter: false });
          }
        },
        {
          text: LocalizedStrings["btn_confirm"],
          callback: () => {
            this.storedEvSelMap = Object.assign({}, this.EvSelMap);
            this.doEvFilter();
            this.setState({ showEvFilter: false });
          }
        }
      ]}>
      <ScrollView style={{marginBottom: 10}} contentContainerStyle={{flexGrow: 1}} showsVerticalScrollIndicator={true}>
        <TouchableOpacity key={Event.Default}
          onPress={() => {
            Object.keys(this.EvSelMap).forEach((item) => {
              this.EvSelMap[item] = !allSelected;
            });
            this.forceUpdate();
          }}>
          <View style={{ flexDirection: "row", alignItems: "center", justifyContent: "space-between", paddingTop: 10, paddingBottom: 10 }}>
            <View style={{ flexDirection: "row", alignItems: "center", marginStart: 30 }}>
              <Image source={EvMap[Event.Default].icon.norm} style={{ width: 30, height: 30, marginEnd: 20 }}></Image>
              <Text style={{ fontWeight: "bold", fontSize: 16 }}>{EvMap[Event.Default].des}</Text>
            </View>
            {allSelected ? <Image source={require("../../Resources/Images/icon_camera_panoram_angle_select.png")} style={{ width: 22, height: 22, marginRight: 30 }}></Image>
              : <Image source={require("../../Resources/Images/icon_camera_panoram_angle_unselect.png")} style={{ width: 22, height: 22, marginRight: 30 }}></Image>}
          </View>
          <View style={{ height: 0.5, marginHorizontal: 30, marginVertical: 10, backgroundColor: Util.isDark() ? "xm#333333" : "rgba(0, 0, 0, 0.1)" }}></View>
        </TouchableOpacity>
        {
          this.mEvFilter.map((item) => {
            let itemInfo = EvMap[item.key];
            if (!itemInfo) {
              return null;
            }
            return <TouchableOpacity key={item.key}
              onPress={() => {
                this.EvSelMap[item.key] = !this.EvSelMap[item.key];
                this.forceUpdate();
                // this.setState({ showEvFilter: false });
              }}>
              <View style={{ flexDirection: "row", alignItems: "center", justifyContent: "space-between", paddingTop: 15, paddingBottom: 15 }}>
                <View style={{ flexDirection: "row", alignItems: "center", marginStart: 30 }}>
                  <Image source={itemInfo.icon.norm} style={{ width: 30, height: 30, marginEnd: 20 }}></Image>
                  <Text style={{ fontWeight: "bold", fontSize: 16 }}>{itemInfo.des}</Text>
                </View>
                {this.EvSelMap[item.key] ? <Image source={require("../../Resources/Images/icon_camera_panoram_angle_select.png")} style={{ width: 22, height: 22, marginRight: 30 }}></Image>
                  : <Image source={require("../../Resources/Images/icon_camera_panoram_angle_unselect.png")} style={{ width: 22, height: 22, marginRight: 30 }}></Image>}
              </View></TouchableOpacity>;
          })
        }
      </ScrollView>
    </AbstractDialog>;
  }

  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 麦克风
    // 
    let message = "";
    if (this.state.permissionRequestState == 0) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_microphone"]);
    }
    return (
      <MessageDialog
        title={LocalizedStrings["tips"]}
        message={message}
        messageStyle={{
          fontSize: 14,
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );

  }

  _renderVideoLayout() {
    return (
      <View style={this.state.fullScreen ? styles.videoContainerFull : styles.videoContainerNormal}>
        {this._renderVideoView()}
        {/* {this._renderClickableView()} */}
        {this._renderMoreDialog()}
        {this._renderAngleView()}
        {this._renderPowerOffView()}
        {/* {this._renderPauseView()} 066放到左下角了，这里隐藏 */}
        {this._renderFastSpeedView()}
        {this._renderErrorRetryView()}
        {this._renderHighTemperatureView()}
        {this._renderLoadingView()}

        {/* {this._renderTitleView()} */}
        {/* 竖屏图标 */}
        {this._renderMoreView()}

        {this._renderVideoControlView()}
        {this._renderSnapshotView()}

        {this._renderRecordTimeView()}
        {this._renderTimeIndicatorView()}
        {/* 横屏图标 */}
        {this._renderLandscapeTopButtons()}
        {this._renderLandscapeRightButtons()}

      </View>
    );
  }

  _renderTitleBar() {
    let isAllEv = this.isAllEvSelect();
    let eventSource = isAllEv ? Util.isDark() ? require("../../Resources/Images/icon_event_choose_all_dark.png") : require("../../Resources/Images/icon_event_choose_all.png")
      : Util.isDark() ? require("../../Resources/Images/icon_event_choose_dark.png") : require("../../Resources/Images/icon_event_choose.png");
    return (
      <View style={{ display: "flex", flexDirection: "row", borderBottomColor: Util.isDark() ? "xm#333333" : "#E6E6E6", borderBottomWidth: 1, alignItems: 'center', paddingVertical: 15 }}>
        {this.dateView()}

        <TouchableOpacity style={[{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "flex-start",
          marginLeft: 15,
          marginRight: 24
        }]}
          onPress={() => { this.setState({ showEvFilter: true }); }}>

          <Image
            accessibilityLabel={DescriptionConstants.kj_1_8}
            style={{ width: 45, height: 45 }} source={eventSource} />
        </TouchableOpacity>
      </View>
    );
  }

  _renderMoreView() {

    if (this.state.fullScreen) {
      return null;
    }

    if (!this.state.showPlayToolBar) {
      return null;
    }

    return (
      <LinearGradient colors={['#00000088', '#00000000']} style={{ position: "absolute", width: "100%", top: 0, paddingBottom: 10 }}>
        <View style={{  flexGrow: 1, display: "flex", justifyContent: 'flex-end', alignItems: 'flex-end' }}>
        <ImageButton
          style={{ width: 40, height: 40, backgroundColor: "#00000000" }}
          source={require("../../Resources/Images/icon_more.png")}
          highlightedSource={require("../../Resources/Images/icon_more.png")}
          onPress={() => {
            this.setState({ showMoreDlg: true, moreIconY: this.moreIconY })
          }}
          accessible={true}
          accessibilityLabel={this.state.isPlaying ?
            !this.state.fullScreen ? DescriptionConstants.rp_14 : DescriptionConstants.rp_31 :
            !this.state.fullScreen ? DescriptionConstants.hk_3_6 : DescriptionConstants.lc_7
          }
          accessibilityState={{
            selected: this.state.isPlaying
          }}
          testId={this.state.isPlaying ? '1' : '0'}
          onLayout={(e) => {
            if (this.moreIconY == -1) {
              // 横屏切到竖屏，会导致计算出的数值有偏差
              // 加个判断有后续就不计算了
              NativeModules.UIManager.measure(e.target, (x, y, width, height, pageX, pageY) => {
                this.moreIconY = pageY;
              });
            }
          }}
        />
      </View>
      </LinearGradient>
    );
  }

  _renderMoreDialog() {
    return (
      <Modal
        style={{ display: "flex"}}
        animationType="none"
        transparent={true}
        visible={this.state.showMoreDlg}
        onRequestClose={() => {
          this.hide();
        }}
      >
        <TouchableOpacity
          style={{ position: "absolute", width: "100%", height: "100%", backgroundColor:"#00000000" }}
          onPress = {() => {
            this.hide();

          }}
        >
        </TouchableOpacity>

        <View style={{ position: "absolute", width: 200, top: this.state.moreIconY+46, right: 20,backgroundColor:"#FFFFFF",borderRadius: 16,paddingVertical: 10,shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.5,
          shadowRadius: 8,
          elevation: 8 }}>
          <TouchableOpacity
            onPress = {() => {
              this.hide();
              // 埋点 -- 查看全部视频点击
              TrackUtil.reportClickEvent("Monitoring_PlayBackTab_AllVideo_ClickNum")
              this._startAllStorageWithPermissionCheck();
            }}>
            <Text style={{ color: '#000000', fontSize: 15, marginHorizontal: 25, marginVertical: 16, fontWeight: "bold" }}>
              {LocalizedStrings['f_all']}
            </Text>
          </TouchableOpacity>
        </View>

      </Modal>
    );
  }

  hide() {
    this.setState({ showMoreDlg: false });
  }

  _startAllStorageWithPermissionCheck() {
    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return;
    }
    if (Platform.OS === "android") {
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this._showAllStorage();
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            // Toast.success("camera_no_write_permission");
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
        Toast.success("action_failed");
      });
    } else {
      // no ios's photos const use hardcode
      System.permission.request("photos").then((res) => {
        this._showAllStorage();
      }).catch((error) => {
        // Toast.success("camera_no_write_permission");
        this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
      });

    }
    TrackUtil.reportClickEvent('Camera_ScreenShot_ClickNum');
  }

  _showAllStorage() {
    TrackUtil.reportClickEvent("Camera_Storage_ClickNum"); // Camera_Storage_ClickNum
    console.log("CameraPlayer.getInstance().sdcardCode=", CameraPlayer.getInstance().sdcardCode);
    let index = 0;
    if (CameraPlayer.getInstance().sdcardCode == 0 || CameraPlayer.getInstance().sdcardCode == 2 || !Device.isOwner) {
      index = 2;
    }
    let navigationParam = { initPageIndex: index, vip: this.isVip, isSupportCloud: CameraConfig.isSupportCloud() };
    LogUtil.logOnAll("AllStorage UI s param:", navigationParam, " isConnected:", CameraPlayer.getInstance().isConnected());
    // 进入回看前 清空一次SdFileManager里的列表。避免缓存的问题
    // 这里不清除了，否则存储管理从新拉数据有阻塞
    // SdcardEventLoader.getInstance().clearSdcardFileList();
    StackNavigationInstance.jumpToStackNavigationPage_forSDCloudPage("AllStorage", navigationParam);
  }
  dateView() {
    let dStr = Util.addDateDes(dayjs(this.state.curDate), dayjs(this.state.curDate).format(LocalizedStrings["mmdd"]));
    return (
      <TouchableOpacity
        style={[{ flexDirection: "row", flex: 1, alignItems: "center", justifyContent: "flex-start", paddingRight: 10, paddingLeft: 24 }]}
        onPress={() => {
          if (this.state.isRecording) {
            Toast.success('camera_recording_block');
            return;
          }
          // 弹出日期选择弹框
          this.mShowPopupView();
          // this.setState({ showDateDialog: true });
        }}>
        <Text
          style={[BaseStyles.text12, { paddingLeft: 0, color: "#8C93B0", fontWeight: "bold" }]}>{dStr}</Text>
        <Image
          accessibilityLabel={DescriptionConstants.kj_1_8}
          style={{ width: 20, height: 20 }} source={require("../../resources2/images/icon_ev_sel.png")} />
      </TouchableOpacity>
    );
  }

  mSwitchOneDay(items) {
    this.coverLayer.hideWithoutAnimated();
    let sltDate = new Date(Date.parse(`${items[0]}/${items[1]}/${items[4]}`));
    this.mCalendar.setDate(sltDate);
    this.setState({ curDate: sltDate });
    console.log("mSwitchOneDay", items, " time=", sltDate.getTime());
    this.timelineView._onPressDayItem(sltDate.getTime());
    this.timelineViewFull._onPressDayItem(sltDate.getTime());
    // 埋点--SD卡日期筛选点击
    TrackUtil.reportClickEvent("Monitoring_PlayBackTab_Date_ClickNum")
  }
  mShowPopupView = () => {
    this.coverLayer.showWithContent(
      () => {
        // let nowDate = new Date();
        // console.log('calendar input: ', this.state.curDate);
        let mWidth = Math.min(Dimensions.get("window").height, Dimensions.get("window").width);
        return (
          <View style={{ height: 500, width: mWidth }}>
            <Calendar
              ref={(hdl) => this.mCalendar = hdl}
              visible={this.showCalendar}
              y={this.state.curDate.getFullYear()}
              m={this.state.curDate.getMonth() + 1}
              d={this.state.curDate.getDate()}
              interval={this.rollingInterval}
              toastStr={LocalizedStrings['sdcard_page_desc_empty']}
              width={mWidth}
              onDateChanged={this.mSwitchOneDay.bind(this)}
              onCancel={() => { this.coverLayer.hideWithoutAnimated(); }}
              onAllVideo={this.mSwitchAllVideo}
              dates={this.state.hasvideoDates}
            >
            </Calendar>
          </View>
        );
      },
      () => this.coverLayer.hideWithoutAnimated(),
      CoverLayer.popupMode.bottom
    );
  }

  mSwitchAllVideo = () => {
    console.log("get selected");
    let nowDate = new Date();
    let sltDate = new Date(nowDate.getFullYear(), nowDate.getMonth(), nowDate.getDate(), 0, 0, 0);
    this.mCalendar.setDate(sltDate);
    this.setState({ curDate: sltDate });
    console.log("mSwitchAllVideo", " time=", sltDate.getTime());
    this.timelineView._onPressDayItem(sltDate.getTime());
    this.timelineViewFull._onPressDayItem(sltDate.getTime());
    this.coverLayer.hideWithoutAnimated();
  }
  _renderClickableView() {
    return (
      <TouchableOpacity
        style={{ width: "100%", height: "100%", backgroundColor: "#00000000", position: "absolute", zIndex: 0 }}
        onPress={() => {
          this._onVideoClick();
        }}
      >
      </TouchableOpacity>
    );
  }

  _renderTimeIndicatorView() {
    return (
      <View
        style={{ width: "100%", height: "100%", position: "absolute", display: "flex", justifyContent: "center", alignItems: "center", marginTop: -50 }}
        pointerEvents={"none"}
      >
        <CenterTimeView
          ref={(ref) => {
            this.timeIndicatorView = ref;
          }}
        >

        </CenterTimeView>
      </View>
    );
  }

  _renderLandscapeRightButtons() {

    if (!this.state.fullScreen) {
      return null;
    }

    if (!this.state.showPlayToolBar) {
      return null;
    }

    let recordIndex = !this.state.isPlaying ? 2 : (this.state.isRecording ? 1 : 0);
    let screenHeight = Dimensions.get("window").height;
    let screenWidth = Dimensions.get("window").width;
    if (screenWidth < screenHeight) { // mi 9 se上 第一次切换回来，获取的高度是竖屏下的高度，需要修改以下。
      screenHeight = screenWidth;
    }
    let viewHeight = screenHeight - 150;
    let StatusBarheight = null, haveStatus = false;
    let marginR;
    if (Platform.OS === "android") {

      StatusBarheight = StatusBar.currentHeight - 30;
      console.log('StatusBarheight', StatusBarheight);
      if (StatusBarheight <= 0) {
        StatusBarheight = 0;
        haveStatus = true;
      }
      // -16 因为返回按钮的图片留白太大
      marginR = this.displayCutoutTop - 16;
    } else {
      StatusBarheight = StatusBarUtil._getInset("top") - 30;
      marginR = this.statusBarHeight;
    }
    console.log("+++++++++++++++",this.displayCutoutTop)
    return (
      <View style={{ position: "absolute", justifyContent: "space-around", bottom: 0, right: marginR, height: viewHeight - 50, top: Host.isPad ? 100 : 70, width: 80, alignItems: "center", backgroundColor: "#00000000" }}
        pointerEvents={"box-none"}
      >
        {/* 目前的 - Lottie动画按钮 */}
        <MHLottieSnapLandscapeButton
          style={{ width: 50, height: 50 }}

          onPress={handlerOnceTapWithToast(() => {
            this._startSnapshot();
            this._hidePlayToolBarLater();
          })}
          disabled={this.state.showPoweroffView}
          displayState={MHLottieSnapLandscapeBtnDisplayState.NORMAL}
          accessibilityLabel={DescriptionConstants.rp_34}
        />

        <MHLottieRecordLandscapeButton
          style={{ width: 50, height: 50 }}

          onPress={() => {
            this._hidePlayToolBarLater();
            if (this.state.isRecording) {
              this._stopRecord();
            } else {
              this._startRecord();
            }
          }}
          displayState={this.state.isRecording ? MHLottieRecordLandscapeBtnDisplayState.RECORDING : MHLottieRecordLandscapeBtnDisplayState.NORMAL}
          disabled={!this.state.isPlaying}
          accessibilityLabel={this.state.isPlaying ? DescriptionConstants.rp_35 : DescriptionConstants.rp_35_1}
        />
      </View>
    );
  }

  _renderLandscapeTopButtons() {


    if (!this.state.fullScreen) {
      return null;
    }

    if (!this.state.showPlayToolBar) {
      return null;
    }

    let screenHeight = Dimensions.get("window").height;
    let screenWidth = Dimensions.get("window").width;
    if (screenWidth < screenHeight) { // mi 9 se上 第一次切换回来，获取的高度是竖屏下的高度，需要修改以下。
      screenHeight = screenWidth;
    }

    let iconBack = require("../../Resources/Images/icon_back_black_nor_dark.png");
    let iconBackPre = require("../../Resources/Images/icon_back_black_nor_dark.png");

    let StatusBarheight = null, haveStatus = false;
    let fullscreenMR = 0;
    if (Platform.OS === "android") {
      StatusBarheight = StatusBar.currentHeight - 16;
      if (StatusBarheight <= 0) {
        haveStatus = true;
        StatusBarheight = 15;
      }
      // -16 因为返回按钮的图片留白太大
    } else {
      StatusBarheight = StatusBarUtil._getInset("top") - 16;
      fullscreenMR = this.statusBarHeight + 15
    }

    let speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
    switch (this.state.speed) {
      case 1:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
        break;
      case 2:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X2;
        break;
      case 4:
        speedDisplayState = this.isCameraV1orV3 ? MHLottieSpeedToolBtnDisplayState.X2 : MHLottieSpeedToolBtnDisplayState.X4; // 将UI页面4/16倍速 改成 2/3倍速
        break;
      case 8:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X8;
        break;
      case 16:
        speedDisplayState = this.isCameraV1orV3 ? MHLottieSpeedToolBtnDisplayState.X3 : MHLottieSpeedToolBtnDisplayState.X16;// 将UI页面4/16倍速 改成 2/3倍速
        if (CameraConfig.support8xSpeed(Device.model) && !this.isCameraV1orV3) {
          speedDisplayState = MHLottieSpeedToolBtnDisplayState.X8;
        }
        break;
      default:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
        break;
    }

    return (
      <LinearGradient colors={['#00000077', '#00000000']} pointerEvents={"box-none"}>
        <View style={{ width: "100%", paddingStart: 0, paddingEnd: this.displayCutoutTop, display: "flex", flexDirection: "row", marginTop: (Host.isPad) ? 50 : 20 }}>
          <View style={{ flexGrow: 1, display: "flex", flexDirection: "row", justifyContent: "space-between", paddingLeft: 20, paddingRight: fullscreenMR }}>
            <ImageButton
              source={iconBack}
              highlightedSource={iconBackPre}
              style={[styles.videoControlBarItemImg, { marginStart: -15 }]}
              onPress={() => {
                LogUtil.logOnAll(TAG, "sdcard timeline page on press onback button，change to potrait");
                this.toPortrait();// 切换到竖屏
                this._hidePlayToolBarLater(true);
              }}
              accessibilityLabel={DescriptionConstants.rp_30}
            >
            </ImageButton>
            <View>
              <View style={{ display: "flex", flexDirection: "row", height: 50, justifyContent: 'flex-end' }}>
                {/* 目前的 - Lottie动画按钮 */}
                <MHLottieAudioToolButton
                  style={{ width: 50, height: 50, marginRight: 20 }}
                  onPress={() => {
                    this._hidePlayToolBarLater();
                    if (this.state.isMute) {
                      // 默认是这个状态，去开启声音
                      if (this.state.isCalling) {
                        this.isAudioMuteTmp = false;
                      }
                      this._toggleAudio(false, true);
                    } else {
                      if (this.state.isCalling) {
                        this.isAudioMuteTmp = true;
                      }
                      this._toggleAudio(true, true);
                    }
                  }}
                  displayState={this.state.isMute ? MHLottieAudioToolBtnDisplayState.MUTED : MHLottieAudioToolBtnDisplayState.NORMAL}
                  landscape={true}
                  disabled={this.state.showPoweroffView ? this.state.showPoweroffView : this.state.speed > 1}
                  accessibilityLabel={this.state.speed <= 1 ?
                    this.state.isMute ? DescriptionConstants.rp_15 : DescriptionConstants.rp_32 :
                    DescriptionConstants.rp_15}
                  accessibilityState={{
                    selected: this.state.isMute
                  }}
                />
                <MHLottieSpeedToolButton
                  style={styles.videoControlBarItemImg}
                  onPress={() => {
                    this._hidePlayToolBarLater();
                    this.setState({ dialogVisibility: true });
                  }}
                  displayState={speedDisplayState}
                  landscape={true}
                  disabled={this.state.showPoweroffView ? this.state.showPoweroffView : this.state.isRecording}
                  accessibilityLabel={DescriptionConstants.rp_13.replace('1', this.state.speed)}
                />
              </View>
            </View>
          </View>
        </View>
      </LinearGradient>

    );
  }

  _renderRecordTimeView() {
    if (!this.state.isRecording) {
      return null;
    }
    let containerHeight = 20;

    if (this.state.fullScreen) {
      containerHeight = 30;
    }

    let seconds = this.state.recordTimeSeconds;
    let second = Number.parseInt(seconds % 60);
    let minute = Number.parseInt(seconds / 60 % 60);
    let hour = Number.parseInt(seconds / 60 / 60);
    this.lastRecordTime = `${hour > 9 ? hour : `0${hour}`}:${minute > 9 ? minute : `0${minute}`}:${second > 9 ? second : `0${second}`}`;

    return (
      <View
        style={{
          position: "absolute", top: containerHeight, display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center", alignSelf: "center",
          backgroundColor: "#00000099", // 0.8 opacity
          borderRadius: 20,
          width: 90,
          height: 24
        }}>
        <View
          accessibilityLabel={!this.state.fullScreen ? DescriptionConstants.rp_43 : DescriptionConstants.rp_45}
          style={{
            backgroundColor: "#FC4949",
            borderRadius: 8,
            width: 8,
            height: 8,
            marginRight: 2
          }} />
        <Text style={{
          color: "#ffffff",
          fontSize: kIsCN ? 13 : 11,
          fontWeight: "bold",
          textAlign: "center",
          textAlignVertical: "center",
          lineHeight: 24,
          marginLeft: 2
        }}
        >
          {this.lastRecordTime}
        </Text>
      </View>
    );

  }
  playPauseClick() {
    TrackUtil.reportClickEvent("TimeSlider_PlayPause_ClickNum")
    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return;
    }
    // todo pause
    this.isUserPause = true;
    this._startPlay(false);
  }
  playStartClick() {
    TrackUtil.reportClickEvent("TimeSlider_PlayPause_ClickNum");
    if (this.networkType == "CELLULAR") {
      this.ignoreDataWarning = true;
    }
    this.isUserPause = false;
    LogUtil.logOnAll(TAG, "press sdcardtimeline pause button ,startPlay:  isPageForeground:" + this.isPageForeGround + " isAppForeground:" + this.isAppForeground + " isPluginForeground:" + this.isPluginForeGround);
    this._onResume();
  }

  _renderPauseView() {
    if (!this.state.showPauseView || this.state.fullScreen) {
      return null;
    }
    if (this.state.showPoweroffView) {
      return;
    }
    if (this.state.showLoadingView) {
      return null;
    }
    let videoViewWidth = this.state.videoViewWidth;
    let translateX = (videoViewWidth - 64) / 2;
    let videoViewHeight = this.state.videoViewHeight;
    let translateY = (videoViewHeight - 64) / 2;
    let pauseIcons = [
      {
        source: require("../../Resources/Images/camera_icon_center_pause_nor.png"),
        highlightedSource: require("../../Resources/Images/camera_icon_center_pause_press.png"),
        onPress: () => {
          this.playPauseClick();
        }
      },
      {
        source: require("../../Resources/Images/camera_icon_center_play_nor.png"),
        highlightedSource: require("../../Resources/Images/camera_icon_center_play_nor.png"),
        onPress: () => {
          this.playStartClick();
        }
      }
    ];
    let index = this.state.isPlaying ? 0 : 1;

    return (
      <View style={{ position: "absolute", width: 100, height: 100, top: "50%", left: "50%", marginTop: -50, marginLeft: -50, backgroundColor: "#00000000", display: "flex", justifyContent: "center", alignItems: "center" }}
        pointerEvents={"auto"}
      >
        <ImageButton
          style={{ width: 64, height: 64 }}
          source={pauseIcons[index].source}
          highlightedSource={pauseIcons[index].highlightedSource}
          onPress={pauseIcons[index].onPress}
          accessible={true}
          accessibilityLabel={this.state.isPlaying ?
            !this.state.fullScreen ? DescriptionConstants.rp_14 : DescriptionConstants.rp_31 :
            !this.state.fullScreen ? DescriptionConstants.hk_3_6 : DescriptionConstants.lc_7
          }
          accessibilityState={{
            selected: this.state.isPlaying
          }}
          testId={this.state.isPlaying ? '1' : '0'}
        />
      </View>
    );
  }

  _renderFastSpeedView() {
    if (!this.state.showFastSpeedView) {
      return null;
    }

    if (this.state.showPauseView) {
      return null;
    }

    // if (this.state.videoViewWidth == 0 || this.state.videoViewHeight == 0) {
    //   console.log("_renderFastSpeedView null2");
    //   return null;
    // }

    if (this.state.showLoadingView) {
      return null;
    }

    return (
      <View style={{
        opacity: 0.6, backgroundColor: "#000000", position: "absolute", width: 100, height: 64, top: "50%", left: "50%",
        marginTop: -32, marginLeft: -50, alignItems: "center", justifyContent: "center", borderRadius: 20
      }}>
        <Text style={{ fontSize: 34, color: "xm#ffffff", alignItems: "center", justifyContent: "center", paddingBottom: 7 }}>{`x${this.state.speed}`}</Text>
      </View>
    );
  }

  _renderVideoView() {
    if (this.isOffline) {
      return null;
    }
    return (
      <CameraRenderView
        accessible={true}
        accessibilityLabel={DescriptionConstants.rp_62}
        ref={(ref) => { this.cameraRenderView = ref; }}
        maximumZoomScale={6.0}
        style={styles.videoView}
        videoCodec={CameraConfig.getCameraCoderParam(Device.model, Device.lastVersion).videoCodec}
        audioCodec={CameraConfig.getCameraCoderParam(Device.model, Device.lastVersion).audioCodec}
        audioRecordSampleRate={CameraConfig.getCameraAudioSampleRate(Device.model)}
        audioRecordChannel={MISSAudioChannel.FLAG_AUDIO_CHANNEL_MONO}
        audioRecordDataBits={MISSDataBits.FLAG_AUDIO_DATABITS_16}
        fullscreenState={this.state.fullScreen}
        // videoRate={15}
        videoRate={this.state.speed == 16 ? 2 : 15}
        correctRadius={CameraConfig.getCameraCorrentParam(Device.model).radius}
        osdx={CameraConfig.getCameraCorrentParam(Device.model).osdx}
        osdy={CameraConfig.getCameraCorrentParam(Device.model).osdy}
        useLenCorrent={false}
        onVideoClick={this._onVideoClick.bind(this)}
        onScaleChanged={this._onVideoScaleChanged.bind(this)}
        did={Device.deviceID}
        isFull={false}
        recordingVideoParam={{ ...CameraConfig.getRecordingVideoParam(Device.model), isInTimeRecord: true, hasRecordAudio: this.state.speed == 1 }}
        {...this.panResponder.panHandlers}>
      </CameraRenderView>
    );
  }

  _renderPowerOffView() {
    // todo render poweroffview  full
    if (!this.state.showPoweroffView) {
      return null;
    }
    if (this.state.videoViewWidth == 0 || this.state.videoViewHeight == 0) {
      return null;
    }


    let videoViewWidth = this.state.videoViewWidth;
    let translateX = (videoViewWidth - 54) / 2;
    let videoViewHeight = this.state.videoViewHeight;
    let translateY = (videoViewHeight - 78) / 2;

    return (

      <TouchableWithoutFeedback style={{ position: "absolute", width: 64, height: 64, top: "50%", left: "50%", marginTop: -32, marginLeft: -32 }}
        onPress={() => { }}
      >
        <View style={{ backgroundColor: "black", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
        >
          <Image
            style={{ width: 54, height: 54 }}
            source={require("../../Resources/Images/icon_camera_sleep.png")} />
          <Text
            style={{ marginTop: 10, fontSize: kIsCN ? 14 : 12, color: "#bfbfbf" }}>
            {LocalizedStrings[this.getPowerOffString()]}
          </Text>
        </View>
      </TouchableWithoutFeedback>
    );
  }

  _renderErrorRetryView() {
    if (this.state.showHighTemperatureView) {
      return null;
    }

    if (!this.state.showErrorView) {
      return null;
    }

    let buttonReConnectItem = (
      <View
        style={{
          paddingTop: 6,
          paddingBottom: 6,
          paddingLeft: 17,
          paddingRight: 17,
          backgroundColor: "#249A9F",
          borderRadius: 20,
          marginTop: 10
        }}>
        <Text style={{
          color: "#fff",
          fontSize: kIsCN ? 12 : 10
        }}
        >{LocalizedStrings['reconnect_button_text']}</Text>
      </View>
    );

    let noNetworkItem = (
      <View style={{ display: "flex", flexDirection: "row" }}>
        <TouchableOpacity
          style={{ display: "flex", alignItems: "center" }}
          onPress={() => {
            if (!Device.isOnline) {
              Toast.success("device_offline");
              return;
            }
            this.setState({ showErrorView: false });
            Service.smarthome.reportLog(Device.model, "on error Retry");
            this._startPlay(false);
            this.queryNetworkJob();
          }}>
          {buttonReConnectItem}
        </TouchableOpacity>

      </View>

    );

    const errIcons = [
      require("../../Resources/Images/icon_connection_failure.png"),
      require("../../Resources/Images/icon_camera_offline.png"),
      require("../../Resources/Images/icon_camera_fail.png")
    ];

    let errIconIndex = 0;

    if (!Device.isOnline) {
      errIconIndex = 1;
    }

    return (
      <View
        style={{ zIndex: 7, position: "absolute", bottom: 0, backgroundColor: "black", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <View
          style={{ display: "flex", alignItems: "center" }}
        >
          <Image
            style={{ width: 54, height: 54 }}
            source={errIcons[errIconIndex]} />
          <Text
            style={{ marginTop: 10, fontSize: kIsCN ? 12 : 10, color: "#999", paddingHorizontal: 10, textAlign: "center", width: kWindowWidth - 60 }}>
            {this.currentNetworkState == 0 ? LocalizedStrings['common_net_error'] : this.state.errTextString}{Device.isOnline ? "" : (this.state.lastOfflineTime == "") ? "" : `, ${this.state.lastOfflineTime}`}
          </Text>
          {/* {Device.isOnline ? null : powerOfflineText} */}
        </View>
        {noNetworkItem}

      </View>
    );
    // todo render errorRetryView not
  }
  _renderHighTemperatureView() {
    if (!this.state.showHighTemperatureView) {
      return null;
    }
    return (
      <View
        style={{ zIndex: 7, position: "absolute", bottom: 0, backgroundColor: "black", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <View
          style={{ display: "flex", alignItems: "center" }}
        >
          <Image
            style={{ width: 54, height: 54 }}
            source={require("../../resources2/images/icon_ev_empty_w.png")} />
          <Text
            style={{ marginTop: 10, fontSize: kIsCN ? 12 : 10, color: "#999", paddingHorizontal: 10, textAlign: "center", width: kWindowWidth - 60 }}>
            {LocalizedStrings['high_temperature_cannot_use']}
          </Text>
        </View>
      </View>
    );
  }


  _renderLoadingView() {
    // todo render loading view 
    if (!this.state.showLoadingView || this.state.showPoweroffView) {
      return null;
    }

    let bgColor = "transparent";
    let loadingViewStyle = {
      zIndex: 0,
      position: "absolute",
      width: "100%",
      height: "100%",
      bottom: 0,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: bgColor
    };

    return (
      <View
        style={loadingViewStyle}
        pointerEvents={"none"}
      >
        <LoadingView
          style={{ width: 54, height: 54 }}
          color={"#ffffff"}
          size={"large"}

        />
        <Text
          style={{ marginTop: 10, fontSize: kIsCN ? 12 : 10, color: "#ffffff" }}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );
  }


  _renderVideoControlView() {

    if (this.state.fullScreen) {
      return null;
    }

    let speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
    let replacedSpeed = 1;
    switch (this.state.speed) {
      case 1:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
        replacedSpeed = 1;
        break;
      case 2:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X2;
        replacedSpeed = 2;
        break;
      case 4:
        speedDisplayState = this.isCameraV1orV3 ? MHLottieSpeedToolBtnDisplayState.X2 : MHLottieSpeedToolBtnDisplayState.X4;
        replacedSpeed = this.isCameraV1orV3 ? 2 : 4;
        break;
      case 8:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X8;
        replacedSpeed = 8;
        break;
      case 16:
        speedDisplayState = this.isCameraV1orV3 ? MHLottieSpeedToolBtnDisplayState.X3 : MHLottieSpeedToolBtnDisplayState.X16;
        replacedSpeed = this.isCameraV1orV3 ? 3 : 16;
        if (CameraConfig.support8xSpeed(Device.model) && !this.isCameraV1orV3) {
          speedDisplayState = MHLottieSpeedToolBtnDisplayState.X8;
          replacedSpeed = 8;
        }
        break;
      default:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
        break;
    }

    if (this.state.showPlayToolBar) {
      return (

        <LinearGradient pointerEvents={"box-none"} colors={this.state.fullScreen ? ['#00000000', '#00000077'] : ['#00000000', '#00000077']} style={[{ position: "absolute", width: "100%" }, this.state.fullScreen ? { top: 0 } : { bottom: 0 }]}
        >

          <View style={this.state.fullScreen ? styles.videoControlBarFull : styles.videoControlBar}>

            {/* 目前的 - Lottie动画按钮 */}
            <View style={styles.videoControlBarItem}>
              <TouchableWithoutFeedback
                onPress={() => {
                  if (!this.state.isPlaying) {
                    this.playStartClick();
                  } else {
                    this.playPauseClick();
                  }
                }}>
                <Image style={styles.videoControlBarItemImg} 
                  source={!this.state.isPlaying ? require("../../Resources/Images/icon_video_play.png") : require("../../Resources/Images/icon_video_pause.png")}>
                </Image>
              </TouchableWithoutFeedback>
            </View>
            <View style={styles.videoControlBarItem}>

              <MHLottieSnapToolButton
                style={styles.videoControlBarItemImg}

                onPress={handlerOnceTapWithToast(() => {
                  this._hidePlayToolBarLater();
                  TrackUtil.reportClickEvent("TimeSlider_ScreenShot_ClickNum");
                  this._startSnapshot();
                })}
                displayState={MHLottieSnapToolBtnDisplayState.NORMAL}
                landscape={this.state.fullScreen}
                disabled={this.state.showPoweroffView}
                accessibilityLabel={!this.state.fullScreen ? DescriptionConstants.rp_11 : DescriptionConstants.rp_34}
              />
            </View>

            <View style={styles.videoControlBarItem}>

              <MHLottieRecordToolButton
                style={styles.videoControlBarItemImg}

                onPress={() => {
                  this._hidePlayToolBarLater();
                  TrackUtil.reportClickEvent("TimeSlider_Record_ClickNum");

                  if (this.state.isRecording) {
                    this._stopRecord();
                  } else {
                    this._startRecord();
                  }

                }}
                displayState={this.state.isRecording ? MHLottieRecordToolBtnDisplayState.RECORDING : MHLottieRecordToolBtnDisplayState.NORMAL}

                disabled={this.state.showPoweroffView ? this.state.showPoweroffView : !this.state.isPlaying}
                accessible={true}
                accessibilityLabel={!this.state.fullScreen ? (this.state.isPlaying ? DescriptionConstants.rp_12 : DescriptionConstants.rp_12_1) : DescriptionConstants.rp_35}
                accessibilityState={{
                  selected: this.state.isRecording,
                  disabled: !this.state.isPlaying
                }}
                testId={this.state.isRecording ? '1' : '0'}
              />
            </View>

            <View style={styles.videoControlBarItem}>

              <MHLottieSpeedToolButton
                style={styles.videoControlBarItemImg}

                onPress={() => {
                  this._hidePlayToolBarLater();
                  TrackUtil.reportClickEvent("TimeSlider_PlaySpeed_ClickNum");
                  this.setState({ dialogVisibility: true });
                }}

                displayState={speedDisplayState}
                landscape={false}

                disabled={this.state.showPoweroffView ? this.state.showPoweroffView : this.state.isRecording}

                accessibilityLabel={DescriptionConstants.rp_13.replace('1', replacedSpeed)}
                accessibilityState={{
                  selected: this.state.dialogVisibility
                }}
                testId={speedDisplayState}
              />
            </View>

            <View style={styles.videoControlBarItem}>

              <MHLottieAudioToolButton
                style={styles.videoControlBarItemImg}

                onPress={() => {
                  this._hidePlayToolBarLater();
                  if (this.state.isMute) {

                    // 默认是这个状态，去开启声音
                    if (this.state.isCalling) {
                      this.isAudioMuteTmp = false;
                    }
                    this._toggleAudio(false, true);
                  } else {

                    if (this.state.isCalling) {
                      this.isAudioMuteTmp = true;
                    }
                    this._toggleAudio(true, true);
                  }
                }}

                displayState={this.state.isMute ? MHLottieAudioToolBtnDisplayState.MUTED : MHLottieAudioToolBtnDisplayState.NORMAL}
                landscape={this.state.fullScreen}
                disabled={this.state.showPoweroffView ? this.state.showPoweroffView : this.state.speed > 1}
                accessible={true}
                accessibilityLabel={this.state.isMute ? DescriptionConstants.rp_15 : DescriptionConstants.rp_32}
                accessibilityState={{
                  selected: this.state.isMute
                }}
                testId={this.state.isMute ? '1' : '0'}
              />
            </View>

            <View style={styles.videoControlBarItem}>

              <MHLottieFullScreenToolButton
                style={styles.videoControlBarItemImg}

                onPress={() => {
                  this._hidePlayToolBarLater();
                  if (!this.state.fullScreen) {
                    this.setState({
                      isWhiteVideoBackground: false
                    });
                    this.toLandscape();
                  } else {
                    this.setState({
                      isWhiteVideoBackground: true
                    });
                    this.toPortrait();
                  }
                }}
                disabled={this.state.showPoweroffView}
                displayState={MHLottieFullScreenToolBtnDisplayState.NORMAL}
                accessibilityLabel={DescriptionConstants.rp_16}
              />
            </View>
          </View>
        </LinearGradient>
      );

    } else {
      return (null);
    }
  }

  _renderSnapshotView() {
    if (!this.state.screenshotVisiblity) {
      return null;
    }

    // let recordItem = (
    //   <View style={{
    //     display: "flex",
    //     flexDirection: "row",
    //     bottom: 0,
    //     position: "absolute",
    //     alignItems: "center"
    //   }}>
    //     <Image style={{ width: 12, height: 12, marginLeft: 10 }} source={require("../../Resources/Images/icon_snapshot_camera_play.png")}></Image>
    //     <Text style={{ fontSize: kIsCN ? 12 : 10, fontWeight: "bold", color: "#ffffff", marginLeft: 5 }}>{this.lastRecordTime}</Text>
    //   </View>
    // );
    let recordItem = (
      <LinearGradient colors={['#00000000', '#00000077']} style={{ position: 'absolute', bottom: 0, width: "100%", height: "30%",borderRadius: 4}}>
        <View style={{
          display: "flex",
          flexDirection: "row",
          bottom: 2,
          left: 6,
          position: "absolute",
          alignItems: "center"
        }}>
          <Image style={{ width: 10, height: 10 }} source={require("../../Resources/Images/icon_snapshot_camera_play.png")}></Image>
          <Text style={{ fontSize: kIsCN ? 12 : 10, fontWeight: "bold", color: "white", marginLeft: 5 }}>{this.lastRecordTime}</Text>
        </View>
      </LinearGradient>
    );

    let sWidth = 90;
    let sHeight = 55;
    let sPadding = 20;
    let leftPading = 15;
    if (this.state.fullScreen) {
      sPadding = 90;
      leftPading = leftPading + StatusBarUtil._getInset("top");
    } else {
    }

    let containerStyle;
    containerStyle = {
      position: "absolute",
      left: leftPading,
      top: sPadding,
      width: sWidth,
      height: sHeight,
      borderRadius: 4,
      borderWidth: 1.5,
      borderColor: "xm#ffffff",
      zIndex: 10
    };

    if (this.state.fullScreen) {
      if (Platform.OS == "ios") {
        if (isIphoneX() || isIphone12 || isIphone12P) {
          containerStyle.left = 60;
        }

      }
      if (Host.isPad) {
        containerStyle.top = "50%";
        containerStyle.marginTop = -1 * sHeight / 2;
      }
    }

    return (
      <View style={containerStyle}
      >
        <ImageButton
          accessibilityLabel={this.isForVideoSnapshot ? DescriptionConstants.hk_2_6 : !this.state.fullScreen ? DescriptionConstants.rp_42 : DescriptionConstants.rp_46}
          style={{ width: "100%", height: "100%", borderRadius: 2.5 }}
          source={(this.state.screenshotPath == "") ? null : ({ uri: `file://${Host.file.storageBasePath}/${this.state.screenshotPath}` })}
          fadeDuration={0}
          onPress={() => {
            if (!this.canStepOut()) {
              return;
            }
            clearTimeout(this.snapshotTimeout);
            this.setState({ screenshotVisiblity: false, screenshotPath: "", isWhiteVideoBackground: true });// 点击后就消失。
            if (this.isForVideoSnapshot) {
              console.log("点击了缩略图，跳转到视频页面");
              this.showLastVideo();
              // this.props.navigation.navigate("AlbumVideoViewPage");
            } else {
              console.log("点击了缩略图，跳转到图片页面");
              this.showLastImage();
              // this.props.navigation.navigate("AlbumPhotoViewPage");
            }

            this.isForVideoSnapshot = false;
            // todo jump to album activity
          }}
        />
        {this.isForVideoSnapshot ? recordItem : null}

      </View>
    );
  }

  canStepOut() {
    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return false;
    }

    if (this.state.showPoweroffView) {
      Toast.success("camera_power_off");
      return false;
    }
    return true;
  }

  _renderTimeLineView() {
    // 如果这里设置为null，等切屏回来后，就生成了一个新的view。原先绑定的数据就没了。
    // if (this.state.fullScreen) {
    //   return null;
    // }
    let containerStyle;
    if (this.state.fullScreen) {
      let padding = 0;
      if (Platform.OS == "ios") {
        if (isIphoneX()) {
          padding = this.statusBarHeight + 5;
        }
        padding = this.statusBarHeight + 15;
      }
      containerStyle = {
        position: "absolute",
        bottom: 0,
        paddingLeft: padding,
        paddingRight: padding,
        width: "100%"
      };
    } else {
      containerStyle = { width: "100%", flex: 1 };
    }
    const { touchMovement } = this.state;
    return (// scaletimelineView自己处理宽高。
      // <SafeAreaView style={{display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center"}}>
      <View style={[containerStyle]}
        accessibilityLabel={DescriptionConstants.rp_60}>
        <View style={this.state.fullScreen && this.state.showPlayToolBar ? { flexGrow: 1 } : { display: "none" }}>
          <TimeScaleView2_1
            style={{ flexGrow: 1 }}
            ref={(ref) => { this.timelineViewFull = ref; }}
            // onCenterValueChanged={this._onCenterValueChanged}
            onScrolling={this._onScrolling}
            onScrollEnd={this._onCenterValueChanged}
            onPlayStateChange={(playing) => {
              if (playing) {
                this.playStartClick();
              } else {
                this.playPauseClick();
              }
            }}
            isRecording={this.state.isRecording}
            isPlaying={this.state.isPlaying}
            isDisabled={(this.state.isEmpty || this.state.isSleep) ? true : false}
            landscape={this.state.fullScreen}
            eventTypeFlags={this.state.eventTypeFlags}
            touchMovement={touchMovement}
          />
        </View>
        <View style={!this.state.fullScreen ? { flexGrow: 1 } : { display: "none" }}>
          <TimeScaleView3
            style={{ flexGrow: 1 }}
            ref={(ref) => { this.timelineView = ref; }}
            // onCenterValueChanged={this._onCenterValueChanged}
            onScrolling={this._onScrolling}
            onScrollEnd={this._onCenterValueChanged}
            isDisabled={(this.state.isEmpty || this.state.isSleep) ? true : false}
            landscape={this.state.fullScreen}
            isRecording={this.state.isRecording}
            eventTypeFlags={this.state.eventTypeFlags}
            touchMovement={touchMovement}
          />
        </View>
      </View>
      // </SafeAreaView>

    );
  }


  // 这里代表时间轴滚动了
  _onCenterValueChanged = (timestamp) => {
    TrackUtil.reportClickEvent("TimeSlider_Drop_ClickNum");
    console.log("滑动结束");
    this.setState({ touchMovement: false });
    this.dateTime.setTime(timestamp);
    // console.log(`timestamp:${timestamp}`);
    console.log(`${this.dateTime.getHours()} ${this.dateTime.getMinutes()} ${this.dateTime.getSeconds()}`);

    if (this.timeIndicatorView == null) {
      return;
    }
    this.timeIndicatorView.setState({ centerTimestamp: 0 });

    this.toStartTime = timestamp;
    // if (this.state.isPlaying) {
    this.queryNetworkJob();// 检测网络
    // }
    // 如果没有播放  就不用管了

  }

  _renderAngleView() {
    if (!this.state.showCameraAngleView) {
      return (null);
    }

    let sPadding = 20;
    let bottom = this.state.fullScreen ? (kWindowHeight > 600 ? 250 : 180) : (kWindowHeight > 600 ? (kWindowWidth / 1.78 - 28 - sPadding) : 80);// 28 is angle view's height
    let left = this.state.fullScreen ? 55 : sPadding;
    let angleStyle = {
      position: "absolute",
      left: left,
      bottom: bottom
    };

    return (
      <View style={angleStyle}>
        <RectAngleView
          ref={(ref) => { this.angleView = ref; }}
          angle={this.state.angle}
          elevation={this.state.elevation}
          scale={this.state.videoScale}
          showScale={this.state.angleViewShowScale}
          accessibilityLabel={DescriptionConstants.zb_39.replace('1', this.state.videoScale)}
        />
      </View>
    );
  }

  _renderBottomSelectView() {
    if (this.state.fullScreen) {
      return;
    }

    return (
      <View style={{ width: "100%", flexGrow: 1, position: "relative" }}>
        <TouchableOpacity
          accessibilityLabel={DescriptionConstants.rp_44}
          style={{ position: "absolute", bottom: 0, width: "100%", height: 46, marginBottom: 20, marginTop: 8, paddingHorizontal: 24 }}
          onPress={() => { this.onPressSeeAllVideo(); }}
        >
          <View
            style={{ width: "100%", height: "100%", backgroundColor: isDark ? "#474747" : "#F5F5F5", borderRadius: 23, display: "flex", alignItems: "center", justifyContent: "center" }}
          >
            <Text
              style={{ color: "#4C4C4C", fontSize: kIsCN ? 16 : 14, fontWeight: 'bold', marginHorizontal: 40, textAlignVertical: "center", textAlign: 'center' }}
            >
              {LocalizedStrings["all_playback_video"] + " "}
            </Text>
          </View>

        </TouchableOpacity>

      </View>
    );
  }

  _orientationListener = (orientation) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    console.log("orientation changed");
    if (!this.isPageForeGround || !this.isPluginForeGround || !this.isAppForeground) {
      return;
    }
    console.log(TAG, `device orientation changed :${orientation} want ${this.mOri}`);
    if ((Platform.OS == "ios" || (Host.isAndroid && Host.isPad)) && ((this.mOri === 'LANDSCAPE' && this.state.fullScreen) || (this.mOri === 'PORTRAIT' && !this.state.fullScreen))) {
      return;
    }
    if (this.mOri === orientation) {
      if (orientation === 'LANDSCAPE') {
        // do something with landscape layout
        this._setNavigation(true);
      } else {
        // do something with portrait layout
        // this.setState({ fullScreen: false });
        this._setNavigation(false);
      }
    } else {
      // ios need restore for next lock
      this.restoreOri();
    }
  };

  _setNavigation(isFull) {
    // if (Host.isPad) { // 直播页面也需要调用
    //   Service.miotcamera.enterFullscreenForPad(isFull ? true : false);
    // }
    this.props.navigation.setParams({ isFullScreen: isFull });
    this.setState({ fullScreen: isFull });
  }

  componentDidMount() {
    TrackUtil.reportClickEvent("TimeSlider_SDCard_Num");
    DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }

    this.restoreOri();
    Orientation.addOrientationListener(this._orientationListener);

    Service.miotcamera.bindTimelinePlaybackEndListener(timelinePlaybackEndListenerName);

    // SdFileManager.getInstance().bindReceiveFilesListener(this._bindFilesHandler);
    // SdFileManager.getInstance().startRequestSdcardFilesRegularly();
    // 加载后，就先destroyInstance，再重新创建，重新加载。



    this.recordListener = DeviceEventEmitter.addListener(kRecordTimeCallbackName, (data) => {
      if (data == null) {
        return;
      }
      let time = Number.parseInt(data.recordTime);// 要屏蔽倍速带来的影响
      this.setState({ recordTimeSeconds: time });
      console.log(data);// 录制时长。
    });

    if (Platform.OS == "ios") {
      this.statusBarHeight = getStatusBarHeight();
    }

    this._loadSdfiles();
    if (!CameraPlayer.getInstance().isConnected()) {
      this.didMountTime = Date.now();
    }

    if (!Device.isOnline) {
      this.setState({ showErrorView: true, errTextString: LocalizedStrings['device_offline'] });
      OfflineHelper.getLastOnlineTime()
        .then((result) => {
          this.setState({ lastOfflineTime: `${LocalizedStrings['offline_time_str']}: ${result}` });
        })
        .catch((rr) => {
        });
    }
    this.isOffline = false;
    if (!Device.isOnline) {
      this.isOffline = true;
    }

    StorageKeys.IS_VIP_STATUS.then((res) => {
      this.isVip = res;
    });
  }

  _loadSdfiles() {
    let sdcardListStartTime = Date.now();
    // here to delete all datas;
    SdcardEventLoader.getInstance().getEventList()
      .then(() => {
        this.sdcardListRequestTime = Date.now() - sdcardListStartTime;
        this.onGetFiles();// 数据已经读取完毕了。
        // 加载成功了。
      })
      .catch(() => {
        // 加载失败了
      });
  }

  _powerOffHandler = (isPowerOn, showSleepDialog, startVideo) => {
    if (this.cameraRenderView == null) {
      CameraPlayer.getInstance().bindPowerOffCallback(null);
      return;
    }
    // 电源属性状态发生了变化
    this.setState({ showPoweroffView: !isPowerOn, showPlayToolBar: isPowerOn, isPlaying: isPowerOn });
    if (!isPowerOn) { // 暂停
      this._startPlay(false);
      this._stopRecord();
    } else {
      this.queryNetworkJob();
    }
  }

  _networkChangeHandler = (networkState) => {
    if (this.cameraRenderView == null) {
      return;
    }
    // if (this.currentNetworkState != networkState) { // 网络状态不一样
    //   this.isChangingNetwork = true;
    // }
    // if (this.currentNetworkState != networkState) { // 网络状态不一样
    //   this.isChangingNetwork = true;
    // }
    console.log("处理网络变化", networkState);

    this.currentNetworkState = networkState;
    clearTimeout(this.showNetworkDisconnectTimeout);
    if (networkState == 0 || networkState == -1) { // 网络断开了连接 showError?
      Service.smarthome.reportLog(Device.model, "网络异常" + networkState);
      // CameraPlayer.getInstance().disconnectToDevice();// 去往其他注册了网络监听的页面，就不会走到这里了，如果走到这里，这里必须先执行断开的操作
      this.showNetworkDisconnectTimeout = setTimeout(() => {
        this.handleDisconnected(MISSError.MISS_ERR_CLOSE_BY_LOCAL);// 刷新UI，避免出现异常。  
      }, 1300);
      return;
    }
    if (this.isPageForeGround) { // 有网络来了  发起重连吧
      this.setState({ showErrorView: false });
      clearTimeout(this.reConnectTimeout);
      this.reConnectTimeout = setTimeout(() => {
        this.queryNetworkJob();
      }, 500);// 过一会再查询 ，那个查询网络类型的api有问题。
    }
  }

  _ServerRespHandler = ({ command, data }) => {
    // todo  处理文件播放的问题
    // console.log("server response:", command, data);
    if (command == MISSCommand.MISS_CMD_PLAYBACK_RESP) {
      console.log(data);
      let dataJSON = Host.isAndroid ? JSON.parse(data) : data;
      let id = dataJSON["id"];
      if (id == null) {
        return;
      }
      if (id != this.sessionId) {
        return;
      }
      let status = dataJSON["status"];
      if (status == null) {
        return;
      }
      let startTime = dataJSON.starttime;
      let duration = dataJSON.duration;
      switch (status) {
        case "filefound":

          console.log(dataJSON);
          LogUtil.logOnAll("SdcardTimeline", "filefound:" + JSON.stringify(dataJSON));//收到的视频帧信息
          // what todo ?  
          // this.setState({ showLoadingView: false });
          // 开始请求timestamp
          this.endTime = startTime + duration;
          //文件找到后才开始拉取时间戳；
          if (this.firstPullVideoFrameTime != 0) {
            let firstVideoFrameShowed = Date.now() - this.firstPullVideoFrameTime;
            LogUtil.logOnAll(TAG, "sdcard connectTime:" + this.connectionTime + " pullSdcardFileList:" + this.sdcardListRequestTime + " firstVideoFrameShowd:" + firstVideoFrameShowed);
            this.firstPullVideoFrameTime = 0;
          }

          CameraPlayer.getInstance().getPlaybackTimetampInterval();
          CameraPlayer.getInstance().bindPlaybackTimestampCallback(this.timestampCallback);


          break;
        case "filenotfound":
          this.setState({ progress: 0, showErrorView: true, showLoadingView: false, showPlayToolBar: false, showPauseView: false, errTextString: LocalizedStrings["camera_play_error_file"] });
          if (this.state.isRecording) {
            this._stopRecord();
          }
          this._startPlay(false);
          break;
        case "endoffile":
          // this.isUserPause = true;
          // this.setState({ showLoadingView: false, progress: 0 });
          // this._startPlay(false);
          // this.setState({ showPauseView: true });
          // 取消timestamp的定时请求
          if (this.islastestFilePlayFailed && (this.penultimateStartTime + 60000 - this.endTime * 1000 < 1500)) { // 上一次播放最后一个文件失败了，这一次播放到倒数第二个文件就不播放了。
            this.toSdcardEnd();
          }
          // CameraPlayer.getInstance().stopPlaybackTimestampInterval();

          LogUtil.logOnAll("SdcardTimeline", "endoffile:" + JSON.stringify(dataJSON) + " last endTime:" + (this.endTime * 1000) + " lastest endTime:" + this.lastTimeItemEndTime);//收到的视频帧信息
          if (this.lastTimeItemEndTime != 0 && this.lastTimeItemEndTime - this.endTime * 1000 < 1500) {
            this.toSdcardEnd();
          }
          break;
        case "readerror":
          if (VersionUtil.judgeIsV1(Device.model) && (this.sessionId * 1000 >= this.lastTimeItemStartTime) && (this.penultimateStartTime > 0)) { // 针对v1做一次纠正，部分固件播放最后一个没问题，部分固件播放最后一个有问题，这里做一次替换。
            this.toStartTime = this.penultimateStartTime;
            clearTimeout(this.timestampTimeout);
            this.islastestFilePlayFailed = true;
            this._startPlay(true);
          } else {
            this.setState({ progress: 0, showLoadingView: false, showErrorView: true, showPauseView: false, showPlayToolBar: false, errTextString: LocalizedStrings["camera_play_error_file"] });
            this._startPlay(false);
          }
          // 取消timestamp的定时请求
          CameraPlayer.getInstance().stopPlaybackTimestampInterval();
          break;
      }
    }
  }

  timestampCallback = (timestamp) => {
    if (this.timelineView == null) {
      return;
    }
    // console.log(`lastTimeItemEndTime:${this.lastTimestamp}`, `currentTimestamp:${timestamp}`);

    // 这里每隔一秒就会触发一次 返回当前的时间戳
    if (timestamp == this.lastTimestamp || this.timelineView.scrollState === SCROLLSTATE.SCROLLING) { // 没有发生更新

      return;
    }

    if (timestamp && timestamp.toString().length < 10) {
      return;
    }

    // if (this.lastTimeItemEndTime - timestamp * 1000 < 1500 ) {//接近文件末尾了 直接pause
    //   this.toSdcardEnd();
    //   return;
    // }
    this.toStartTime = timestamp * 1000;
    if (this.isSetPlayTime) {
      let diff = timestamp - this.startTime;
      // console.log(diff, timestamp, this.startTime);
      if (Platform.OS == "ios") {
        this.setState({ showLoadingView: false, showPauseView: true, showPlayToolBar: true });
        this.isSetPlayTime = false;
        this._hidePlayToolBarLater();
      }
      if (Math.abs(this.offset - diff) <= 20 || (new Date().getTime() - this.setPlayTimeMillis) > 6000) { // 刚刚设置了playback指令， 要消除loading
        // todo hide loading
        this.setState({ showLoadingView: false, showPauseView: true, showPlayToolBar: true });
        this.isSetPlayTime = false;
        this._hidePlayToolBarLater();
      } else {
        return;// 不管，等后面来timestamp
      }
    }

    this.lastTimestamp = timestamp;
    // console.log(`updated time:${ this.lastTimestamp }`);
    let timestampMillons = timestamp * 1000;
    this.dateTime.setTime(timestampMillons);
    // console.log(`scroll to time:${ this.dateTime.getHours() }:${ this.dateTime.getMinutes() }:${ this.dateTime.getSeconds() }`);
    // 可能存在播放与日期时间不一致 场景：从看家列表查看更多-这里播放无对应天的SD卡回看数据
    if (this.state.curDate.getMonth() != this.dateTime.getMonth() || this.state.curDate.getDate() != this.dateTime.getDate()) {
      console.log(TAG, "update curDate for Calendar", `${ this.dateTime.getHours() }:${ this.dateTime.getMinutes() }:${ this.dateTime.getSeconds() }`);
      this.setState({ curDate: new Date(timestampMillons) });
    }
    this.timelineView && this.timelineView.scrollToTimestamp(timestampMillons);
    this.timelineViewFull && this.timelineViewFull.scrollToTimestamp(timestampMillons);
  }

  _startSnapshot() {
    if (Platform.OS === "android") {
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this._realStartSnapshot(false);
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            // Toast.success("camera_no_write_permission");
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
          Toast.success("action_failed");
        });
    } else {
      // no ios's photos const use hardcode
      System.permission.request("photos").then((res) => {
        this._realStartSnapshot(false);
      }).catch((error) => {
        // Toast.success("camera_no_write_permission");
        this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
      });
    }
  }

  _realStartSnapshot(isFromVideo) {
    clearTimeout(this.snapshotTimeout);
    this.setState({ screenshotVisiblity: false, screenshotPath: null });
    AlbumHelper.snapShot(this.cameraRenderView)
      .then((path) => {
        // console.log(path);
        this.isForVideoSnapshot = isFromVideo;
        this.setState({ screenshotVisiblity: true, screenshotPath: path });// show snapshotview
        clearTimeout(this.snapshotTimeout);
        this.snapshotTimeout = setTimeout(() => {
          this.isForVideoSnapshot = false;
          this.setState({ screenshotVisiblity: false, screenshotPath: null });
        }, 3000);
        // 文件路径。
      })
      .catch((error) => {
        // console.log(JSON.stringify(error));
        Toast.success("action_failed");
      });
  }

  _toggleAudio(isMute, changeUnitMute = false) {
    if (!isMute) {
      TrackUtil.reportClickEvent("TimeSlider_OpenVolume_ClickNum")
    }

    if (isMute) {
      if (!this.state.isRecording && this.state.isPlaying) {
        Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {}).then((retCode) => {
          // console.log("audio stop get send callback");
          // console.log(retCode);
        });
      }
      if (this.state.isPlaying) {
        this.cameraRenderView && this.cameraRenderView.stopAudioPlay();
      }

      this.setState({ isMute: true });
      if (changeUnitMute) {
        CameraConfig.setUnitMute(true);
      }
      return;
    }
    if (this.state.speed > 1) {
      this._toggleAudio(true, false);
      return;// 倍速模式下 不要播放声音
    }
    if (!this.state.isRecording && this.state.isPlaying) {
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
        // console.log("audio stop get send callback");
        // console.log(retCode);
      });
    }

    if (this.state.isPlaying) {
      this.cameraRenderView && this.cameraRenderView.startAudioPlay();
    }
    this.setState({ isMute: false });
    if (changeUnitMute) {
      CameraConfig.setUnitMute(false);
    }
  }

  // 进来先调用startPlay  startPlay里会check连接状态，如果是连接中的状态，直接发playback指令；如果不是连接中的状态，先连接，连接走到这里 如果连接成功，就会重新走startPlay流程
  _connectionHandler = (connectionState) => {
    if (connectionState == null) {
      return;
    }
    if (connectionState.state == 4) { // for tutk 4 是 miss状态1的一个子状态，去掉
      connectionState.state = 1;
    }
    if (this.tempState == connectionState.state && this.state.error == connectionState.error) {
      return;// 状态一样 没有必要通知
    }
    this.tempState = connectionState.state;
    this.tempError = connectionState.error;
    if (connectionState.state == 0) { // 断开连接
      this.isConnecting = false;
      if (this.connRetry > 0) {
        this.connRetry = this.connRetry - 1;
        setTimeout(() => {
          this.queryNetworkJob();
        }, 300);
        // console.log("connection retry");

        return;
      }
      this.handleDisconnected(connectionState.error);
      this.setState({
        pstate: connectionState.state,
        error: connectionState.error
      });
      return;
    }


    this.setState((state) => {
      return {
        pstate: connectionState.state,
        error: connectionState.error
      };
    }, () => {
      if (this.state.pstate >= 2) { // 连接成功后就会重新开始播放
        if (!this.isConnecting) { // 处理tutk断线自动重连重复发送connected消息导致的问题。
          return;
        }

        if (this.isFirstReceiveFiles) {
          this.connectionTime = Date.now() - this.didMountTime;
        }
        this.connRetry = 2;
        this.isConnecting = false;
        // on connected
        if (this.lastTimeItemEndTime == 0 || this.lastTimeItemEndTime == null) {
          this._loadSdfiles();// load sdcards
        } else { // 已经有数据了。
          this._startPlay(true);
        }
      }
    });

  }


  handleDisconnected(errorCode) {
    this.cameraRenderView && this.cameraRenderView.stopAudioPlay();
    this.cameraRenderView && this.cameraRenderView.stopRender();
    // sync ui state
    this.setState({ isMute: CameraConfig.getUnitMute() });
    this._stopRecord();
    // 如果网络已经断开了连接。  network change引起的p2p连接变更已经不能走到这里了

    if (!Device.isOnline) {
      return;
    }
    let errorStr = ((errorCode == 36 || errorCode == MISSError.MISS_ERR_MAX_SESSION) && VersionUtil.judgeIsMiss(Device)) ? LocalizedStrings["max_client_exceed"] : (errorCode == -6 && !VersionUtil.judgeIsMiss(Device) ? LocalizedStrings["max_client_exceed"] : `${LocalizedStrings["camera_connect_error"]} ${errorCode}, ${LocalizedStrings["camera_connect_retry"]}`);

    this.setState({ showPlayToolBar: false, showErrorView: true, isPlaying: false, showLoadingView: false, errTextString: errorStr });
  }

  _startPlay(isPlay, ignoreState = false) {

    if (isPlay) {
      if (this.lastTimeItemEndTime == 0) { // 没有数据
        return;
      }
      this.setState({ showPlayToolBar: true });
      // 开始寻找合适的item
      let startTime = this.toStartTime;
      if (startTime >= this.lastTimeItemEndTime) {
        startTime = SdFileManager.getInstance().getLastestItemStartTime();
      }
      if (startTime <= 0) {
        return;
      }
      if (SdcardCloudTimelinePageV2.toSDCardPageTime > 0) {
        startTime = SdcardCloudTimelinePageV2.toSDCardPageTime;
        SdcardCloudTimelinePageV2.toSDCardPageTime = 0;
      }
      this.dateTime.setTime(startTime);
      // console.log(`开始播放 format time:${this.dateTime.getHours()}:${this.dateTime.getMinutes()}:${this.dateTime.getSeconds()}`);

      let timeItem;
      if (Device.model === VersionUtil.Model_Camera_V1) {
        timeItem = SdFileManager.getInstance().getTimeItemClosestForV1(startTime);
      } else {
        timeItem = SdFileManager.getInstance().getTimeItemClosest(startTime);
      }
      if (timeItem != null) {
        this.startTime = Number.parseInt(timeItem.startTime / 1000);
        let duration = Number.parseInt(timeItem.duration / 1000);
        if (timeItem.startTime < startTime) {
          this.offset = Number.parseInt((startTime - timeItem.startTime) / 1000);
          if (this.offset >= duration) {
            this.offset = duration - 2;
          }
        } else {
          this.offset = 0;
        }
        this.isSetPlayTime = true;
        this.setPlayTimeMillis = new Date().getTime();
        // show loading

      } else { // 这里只可能是选中的时间比最后一个文件的endTime 还远一些。
        this.toSdcardEnd();
        return;
      }

      CameraPlayer.getInstance().stopPlaybackTimestampInterval();// 停止抓取

      console.log(`start play:${this.startTime}`);
      this.dateTime.setTime(this.startTime * 1000);
      console.log(`start play format time:${this.dateTime.getHours()}:${this.dateTime.getMinutes()}:${this.dateTime.getSeconds()}`);


      this.cameraRenderView && this.cameraRenderView.stopRender();// 重新开启播放之前 先暂停播放。
      this.cameraRenderView && this.cameraRenderView.startRender();// startBindVideo
      if (Platform.OS == 'ios' && !VersionUtil.judgeIsMiss(Device)) {
        Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_VIDEO_START, {})
          .then((retCode) => {
            this._startPlayback();
          }).catch((err) => {
            this._p2pCommandErrHandler(err, "startVideo");
          });
      } else {
        this._startPlayback();
      }

    } else {
      CameraPlayer.getInstance().stopPlaybackTimestampInterval();// 停止抓取
      CameraPlayer.getInstance().bindPlaybackTimestampCallback(null);
      if (!ignoreState) {
        // stop播放
        this.setState({ isMute: this.state.speed > 1 ? true : CameraConfig.getUnitMute(), isPlaying: false, showPauseView: true, showPlayToolBar: true, showLoadingView: false });
      }
      clearTimeout(this.showPlayToolBarTimer);
      // stop video
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_VIDEO_STOP, {}).then((retCode) => {
        console.log("video stop");

        console.log(retCode);
      }).catch((err) => console.log(err));

      if (!this.state.isRecording) {
        // stop audio
        Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {}).then((retCode) => {
          console.log("audio stop");
          console.log(retCode);
        }).catch((err) => console.log(err));

      }

      this.cameraRenderView && this.cameraRenderView.stopAudioPlay();
      this.cameraRenderView && this.cameraRenderView.stopRender();

      Service.miotcamera.setTimelinePlaybackMode(false);
      // stop audio local resources

    }
  }

  _p2pCommandErrHandler(err, src) {
    Service.smarthome.reportLog(Device.model, `${TAG} p2p err, src: ${src}`);

    this.cameraRenderView && this.cameraRenderView.stopRender();// 重新开启播放之前 先暂停播放。
    if (err == -1 || err == -8888) { // - 8888重置本地连接，然后开始重连。
      CameraPlayer.getInstance().resetConnectionState();
      this.queryNetworkJob();
      return;
    }

    this.setState({ pstate: 0, showLoadingView: false, showErrorView: true, errTextString: `${LocalizedStrings["camera_connect_error"]} ${err} ${LocalizedStrings["camera_connect_retry"]}` });// 已经渲染过  直接跳过去

    Service.miotcamera.setTimelinePlaybackMode(false);
  }

  _startPlayback() {
    CameraPlayer.getInstance().startPlayBack(this.startTime, this.offset, 0, this.state.speed)
      .then((res) => {
        Service.miotcamera.bindTimelinePlaybackEndListener(timelinePlaybackEndListenerName);
        //
        this.setState(() => { return { showErrorView: false, showLoadingView: true, isPlaying: true, showPauseView: true }; }, () => {
          let position = (this.state.speed == 1 ? 0 : (this.state.speed == 4 ? 1 : 2));
          this.updateSpeed(position);
        });
        this.sessionId = this.startTime;
        if (!this.state.isMute) {
          this.cameraRenderView && this.cameraRenderView.stopAudioPlay();
          Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
            // console.log("audio stop get send callback");
            console.log(retCode);
          });
          this.cameraRenderView && this.cameraRenderView.startAudioPlay();
        }
        setTimeout(() => {
          CameraPlayer.getInstance().getPlaybackTimetampInterval();
          CameraPlayer.getInstance().bindPlaybackTimestampCallback(this.timestampCallback);
        }, 1500);// 1.5s后再拉

        Service.miotcamera.setTimelinePlaybackMode(true);
      })
      .catch((err) => {
        this._p2pCommandErrHandler(err, "startPlayback");
      });

  }

  changeEventType(eventType) {
    TrackUtil.reportClickEvent("TimeSlider_Motion_ClickNum");
    let eventTypeFlag = this.state.eventTypeFlags;
    console.log("eventTypeFlag:", eventTypeFlag, "eventType:", eventType, "result:", eventTypeFlag & eventType);
    let temp = eventTypeFlag & eventType;
    if (temp !== 0) { // 已经有了这个值  取反
      eventTypeFlag = eventTypeFlag & ~eventType;
    } else {
      eventTypeFlag = eventTypeFlag | eventType;
    }
    this.setState({ eventTypeFlags: eventTypeFlag });
  }

  updateSpeed(position, lockSpeed = true) {

    if (this.state.isRecording) {
      return;
    }
    let speed = 1;
    // if (specificSpeed == null) {
    switch (position) {
      case 0:
        speed = 1;
        break;
      case 1:
        speed = 4;
        break;
      case 2:
        speed = 16;
        break;
      default:
        speed = 1;
        break;
    }
    // }
    if (lockSpeed) {
      this.selectedIndexArray = [position];
    }
    this.setState({ speed: speed });

    if (!this.state.isPlaying) {
      if (speed != 1) {
        this._toggleAudio(true, false);
      } else {
        this._toggleAudio(CameraConfig.getUnitMute(), false);
      }
      return;
    }
    CameraPlayer.getInstance().changeSpeed(speed)
      .then(() => {
        if (speed == 1) {
          this._toggleAudio(CameraConfig.getUnitMute(), false);
        } else if (!this.state.isMute) {
          this._toggleAudio(true, false);
        }
      })
      .catch((err) => {
        Toast.fail("action_failed", err);
      });
  }

  _onVideoClick() {
    LogUtil.logOnAll(TAG, "sdcard timeline player page onVideoClick");

    if (!CameraPlayer.getInstance().isConnected()) {
      return;
    }

    this.setState((state) => {
      return {
        showPlayToolBar: !this.state.showPlayToolBar,
        showPauseView: !this.state.showPlayToolBar
      };
    }, () => {
      this._hidePlayToolBarLater();
    });
    LogUtil.logOnAll("sdcardtimeline click video view");
  }

  // 更新倍数
  _updateScale(scale) {
    if (scale) {
      scale = Number(scale);

      if (scale < 1) {
        scale = 1;
      }

      if (this.angleViewTimeout) {
        clearTimeout(this.angleViewTimeout);
        this.angleViewTimeout = null;
      }

      this.angleViewTimeout = setTimeout(() => {
        this.setState({ showCameraAngleView: false, angleViewShowScale: false });
      }, 3000);
      console.log(`_onVideoScaleChange111${scale}`);
      this.angleView?.setScale(scale);
      this.setState({ videoScale: scale, showCameraAngleView: true, angleViewShowScale: true, showPlayToolBar: scale > 1 ? false : true, showPauseView: scale > 1 ? false : true });
    }
  }
  // 设置定时器
  videoScaleTimer = null;

  _onVideoScaleChanged(params) {
    if (Platform.OS === "android" && this.firstInPage) {//刚进入页面不调用
      this.firstInPage = false;
      return;
    }
    let scale = params.nativeEvent?.scale;
    // 当返回有倍数时 清除定时器 并更新倍数 相当于防抖操作 一直触发事件就一直清空定时器
    if (scale) {
      clearTimeout(this.videoScaleTimer);

      this.videoScaleTimer = setTimeout(() => {
        console.log("tick" + scale);
        this._updateScale(scale); // 更新倍数
      }, 0);
    }
    if (params && params.nativeEvent && params.nativeEvent.firstVideoFrame) {
      console.log(TAG, "received firstVideoFrame");
      this.setState({ showDefaultBgView: false, showLoadingView: false, whiteTitleBg: false });
    }
    // here can be replaced by throttol
    let endTime = new Date().getTime();
    if ((endTime - this.startScaleTime) < 50) {
      return;
    }
    this.startScaleTime = endTime;
    this._updateScale(scale);
  }

  // android返回键处理
  onBackHandler = () => {
    if (!this.isPageForeGround) {
      return false;
    }
    if (this.state.fullScreen) {
      this.toPortrait();
      this._hidePlayToolBarLater(true);
      return true;
    }
    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return true;
    }

    this._onPause();
    return false;// 不接管
  }

  queryNetworkJob() {
    if (this.isUserPause) {
      return;// 用户主动暂停的
    }
    if (!this.props.navigation.isFocused()) {
      return;
    }

    CameraPlayer.getInstance().queryShouldPauseOn4G()
      .then(({ state, pauseOnCellular }) => {
        this.networkType = state;
        if (state === "NONE" || state === "UNKNOWN") {
          this._networkChangeHandler(0);
          return;
        }
        if (state === "CELLULAR" && pauseOnCellular && !this.ignoreDataWarning) { // 普通网络 && 数据流量提醒
          if (this.state.isPlaying) {
            Toast.success("nowifi_pause", true);
          }
          this._startPlay(false);
          return;
        }

        // 其他网络条件 走连接的步骤吧
        this._startConnect();// 开始连接
      })
      .catch(() => { // 获取网络状态失败 也直接走开始连接的流程
        this._startConnect();// 开始连接
      });
  }

  _startConnect() {

    if (!this.props.navigation.isFocused()) { // 当前页面已经不在前台了
      this.setState({ showLoadingView: false });
      return;
    }
    if (!this.state.showLoadingView) { // 如果没有loading
      this.setState({ showLoadingView: true });
    }
    if (this.state.showPauseView) {
      this.setState({ showPauseView: false });
    }
    // 开始连接
    if (CameraPlayer.getInstance().isConnected()) {
      // 如果已经连接成功 直接发送video_start
      this.setState({ pstate: 2, error: 0 });
      this._startPlay(true);
      return;
    }
    this.setState({ pstate: 0, error: 1 });
    this.isConnecting = true;
    CameraPlayer.getInstance().startConnect();
  }


  _stopRecord() {
    if (!this.state.isRecording) {
      return;
    }

    StackNavigationInstance.isRecording = false;

    // if (this.state.resolution != 3) { // 不是高清
    //   Service.miotcamera.sendP2PCommandToDevice(
    //     MISSCommand.MISS_CMD_STREAM_CTRL_REQ, { "videoquality": this.state.resolution })
    //     .then(() => { // 不修改这些信息。
    //
    //     })
    //     .catch((err) => {
    //       console.log(err);
    //     });
    // }

    if (this.state.isMute) { // 原本静音
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {}).then((retCode) => {

      });
    }

    if (this.isStoringVideo) {
      return;
    }
    this.isStoringVideo = true;

    this.cameraRenderView && this.cameraRenderView.stopRecord().then(() => {
      console.log("stopRecord failed   success branch");
      // this.setState({ isRecording: false, recordTimeSeconds: 0 });
      if (this.videoRecordPath == null || this.videoRecordPath == "") {
        this.isStoringVideo = false;
        return;
      }
      this.mRecordOkTimer = setTimeout(() => {
        // 录制成功后 要把视频转存储到相册。
        AlbumHelper.saveToAlbum(this.videoRecordPath, true)
          .then((result) => {
            this.setState({
              videoName: result,
              isRecording: false,
              recordTimeSeconds: 0
            })
            console.log(result);
            if (this.justSnapshotResult) {
              this.isForVideoSnapshot = true;
              this.setState({ screenshotVisiblity: true, screenshotPath: AlbumHelper.getSnapshotName() });// show snapshotview
              clearTimeout(this.snapshotTimeout);
              this.snapshotTimeout = setTimeout(() => {
                this.setState({ screenshotVisiblity: false, screenshotPath: null });
              }, 5000);
            } else { // 截图失败的时候，就使用videoRecordPath
              this.isForVideoSnapshot = true;
              this.setState({ screenshotVisiblity: true, screenshotPath: this.videoRecordPath });// show snapshotview
              clearTimeout(this.snapshotTimeout);
              this.snapshotTimeout = setTimeout(() => {
                this.setState({ screenshotVisiblity: false, screenshotPath: null });
              }, 5000);
            }
            this.isStoringVideo = false;
          })
          .catch((err) => {
            console.log(err);
            this.isStoringVideo = false;
            this.setState({ isRecording: false, recordTimeSeconds: 0 });
          });
      }, 100);
    })
      .catch((error) => {
        this.isStoringVideo = false;
        console.log("stopRecord failed:" + error);
        this.setState({ isRecording: false, recordTimeSeconds: 0 });
        if (error == -2) {
          if (Host.isIOS && this.isPlayEnd) {
            this.isPlayEnd = false;
          } else {
            Toast.fail("record_video_failed_time_mini");
          }
        } else {
          Toast.fail("record_video_failed");
        }
      });
    this.justSnapshotResult = false;
    AlbumHelper.justSnapshot(this.cameraRenderView)
      .then((path) => {
        this.justSnapshotResult = true;
      })
      .catch((error) => {
        this.justSnapshotResult = false;
      })

  }

  _startRecord() {
    if (Platform.OS === "ios") {
      this.isPlayEnd = false;
      // no ios's photos const use hardcode
      System.permission.request("photos").then((res) => {
        this.realStartRecord();
      }).catch((error) => {
        // Toast.success("camera_no_write_permission");
        this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
      });
    } else {

      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this.realStartRecord();
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            // Toast.success("camera_no_write_permission");
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
          Toast.success("action_failed");
        });
    }
  }

  realStartRecord() {
    // 打开声音
    if (this.state.isMute) { // 不是有声音 开启声音 并且播放
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
        console.log("audio stop get send callback");
        console.log(retCode);
      });
    }
    let path = AlbumHelper.getDownloadTargetPathName(true);
    this.videoRecordPath = path;
    this.cameraRenderView && this.cameraRenderView.startRecord(`${Host.file.storageBasePath}/${path}`, kRecordTimeCallbackName)
      .then((retCode) => {
        console.log(`start record, retCode: ${retCode}`);
        this.setState({ isRecording: true, screenshotVisiblity: false });
        StackNavigationInstance.isRecording = true;
      })
      .catch((err) => {
        console.log(err);
        Toast.success("action_failed");
      });
  }


  onPressSeeAllVideo() {
    TrackUtil.reportClickEvent("TimeSlider_AllVedio_ClickNum");

    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return;
    }
    let isOutsea = !CameraConfig.isSupportCloud(); // 不支持云存 只有两个页面 跳index 1   支持云存  有三个页面 跳index 2
    if (this.state.showPoweroffView) {
      Toast.success("camera_power_off");
      return
    }
    // StackNavigationInstance.jumpToStackNavigationPage("SdcardPage", { title: "sdcardPage" });
    if (CameraConfig.shouldDisplayNewStorageManage(Device.model)) {
      StackNavigationInstance.jumpToStackNavigationPage_forSDCloudPage("AllStorage", { initPageIndex: isOutsea ? 1 : 2, isSupportCloud: CameraConfig.isSupportCloud(Device.model) });
    } else {
      StackNavigationInstance.jumpToStackNavigationPage_forSDCloudPage("SdcardPage");
    }
  }

  _onScrolling = (timestamp) => {
    // console.log("滑动中");
    // console.log(timestamp);
    this.dateTime.setTime(timestamp);
    // console.log(`${this.dateTime.getHours()} ${this.dateTime.getMinutes()} ${this.dateTime.getSeconds()}`);
    if (this.timeIndicatorView == null) {
      return;
    }
    this.timeIndicatorView.setState({ centerTimestamp: timestamp });

    if (this.state.curDate.getMonth() != this.dateTime.getMonth() || this.state.curDate.getDate() != this.dateTime.getDate()) {
      console.log(TAG, "update curDate for Calendar", `${ this.dateTime.getHours() }:${ this.dateTime.getMinutes() }:${ this.dateTime.getSeconds() }`);
      this.setState({ curDate: new Date(timestamp) });
    }
  }

  _bindFilesHandler = (status) => { // 收到文件列表的回调
    this.onGetFiles();
  }

  onGetFiles() {
    console.log("-=-=-=-=-=-=onGetFiles-=-=-=-=-=-=");
    let timeItems = SdFileManager.getInstance().getTimeItems();
    if (timeItems == null || timeItems.length <= 0) {
      this.isFirstReceiveFiles = true;
      this.timelineView && this.timelineView.clearList();
      this.timelineViewFull && this.timelineViewFull.clearList();
      let hasvideoDates = [];
      this.setState({ isEmpty: true, hasvideoDates });
      console.log("-=-=-=-=-=-=-=onGetFiles-=-=-=-= empty");
      return;
    }

    this.setState({ isEmpty: false });
    // 这里把数据丢给timelineview
    let hasvideoDates = this.timelineView && this.timelineView.initData(timeItems);
    console.log("+++++++++++++++++++++++++++++hasVideoDates",hasvideoDates);

    let sdcardDays = SdFileManager.getInstance().getTimeItemDays();

    if (sdcardDays == null || sdcardDays.length == 0) {
      sdcardDays = []
    } else {
      sdcardDays = sdcardDays.map((item) => {
        return item.startTime;
      });
    }
    console.log("+++++++++++++++++++++++++++++sdcardDays",sdcardDays);

    this.timelineViewFull && this.timelineViewFull.initData(timeItems);
    this.setState({ hasvideoDates: sdcardDays });
    let lastTimeItem = timeItems[timeItems.length - 1];
    this.lastTimeItemEndTime = lastTimeItem.endTime;
    // LogUtil.logOnAll("SdcardTimeline", "刷新sdcard列表：最后一个文件的信息： " + JSON.stringify(lastTimeItem) + " lastest endTime:" + this.lastTimeItemEndTime);//收到的视频帧信息

    this.lastTimeItemStartTime = lastTimeItem.startTime;
    let penultimateItem = timeItems.length > 1 ? timeItems[timeItems.length - 2] : null;
    if (!this.isFirstReceiveFiles) {
      return;
    }
    if (SdcardCloudTimelinePageV2.toSDCardPageTime > 0) {
      this.lastTimeItemStartTime = SdcardCloudTimelinePageV2.toSDCardPageTime;
      SdcardCloudTimelinePageV2.toSDCardPageTime = 0;
    }
    this.isFirstReceiveFiles = false;
    this.firstPullVideoFrameTime = Date.now();
    this.scrollTimeout1 = setTimeout(() => {

      if (penultimateItem) {
        this.penultimateStartTime = penultimateItem.startTime;
      } else {
        this.penultimateStartTime = 0;
      }
      this.timelineView && this.timelineView.scrollToTimestamp(this.lastTimeItemStartTime);// 不通知
      this.timelineViewFull && this.timelineViewFull.scrollToTimestamp(this.lastTimeItemStartTime);// 不通知
      // 这里开始播放了.
      this.toStartTime = this.lastTimeItemStartTime;
      this.queryNetworkJob();
    });

  }

  toSdcardEnd() {
    // todo jump to sdcard file end
    // todo  pauseCamera
    console.log('停止播放了');
    this.toStartTime = this.lastTimeItemEndTime + 1000;// 播放到文件末尾后，强制+ 1000, 下次找的时候 就会找到文件列表的katou 开头
    this.timelineView && this.timelineView.scrollToTimestamp(this.lastTimeItemEndTime);
    this.timelineViewFull && this.timelineViewFull.scrollToTimestamp(this.lastTimeItemEndTime);
    this._startPlay(false);
    if (Platform.OS === "ios") {
      this.isPlayEnd = true;
    }
    this._stopRecord();// 播放到文件末尾后，主动停止录制。
  }


  showLastImage() {
    StackNavigationInstance.jumpToStackNavigationPage_forSDCloudPage("AlbumPhotoViewPage", { preOri: this.state.fullScreen ? "landscape" : "portrait" });
  }

  showLastVideo() {
    StackNavigationInstance.jumpToStackNavigationPage_forSDCloudPage("AlbumVideoViewPage", { preOri: this.state.fullScreen ? "landscape" : "portrait" });
  }

  _renderResolutionDialog() {
    let bottomWeight = 0.07;
    if (Platform.OS === 'ios') {
      bottomWeight = 0.12;
    }
    let modalStyle = this.state.fullScreen ? {width: kWindowHeight * 0.5, maxHeight: kWindowWidth * 0.85, alignSelf: 'center', bottom: kWindowWidth * bottomWeight, borderBottomLeftRadius: 20, borderBottomRightRadius: 20} : {};
    let options = this.isCameraV1orV3 ? [
      { "title": "x1", accessibilityLabel: DescriptionConstants.rp_13 },
      { "title": "x2", accessibilityLabel: DescriptionConstants.rp_13.replace('1', 2) },
      { "title": "x3", accessibilityLabel: DescriptionConstants.rp_13.replace('1', 3) }
    ] : [
      { "title": "x1", accessibilityLabel: DescriptionConstants.rp_13 },
      { "title": "x4", accessibilityLabel: DescriptionConstants.rp_13.replace('1', 4) },
      { "title": CameraConfig.support8xSpeed(Device.model) ? "x8" : "x16", accessibilityLabel: CameraConfig.support8xSpeed(Device.model) ? DescriptionConstants.rp_13.replace('1', 8) : DescriptionConstants.rp_13.replace('1', 16) }
    ];
    return (
      <AbstractDialog
        visible={this.state.dialogVisibility}
        title={LocalizedStrings["ptzc_play_back_speed_selection_title"]}
        style={modalStyle}
        showSubtitle={false}
        onDismiss={() => {
          this.setState({ dialogVisibility: false });
        }}
        canDismiss={true}
        useNewTheme={true}
        buttons={[
          {
            text: LocalizedStrings["btn_cancel"],
            titleColor: 'rgba(0, 0, 0, 0.8)',
            backgroundColor: { bgColorNormal: DarkMode.getColorScheme() === 'dark' ? '#EEEEEE' : 'rgba(0, 0, 0, 0.04)', bgColorPressed: "rgba(0, 0, 0, 0.08)" },
            callback: () => {
              this.setState({ dialogVisibility: false });
            }
          }
        ]}>
        <ScrollView style={{marginBottom: 10}} contentContainerStyle={{flexGrow: 1}} showsVerticalScrollIndicator={true}>

          {
            options.map((item,index) => {
              return <TouchableOpacity key={`speed_${index}`}
                                       onPress={() => {
                                         this.selectedIndexArray = [index];
                                         this.updateSpeed(index);
                                         this.setState({ dialogVisibility: false });
                                       }}>
                <View style={[{ flexDirection: "row", alignItems: "center", justifyContent: "space-between", paddingTop: 15, paddingBottom: 15 }, this.selectedIndexArray[0] == index ? { backgroundColor: 'rgba(50, 186, 192, 0.1)' }:{}]}>
                  <View style={{ flexDirection: "row", alignItems: "center", marginStart: 30 }}>
                    <Text style={{ fontWeight: "bold", color: this.selectedIndexArray[0] == index ? "#32BAC0" : "#000000" }}>{item.title}</Text>
                  </View>
                  { this.selectedIndexArray[0] == index ? <Image source={require("../../Resources/Images/plugin_icon_customVoice_selectItem.png")} style={{ width: 22, height: 22, marginRight: 30 }}></Image> : null}
                </View>
              </TouchableOpacity>;
            })
          }
        </ScrollView>
      </AbstractDialog>
    );
  }

  _renderResolutionDialogV2() {
    return (
      <ChoiceDialog
        modalStyle={this.state.fullScreen ? {width: kWindowHeight * 0.5, height: kWindowWidth * 0.75, alignSelf: 'center', bottom: kWindowWidth * 0.12, borderBottomLeftRadius: 20, borderBottomRightRadius: 20} : {}}

        visible={this.state.dialogVisibility}
        title={LocalizedStrings["ptzc_play_back_speed_selection_title"]}
        options={this.isCameraV1orV3 ? [
          { "title": "x1", accessibilityLabel: DescriptionConstants.rp_13 },
          { "title": "x2", accessibilityLabel: DescriptionConstants.rp_13.replace('1', 2) },
          { "title": "x3", accessibilityLabel: DescriptionConstants.rp_13.replace('1', 3) }
        ] : [
          { "title": "x1", accessibilityLabel: DescriptionConstants.rp_13 },
          { "title": "x4", accessibilityLabel: DescriptionConstants.rp_13.replace('1', 4) },
          { "title": CameraConfig.support8xSpeed(Device.model) ? "x8" : "x16", accessibilityLabel: CameraConfig.support8xSpeed(Device.model) ? DescriptionConstants.rp_13.replace('1', 8) : DescriptionConstants.rp_13.replace('1', 16) }
        ]}
        selectedIndexArray={this.selectedIndexArray}
        onDismiss={(_) => this.setState({ dialogVisibility: false })}
        onSelect={(result) => {
          this.selectedIndexArray = result;
          this.updateSpeed(result[0]);
          this.setState({ dialogVisibility: false });
        }}
      />
    );
  }

  _getWindowPortraitHeight() {
    let winWidth = Dimensions.get('window').width;
    let winHeight = Dimensions.get('window').height;
    let width = Math.min(winWidth, winHeight);
    let height = Math.max(winWidth, winHeight);
    return height;
  }

}


const styles = StyleSheet.create({

  container: {
    backgroundColor: Util.isDark() ? "xm#000000" : "#ffffff",
    width: '100%',
    height: '100%',
    display: "flex",
    flexWrap: "nowrap"
  },

  main: {
    display: 'flex',
    flexDirection: 'column',
    flexWrap: 'nowrap',
    backgroundColor: 'white',
    height: "100%"
  },

  videoContainerNormal: {
    backgroundColor: 'black',
    width: kWindowWidth,
    height: portraitVideoHeight,
    position: "relative"// absolute以第一个非static的父类变量作为定位标准
  },

  videoContainerFull: {
    backgroundColor: 'black',
    width: "100%",
    height: "100%",
    position: "relative"// absolute以第一个非static的父类变量作为定位标准
  },

  videoView: {
    position: "absolute",
    width: "100%",
    height: "100%"
  },

  videoControl: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    height: 80
  },

  videoControlBar: {// 横向
    display: "flex",
    flexWrap: "nowrap",
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-around"
    // backgroundColor: '#FFF1'
  },
  videoControlBarFull: {
    // backgroundColor: '#FFF1',
    display: "flex",
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end"

  },
  videoControlBarItem: {// 内容居中排列
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    flexGrow: 1

  },

  videoControlBarItemImg: {
    width: 50,
    height: 50
  },

  landscapeCallViewLayout: {
    width: "100%",
    paddingBottom: 10,
    paddingTop: 20,
    position: "absolute",
    bottom: 0
  },
  landscapeCallViewLayoutImg: {
    display: "flex",
    margin: "auto",
    width: "100%",
    flexDirection: "row",
    justifyContent: "center"
    // textAlign:"center"
  },

  callViewLayout: {
    flexGrow: 1,
    width: "100%",
    flexDirection: "column",
    display: "flex",
    alignItems: "center",
    justifyContent: "center"
  },

  bottomLayout: {
    display: "flex",
    width: "100%",
    height: 60,
    flexDirection: "row",
    flexWrap: 'nowrap'
  },

  bottomLayoutItem: {
    flexGrow: 1,
    height: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center"
  },

  whiteText: {
    fontSize: kIsCN ? 10 : 8,
    textAlign: "center",
    padding: 4,
    color: "#ffffff",
    borderColor: "#FFFFFFCC",
    borderRadius: 3,
    borderWidth: 1
  },
  snapShot: {
    position: "absolute",
    bottom: 40,
    left: 5,
    margin: 6,
    width: 100,
    height: 66,
    borderRadius: 5
  },
  snapShotFull: {
    position: "absolute",
    bottom: 84,
    left: 35,
    margin: 6,
    width: 100,
    height: 66,
    borderRadius: 5
  }
});
