'use strict';

import React from 'react';
import { isIphoneX, getStatusBarHeight } from 'react-native-iphone-x-helper';
import { ScrollView, ActivityIndicator, SafeAreaView, StatusBar, BackHandler, View, Text, Image, StyleSheet, TouchableOpacity, TouchableWithoutFeedback, PermissionsAndroid, Platform, DeviceEventEmitter, Dimensions, NativeModules, PanResponder } from 'react-native';

import LinearGradient from 'react-native-linear-gradient';
import { Device, Service, PackageEvent, Host, System, DarkMode } from 'miot';
import AlbumHelper from "../util/AlbumHelper";

import ImageButton from "miot/ui/ImageButton";

import Toast from '../components/Toast';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';

import SdFileManager from './util/SdFileManager';

import CameraRenderView from 'miot/ui/CameraRenderView';
import { MISSSampleRate, MISSDataBits, MISSAudioChannel } from 'miot/ui/CameraRenderView';
import CameraPlayer from '../util/CameraPlayer';
import { MISSCommand, MISSError } from "miot/service/miotcamera";
import Orientation from 'react-native-orientation';

import StorageKeys from '../StorageKeys';
import ScaleableTimelineView from '../ui/ScaleableTimelineView';
import VersionUtil from '../util/VersionUtil';
import CameraConfig from '../util/CameraConfig';
import TimeScaleView from '../ui/TimeScaleView';
import CenterTimeView from '../ui/CenterTImeView';
import TimeScaleView2 from '../ui/TimeScaleView2';
import DeviceOfflineDialog from '../ui/DeviceOfflineDialog';
import NoNetworkDialog from '../ui/NoNetworkDialog';
import { EVENT_TYPE, EVENT_TYPE_COLOR } from './util/EventTypeConfig';
import StackNavigationInstance, { SD_CLOUD_STACK_NAVIGATION_ONBACK, SD_CLOUD_STACK_NAVIGATION_ONPAUSE, SD_CLOUD_STACK_NAVIGATION_ONRESUME } from '../StackNavigationInstance';
import th from 'miot/resources/strings/th';
import { ChoiceDialog } from 'mhui-rn';
import { MessageDialog } from "mhui-rn";
import RectAngleView from '../ui/RectAngleView';
import { PixelRatio } from 'react-native';
import LoadingView from '../ui/LoadingView';
import SdcardEventLoader from '../framework/sdcard/SdcardEventLoader';
import CommonMsgDialog from '../ui/CommonMsgDialog';

import MHLottieSnapToolButton, { MHLottieSnapToolBtnDisplayState } from '../ui/animation/lottie-view/MHLottieSnapToolButton';
import MHLottieRecordToolButton, { MHLottieRecordToolBtnDisplayState } from '../ui/animation/lottie-view/MHLottieRecordToolButton';
import MHLottieSpeedToolButton, { MHLottieSpeedToolBtnDisplayState } from '../ui/animation/lottie-view/MHLottieSpeedToolButton';
import MHLottieAudioToolButton, { MHLottieAudioToolBtnDisplayState } from '../ui/animation/lottie-view/MHLottieAudioToolButton';
import MHLottieFullScreenToolButton, { MHLottieFullScreenToolBtnDisplayState } from '../ui/animation/lottie-view/MHLottieFullScreenToolButton';

import MHLottieSnapLandscapeButton, { MHLottieSnapLandscapeBtnDisplayState } from '../ui/animation/lottie-view/MHLottieSnapLandscapeButton';
import MHLottieRecordLandscapeButton, { MHLottieRecordLandscapeBtnDisplayState } from '../ui/animation/lottie-view/MHLottieRecordLandscapeButton';
import OfflineHelper from '../util/OfflineHelper';
import RPC from '../util/RPC';
import StatusBarUtil from '../util/StatusBarUtil';
import { log } from 'miot/utils/fns';

import { DescriptionConstants } from '../Constants';
import SpecUtil from '../util/SpecUtil';
import TrackUtil from '../util/TrackUtil';
import LogUtil from '../util/LogUtil';
import Util from '../util2/Util';
import DldMgr from '../framework/DldMgr';
import peopleSelectedDrawable from "../../Resources/Images/time_line_event_icon_people_move_nor.png";
import peopleUnselectedDrawable from "../../Resources/Images/time_line_event_icon_people_selected.png";
import { BaseStyles } from "../BasePage";
import NoSdcardPage from '../setting/NoSdcardPage';
import SDCardSetting from "../setting/SDCardSetting";
import { handlerOnceTapWithToast } from "../util/HandlerOnceTap";

const kIsCN = Util.isLanguageCN();
const winHeight = Dimensions.get('window').height;
let isDark = DarkMode.getColorScheme() == "dark";



const timelinePlaybackEndListenerName = "onTimelinePlaybackEnd";
const kRecordTimeCallbackName = "recordTimeCallback";
const TAG = "SdcardTimelinePlayerFragment";
const kWindowHeight = Math.max(Dimensions.get("window").height, Dimensions.get("window").width);
const kWindowWidth = Math.min(Dimensions.get("window").height, Dimensions.get("window").width);
const portraitVideoHeight = Number.parseInt(kWindowWidth / 1.78);
const TW_WIDTH = 390;
const TW_HEIGHT = 844;
const TWP_WIDTH = 428;
const TWP_HEIGHT = 926;
const isIphone12 = (() => {
  if (Platform.OS === 'web') return false;
  return (
    Platform.OS === 'ios' &&
    ((TW_HEIGHT === kWindowHeight && TW_WIDTH === kWindowWidth))

  )
})()

const isIphone12P = (() => {
  if (Platform.OS === 'web') return false;
  return (
    Platform.OS === 'ios' &&
    ((TWP_HEIGHT === kWindowHeight && TWP_WIDTH === kWindowWidth))

  )
})()
export default class SdcardTimelinePage extends React.Component {

  static navigationOptions = ({ navigation }) => {
    // if (true) {//不要导航条
    //   return null;
    // }

    let tabBarVisible = true;
    let param = navigation.state.params || {};
    if (param.isFullScreen) {
      tabBarVisible = false;
    }
    return {
      headerTransparent: true,
      header: null,
      headerShown: false,
    };
  }

  state = {
    isEmpty: true,
    sdcardFiles: [],
    showPlayToolBar: false,
    touchMovement: false,

    pstate: 0,
    error: 0,

    fullScreen: false,
    isMute: CameraConfig.getUnitMute(),
    showHighTemperatureView: CameraConfig.isDeviceTemperatureHigh,
    isSleep: false,
    resolution: 0,
    speed: 1, // 倍速
    isPlaying: true,
    isRecording: false,

    screenshotVisiblity: false, // 截图是否可见

    showErrorView: false,
    showLoadingView: false,
    showPoweroffView: false,
    showPauseView: false,
    errTextString: "", // 错误提示文案

    screenshotPath: null,
    recordTimeSeconds: 0,

    eventTypeFlags: EVENT_TYPE.Default,

    videoViewWidth: 0,
    videoViewHeight: 0,

    dialogVisibility: false,
    angle: 51,
    elevation: 51,
    videoScale: 1.0,
    angleViewShowScale: false,
    showCameraAngleView: false,

    permissionRequestState: 0,
    showPermissionDialog: false,
    lastOfflineTime: "",
    isInSameLAN: CameraConfig.isLocalNetwork,
    showNetworkDialog: false
  };


  constructor(props) {
    super(props);
    this.isSupportPhysicalCover = CameraConfig.isSupportPhysicalCover(Device.model);
    this.timeIndicatorView = null;
    this.dateTime = new Date();
    this.isFirstReceiveFiles = true;
    this.isUserPause = false;
    this.isPlayEnd = false; //是否播放到末尾
    this.isPluginForeGround = true;// sdcard页面不存在跳到其他页面的情况
    this.isPageForeGround = true;
    this.isAppForeground = true;

    this.isParentPageBackground = false;
    this.firstInPage = true;
    this.sdcardCode = this.props.navigation.getParam("sdcardCode");
    if (this.sdcardCode == null || this.sdcardCode == undefined) {
      this.sdcardCode = -1;
    }
    //单独判断是否是V1 V3
    this.isCameraV1orV3 = CameraConfig.isCameraV1orV3(Device.model);
    this.isCheckingPermission = false;

    // see https://blog.csdn.net/u011068702/article/details/83218639
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来 这里监听的是tabNavigation的事件，不是stackNavigation的事件。
      'didFocus',
      () => {
        console.log("will focus");

        this.isPageForeGround = true;
        this.isPluginForeGround = true;
        this.isAppForeground = true;
        this.restoreOri();
        this._onResume();
      }
    );
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'willBlur',
      () => {
        console.log("SdcardTimelinePlayerFragment did blur");

        this.isPageForeGround = false;
        this._onPause();
        if (this.cameraRenderView != null && !this.isCheckingPermission) {
          this.cameraRenderView && this.cameraRenderView.hidesSurfaceView();
        }
      }
    );


    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      LogUtil.logOnAll(TAG, "packageEvent.didResume: isPluginForeground:" + this.isPluginForeGround + " isPageForeround:" + this.isPageForeGround);
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }

      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.isAppForeground = true;// rnactivity调用了onresume
      this.restoreOri();
      this._onResume();
    });

    this.willPauseListener = PackageEvent.packageWillPause.addListener(() => {
      LogUtil.logOnAll(TAG, "packageEvent.didPause: isPluginForeground:" + this.isPluginForeGround + " isPageForeround:" + this.isPageForeGround);
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      if (this.isCheckingPermission) {
        console.log("============packageWillPause isCheckingPermission==========")
        // 请求权限
        return;
      }
      this.isAppForeground = false;// rnactivity调用了onresume
      this._onPause();
    });

    if (Platform.OS == "ios") {
      this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {
        LogUtil.logOnAll(TAG, "packageEvent.willAppear: isPluginForeground:" + this.isPluginForeGround + "  isPageForeground:" + this.isPageForeGround);
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.isPluginForeGround = true;// rnactivity调用了onresume
        this._onResume();
      });

      this.willDisappearListener = PackageEvent.packageViewWillDisappearIOS.addListener(() => {

        LogUtil.logOnAll(TAG, "packageEvent.willDisappear: isPluginForeground:" + this.isPluginForeGround + "  isPageForeground:" + this.isPageForeGround);
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        if (this.isCheckingPermission) {
          console.log("============packageWillPause isCheckingPermission==========")
          // 请求权限
          return;
        }
        this.toPortrait();// ios强制切换到竖屏去
        setTimeout(() => {
          this.isPluginForeGround = false;// rnactivity调用了onpause
          // console.log('testaaa', 'willDisappearListener', 'page: ', this.isPageForeGround, ' plugin: ', this.isPluginForeGround);
          this._onPause();
          
        }, 500);

      });
    } else {
      if (PackageEvent.packageWillStopAndroid) {
        this.willStopListener = PackageEvent.packageWillStopAndroid.addListener(() => {

          // this.cameraRenderView && this.cameraRenderView.hidesSurfaceView();

        });
      }

    }


    this.currentNetworkState = -1;
    this.isChangingNetwork = false;

    this.toStartTime = 0;// 暂存要播放的view的时间戳

    this.startTime = 0;
    this.endTime = 0;
    this.offset = 0;
    this.sessionId = 0;

    this.lastTimeItemEndTime = 0;// 从rdt捞过来的数据 的最后一个item的endtime

    this.isSetPlayTime = false;// 是否正在设置播放时间
    this.setPlayTimeMillis = 0;// 记录本次设置playback命令的时间
    this.lastTimestamp = 0;// 记录上次返回回调的时间戳
    // todo notify native side  whether in continue playback mode

    this.timelinePlaybackEndListener = DeviceEventEmitter.addListener(timelinePlaybackEndListenerName, () => {
      // 走到这里说明连续回放结束了
      console.log("时间轴模式下，从点播切换成了直播 要暂停camera");
      this.toSdcardEnd();// 停止播放
    });

    this.timelineView = null;
    this.cameraRenderView = null;
    this.scrollTimeout = null;
    this.scrollTimeout1 = null;
    this.isConnecting = false;
    this.mOri = "PORTRAIT";
    this.connRetry = 2;

    this.stackNavigationOnPauseListener = null;
    this.stackNavigationOnResumeListener = null;

    if (this.stackNavigationOnPauseListener == null) {
      this.stackNavigationOnPauseListener = DeviceEventEmitter.addListener(SD_CLOUD_STACK_NAVIGATION_ONPAUSE, () => {
        LogUtil.logOnAll(TAG, "sdcard timeline page receive parent onpause event:  ispageForeground:" + this.isPageForeGround);
        // 父类收到了pause事件，传给了子控件。
        if (!this.isPageForeGround) { // 只有页面处于前台的时候，这个属性才奏效
          return;
        }
        this.cameraRenderView && this.cameraRenderView.hidesSurfaceView();
        this.isParentPageBackground = true;
        this.isPageForeGround = false;
        console.log("SdcardTimelinePlayerFragment", "receive parents' onPause");
        this._onPause();// 暂停咯
      });
    }
    if (this.stackNavigationOnResumeListener == null) {
      this.stackNavigationOnResumeListener = DeviceEventEmitter.addListener(SD_CLOUD_STACK_NAVIGATION_ONRESUME, () => {
        LogUtil.logOnAll(TAG, "sdcard timeline page receive parent onresume event:  isParentPageBackground:" + this.isParentPageBackground);

        if (!this.isParentPageBackground) {
          return;
        }
        this.restoreOri();
        this.isParentPageBackground = false;
        this.isPageForeGround = true;
        this.isPluginForeGround = true;
        this.isAppForeground = true;
        this._onResume();
      });
    }

    if (this.stackNavigationOnbackListener == null) {
      this.stackNavigationOnbackListener = DeviceEventEmitter.addListener(SD_CLOUD_STACK_NAVIGATION_ONBACK, () => {
        this.cameraRenderView && this.cameraRenderView.hidesSurfaceView();

        this.isParentPageBackground = true;
        this.isPageForeGround = false;
        console.log("SdcardTimelinePlayerFragment", "receive parents' onBack");
        this._onPause();// 暂停咯
      });
    }

    this.selectedIndexArray = [0];
    this.displayCutoutTop = 0;
    Host.getPhoneScreenInfo()
      .then((result) => {
        this.displayCutoutTop = PixelRatio.roundToNearestPixel(result.displayCutoutTop / PixelRatio.get() || 0);
        if (isNaN(this.displayCutoutTop)) {
          this.displayCutoutTop = 0;
        }
        console.log(TAG, "result:", result);
      })
      .catch((error) => {

      });
    this.lastTimeItemStartTime = 0;
    this.timestampRequestSend = false;
    this.ignoreDataWarning = false;

    this.isInternationalServer = CameraConfig.getInternationalServerStatus();
    this.mLastScreenSize = Dimensions.get('window');
    Dimensions.addEventListener("change", this.dimensionListener);
  }

  dimensionListener = (args) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    if (Platform.OS === "ios") {
      if (args && args.screen && args.window) {
        if (args.screen.width !== args.window.width || args.screen.height !== args.window.height || this.mLastScreenSize.width != Dimensions.get('screen').width) {
          // this.setState({ screenSize: Dimensions.get('screen') });
          this.mLastScreenSize = Dimensions.get('screen');
          console.log('Dimensions changed========4');
          Dimensions.set({ "window": args.screen });
          this.setState({});
        }
      }
    }
  }

  _onResume() {
    if (!this.isAppForeground || !this.isPluginForeGround || !this.isPageForeGround) {
      return;
    }
    CameraPlayer.getInstance().bindConnectionCallback(this._connectionHandler);
    CameraPlayer.getInstance().bindP2pCommandCallback(this._ServerRespHandler);
    CameraPlayer.getInstance().bindNetworkInfoCallback(this._networkChangeHandler);
    CameraPlayer.getInstance().bindPauseAllCallback(() => { this._stopAll(false, false)});
    DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');
    CameraPlayer.getInstance().bindPowerOffCallback(this._powerOffHandler);
    this.sdcardFilesListner = SdcardEventLoader.getInstance().addListener(() => {
      this.onGetFiles();// 刷新数据。
    });
    // 检查是否在同一局域网
    this.checkLocalNetwork();
    // // 如果已经播放完成了 或者是播放error  或者是用户手动暂停的
    // if (this.state.showErrorView) {
    //   return;
    // }
    if (this.state.showPoweroffView) {
      return;
    }
    if (this.isUserPause) {
      // byh@20250725 解决问题 MIECOCMCZ-1289
      // 开启线程渲染
      this.cameraRenderView && this.cameraRenderView.startRender();
      // 开启后再给关掉
      this.cameraRenderView && this.cameraRenderView.stopRender();

      return;
    }
    // 重新进来 要绑定一遍这些事件。

    this.onGetFiles();// 从其他页面回来 要刷新一遍数据，避免出现其他页面删了  这个页面还没有同步数据的情况。

    Service.miotcamera.bindTimelinePlaybackEndListener(timelinePlaybackEndListenerName);


    this.queryNetworkJob();

  }

  _onPause(ignoreState = false) {
    if (this.cameraRenderView != null) {
      this.cameraRenderView.stopRender();// stopRender
    }
    CameraPlayer.getInstance().bindConnectionCallback(null);
    CameraPlayer.getInstance().bindP2pCommandCallback(null);
    CameraPlayer.getInstance().bindPowerOffCallback(null);
    CameraPlayer.getInstance().bindPauseAllCallback(null);

    CameraPlayer.getInstance().bindNetworkInfoCallback(null);

    this.sdcardFilesListner && this.sdcardFilesListner.remove();

    this._stopRecord(ignoreState);

    if (this.state.showErrorView) {
      return;
    }
    if (this.state.showPoweroffView) {
      return;
    }
    console.log("SdcardTimelinePlayerFragment", "onPause");
    this._startPlay(false, ignoreState);
  }

  restoreOri() {
    console.log(TAG, "restoreOri");
    if ("PORTRAIT" === this.mOri) {
      this.toPortrait();
    } else {
      this.toLandscape();
    }
  }


  toPortrait(ignoreState = false) {
    if (Platform.constants.Version == 26) {
      return;
    }
    StatusBar.setHidden(false);
    console.log(TAG, "toPortrait");
    this.mOri = "PORTRAIT";
    CameraConfig.lockToPortrait();
    if (ignoreState) {
      return;
    }
    this._setNavigation(false);
  }

  toLandscape() {
    if (Platform.constants.Version == 26) {
      return;
    }
    console.log(TAG, "toLandscape");
    this.mOri = "LANDSCAPE";
    if (Platform.OS === "android") {
      Orientation.lockToLandscape();
    } else {
      Orientation.lockToLandscapeRight();
    }
    if (Host.isPad) {
      Service.miotcamera.enterFullscreenForPad(true);
    }

    StatusBar.setHidden(true);
    
    this._setNavigation(true);
  }
  componentWillMount() {
    this._panResponder = PanResponder.create({
      onStartShouldSetPanResponderCapture: (evt, gestureState) => {
        if (Platform.OS == "ios") {
          this.setState({ touchMovement: true }) ;//手指移动，用来监听IOS
        }
      },
    });
  }
  componentWillUnmount() {
    console.log("SdcardTimelinePlayerFragment", "unmount");

    try {
      this._onPause(true);
    } catch (exception) {
      console.log(TAG, "unmount error", exception);
    }
    this.destroyed = true;

    this.sdcardFilesListner && this.sdcardFilesListner.remove();
    this.stackNavigationOnPauseListener && this.stackNavigationOnPauseListener.remove();
    this.stackNavigationOnResumeListener && this.stackNavigationOnResumeListener.remove();
    this.stackNavigationOnbackListener && this.stackNavigationOnbackListener.remove();

    this.toPortrait(true);
    clearTimeout(this.scrollTimeout);
    clearTimeout(this.scrollTimeout1);
    clearTimeout(this.timeoutLocalPing);
    CameraPlayer.getInstance().bindPlaybackTimestampCallback(null);
    CameraPlayer.getInstance().stopPlaybackTimestampInterval();// 防止崩溃

    Service.miotcamera.bindTimelinePlaybackEndListener(null);

    // SdFileManager.getInstance().bindReceiveFilesListener(null);
    // this._onPause();
    Service.miotcamera.setTimelinePlaybackMode(false);

    this.didResumeListener.remove();
    this.willPauseListener.remove();
    if (this.willAppearListener) {
      this.willAppearListener.remove();
    }
    if (this.willDisappearListener) {
      this.willDisappearListener.remove();
    }
    this.willStopListener && this.willStopListener.remove();
    this.timelinePlaybackEndListener.remove();
    this.didFocusListener.remove();
    this.didBlurListener.remove();
    this.recordListener.remove();
    Orientation.removeOrientationListener(this._orientationListener);
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
    // SdFileManager.getInstance().destroyInstance();
  }


  _stopAll(showPauseView = false) {

    this._stopRecord();
    this._toggleAudio(true, false);
    CameraPlayer.getInstance().stopVideoPlay();
    this.cameraRenderView && this.cameraRenderView.stopRender();// stopRender
    this.hideshowPauseViewBgBpsCount = 0;

    this.setState({ showPauseView: showPauseView, showLoadingView: false, showPlayToolBar: false });
  }

  getPowerOffString() {
    return this.isSupportPhysicalCover ? "camera_physical_covered" : "camera_power_off";
  }


  _hidePlayToolBarLater(ignoreFull = false) {

    let tTimer = 5000;
    clearTimeout(this.showPlayToolBarTimer);
    this.showPlayToolBarTimer = setTimeout(() => {
      if (this.timelineView && !this.timelineView.isTimelineIdle() && this.state.fullScreen) {
        this._hidePlayToolBarLater();
        return;
      }
      this.setState({ showPlayToolBar: false, showPauseView: false });
    }, tTimer);
  }

  render() {
    let showMain = this.sdcardCode == 0 || this.sdcardCode == -1 || this.sdcardCode == 2 || this.sdcardCode == CameraPlayer.SD_CARD_FILE_ERROR_CODE;
    let showSdView = this.sdcardCode != 1 && this.sdcardCode != 5;
    return (
      <View {...this._panResponder.panHandlers} style={[styles.container, {paddingTop: this.state.fullScreen ? 0 : StatusBar.currentHeight}]}>
        {/*<SafeAreaView style={{ backgroundColor: "#ffffff" }}></SafeAreaView>*/}
        {this.renderTitle()}
        {showMain ? this._renderMainView() : showSdView ? this._renderSdcardAbnormal() : this._renderNoSdcardView()}

        {/*<SafeAreaView></SafeAreaView>*/}
      </View>
    );
  }

  renderTitle() {
    if (this.state.fullScreen) {
      return null;
    }

    return (
      <View style={[BaseStyles.row, {
        height: 50, paddingLeft: 12, paddingRight: 52, // 12+40
        marginTop: Platform.OS == 'android' ? 0 : StatusBarUtil._getInset("top"),
        backgroundColor: "white",
        width: "100%"
      }]}>

        <ImageButton
          style={BaseStyles.icon40}
          source={Util.isDark() ? require("../../Resources/Images/icon_back_black_nor_dark.png") : require("../../Resources/Images/icon_back_black.png")}
          onPress={() => {
            if (this.state.isRecording) {
              Toast.success("camera_recording_block");
              return;
            }
            this.props.navigation.goBack();
          }}
          accessibilityLabel={DescriptionConstants.yc_1}

        />
        <Text style={[BaseStyles.text18, { fontWeight: "bold" }]}>{LocalizedStrings["sdcard_video"]}</Text>
        <View></View>
      </View>
    );
  }

  _renderMainView() {
    return (
      <>
        <ScrollView
          style={{flex: 1, width: '100%'}}
          contentContainerStyle={{flexGrow: 1}}
          showsVerticalScrollIndicator={false}
        >
          {this._renderVideoLayout()}

          {this._renderTimeLineView()}
          {this._renderEventTypes()}
          {this._renderBottomSelectView()}

          <DeviceOfflineDialog
            ref="powerOfflineDialog"
          />
          <NoNetworkDialog
            ref="noNetworkDialog"
          />
          {this._renderResolutionDialog()}
          {this._renderPermissionDialog()}
          {this._renderNetworkDialog()}
        </ScrollView>
      </>
    );
  }

  _renderNetworkDialog() {
    return (
      <MessageDialog
        message={LocalizedStrings["lan_is_different"]}
        buttons={[
          {
            text: LocalizedStrings["offline_divice_ok"],
            colorType: "grayLayerBlack",
            callback: () => {
              this.setState({ showNetworkDialog: false });
              this.props.navigation.goBack();
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showNetworkDialog: false });
          this.props.navigation.goBack();
        }}
        cancelable={true}
        visible={this.state.showNetworkDialog}
      />
    );
  }
  _renderNoSdcardView() {
    return(
      <NoSdcardPage
        key={'NoSdcardPage'}
        onButtonClick={
          () => {
            // 只会支持10069  直接跳购买页啊
            let mSupportCloudCountry = CameraConfig.isSupportCloud();
            let isInternationalServer = CameraConfig.getInternationalServerStatus();
            Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: "sdmgt_button" });// 直接跳过去了
          }
        }
        showBuyButton={false}
      />
    );
  }

  _renderSdcardAbnormal() {
    return (
      <View style={{width: '100%', alignItems: 'center'}}
            key={"sd_abnormal"}
      >
        <View style={{ width: 217, height: 217, display: "flex", alignItems: "center", marginTop: 30 }}>
          <Image
            style={{ width: '100%', height: '100%' }}
            source={ require('../../Resources/Images/mjv3_sdCard_abnormal.png') }
          />
          <View style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            display: "flex",
            alignItems: 'center',
            justifyContent: 'space-evenly',
            bottom: 13 }}>
            {/* 上面的存储暂停 */}
            <View style={{ height: 124, width: 180, justifyContent: "center", alignItems: "center", backgroundColor: "#00000000", paddingBottom: 30 }}>
              <Text style={[{ fontSize: Util.isLanguageCN() ? 22 : 20,
                color: 'white',
                textAlign: "center",
                textAlignVertical: "center" }, { fontSize: Util.isLanguageCN() ? 22 : 18 }]} numberOfLines={3}>{(LocalizedStrings['sds_status'])}</Text>
            </View>

            <View style={{ position: "absolute", top: 124, backgroundColor: '#e5e5e5', height: 1, width: '80%' }}></View>
            <Text style={{ position: "absolute",
              bottom: 45,
              fontSize: Util.isLanguageCN() ? 20 : 18,
              color: 'white',
              width: '90%',
              textAlign: "center" }}>{LocalizedStrings[`sds_status_${ this.sdcardCode }`]}</Text>
          </View>
        </View>
        <Text
          style={{ textAlign:'center',
            marginTop: 20,
            marginBottom: 30,
            fontSize: Util.isLanguageCN() ? 16 : 14,
            color: 'rgba(0,0,0,0.6)' }}>
          { LocalizedStrings[`sds_try_action_${ this.sdcardCode }`] }
        </Text>
      </View>
    );
  }

  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 麦克风
    // 
    let message = "";
    if (this.state.permissionRequestState == 0) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_microphone"]);
    }
    return (
      <MessageDialog
        title={LocalizedStrings["tips"]}
        message={message}
        messageStyle={{
          fontSize: 14,
      }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );

  }

  _renderEventTypes() {
    if (this.state.fullScreen) {
      return null;
    }
    if (this.state.isEmpty) {
      return (
        <View style={{ flex: 1 }}>
        </View>
      );
    }
    // here judge whether to show baby cry or others   currently 021 support all feature
    let isMotionSelected = this.state.eventTypeFlags & EVENT_TYPE.EVENT_TYPE_AREA_MOTION;
    let isBabyCrySelected = this.state.eventTypeFlags & EVENT_TYPE.EVENT_TYPE_BABY_CRY;
    let isCameraCallingSelected = this.state.eventTypeFlags & EVENT_TYPE.EVENT_TYPE_CAMERA_CALLING;
    let isPeopleSelected = this.state.eventTypeFlags & EVENT_TYPE.EVENT_TYPE_PEOPLE_MOTION;
    let isPetSelected = this.state.eventTypeFlags & EVENT_TYPE.Pet;
    let isFaceSelected = this.state.eventTypeFlags & EVENT_TYPE.EVENT_TYPE_FACE;
    let isLoudSoundSelected = this.state.eventTypeFlags & EVENT_TYPE.EVENT_TYPE_LOUDER_SOUND;
    let isChildSelected = this.state.eventTypeFlags & EVENT_TYPE.ChildDetected;
    let isIgnoreSelected = this.state.eventTypeFlags & EVENT_TYPE.IgnoreEvent;


    // let cameraCallingUnselectedDrawable = require("../../Resources/Images/time_line_event_icon_camera_calling_sel.png");
    // let cameraCallingSelectedDrawable = require("../../Resources/Images/time_line_event_icon_camera_calling_nor.png");
    let cameraCallingUnselectedDrawable = require("../../Resources/Images/time_line_event_icon_camera_video_calling_sel.png");
    let cameraCallingSelectedDrawable = require("../../Resources/Images/time_line_event_icon_camera_video_calling_nor.png");
    let babyCryUnselectedDrawable = require("../../Resources/Images/time_line_event_icon_baby_cry_sel.png");
    let babyCrySelectedDrawable = require("../../Resources/Images/time_line_event_icon_baby_cry_nor.png");

    let peopleUnselectedDrawable = require("../../Resources/Images/time_line_event_icon_people_selected.png");
    let peopleSelectedDrawable = require("../../Resources/Images/time_line_event_icon_people_move_nor.png");

    let motionUnselectedDrawable = require("../../Resources/Images/time_line_event_icon_erea_move_sel.png");
    let motionSelectedDrawable = require("../../Resources/Images/time_line_event_icon_erea_move_nor.png");

    let faceUnselectedDrawable = require("../../Resources/Images/time_line_event_icon_face_sel.png");
    let faceSelectedDrawable = require("../../Resources/Images/time_line_event_icon_face_nor.png");

    let petUnselectedDrawable = require("../../Resources/Images/time_line_event_icon_pet_move_sel.png");
    let petSelectedDrawable = require("../../Resources/Images/time_line_event_icon_pet_move_nor.png");

    let ignoreSelectedDrawable = require("../../Resources/Images/time_line_event_icon_ignore_nor.png");
    let ignoreUnselectedDrawable = require("../../Resources/Images/time_line_event_icon_ignore.png");

    let childSelectedDrawable = require("../../Resources/Images/time_line_event_icon_child_nor.png");
    let childUnselectedDrawable = require("../../Resources/Images/time_line_event_icon_child.png");

    let textColorSelected = "#FFFFFF";
    let textColorUnselected = "#666666";

    let containerPadding = 15;
    let eventPadding = 10;
    let eventMargin = 13.5;
    let containerWidth = (kWindowWidth - 40 - eventMargin * 4) / 3;
    let eventMarginLeft = (kWindowWidth - containerWidth * 3 - eventMargin * 4) / 2;

    if (kWindowHeight < 700) {
      containerPadding = 10;
      eventPadding = 7;
      eventMargin = 13.5;
    }

    return (
      <ScrollView
        contentContainerStyle={{
          flexDirection: "row",
          flexWrap: "wrap",
          justifyContent: 'flex-start',
          paddingBottom: 72
        }}
        style={{
          display: "flex",
          flex: 1,
          paddingHorizontal: containerPadding,
          paddingTop: 5,
          marginTop: 20
        }}
      >

        {
          !CameraConfig.displayChild(Device.model) ? null :
            <TouchableOpacity
              style={{ height: 36, padding: eventPadding, marginHorizontal: eventMargin / 2, marginBottom: 10, backgroundColor: (isChildSelected ? EVENT_TYPE_COLOR.childEventColor : EVENT_TYPE_COLOR.unselectedColor), borderRadius: 360, display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center" }}
              onPress={() => {
                this.changeEventType(EVENT_TYPE.ChildDetected);
              }}

            >
              <Image
                style={{ width: 15, height: 15, marginRight: 5 }}
                source={isChildSelected ? childSelectedDrawable : childUnselectedDrawable}
              />

              <Text style={{ color: (isChildSelected ? textColorSelected : textColorUnselected), fontSize: kIsCN ? 12 : 10 }}
              >
                {LocalizedStrings["child_desc"]}
              </Text>

            </TouchableOpacity>
        }


        {
          !CameraConfig.displayPetInTimeline(Device.model) ? null :
            <TouchableOpacity
              style={{ height: 36, padding: eventPadding, marginHorizontal: eventMargin / 2, marginBottom: 10, backgroundColor: (isPetSelected ? EVENT_TYPE_COLOR.petSelectedColor : EVENT_TYPE_COLOR.unselectedColor), borderRadius: 360, display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center" }}
              onPress={() => {
                this.changeEventType(EVENT_TYPE.Pet);
              }}
            >
              <Image
                style={{ width: 15, height: 15, marginRight: 5 }}
                source={isPetSelected ? petSelectedDrawable : petUnselectedDrawable}
              />

              <Text style={{ color: (isPetSelected ? textColorSelected : textColorUnselected), fontSize: 12 }}>
                {LocalizedStrings["pet_desc"]}
              </Text>

            </TouchableOpacity>
        }
        {
          !CameraConfig.displayIgnoreEvent(Device.model) ? null :
            <TouchableOpacity
              style={{ height: 36, padding: eventPadding, marginHorizontal: eventMargin / 2, marginBottom: 10, backgroundColor: (isIgnoreSelected ? EVENT_TYPE_COLOR.ignoreEventColor : EVENT_TYPE_COLOR.unselectedColor), borderRadius: 360, display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center" }}
              onPress={() => {
                this.changeEventType(EVENT_TYPE.IgnoreEvent);
              }}
            >
              <Image
                style={{ width: 15, height: 15, marginRight: 5 }}
                source={isIgnoreSelected ? ignoreSelectedDrawable : ignoreUnselectedDrawable}
              />

              <Text style={{ color: (isIgnoreSelected ? textColorSelected : textColorUnselected), fontSize: 12 }}>
                {LocalizedStrings['car_video']}
              </Text>

            </TouchableOpacity>
        }




      </ScrollView>

    );
  }

  _renderVideoLayout() {
    return (
      <View style={this.state.fullScreen ? styles.videoContainerFull : styles.videoContainerNormal}>
        {this._renderVideoView()}
        {/* {this._renderClickableView()} */}
        {this._renderAngleView()} 
        {this._renderPowerOffView()}
        {this._renderPauseView()}
        {this._renderErrorRetryView()}
        {this._renderHighTemperatureView()}
        {this._renderLoadingView()}


        {/* {this._renderTitleView()} */}
        {/* 竖屏图标 */}
        {this._renderVideoControlView()}
        {this._renderSnapshotView()}

        {this._renderRecordTimeView()}
        {this._renderTimeIndicatorView()}
        {/* 横屏图标 */}
        {this._renderLandscapeTopButtons()}
        {this._renderLandscapeRightButtons()}

      </View>
    );
  }

  _renderHighTemperatureView() {
    if (!this.state.showHighTemperatureView) {
      return null;
    }
    return (
      <View
        style={{ zIndex: 7, position: "absolute", bottom: 0, backgroundColor: "black", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <View
          style={{ display: "flex", alignItems: "center" }}
        >
          <Image
            style={{ width: 54, height: 54 }}
            source={require("../../resources2/images/icon_ev_empty_w.png")} />
          <Text
            style={{ marginTop: 10, fontSize: kIsCN ? 12 : 10, color: "#999", paddingHorizontal: 10, textAlign: "center", width: kWindowWidth - 60 }}>
            {LocalizedStrings['high_temperature_cannot_use']}
          </Text>
        </View>
      </View>
    );
  }

  _renderClickableView() {
    return (
      <TouchableOpacity
        style={{ width: "100%", height: "100%", backgroundColor: "#00000000", position: "absolute", zIndex: 0 }}
        onPress={() => {
          this._onVideoClick();
        }}
      >
      </TouchableOpacity>
    );
  }

  _renderTimeIndicatorView() {
    return (
      <View
        style={{ width: "100%", height: "100%", position: "absolute", display: "flex", justifyContent: "center", alignItems: "center", marginTop: -50 }}
        pointerEvents={"none"}
      >
        <CenterTimeView
          ref={(ref) => {
            this.timeIndicatorView = ref;
          }}
        >

        </CenterTimeView>
      </View>
    );
  }

  _renderLandscapeRightButtons() {

    if (!this.state.fullScreen) {
      return null;
    }

    if (!this.state.showPlayToolBar) {
      return null;
    }

    let recordIndex = !this.state.isPlaying ? 2 : (this.state.isRecording ? 1 : 0);
    let screenHeight = Dimensions.get("window").height;
    let screenWidth = Dimensions.get("window").width;
    if (screenWidth < screenHeight) { // mi 9 se上 第一次切换回来，获取的高度是竖屏下的高度，需要修改以下。
      screenHeight = screenWidth;
    }
    let viewHeight = screenHeight - 150;
    let StatusBarheight = null, haveStatus = false;
    if (Platform.OS === "android") {

      StatusBarheight = StatusBar.currentHeight - 30;
      console.log('StatusBarheight', StatusBarheight);
      if (StatusBarheight <= 0) {
        StatusBarheight = 0;
        haveStatus = true;
      }
      // -16 因为返回按钮的图片留白太大
    } else {
      StatusBarheight = StatusBarUtil._getInset("top") - 30;
    }
    return (
      <View style={{ position: "absolute", justifyContent: "space-around", bottom: 0, right: this.displayCutoutTop - 16, height: viewHeight - 50, top: Host.isPad ? 80 : 50, width: 80, alignItems: "center", backgroundColor: "#00000000" }}
        pointerEvents={"box-none"}
      >
        {/* 目前的 - Lottie动画按钮 */}
        <MHLottieSnapLandscapeButton
          style={{ width: 50, height: 50 }}

          onPress={handlerOnceTapWithToast(() => {
            this._startSnapshot();
            this._hidePlayToolBarLater();
          })}
          disabled={this.state.showPoweroffView}
          displayState={MHLottieSnapLandscapeBtnDisplayState.NORMAL}
          accessibilityLabel={DescriptionConstants.rp_34}
        />

        <MHLottieRecordLandscapeButton
          style={{ width: 50, height: 50 }}

          onPress={() => {
            this._hidePlayToolBarLater();
            if (this.state.isRecording) {
              this._stopRecord();
            } else {
              this._startRecord();
            }
          }}
          displayState={this.state.isRecording ? MHLottieRecordLandscapeBtnDisplayState.RECORDING : MHLottieRecordLandscapeBtnDisplayState.NORMAL}
          disabled={!this.state.isPlaying}
          accessibilityLabel={this.state.isPlaying ? DescriptionConstants.rp_35 : DescriptionConstants.rp_35_1}
        />
      </View>
    );
  }

  _renderLandscapeTopButtons() {


    if (!this.state.fullScreen) {
      return null;
    }

    if (!this.state.showPlayToolBar) {
      return null;
    }

    let screenHeight = Dimensions.get("window").height;
    let screenWidth = Dimensions.get("window").width;
    if (screenWidth < screenHeight) { // mi 9 se上 第一次切换回来，获取的高度是竖屏下的高度，需要修改以下。
      screenHeight = screenWidth;
    }

    let iconBack = require("../../Resources/Images/icon_back_black_nor_dark.png");
    let iconBackPre = require("../../Resources/Images/icon_back_black_nor_dark.png");

    let StatusBarheight = null, haveStatus = false;
    if (Platform.OS === "android") {
      StatusBarheight = StatusBar.currentHeight - 16;
      if (StatusBarheight <= 0) {
        haveStatus = true;
        StatusBarheight = 15;
      }
      // -16 因为返回按钮的图片留白太大
    } else {
      StatusBarheight = StatusBarUtil._getInset("top") - 16;
    }

    let speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
    switch (this.state.speed) {
      case 1:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
        break;
      case 2:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X2;
        break;
      case 4:
        speedDisplayState =this.isCameraV1orV3 ? MHLottieSpeedToolBtnDisplayState.X2 : MHLottieSpeedToolBtnDisplayState.X4; // 将UI页面4/16倍速 改成 2/3倍速
        break;
      case 8:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X8;
        break;
      case 16:
        speedDisplayState = this.isCameraV1orV3 ? MHLottieSpeedToolBtnDisplayState.X3 : MHLottieSpeedToolBtnDisplayState.X16;// 将UI页面4/16倍速 改成 2/3倍速
        if (CameraConfig.support8xSpeed(Device.model) && !this.isCameraV1orV3) {
          speedDisplayState = MHLottieSpeedToolBtnDisplayState.X8;
        }
        break;
      default:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
        break;
    }

    return (
      <LinearGradient colors={['#00000077', '#00000000']} pointerEvents={"box-none"}>
        <View style={{ width: "100%", paddingHorizontal: this.displayCutoutTop, display: "flex", flexDirection: "row", marginTop: (Host.isPad) ? 50 : 20 }}>
          <View style={{ flexGrow: 1, display: "flex", flexDirection: "row", justifyContent: "space-between", paddingLeft: 20, paddingRight: 15 }}>
            <ImageButton
              source={iconBack}
              highlightedSource={iconBackPre}
              style={[styles.videoControlBarItemImg]}
              onPress={() => {
                LogUtil.logOnAll(TAG, "sdcard timeline page on press onback button，change to potrait");
                this.toPortrait();// 切换到竖屏
                this._hidePlayToolBarLater(true);
              }}
              accessibilityLabel={DescriptionConstants.rp_30}
            >
            </ImageButton>
          </View>
          <View>
            <View style={{ display: "flex", flexDirection: "row", height: 50, justifyContent: 'flex-end' }}>

              {/* 目前的 - Lottie动画按钮 */}
              <MHLottieAudioToolButton
                style={{ width: 50, height: 50, marginRight: 20 }}

                onPress={() => {
                  this._hidePlayToolBarLater();
                  if (this.state.isMute) {

                    // 默认是这个状态，去开启声音
                    if (this.state.isCalling) {
                      this.isAudioMuteTmp = false;
                    }
                    this._toggleAudio(false);
                  } else {

                    if (this.state.isCalling) {
                      this.isAudioMuteTmp = true;
                    }
                    this._toggleAudio(true);
                  }
                }}

                displayState={this.state.isMute ? MHLottieAudioToolBtnDisplayState.MUTED : MHLottieAudioToolBtnDisplayState.NORMAL}
                landscape={true}
                disabled={this.state.showPoweroffView ? this.state.showPoweroffView : this.state.speed > 1}
                accessibilityLabel={this.state.speed <= 1 ? 
                  this.state.isMute ? DescriptionConstants.rp_15 : DescriptionConstants.rp_32 : 
                  DescriptionConstants.rp_15}
                accessibilityState={{
                  selected: this.state.isMute
                }}
              />

              <MHLottieSpeedToolButton

                style={styles.videoControlBarItemImg}

                onPress={() => {
                  this._hidePlayToolBarLater();
                  this.setState({ dialogVisibility: true });
                }}

                displayState={speedDisplayState}
                landscape={true}

                disabled={this.state.showPoweroffView ? this.state.showPoweroffView : this.state.isRecording}
                accessibilityLabel={DescriptionConstants.rp_13.replace('1',this.state.speed)}
              />
            </View>
          </View>
        </View>


      </LinearGradient>

    );
  }

  _renderRecordTimeView() {
    if (!this.state.isRecording) {
      return null;
    }
    let containerHeight = 20;

    if (this.state.fullScreen) {
      containerHeight = 30;
    }

    let seconds = this.state.recordTimeSeconds;
    let second = Number.parseInt(seconds % 60);
    let minute = Number.parseInt(seconds / 60 % 60);
    let hour = Number.parseInt(seconds / 60 / 60);
    this.lastRecordTime = `${hour > 9 ? hour : `0${hour}`}:${minute > 9 ? minute : `0${minute}`}:${second > 9 ? second : `0${second}`}`;

    return (
      <View
        style={{
          position: "absolute", top: containerHeight, display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center", alignSelf: "center",
          backgroundColor: "#00000099", // 0.8 opacity
          borderRadius: 20,
          width: 90,
          height: 24
        }}>
        <View
          accessibilityLabel={!this.state.fullScreen ? DescriptionConstants.rp_43 : DescriptionConstants.rp_45}
          style={{
            backgroundColor: "#FC4949",
            borderRadius: 8,
            width: 8,
            height: 8,
            marginRight: 2
          }} />
        <Text style={{
          color: "#ffffff",
          fontSize: kIsCN ? 13 : 11,
          fontWeight: "bold",
          textAlign: "center",
          textAlignVertical: "center",
          lineHeight: 24,
          marginLeft: 2
        }}
        >
          {this.lastRecordTime}
        </Text>
      </View>
    );

  }


  _renderPauseView() {

    if (!this.state.showPauseView) {
      return null;
    }
    if (this.state.showPoweroffView) {
      return;
    }
    if (this.state.showLoadingView) {
      return null;
    }


    let videoViewWidth = this.state.videoViewWidth;
    let translateX = (videoViewWidth - 64) / 2;
    let videoViewHeight = this.state.videoViewHeight;
    let translateY = (videoViewHeight - 64) / 2;


    let pauseIcons = [
      {
        source: require("../../Resources/Images/camera_icon_center_pause_nor.png"),
        highlightedSource: require("../../Resources/Images/camera_icon_center_pause_press.png"),
        onPress: () => {
          TrackUtil.reportClickEvent("TimeSlider_PlayPause_ClickNum")
          if (this.state.isRecording) {

            Toast.success("camera_recording_block");
            return;

          }
          // todo pause
          this.isUserPause = true;
          this._startPlay(false);
        }
      },
      {
        source: require("../../Resources/Images/camera_icon_center_play_nor.png"),
        highlightedSource: require("../../Resources/Images/camera_icon_center_play_nor.png"),
        onPress: () => {
          TrackUtil.reportClickEvent("TimeSlider_PlayPause_ClickNum");

          if (this.networkType == "CELLULAR") {
            this.ignoreDataWarning = true;
          }
          this.isUserPause = false;
          LogUtil.logOnAll(TAG, "press sdcardtimeline pause button ,startPlay:  isPageForeground:" + this.isPageForeGround + " isAppForeground:" + this.isAppForeground + " isPluginForeground:" + this.isPluginForeGround);
          this._onResume();
        }
      }
    ];
    let index = this.state.isPlaying ? 0 : 1;

    return (
      <View style={{ position: "absolute", width: 100, height: 100, top: "50%", left: "50%", marginTop: -50, marginLeft: -50, backgroundColor: "#00000000", display: "flex", justifyContent: "center", alignItems: "center" }}
        pointerEvents={"auto"}
      >
        <ImageButton
          style={{ width: 64, height: 64 }}
          source={pauseIcons[index].source}
          highlightedSource={pauseIcons[index].highlightedSource}
          onPress={pauseIcons[index].onPress}
          accessible={true}
          accessibilityLabel={this.state.isPlaying ? 
            !this.state.fullScreen ? DescriptionConstants.rp_14 : DescriptionConstants.rp_31 :
            !this.state.fullScreen ? DescriptionConstants.hk_3_6 : DescriptionConstants.lc_7
          }
          accessibilityState={{
            selected: this.state.isPlaying
          }}
          testId={this.state.isPlaying ? '1' : '0'}
        />
      </View>
    );
  }

  _renderVideoView() {
    if (this.isOffline) {
      return null;
    }
    return (
      <CameraRenderView
        accessible={true}
        accessibilityLabel={DescriptionConstants.rp_62}
        ref={(ref) => { this.cameraRenderView = ref; }}
        maximumZoomScale={6.0}
        style={styles.videoView}
        videoCodec={CameraConfig.getCameraCoderParam(Device.model, Device.lastVersion).videoCodec}
        audioCodec={CameraConfig.getCameraCoderParam(Device.model, Device.lastVersion).audioCodec}
        audioRecordSampleRate={CameraConfig.getCameraAudioSampleRate(Device.model)}
        audioRecordChannel={MISSAudioChannel.FLAG_AUDIO_CHANNEL_MONO}
        audioRecordDataBits={MISSDataBits.FLAG_AUDIO_DATABITS_16}
        fullscreenState={this.state.fullScreen}
        // videoRate={15}
        videoRate={this.state.speed == 16 ? 2 : 15}
        correctRadius={CameraConfig.getCameraCorrentParam(Device.model).radius}
        osdx={CameraConfig.getCameraCorrentParam(Device.model).osdx}
        osdy={CameraConfig.getCameraCorrentParam(Device.model).osdy}
        useLenCorrent={false}
        onVideoClick={this._onVideoClick.bind(this)}
        onScaleChanged={this._onVideoScaleChanged.bind(this)}
        did={Device.deviceID}
        isFull={false}
        recordingVideoParam={{ ...CameraConfig.getRecordingVideoParam(Device.model), isInTimeRecord: true, hasRecordAudio: this.state.speed == 1 }}
      >
      </CameraRenderView>
    );
  }

  _renderPowerOffView() {
    // todo render poweroffview  full
    if (!this.state.showPoweroffView) {
      return null;
    }
    if (this.state.videoViewWidth == 0 || this.state.videoViewHeight == 0) {
      return null;
    }


    let videoViewWidth = this.state.videoViewWidth;
    let translateX = (videoViewWidth - 54) / 2;
    let videoViewHeight = this.state.videoViewHeight;
    let translateY = (videoViewHeight - 78) / 2;

    return (

      <TouchableWithoutFeedback style={{ position: "absolute", width: 64, height: 64, top: "50%", left: "50%", marginTop: -32, marginLeft: -32 }}
        onPress={() => { }}
      >
        <View style={{ backgroundColor: "black", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
        >
          <Image
            style={{ width: 54, height: 54 }}
            source={require("../../Resources/Images/icon_camera_sleep.png")} />
          <Text
            style={{ marginTop: 10, fontSize: kIsCN ? 14 : 12, color: "#bfbfbf" }}>
            {LocalizedStrings[this.getPowerOffString()]}
          </Text>
        </View>
      </TouchableWithoutFeedback>
    );
  }

  _renderErrorRetryView() {
    if (this.state.showHighTemperatureView) {
      return null;
    }

    if (!this.state.showErrorView) {
      return null;
    }

    let buttonReConnectItem = (
      <View
        style={{
          paddingTop: 6,
          paddingBottom: 6,
          paddingLeft: 17,
          paddingRight: 17,
          backgroundColor: "#249A9F",
          borderRadius: 20,
          marginTop: 10
        }}>
        <Text style={{
          color: "#fff",
          fontSize: kIsCN ? 12 : 10
        }}
        >{LocalizedStrings['reconnect_button_text']}</Text>
      </View>
    );

    let noNetworkItem = (
      <View style={{ display: "flex", flexDirection: "row" }}>
        <TouchableOpacity
          style={{ display: "flex", alignItems: "center" }}
          onPress={() => {
            if (!Device.isOnline) {
              Toast.success("device_offline");
              return;
            }
            this.setState({ showErrorView: false });
            Service.smarthome.reportLog(Device.model, "on error Retry");
            this._startPlay(false);
            this.queryNetworkJob();
          }}>
          {buttonReConnectItem}
        </TouchableOpacity>

      </View>

    );

    const errIcons = [
      require("../../Resources/Images/icon_connection_failure.png"),
      require("../../Resources/Images/icon_camera_offline.png"),
      require("../../Resources/Images/icon_camera_fail.png")
    ];

    let errIconIndex = 0;

    if (!Device.isOnline) {
      errIconIndex = 1;
    }

    return (
      <View
        style={{ zIndex: 7, position: "absolute", bottom: 0, backgroundColor: "black", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <View
          style={{ display: "flex", alignItems: "center" }}
        >
          <Image
            style={{ width: 54, height: 54 }}
            source={errIcons[errIconIndex]} />
          <Text
            style={{ marginTop: 10, fontSize: kIsCN ? 12 : 10, color: "#999", paddingHorizontal: 10, textAlign:"center", width: kWindowWidth - 60 }}>
            {this.currentNetworkState == 0 ? LocalizedStrings['common_net_error'] : this.state.errTextString}{Device.isOnline ? "" : (this.state.lastOfflineTime == "") ? "" : `, ${this.state.lastOfflineTime}`}
          </Text>
          {/* {Device.isOnline ? null : powerOfflineText} */}
        </View>
        {noNetworkItem}

      </View>
    );
    // todo render errorRetryView not
  }


  _renderLoadingView() {
    // todo render loading view 
    if (!this.state.showLoadingView || this.state.showPoweroffView) {
      return null;
    }

    let bgColor = "transparent";
    let loadingViewStyle = {
      zIndex: 0,
      position: "absolute",
      width: "100%",
      height: "100%",
      bottom: 0,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: bgColor
    };

    return (
      <View
        style={loadingViewStyle}
        pointerEvents={"none"}
      >
        <LoadingView
          style={{ width: 54, height: 54 }}
          color={"#ffffff"}
          size={"large"}

        />
        <Text
          style={{ marginTop: 10, fontSize: kIsCN ? 12 : 10, color: "#ffffff" }}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );
  }


  _renderVideoControlView() {

    if (this.state.fullScreen) {
      return null;
    }

    let speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
    let replacedSpeed = 1;
    switch (this.state.speed) {
      case 1:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
        replacedSpeed = 1;
        break;
      case 2:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X2;
        replacedSpeed = 2;
        break;
      case 4:
        speedDisplayState = this.isCameraV1orV3 ? MHLottieSpeedToolBtnDisplayState.X2 : MHLottieSpeedToolBtnDisplayState.X4;
        replacedSpeed = this.isCameraV1orV3 ? 2 : 4;
        break;
      case 8:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X8;
        replacedSpeed = 8;
        break;
      case 16:
        speedDisplayState = this.isCameraV1orV3 ? MHLottieSpeedToolBtnDisplayState.X3 : MHLottieSpeedToolBtnDisplayState.X16;
        replacedSpeed = this.isCameraV1orV3 ? 3 : 16;
        if (CameraConfig.support8xSpeed(Device.model) && !this.isCameraV1orV3) {
          speedDisplayState = MHLottieSpeedToolBtnDisplayState.X8;
          replacedSpeed = 8;
        }
        break;
      default:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
        break;
    }

    if (this.state.showPlayToolBar) {
      return (

        <LinearGradient pointerEvents={"box-none"} colors={['#00000000', '#00000077']} style={[{ position: "absolute", width: "100%" }, this.state.fullScreen ? { top: 0 } : { bottom: 0 }]}
        >

          <View style={this.state.fullScreen ? styles.videoControlBarFull : styles.videoControlBar}>

            {/* 目前的 - Lottie动画按钮 */}
            <View style={styles.videoControlBarItem}>

              <MHLottieSnapToolButton
                style={styles.videoControlBarItemImg}

                onPress={handlerOnceTapWithToast(() => {
                  this._hidePlayToolBarLater();
                  TrackUtil.reportClickEvent("TimeSlider_ScreenShot_ClickNum");
                  this._startSnapshot();
                })}
                displayState={MHLottieSnapToolBtnDisplayState.NORMAL}
                landscape={this.state.fullScreen}
                disabled={this.state.showPoweroffView}
                accessibilityLabel={!this.state.fullScreen ? DescriptionConstants.rp_11 : DescriptionConstants.rp_34}
              />
            </View>

            <View style={styles.videoControlBarItem}>

              <MHLottieRecordToolButton
                style={styles.videoControlBarItemImg}

                onPress={() => {
                  this._hidePlayToolBarLater();
                  TrackUtil.reportClickEvent("TimeSlider_Record_ClickNum");

                  if (this.state.isRecording) {
                    this._stopRecord();
                  } else {
                    this._startRecord();
                  }

                }}
                displayState={this.state.isRecording ? MHLottieRecordToolBtnDisplayState.RECORDING : MHLottieRecordToolBtnDisplayState.NORMAL}

                disabled={this.state.showPoweroffView ? this.state.showPoweroffView : !this.state.isPlaying}
                accessible={true}
                accessibilityLabel={!this.state.fullScreen ? (this.state.isPlaying ? DescriptionConstants.rp_12 : DescriptionConstants.rp_12_1) : DescriptionConstants.rp_35}
                accessibilityState={{
                  selected: this.state.isRecording,
                  disabled: !this.state.isPlaying
                }}
                testId={this.state.isRecording ? '1' : '0'}
              />
            </View>

            <View style={styles.videoControlBarItem}>

              <MHLottieSpeedToolButton
                style={styles.videoControlBarItemImg}

                onPress={() => {
                  this._hidePlayToolBarLater();
                  TrackUtil.reportClickEvent("TimeSlider_PlaySpeed_ClickNum");
                  this.setState({ dialogVisibility: true });
                }}

                displayState={speedDisplayState}
                landscape={false}

                disabled={this.state.showPoweroffView ? this.state.showPoweroffView : this.state.isRecording}

                accessibilityLabel={ DescriptionConstants.rp_13.replace('1', replacedSpeed)}
                accessibilityState={{
                  selected: this.state.dialogVisibility
                }}
                testId={speedDisplayState}
              />
            </View>

            <View style={styles.videoControlBarItem}>

              <MHLottieAudioToolButton
                style={styles.videoControlBarItemImg}

                onPress={() => {
                  this._hidePlayToolBarLater();
                  if (this.state.isMute) {

                    // 默认是这个状态，去开启声音
                    if (this.state.isCalling) {
                      this.isAudioMuteTmp = false;
                    }
                    this._toggleAudio(false);
                  } else {

                    if (this.state.isCalling) {
                      this.isAudioMuteTmp = true;
                    }
                    this._toggleAudio(true);
                  }
                }}

                displayState={this.state.isMute ? MHLottieAudioToolBtnDisplayState.MUTED : MHLottieAudioToolBtnDisplayState.NORMAL}
                landscape={this.state.fullScreen}
                disabled={this.state.showPoweroffView ? this.state.showPoweroffView : this.state.speed > 1}
                accessible={true}
                accessibilityLabel={this.state.isMute ? DescriptionConstants.rp_15 : DescriptionConstants.rp_32}
                accessibilityState={{
                  selected: this.state.isMute
                }}
                testId={this.state.isMute ? '1' : '0'}
              />
            </View>

            <View style={styles.videoControlBarItem}>

              <MHLottieFullScreenToolButton
                style={styles.videoControlBarItemImg}

                onPress={() => {
                  this._hidePlayToolBarLater();
                  if (!this.state.fullScreen) {
                    this.setState({
                      isWhiteVideoBackground: false
                    });
                    this.toLandscape();
                  } else {
                    this.setState({
                      isWhiteVideoBackground: true
                    });
                    this.toPortrait();
                  }
                }}
                disabled={this.state.showPoweroffView}
                displayState={MHLottieFullScreenToolBtnDisplayState.NORMAL}
                accessibilityLabel={DescriptionConstants.rp_16}
              />
            </View>
          </View>
        </LinearGradient>
      );

    } else {
      return (null);
    }
  }

  _renderSnapshotView() {
    if (!this.state.screenshotVisiblity) {
      return null;
    }

    let recordItem = (
      <View style={{
        display: "flex",
        flexDirection: "row",
        bottom: 0,
        position: "absolute",
        alignItems: "center"
      }}>
        <Image style={{ width: 12, height: 12, marginLeft: 10 }} source={require("../../Resources/Images/icon_snapshot_camera_play.png")}></Image>
        <Text style={{ fontSize: kIsCN ? 12 : 10, fontWeight: "bold", color: "#ffffff", marginLeft: 5 }}>{this.lastRecordTime}</Text>
      </View>
    );

    let sWidth = 90;
    let sHeight = 55;
    let sPadding = 20;
    let leftPading = 15;
    if (this.state.fullScreen) {
      sPadding = 90;
      leftPading = leftPading + StatusBarUtil._getInset("top");
    } else {
    }

    let containerStyle;
    containerStyle = {
      position: "absolute",
      left: leftPading,
      top: sPadding,
      width: sWidth,
      height: sHeight,
      borderRadius: 4,
      borderWidth: 1.5,
      borderColor: "xm#ffffff",
      zIndex: 10
    };

    if (this.state.fullScreen) {
      if (Platform.OS == "ios") {
        if (isIphoneX() || isIphone12 || isIphone12P) {
          containerStyle.left = 60;
        }

      }
      if (Host.isPad) {
        containerStyle.top = "50%";
        containerStyle.marginTop = -1 * sHeight / 2;
      }
    }

    return (
      <View style={containerStyle}
      >
        <ImageButton
          accessibilityLabel={this.isForVideoSnapshot ? DescriptionConstants.hk_2_6 : !this.state.fullScreen ? DescriptionConstants.rp_42 : DescriptionConstants.rp_46}
          style={{ width: "100%", height: "100%", borderRadius: 4 }}
          source={(this.state.screenshotPath == "") ? null : ({ uri: `file://${Host.file.storageBasePath}/${this.state.screenshotPath}` })}
          fadeDuration={0}
          onPress={() => {
            if (!this.canStepOut()) {
              return;
            }
            clearTimeout(this.snapshotTimeout);
            this.setState({ screenshotVisiblity: false, screenshotPath: "", isWhiteVideoBackground: true });// 点击后就消失。
            if (this.isForVideoSnapshot) {
              console.log("点击了缩略图，跳转到视频页面");
              this.showLastVideo();
              // this.props.navigation.navigate("AlbumVideoViewPage");
            } else {
              console.log("点击了缩略图，跳转到图片页面");
              this.showLastImage();
              // this.props.navigation.navigate("AlbumPhotoViewPage");
            }

            this.isForVideoSnapshot = false;
            // todo jump to album activity
          }}
        />
        {this.isForVideoSnapshot ? recordItem : null}

      </View>
    );
  }

  canStepOut() {
    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return false;
    }

    if (this.state.showPoweroffView) {
      Toast.success("camera_power_off");
      return false;
    }
    return true;
  }

  _renderTimeLineView() {
    // 如果这里设置为null，等切屏回来后，就生成了一个新的view。原先绑定的数据就没了。
    // if (this.state.fullScreen) {
    //   return null;
    // }
    let containerStyle;
    if (this.state.fullScreen) {
      let padding = 0;
      if (Platform.OS == "ios") {
        if (isIphoneX()) {
          padding = this.statusBarHeight + 5;
        }
        padding = this.statusBarHeight + 15;
      }
      containerStyle = {
        position: "absolute",
        bottom: 0,
        paddingLeft: padding,
        paddingRight: padding,
        width: "100%"
      };
    } else {
      containerStyle = { width: "100%" };
    }
    const { touchMovement } = this.state;
    return (// scaletimelineView自己处理宽高。
      // <SafeAreaView style={{display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center"}}>
      <View style={[(this.state.fullScreen && !this.state.showPlayToolBar) ? { display: "none" } : containerStyle]}
      accessibilityLabel={DescriptionConstants.rp_60}
      >
        <TimeScaleView2
          ref={(ref) => { this.timelineView = ref; }}
          // onCenterValueChanged={this._onCenterValueChanged}
          onScrolling={this._onScrolling}
          onScrollEnd={this._onCenterValueChanged}
          isDisabled={(this.state.isEmpty || this.state.isSleep) ? true : false}
          isRecording={this.state.isRecording}
          landscape={this.state.fullScreen}
          eventTypeFlags={this.state.eventTypeFlags}
          touchMovement={touchMovement}
        />
      </View>
      // </SafeAreaView>

    );
  }


  // 这里代表时间轴滚动了
  _onCenterValueChanged = (timestamp) => {
    TrackUtil.reportClickEvent("TimeSlider_Drop_ClickNum");
    console.log("滑动结束");
    this.setState({ touchMovement: false });
    this.dateTime.setTime(timestamp);
    // console.log(`timestamp:${timestamp}`);
    console.log(`${this.dateTime.getHours()} ${this.dateTime.getMinutes()} ${this.dateTime.getSeconds()}`);

    if (this.timeIndicatorView == null) {
      return;
    }
    this.timeIndicatorView.setState({ centerTimestamp: 0 });

    this.toStartTime = timestamp;
    if (this.state.isPlaying) {
      this.queryNetworkJob();// 检测网络
    }
    // 如果没有播放  就不用管了

  }

  _renderAngleView() {
    if (!this.state.showCameraAngleView) {
      return (null);
    }

    let sPadding = 20;
    let bottom = this.state.fullScreen ? (kWindowHeight > 600 ? 250 : 180) : (kWindowHeight > 600 ? (kWindowWidth / 1.78 - 28 - sPadding) : 80);// 28 is angle view's height
    let left = this.state.fullScreen ? 55 : sPadding;
    let angleStyle = {
      position: "absolute",
      left: left,
      bottom: bottom
    };

    return (
      <View style={angleStyle}>
        <RectAngleView
          ref={(ref) => { this.angleView = ref; }}
          angle={this.state.angle}
          elevation={this.state.elevation}
          scale={this.state.videoScale}
          showScale={this.state.angleViewShowScale}
          accessibilityLabel={DescriptionConstants.zb_39.replace('1',this.state.videoScale)}
        />
      </View>
    );
  }

  _renderBottomSelectView() {
    if (this.state.fullScreen) {
      return;
    }

    return (
      <View style={{ width: "100%", flexGrow: 1, position: "relative" }}>
        <TouchableOpacity
          accessibilityLabel={DescriptionConstants.rp_44}
          style={{ position: "absolute", bottom: 0, width: "100%", height: 46, marginBottom: 20, marginTop: 8, paddingHorizontal: 24 }}
          onPress={() => { this._startAllStorageWithPermissionCheck(); }}
        >
          <View
            style={{ width: "100%", height: "100%", backgroundColor: isDark ? "#474747" : "#F5F5F5", borderRadius: 23, display: "flex", alignItems: "center", justifyContent: "center" }}
          >
            <Text
              style={{ color: "#4C4C4C", fontSize: kIsCN ? 16 : 14, fontWeight: 'bold' , marginHorizontal: 40, textAlignVertical: "center", textAlign: 'center' }}
            >
              {LocalizedStrings["all_sdcard_video"] + " "}
            </Text>
          </View>

        </TouchableOpacity>

      </View>
    );
  }

  _orientationListener = (orientation) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    console.log("orientation changed");
    if (!this.isPageForeGround || !this.isPluginForeGround || !this.isAppForeground) {
      return;
    }
    if (this.isCheckingPermission) {
      // 全屏请求权限，横竖屏切来切去
      return;
    }
    console.log(TAG, `device orientation changed :${orientation} want ${this.mOri}`);
    if ((Platform.OS == "ios" || (Host.isAndroid && Host.isPad)) && ((this.mOri === 'LANDSCAPE' && this.state.fullScreen) || (this.mOri === 'PORTRAIT' && !this.state.fullScreen))) {
      return;
    }
    if (this.mOri === orientation) {
      if (orientation === 'LANDSCAPE') {
        // do something with landscape layout
        this._setNavigation(true);
      } else {
        // do something with portrait layout
        // this.setState({ fullScreen: false });
        this._setNavigation(false);
      }
    } else {
      // ios need restore for next lock
      this.restoreOri();
    }
  };

  _setNavigation(isFull) {
    // if (Host.isPad) { // 直播页面也需要调用
    //   Service.miotcamera.enterFullscreenForPad(isFull ? true : false);
    // }
    this.props.navigation.setParams({ isFullScreen: isFull });
    this.setState({ fullScreen: isFull });
  }

  componentDidMount() {
    TrackUtil.reportClickEvent("TimeSlider_SDCard_Num");
    DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }

    this.restoreOri();
    Orientation.addOrientationListener(this._orientationListener);

    Service.miotcamera.bindTimelinePlaybackEndListener(timelinePlaybackEndListenerName);

    // SdFileManager.getInstance().bindReceiveFilesListener(this._bindFilesHandler);
    // SdFileManager.getInstance().startRequestSdcardFilesRegularly();
    // 加载后，就先destroyInstance，再重新创建，重新加载。

    

    this.recordListener = DeviceEventEmitter.addListener(kRecordTimeCallbackName, (data) => {
      if (data == null) {
        return;
      }
      let time = Number.parseInt(data.recordTime);// 要屏蔽倍速带来的影响
      this.setState({ recordTimeSeconds: time });
      console.log(data);// 录制时长。
    });

    if (Platform.OS == "ios") {
      this.statusBarHeight = getStatusBarHeight();
    }

    this._loadSdfiles();
    if (!CameraPlayer.getInstance().isConnected()) {
      this.didMountTime = Date.now();
    }
    // 不同model  都不一样.
    let eventFlags = EVENT_TYPE.Default;
    if (CameraConfig.displayMotionInTimeline(Device.model)) {
      eventFlags = eventFlags | EVENT_TYPE.EVENT_TYPE_AREA_MOTION;
    }
    if (CameraConfig.displayPeopleInTimeline(Device.model)) {
      eventFlags = eventFlags | EVENT_TYPE.EVENT_TYPE_PEOPLE_MOTION;
    }
    if (CameraConfig.displayBabyCryInTimeline(Device.model)) {
      eventFlags = eventFlags | EVENT_TYPE.EVENT_TYPE_BABY_CRY;
    }
    if (CameraConfig.displayPetInTimeline(Device.model)) {
      eventFlags = eventFlags | EVENT_TYPE.EVENT_TYPE_PET_MOTION;
    }
    if (CameraConfig.displayFaceInTimeline(Device.model)) {
      eventFlags = eventFlags | EVENT_TYPE.EVENT_TYPE_FACE;
    }
    if (CameraConfig.displayCameraCallingTimeline(Device.model)) {
      eventFlags = eventFlags | EVENT_TYPE.EVENT_TYPE_CAMERA_CALLING;
    }
    if (CameraConfig.displayLOUDER_SOUND_Timeline(Device.model)) {
      eventFlags = eventFlags | EVENT_TYPE.EVENT_TYPE_LOUDER_SOUND;
    }
    let supportEvents = CameraConfig.pgSupportEvents();
    for (let supportEvent of supportEvents) {
      eventFlags = eventFlags | EVENT_TYPE[supportEvent];
    }
    this.setState({ eventTypeFlags: eventFlags });

    if (!Device.isOnline) {
      this.setState({ showErrorView: true, errTextString: LocalizedStrings['device_offline'] });
      OfflineHelper.getLastOnlineTime()
        .then((result) => {
          this.setState({ lastOfflineTime: `${LocalizedStrings['offline_time_str']}: ${result}` });
        })
        .catch((rr) => {
        });
    }
    this.isOffline = false;
    if (!Device.isOnline) {
      this.isOffline = true;
    }
  }

  checkLocalNetwork() {
    console.log("检查局域网状态");
    // 设置超时，防止 localPing 在非局域网内执行时间过长
    this.timeoutLocalPing && clearTimeout(this.timeoutLocalPing);
    this.timeoutLocalPing = setTimeout(() => {
      CameraConfig.isLocalNetwork = false;
      if (this.state.isInSameLAN) {
        this.setState({
          isInSameLAN: false,
          showNetworkDialog: true
        });
        // 非局域网弹框弹出时暂停播放
        this.isUserPause = true;
        this._startPlay(false);
      }
    }, 3000);

    Device.getDeviceWifi().localPing().then((response) => {
      console.log("localPing 结果:", response);
      this.timeoutLocalPing && clearTimeout(this.timeoutLocalPing);
      CameraConfig.isLocalNetwork = response;
      if (!response && this.isPageForeGround) {
        // 不在同一局域网，显示提示弹窗并暂停播放
        this.setState({
          isInSameLAN: false,
          showNetworkDialog: true
        });
        // 非局域网弹框弹出时暂停播放
        this.isUserPause = true;
        this._startPlay(false);
      } else if (response && !this.state.isInSameLAN) {
        // 恢复到同一局域网
        this.setState({
          isInSameLAN: true,
          showNetworkDialog: false
        });
        this.isUserPause = false;
        setTimeout(() => {
          // 重新尝试连接
          // 立即连接可能无法正常播放
          this.queryNetworkJob();
        }, 500);

      }
    }).catch((err) => {
      console.log("localPing 错误", err);
      this.timeoutLocalPing && clearTimeout(this.timeoutLocalPing);
      CameraConfig.isLocalNetwork = false;
      if (this.isPageForeGround) {
        // 发生错误，认为不在同一局域网，显示提示弹窗
        this.setState({
          isInSameLAN: false,
          showNetworkDialog: true
        });
        // 非局域网弹框弹出时暂停播放
        this.isUserPause = true;
        this._startPlay(false);
      }
    });
  }

  _loadSdfiles() {
    let sdcardListStartTime = Date.now();
    // here to delete all datas;
    SdcardEventLoader.getInstance().getEventList()
      .then(() => {
        this.sdcardListRequestTime = Date.now() - sdcardListStartTime;
        this.onGetFiles();// 数据已经读取完毕了。
        // 加载成功了。
      })
      .catch(() => {
        // 加载失败了
      });
  }

  _powerOffHandler = (isPowerOn, showSleepDialog, startVideo) => {
    if (this.cameraRenderView == null) {
      CameraPlayer.getInstance().bindPowerOffCallback(null);
      return;
    }
    if (this.isUserPause) {
      // 用户暂停了播放
      return;
    }
    // 电源属性状态发生了变化
    this.setState({ showPoweroffView: !isPowerOn, showPlayToolBar: isPowerOn, isPlaying: isPowerOn });
    if (!isPowerOn) { // 暂停
      this._startPlay(false);
      this._stopRecord();
    } else {
      this.queryNetworkJob();
    }
  }

  _networkChangeHandler = (networkState) => {
    this.checkLocalNetwork();

    if (this.cameraRenderView == null) {
      return;
    }
    // if (this.currentNetworkState != networkState) { // 网络状态不一样
    //   this.isChangingNetwork = true;
    // }
    // if (this.currentNetworkState != networkState) { // 网络状态不一样
    //   this.isChangingNetwork = true;
    // }
    console.log("处理网络变化", networkState);

    this.currentNetworkState = networkState;
    clearTimeout(this.showNetworkDisconnectTimeout);
    if (networkState == 0 || networkState == -1) { // 网络断开了连接 showError?
      Service.smarthome.reportLog(Device.model, "网络异常" + networkState);
      // CameraPlayer.getInstance().disconnectToDevice();// 去往其他注册了网络监听的页面，就不会走到这里了，如果走到这里，这里必须先执行断开的操作
      this.showNetworkDisconnectTimeout = setTimeout(() => {
        this.handleDisconnected(MISSError.MISS_ERR_CLOSE_BY_LOCAL);// 刷新UI，避免出现异常。  
      }, 1300);
      return;
    }
    if (this.isPageForeGround) { // 有网络来了  发起重连吧
      this.setState({ showErrorView: false });
      clearTimeout(this.reConnectTimeout);
      this.reConnectTimeout = setTimeout(() => {
        this.queryNetworkJob();
      }, 500);// 过一会再查询 ，那个查询网络类型的api有问题。
    }
  }

  _ServerRespHandler = ({ command, data }) => {
    // todo  处理文件播放的问题
    console.log("server response:", command, data);
    if (command == MISSCommand.MISS_CMD_PLAYBACK_RESP) {
      console.log(data);
      let dataJSON = Host.isAndroid ? JSON.parse(data) : data;
      let id = dataJSON["id"];
      if (id == null) {
        return;
      }
      if (id != this.sessionId) {
        return;
      }
      let status = dataJSON["status"];
      if (status == null) {
        return;
      }
      let startTime = dataJSON.starttime;
      let duration = dataJSON.duration;
      switch (status) {
        case "filefound":

          console.log(dataJSON);
          LogUtil.logOnAll("SdcardTimeline", "filefound:" + JSON.stringify(dataJSON));//收到的视频帧信息
          // what todo ?  
          // this.setState({ showLoadingView: false });
          // 开始请求timestamp
          this.endTime = startTime + duration;
          //文件找到后才开始拉取时间戳；
          if (this.firstPullVideoFrameTime != 0) {
            let firstVideoFrameShowed = Date.now() - this.firstPullVideoFrameTime;
            LogUtil.logOnAll(TAG, "sdcard connectTime:" + this.connectionTime + " pullSdcardFileList:" + this.sdcardListRequestTime + " firstVideoFrameShowd:" + firstVideoFrameShowed);
            this.firstPullVideoFrameTime = 0;
          }

          CameraPlayer.getInstance().getPlaybackTimetampInterval();
          CameraPlayer.getInstance().bindPlaybackTimestampCallback(this.timestampCallback);


          break;
        case "filenotfound":
          this.setState({ progress: 0, showErrorView: true, showLoadingView: false, showPlayToolBar: false, showPauseView: false, errTextString: LocalizedStrings["camera_play_error_file"] });
          if (this.state.isRecording) {
            this._stopRecord();
          }
          this._startPlay(false);
          break;
        case "endoffile":
          // this.isUserPause = true;
          // this.setState({ showLoadingView: false, progress: 0 });
          // this._startPlay(false);
          // this.setState({ showPauseView: true });
          // 取消timestamp的定时请求
          if (this.islastestFilePlayFailed && (this.penultimateStartTime + 60000 - this.endTime * 1000 < 1500)) { // 上一次播放最后一个文件失败了，这一次播放到倒数第二个文件就不播放了。
            this.toSdcardEnd();
          }
          // CameraPlayer.getInstance().stopPlaybackTimestampInterval();

          LogUtil.logOnAll("SdcardTimeline", "endoffile:" + JSON.stringify(dataJSON) + " last endTime:" + (this.endTime * 1000) + " lastest endTime:" + this.lastTimeItemEndTime);//收到的视频帧信息
          if (this.lastTimeItemEndTime != 0 && this.lastTimeItemEndTime - this.endTime * 1000 < 1500) {
            this.toSdcardEnd();
          }
          break;
        case "readerror":
          if (VersionUtil.judgeIsV1(Device.model) && (this.sessionId * 1000 >= this.lastTimeItemStartTime) && (this.penultimateStartTime > 0)) { // 针对v1做一次纠正，部分固件播放最后一个没问题，部分固件播放最后一个有问题，这里做一次替换。
            this.toStartTime = this.penultimateStartTime;
            clearTimeout(this.timestampTimeout);
            this.islastestFilePlayFailed = true;
            this._startPlay(true);
          } else {
            this.setState({ progress: 0, showLoadingView: false, showErrorView: true, showPauseView: false, showPlayToolBar: false, errTextString: LocalizedStrings["camera_play_error_file"] });
            this._startPlay(false);
          }
          // 取消timestamp的定时请求
          CameraPlayer.getInstance().stopPlaybackTimestampInterval();
          break;
      }
    }
  }

  timestampCallback = (timestamp) => {
    if (this.timelineView == null) {
      return;
    }
    // console.log(`lastTimeItemEndTime:${this.lastTimestamp}`, `currentTimestamp:${timestamp}`);

    // 这里每隔一秒就会触发一次 返回当前的时间戳
    if (timestamp == this.lastTimestamp) { // 没有发生更新

      return;
    }

    // if (this.lastTimeItemEndTime - timestamp * 1000 < 1500 ) {//接近文件末尾了 直接pause
    //   this.toSdcardEnd();
    //   return;
    // }
    this.toStartTime = timestamp * 1000;
    if (this.isSetPlayTime) {
      let diff = timestamp - this.startTime;
      // console.log(diff, timestamp, this.startTime);
      if (Platform.OS == "ios") {
        this.setState({ showLoadingView: false, showPauseView: true, showPlayToolBar: true });
        this.isSetPlayTime = false;
        this._hidePlayToolBarLater();
      }
      if (Math.abs(this.offset - diff) <= 20 || (new Date().getTime() - this.setPlayTimeMillis) > 6000) { // 刚刚设置了playback指令， 要消除loading
        // todo hide loading
        this.setState({ showLoadingView: false, showPauseView: true, showPlayToolBar: true });
        this.isSetPlayTime = false;
        this._hidePlayToolBarLater();
      } else {
        return;// 不管，等后面来timestamp
      }
    }

    this.lastTimestamp = timestamp;
    // console.log(`updated time:${ this.lastTimestamp }`);
    this.dateTime.setTime(timestamp * 1000);
    // console.log(`scroll to time:${ this.dateTime.getHours() }:${ this.dateTime.getMinutes() }:${ this.dateTime.getSeconds() }`);
    this.timelineView && this.timelineView.scrollToTimestamp(this.lastTimestamp * 1000);
  }

  _startSnapshot() {
    if (Platform.OS === "android") {
      this.isCheckingPermission = true;
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          this.isCheckingPermission = false;
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this._realStartSnapshot(false);
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            // Toast.success("camera_no_write_permission");
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
        this.isCheckingPermission = false;
        Toast.success("action_failed");
        });
    } else {
      // no ios's photos const use hardcode
      this.isCheckingPermission = true;
      System.permission.request("photos").then((res) => {
        this.isCheckingPermission = false;
        this._realStartSnapshot(false);
      }).catch((error) => {
        // Toast.success("camera_no_write_permission");
        this.isCheckingPermission = false;
        this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
      });
    }
  }

  _realStartSnapshot(isFromVideo) {
    AlbumHelper.snapShot(this.cameraRenderView)
      .then((path) => {
        // console.log(path);
        this.isForVideoSnapshot = isFromVideo;
        this.setState({ screenshotVisiblity: true, screenshotPath: path });// show snapshotview
        clearTimeout(this.snapshotTimeout);
        this.snapshotTimeout = setTimeout(() => {
          this.isForVideoSnapshot = false;
          this.setState({ screenshotVisiblity: false, screenshotPath: null });
        }, 3000);
        // 文件路径。
      })
      .catch((error) => {
        // console.log(JSON.stringify(error));
        Toast.success("action_failed");
      });
  }

  _toggleAudio(isMute, changeUnitMute = true) {
    if (!isMute) {
      TrackUtil.reportClickEvent("TimeSlider_OpenVolume_ClickNum")
    }

    if (isMute) {
      if (!this.state.isRecording && this.state.isPlaying) {
        Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {}).then((retCode) => {
          // console.log("audio stop get send callback");
          // console.log(retCode);
        });        
      }
      if (this.state.isPlaying) {
        this.cameraRenderView && this.cameraRenderView.stopAudioPlay();
      }

      this.setState({ isMute: true });
      if (changeUnitMute) {
        CameraConfig.setUnitMute(true);
      }
      return;
    }
    if (this.state.speed > 1) {
      this._toggleAudio(true, false);
      return;// 倍速模式下 不要播放声音
    }
    if (!this.state.isRecording && this.state.isPlaying) {
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
        // console.log("audio stop get send callback");
        // console.log(retCode);
      });
    }

    if (this.state.isPlaying) {
      this.cameraRenderView && this.cameraRenderView.startAudioPlay();
    }
    this.setState({ isMute: false });
    if (changeUnitMute) {
      CameraConfig.setUnitMute(false);
    }
  }

  // 进来先调用startPlay  startPlay里会check连接状态，如果是连接中的状态，直接发playback指令；如果不是连接中的状态，先连接，连接走到这里 如果连接成功，就会重新走startPlay流程
  _connectionHandler = (connectionState) => {
    if (connectionState == null) {
      return;
    }
    if (connectionState.state == 4) { // for tutk 4 是 miss状态1的一个子状态，去掉
      connectionState.state = 1;
    }
    if (this.tempState == connectionState.state && this.state.error == connectionState.error) {
      return;// 状态一样 没有必要通知
    }
    this.tempState = connectionState.state;
    this.tempError = connectionState.error;
    if (connectionState.state == 0) { // 断开连接
      this.isConnecting = false;
      if (this.connRetry > 0) {
        this.connRetry = this.connRetry - 1;
        setTimeout(() => {
          this.queryNetworkJob();
        }, 300);
        // console.log("connection retry");

        return;
      }
      this.handleDisconnected(connectionState.error);
      this.setState({
        pstate: connectionState.state,
        error: connectionState.error
      });
      return;
    }


    this.setState((state) => {
      return {
        pstate: connectionState.state,
        error: connectionState.error
      };
    }, () => {
      if (this.state.pstate >= 2) { // 连接成功后就会重新开始播放
        if (!this.isConnecting) { // 处理tutk断线自动重连重复发送connected消息导致的问题。
          return;
        }

        if (this.isFirstReceiveFiles) {
          this.connectionTime = Date.now() - this.didMountTime;
        }
        this.connRetry = 2;
        this.isConnecting = false;
        // on connected
        if (this.lastTimeItemEndTime == 0 || this.lastTimeItemEndTime == null) {
          this._loadSdfiles();// load sdcards
        } else { // 已经有数据了。
          this._startPlay(true);
        }
      }
    });

  }


  handleDisconnected(errorCode) {
    this.cameraRenderView && this.cameraRenderView.stopAudioPlay();
    this.cameraRenderView && this.cameraRenderView.stopRender();
    // sync ui state
    this.setState({ isMute: CameraConfig.getUnitMute() });
    this._stopRecord();
    // 如果网络已经断开了连接。  network change引起的p2p连接变更已经不能走到这里了

    if (!Device.isOnline) {
      return;
    }
    let errorStr = ((errorCode == 36 || errorCode == MISSError.MISS_ERR_MAX_SESSION) && VersionUtil.judgeIsMiss(Device)) ? LocalizedStrings["max_client_exceed"] : (errorCode == -6 && !VersionUtil.judgeIsMiss(Device) ? LocalizedStrings["max_client_exceed"] : `${LocalizedStrings["camera_connect_error"]} ${errorCode}, ${LocalizedStrings["camera_connect_retry"]}`);

    this.setState({ showPlayToolBar: false, showErrorView: true, isPlaying: false, showLoadingView: false, errTextString: errorStr });
  }

  _startPlay(isPlay, ignoreState = false) {

    if (isPlay) {
      if (this.lastTimeItemEndTime == 0) { // 没有数据
        return;
      }
      this.setState({ showPlayToolBar: true });
      // 开始寻找合适的item
      let startTime = this.toStartTime;
      if (startTime >= this.lastTimeItemEndTime) {
        startTime = SdFileManager.getInstance().getLastestItemStartTime();
      }
      if (startTime <= 0) {
        return;
      }
      this.dateTime.setTime(startTime);
      // console.log(`开始播放 format time:${this.dateTime.getHours()}:${this.dateTime.getMinutes()}:${this.dateTime.getSeconds()}`);

      let timeItem;
      if (Device.model === VersionUtil.Model_Camera_V1) {
        timeItem = SdFileManager.getInstance().getTimeItemClosestForV1(startTime);
      } else {
        timeItem = SdFileManager.getInstance().getTimeItemClosest(startTime);
      }
      if (timeItem != null) {
        this.startTime = Number.parseInt(timeItem.startTime / 1000);
        let duration = Number.parseInt(timeItem.duration / 1000);
        if (timeItem.startTime < startTime) {
          this.offset = Number.parseInt((startTime - timeItem.startTime) / 1000);
          if (this.offset >= duration) {
            this.offset = duration - 2;
          }
        } else {
          this.offset = 0;
        }
        this.isSetPlayTime = true;
        this.setPlayTimeMillis = new Date().getTime();
        // show loading

      } else { // 这里只可能是选中的时间比最后一个文件的endTime 还远一些。
        this.toSdcardEnd();
        return;
      }

      CameraPlayer.getInstance().stopPlaybackTimestampInterval();// 停止抓取

      console.log(`start play:${this.startTime}`);
      this.dateTime.setTime(this.startTime * 1000);
      console.log(`start play format time:${this.dateTime.getHours()}:${this.dateTime.getMinutes()}:${this.dateTime.getSeconds()}`);


      this.cameraRenderView && this.cameraRenderView.stopRender();// 重新开启播放之前 先暂停播放。
      this.cameraRenderView && this.cameraRenderView.startRender();// startBindVideo
      if (Platform.OS == 'ios' && !VersionUtil.judgeIsMiss(Device)) {
        Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_VIDEO_START, {})
          .then((retCode) => {
            this._startPlayback();
          }).catch((err) => {
            this._p2pCommandErrHandler(err, "startVideo");
          });
      } else {
        this._startPlayback();
      }

    } else {
      CameraPlayer.getInstance().stopPlaybackTimestampInterval();// 停止抓取
      CameraPlayer.getInstance().bindPlaybackTimestampCallback(null);
      if (!ignoreState) {
        // stop播放
        this.setState({ isMute: this.state.speed > 1 ? true : CameraConfig.getUnitMute(), isPlaying: false, showPauseView: true, showPlayToolBar: true, showLoadingView: false });
      }
      clearTimeout(this.showPlayToolBarTimer);
      // stop video
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_VIDEO_STOP, {}).then((retCode) => {
        console.log("video stop");

        console.log(retCode);
      }).catch((err) => console.log(err));

      if (!this.state.isRecording) {
        // stop audio
        Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {}).then((retCode) => {
          console.log("audio stop");
          console.log(retCode);
        }).catch((err) => console.log(err));

      }

      this.cameraRenderView && this.cameraRenderView.stopAudioPlay();
      this.cameraRenderView && this.cameraRenderView.stopRender();

      Service.miotcamera.setTimelinePlaybackMode(false);
      // stop audio local resources

    }
  }

  _p2pCommandErrHandler(err, src) {
    Service.smarthome.reportLog(Device.model, `sdcardTimelinePlayerFragment p2p err, src: ${ src }`);
    
    this.cameraRenderView && this.cameraRenderView.stopRender();// 重新开启播放之前 先暂停播放。
    if (err == -1 || err == -8888) { // - 8888重置本地连接，然后开始重连。
      CameraPlayer.getInstance().resetConnectionState();
      this.queryNetworkJob();
      return;
    }

    this.setState({ pstate: 0, showLoadingView: false, showErrorView: true, errTextString: `${ LocalizedStrings["camera_connect_error"]} ${ err } ${ LocalizedStrings["camera_connect_retry"] }` });// 已经渲染过  直接跳过去

    Service.miotcamera.setTimelinePlaybackMode(false);
  }

  _startPlayback() {
    CameraPlayer.getInstance().startPlayBack(this.startTime, this.offset, 0, this.state.speed)
      .then((res) => {
        Service.miotcamera.bindTimelinePlaybackEndListener(timelinePlaybackEndListenerName);
        //
        this.setState(() => { return { showErrorView: false, showLoadingView: true, isPlaying: true, showPauseView: true }; }, () => {
          let position = (this.state.speed == 1 ? 0 : (this.state.speed == 4 ? 1 : 2));
          this.updateSpeed(position);
        });
        this.sessionId = this.startTime;
        if (!this.state.isMute) {
          this.cameraRenderView && this.cameraRenderView.stopAudioPlay();
          Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
            // console.log("audio stop get send callback");
            console.log(retCode);
          });
          this.cameraRenderView && this.cameraRenderView.startAudioPlay();
        }
        setTimeout(() => {
          CameraPlayer.getInstance().getPlaybackTimetampInterval();
          CameraPlayer.getInstance().bindPlaybackTimestampCallback(this.timestampCallback);
        }, 1500);// 1.5s后再拉
  
        Service.miotcamera.setTimelinePlaybackMode(true);
      })
      .catch((err) => {
        this._p2pCommandErrHandler(err, "startPlayback");
      });
    
  }

  changeEventType(eventType) {
    TrackUtil.reportClickEvent("TimeSlider_Motion_ClickNum");
    let eventTypeFlag = this.state.eventTypeFlags;
    console.log("eventTypeFlag:", eventTypeFlag, "eventType:", eventType, "result:", eventTypeFlag & eventType);
    let temp = eventTypeFlag & eventType;
    if (temp !== 0) { // 已经有了这个值  取反
      eventTypeFlag = eventTypeFlag & ~eventType;
    } else {
      eventTypeFlag = eventTypeFlag | eventType;
    }
    this.setState({ eventTypeFlags: eventTypeFlag });
  }

  updateSpeed(position) {

    if (this.state.isRecording) {
      return;
    }
    let speed = 1;
    // if (specificSpeed == null) {
    switch (position) {
      case 0:
        speed = 1;
        break;
      case 1:
        speed = 4;
        break;
      case 2:
        speed = 16;
        break;
      default:
        speed = 1;
        break;
    }
    // }
    this.selectedIndexArray = [position];
    this.setState({ speed: speed });
    
    if (!this.state.isPlaying) {
      if (speed != 1) {
        this._toggleAudio(true, false);
      } else {
        this._toggleAudio(CameraConfig.getUnitMute(), false);
      }
      return;
    }
    CameraPlayer.getInstance().changeSpeed(speed)
      .then(() => {
        if (speed == 1) {
          this._toggleAudio(CameraConfig.getUnitMute(), false);
        } else if (!this.state.isMute) {
          this._toggleAudio(true, false);
        }
      })
      .catch((err) => {
        Toast.fail("action_failed", err);
      });
  }

  _onVideoClick() {
    LogUtil.logOnAll(TAG, "sdcard timeline player page onVideoClick");

    if (!CameraPlayer.getInstance().isConnected()) {
      return;
    }

    this.setState((state) => {
      return {
        showPlayToolBar: !this.state.showPlayToolBar,
        showPauseView: !this.state.showPlayToolBar
      };
    }, () => {
      this._hidePlayToolBarLater();
    });
    LogUtil.logOnAll("sdcardtimeline click video view");
  }

  // 更新倍数
  _updateScale(scale) {
    if (scale) {
      scale = Number(scale);

      if (scale < 1) {
        scale = 1;
      }

      if (this.angleViewTimeout) {
        clearTimeout(this.angleViewTimeout);
        this.angleViewTimeout = null;
      }

      this.angleViewTimeout = setTimeout(() => {
        this.setState({ showCameraAngleView: false, angleViewShowScale: false });
      }, 3000);
      console.log(`_onVideoScaleChange111${scale}`);
      this.angleView?.setScale(scale);
      this.setState({ videoScale: scale, showCameraAngleView: true, angleViewShowScale: true, showPlayToolBar: scale > 1 ? false : true, showPauseView: scale > 1 ? false : true });
    }
  }
   // 设置定时器
   videoScaleTimer = null; 

  _onVideoScaleChanged(params) {
    if (Platform.OS === "android"&&this.firstInPage) {//刚进入页面不调用
      this.firstInPage = false;
      return;
    }
    let scale = params.nativeEvent?.scale;
    // 当返回有倍数时 清除定时器 并更新倍数 相当于防抖操作 一直触发事件就一直清空定时器
    if (scale) {
      clearTimeout(this.videoScaleTimer);
      
      this.videoScaleTimer = setTimeout(() => {
        console.log("tick"+ scale);
        this._updateScale(scale); // 更新倍数
      }, 0);  
    }
    if (params && params.nativeEvent && params.nativeEvent.firstVideoFrame) {
      console.log(TAG, "received firstVideoFrame");
      this.setState({ showDefaultBgView: false, showLoadingView: false, whiteTitleBg: false });
    }
    // here can be replaced by throttol
    let endTime = new Date().getTime();
    if ((endTime - this.startScaleTime) < 50) {
      return;
    }
    this.startScaleTime = endTime;
    this._updateScale(scale);
  }

  // android返回键处理
  onBackHandler = () => {
    if (!this.isPageForeGround) {
      return false;
    }
    if (this.state.fullScreen) {
      this.toPortrait();
      this._hidePlayToolBarLater(true);
      return true;
    }
    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return true;
    }

    this._onPause();
    return false;// 不接管
  }

  queryNetworkJob() {
    if (this.isUserPause) {
      return;// 用户主动暂停的
    }
    if (!this.props.navigation.isFocused()) {
      return;
    }

    CameraPlayer.getInstance().queryShouldPauseOn4G()
      .then(({ state, pauseOnCellular }) => {
        this.networkType = state;
        if (state === "NONE" || state === "UNKNOWN") {
          this._networkChangeHandler(0);
          return;
        }
        if (state === "CELLULAR" && pauseOnCellular && !this.ignoreDataWarning) { // 普通网络 && 数据流量提醒
          if (this.state.isPlaying) {
            Toast.success("nowifi_pause", true);
          }
          this._startPlay(false);
          return;
        }
        
        // 其他网络条件 走连接的步骤吧
        this._startConnect();// 开始连接
      })
      .catch(() => { // 获取网络状态失败 也直接走开始连接的流程
        this._startConnect();// 开始连接
      });
  }

  _startConnect() {

    if (!this.props.navigation.isFocused()) { // 当前页面已经不在前台了
      this.setState({ showLoadingView: false });
      return;
    }
    if (!this.state.showLoadingView) { // 如果没有loading
      this.setState({ showLoadingView: true });
    }
    if (this.state.showPauseView) {
      this.setState({ showPauseView: false });
    }
    // 开始连接
    if (CameraPlayer.getInstance().isConnected()) {
      // 如果已经连接成功 直接发送video_start
      this.setState({ pstate: 2, error: 0 });
      this._startPlay(true);
      return;
    }
    this.setState({ pstate: 0, error: 1 });
    this.isConnecting = true;
    CameraPlayer.getInstance().startConnect();
  }


  _stopRecord() {
    if (!this.state.isRecording) {
      return;
    }

    StackNavigationInstance.isRecording = false;

    // if (this.state.resolution != 3) { // 不是高清
    //   Service.miotcamera.sendP2PCommandToDevice(
    //     MISSCommand.MISS_CMD_STREAM_CTRL_REQ, { "videoquality": this.state.resolution })
    //     .then(() => { // 不修改这些信息。
    //
    //     })
    //     .catch((err) => {
    //       console.log(err);
    //     });
    // }

    if (this.state.isMute) { // 原本静音
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {}).then((retCode) => {

      });
    }
    if (this.isStoringVideo) {
      return;
    }
    this.isStoringVideo = true;
    this.cameraRenderView && this.cameraRenderView.stopRecord().then(() => {
      console.log("stopRecord failed   success branch");
      // this.setState({ isRecording: false, recordTimeSeconds: 0 });
      if (this.videoRecordPath == null || this.videoRecordPath == "") {
        this.isStoringVideo = false;
        return;
      }
      this.mRecordOkTimer = setTimeout(() => {
        // 录制成功后 要把视频转存储到相册。
        AlbumHelper.saveToAlbum(this.videoRecordPath, true)
          .then((result) => {
            this.setState({
              videoName: result,
              isRecording: false,
              recordTimeSeconds: 0
            })
            console.log(result);
            if (this.justSnapshotResult) {
              this.isForVideoSnapshot = true;
              this.setState({ screenshotVisiblity: true, screenshotPath: AlbumHelper.getSnapshotName() });// show snapshotview
              clearTimeout(this.snapshotTimeout);
              this.snapshotTimeout = setTimeout(() => {
                this.setState({ screenshotVisiblity: false, screenshotPath: null });
              }, 5000);
            } else { // 截图失败的时候，就使用videoRecordPath
              this.isForVideoSnapshot = true;
              this.setState({ screenshotVisiblity: true, screenshotPath: this.videoRecordPath });// show snapshotview
              clearTimeout(this.snapshotTimeout);
              this.snapshotTimeout = setTimeout(() => {
                this.setState({ screenshotVisiblity: false, screenshotPath: null });
              }, 5000);
            }
            this.isStoringVideo = false;
          })
          .catch((err) => {
            this.isStoringVideo = false;
            console.log(err);
            this.setState({ isRecording: false, recordTimeSeconds: 0 });
          });
        // 500-->改为100
      }, 100);
    })
      .catch((error) => {
        console.log("stopRecord failed:" + error);
        this.isStoringVideo = false;
        this.setState({ isRecording: false, recordTimeSeconds: 0 });
        if (error == -2) {
          if (Host.isIOS && this.isPlayEnd) {
            this.isPlayEnd = false;
          } else {
            Toast.fail("record_video_failed_time_mini");
          }
        } else {
          Toast.fail("record_video_failed");
        }
      });
    this.justSnapshotResult = false;
    AlbumHelper.justSnapshot(this.cameraRenderView)
      .then((path) => {
        this.justSnapshotResult = true;
      })
      .catch((error) => {
        this.justSnapshotResult = false;
      })

  }

  _startRecord() {
    if (Platform.OS === "ios") {
      this.isPlayEnd = false;
      // no ios's photos const use hardcode
      System.permission.request("photos").then((res) => {
        this.realStartRecord();
      }).catch((error) => {
        // Toast.success("camera_no_write_permission");
        this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
      });
    } else {

      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this.realStartRecord();
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            // Toast.success("camera_no_write_permission");
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
          Toast.success("action_failed");
        });
    }
  }

  realStartRecord() {
    // 打开声音
    if (this.state.isMute) { // 不是有声音 开启声音 并且播放
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
        console.log("audio stop get send callback");
        console.log(retCode);
      });
    }
    let path = AlbumHelper.getDownloadTargetPathName(true);
    this.videoRecordPath = path;
    this.cameraRenderView && this.cameraRenderView.startRecord(`${Host.file.storageBasePath}/${path}`, kRecordTimeCallbackName)
      .then((retCode) => {
        console.log(`start record, retCode: ${retCode}`);
        this.setState({ isRecording: true, screenshotVisiblity: false });
        StackNavigationInstance.isRecording = true;
      })
      .catch((err) => {
        console.log(err);
        Toast.success("action_failed");
      });
  }

  _startAllStorageWithPermissionCheck() {
    if (Platform.OS === "android") {
      this.isCheckingPermission = true;
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          this.isCheckingPermission = false;
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this.onPressSeeAllVideo();
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            // Toast.success("camera_no_write_permission");
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
        this.isCheckingPermission = false;
        Toast.success("action_failed");
      });
    } else {
      // no ios's photos const use hardcode
      this.isCheckingPermission = true;
      System.permission.request("photos").then((res) => {
        this.isCheckingPermission = false;
        this.onPressSeeAllVideo();
      }).catch((error) => {
        this.isCheckingPermission = false;
        // Toast.success("camera_no_write_permission");
        this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
      });

    }
    TrackUtil.reportClickEvent('Camera_ScreenShot_ClickNum');
  }

  onPressSeeAllVideo() {
    TrackUtil.reportClickEvent("TimeSlider_AllVedio_ClickNum");

    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return;
    }
    let isOutsea = !CameraConfig.isSupportCloud(); // 不支持云存 只有两个页面 跳index 1   支持云存  有三个页面 跳index 2
    if (this.state.showPoweroffView) {
      Toast.success("camera_power_off");
      return;
    }
    this.props.navigation.navigate("AllStorage", { initPageIndex: CameraConfig.isDeviceSupportCloud() ? 2 : 1, isSupportCloud: CameraConfig.isDeviceSupportCloud() });
  }

  _onScrolling = (timestamp) => {
    console.log("滑动中");
    // console.log(timestamp);
    this.dateTime.setTime(timestamp);
    console.log(`${this.dateTime.getHours()} ${this.dateTime.getMinutes()} ${this.dateTime.getSeconds()}`);
    if (this.timeIndicatorView == null) {
      return;
    }
    this.timeIndicatorView.setState({ centerTimestamp: timestamp });
  }

  _bindFilesHandler = (status) => { // 收到文件列表的回调
    this.onGetFiles();
  }

  onGetFiles() {
    let timeItems = SdFileManager.getInstance().getTimeItems();
    if (timeItems == null || timeItems.length <= 0) {
      return;
    }

    this.setState({ isEmpty: false });
    // 这里把数据丢给timelineview
    this.timelineView && this.timelineView.initData(timeItems);
    let lastTimeItem = timeItems[timeItems.length - 1];
    this.lastTimeItemEndTime = lastTimeItem.endTime;
    LogUtil.logOnAll("SdcardTimeline", "刷新sdcard列表：最后一个文件的信息： " + JSON.stringify(lastTimeItem) + " lastest endTime:" + this.lastTimeItemEndTime);//收到的视频帧信息

    this.lastTimeItemStartTime = lastTimeItem.startTime;
    let penultimateItem = timeItems.length > 1 ? timeItems[timeItems.length - 2] : null;
    if (!this.isFirstReceiveFiles) {
      return;
    }
    
    this.isFirstReceiveFiles = false;
    this.firstPullVideoFrameTime = Date.now();
    this.scrollTimeout1 = setTimeout(() => {

      if (penultimateItem) {
        this.penultimateStartTime = penultimateItem.startTime;
      } else {
        this.penultimateStartTime = 0;
      }
      this.timelineView && this.timelineView.scrollToTimestamp(this.lastTimeItemStartTime);// 不通知
      // 这里开始播放了.
      this.toStartTime = this.lastTimeItemStartTime;
      this.queryNetworkJob();
    });

  }

  toSdcardEnd() {
    // todo jump to sdcard file end
    // todo  pauseCamera
    console.log('停止播放了');
    this.toStartTime = this.lastTimeItemEndTime + 1000;// 播放到文件末尾后，强制+ 1000, 下次找的时候 就会找到文件列表的katou 开头
    this.timelineView && this.timelineView.scrollToTimestamp(this.lastTimeItemEndTime);
    this._startPlay(false);
    if (Platform.OS === "ios") { 
      this.isPlayEnd = true;
    }
    this._stopRecord();// 播放到文件末尾后，主动停止录制。
  }


  showLastImage() {
    this.props.navigation.navigate("AlbumPhotoViewPage", { preOri: this.state.fullScreen ? "landscape" : "portrait" });
  }

  showLastVideo() {
    this.props.navigation.navigate("AlbumVideoViewPage", { preOri: this.state.fullScreen ? "landscape" : "portrait" });
  }

  _renderResolutionDialog() {
    return (
      <ChoiceDialog
        modalStyle={{ marginLeft: this.state.fullScreen ? 100 : 0, width: this.state.fullScreen ? (this._getWindowPortraitHeight() - 100 * 2) : "100%" }}

        visible={this.state.dialogVisibility}
        title={LocalizedStrings["ptzc_play_back_speed_selection_title"]}
        options={this.isCameraV1orV3 ? [
          { "title": "x1", accessibilityLabel: DescriptionConstants.rp_13 },
          { "title": "x2", accessibilityLabel: DescriptionConstants.rp_13.replace('1', 2) },
          { "title": "x3", accessibilityLabel: DescriptionConstants.rp_13.replace('1', 3) }
        ] : [
          { "title": "x1", accessibilityLabel: DescriptionConstants.rp_13 },
          { "title": "x4", accessibilityLabel: DescriptionConstants.rp_13.replace('1', 4) },
          { "title": CameraConfig.support8xSpeed(Device.model) ? "x8" : "x16", accessibilityLabel: CameraConfig.support8xSpeed(Device.model) ? DescriptionConstants.rp_13.replace('1', 8) : DescriptionConstants.rp_13.replace('1', 16) }
        ]}
        selectedIndexArray={this.selectedIndexArray}
        onDismiss={(_) => this.setState({ dialogVisibility: false })}
        onSelect={(result) => {
          this.selectedIndexArray = result;
          this.updateSpeed(result[0]);
          this.setState({ dialogVisibility: false });
        }}
      />
    );
  }

  _getWindowPortraitHeight() {
    let winWidth = Dimensions.get('window').width;
    let winHeight = Dimensions.get('window').height;
    let width = Math.min(winWidth, winHeight);
    let height = Math.max(winWidth, winHeight);
    return height;
  }

}


const styles = StyleSheet.create({

  container: {
    backgroundColor: "#ffffff",
    width: '100%',
    height: '100%',
    display: "flex",
    flexWrap: "nowrap"
  },

  main: {
    display: 'flex',
    flexDirection: 'column',
    flexWrap: 'nowrap',
    backgroundColor: 'white',
    height: "100%"
  },

  videoContainerNormal: {
    backgroundColor: 'black',
    width: kWindowWidth,
    height: portraitVideoHeight,
    position: "relative"// absolute以第一个非static的父类变量作为定位标准
  },

  videoContainerFull: {
    backgroundColor: 'black',
    width: "100%",
    height: "100%",
    position: "relative"// absolute以第一个非static的父类变量作为定位标准
  },

  videoView: {
    position: "absolute",
    width: "100%",
    height: "100%"
  },

  videoControl: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    height: 80
  },

  videoControlBar: {// 横向
    display: "flex",
    flexWrap: "nowrap",
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-around"
    // backgroundColor: '#FFF1'
  },
  videoControlBarFull: {
    // backgroundColor: '#FFF1',
    display: "flex",
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end"

  },
  videoControlBarItem: {// 内容居中排列
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    flexGrow: 1

  },

  videoControlBarItemImg: {
    width: 50,
    height: 50
  },

  landscapeCallViewLayout: {
    width: "100%",
    paddingBottom: 10,
    paddingTop: 20,
    position: "absolute",
    bottom: 0
  },
  landscapeCallViewLayoutImg: {
    display: "flex",
    margin: "auto",
    width: "100%",
    flexDirection: "row",
    justifyContent: "center"
    // textAlign:"center"
  },

  callViewLayout: {
    flexGrow: 1,
    width: "100%",
    flexDirection: "column",
    display: "flex",
    alignItems: "center",
    justifyContent: "center"
  },

  bottomLayout: {
    display: "flex",
    width: "100%",
    height: 60,
    flexDirection: "row",
    flexWrap: 'nowrap'
  },

  bottomLayoutItem: {
    flexGrow: 1,
    height: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center"
  },

  whiteText: {
    fontSize: kIsCN ? 10 : 8,
    textAlign: "center",
    padding: 4,
    color: "#ffffff",
    borderColor: "#FFFFFFCC",
    borderRadius: 3,
    borderWidth: 1
  },
  snapShot: {
    position: "absolute",
    bottom: 40,
    left: 5,
    margin: 6,
    width: 100,
    height: 66,
    borderRadius: 5
  },
  snapShotFull: {
    position: "absolute",
    bottom: 84,
    left: 35,
    margin: 6,
    width: 100,
    height: 66,
    borderRadius: 5
  }
});
