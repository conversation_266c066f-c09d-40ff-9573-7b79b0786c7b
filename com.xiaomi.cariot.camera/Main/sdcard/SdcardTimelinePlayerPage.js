'use strict';

import React from 'react';
import { isIphoneX } from 'react-native-iphone-x-helper';
import { Safe<PERSON>reaView, StatusBar, BackHandler, View, Text, Image, StyleSheet, TouchableOpacity, TouchableWithoutFeedback, PermissionsAndroid, Platform, DeviceEventEmitter, Dimensions } from 'react-native';

import LinearGradient from 'react-native-linear-gradient';
import { Device, Service, PackageEvent, Host, System } from 'miot';
import AlbumHelper from "../util/AlbumHelper";

import ImageButton from "miot/ui/ImageButton";


import Toast from '../components/Toast';
import { DescriptionConstants } from '../Constants';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';

import SdFileManager from './util/SdFileManager';

import CameraRenderView from 'miot/ui/CameraRenderView';
import { MISSSampleRate, MISSDataBits, MISSAudioChannel } from 'miot/ui/CameraRenderView';
import CameraPlayer from '../util/CameraPlayer';
import { MISSCommand, MISSError } from "miot/service/miotcamera";
import Orientation from 'react-native-orientation';

import StorageKeys from '../StorageKeys';
import ScaleableTimelineView from '../ui/ScaleableTimelineView';
import VersionUtil from '../util/VersionUtil';
import CameraConfig from '../util/CameraConfig';
import TimeScaleView from '../ui/TimeScaleView';
import CenterTimeView from '../ui/CenterTImeView';
import TrackUtil from '../util/TrackUtil';
import LoadingView from '../ui/LoadingView';
import { MessageDialog } from "mhui-rn";
import CommonMsgDialog from '../ui/CommonMsgDialog';
import RPC from '../util/RPC';
import SpecUtil from '../util/SpecUtil';
import { ChoiceDialog } from 'mhui-rn';
import RectAngleView from '../ui/RectAngleView';


const timelinePlaybackEndListenerName = "onTimelinePlaybackEnd";
const kRecordTimeCallbackName = "recordTimeCallback";
const TAG = "SdcardTimelinePlayerPage";

const kWindowHeight = Math.max(Dimensions.get("screen").height, Dimensions.get("screen").width);

export default class SdcardTimelinePlayerPage extends React.Component {

  static navigationOptions = ({ navigation }) => {
    // const { titleProps } = navigation.state.params || {};
    // if (!titleProps) 
    return {
      headerTransparent: true,
      header: null
    };

  };

  state = {
    isEmpty: true,
    sdcardFiles: [],

    showPlayToolBar: false,

    fullScreen: false,
    isMute: CameraConfig.getUnitMute(),
    isSleep: false,
    resolution: 0,
    speed: 1, // 倍速
    isPlaying: false,

    screenshotVisiblity: false, // 截图是否可见

    showErrorView: false,
    showLoadingView: false,
    showPoweroffView: false,
    showPauseView: false,
    errTextString: "", // 错误提示文案

    screenshotPath: null,
    recordTimeSeconds: 0,

    permissionRequestState: 0,
    showPermissionDialog: false,
    dialogVisibility: false
  };


  constructor(props) {
    super(props);

    this.timeIndicatorView = null;
    this.dateTime = new Date();
    this.isFirstReceiveFiles = true;

    this.isUserPause = false;

    this.isPluginForeGround = true;// sdcard页面不存在跳到其他页面的情况
    this.isPageForeGround = true;
    this.isAppForeground = true;

    // see https://blog.csdn.net/u011068702/article/details/83218639
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        console.log("will focus");

        this.isPageForeGround = true;
        this.isPluginForeGround = true;
        this.isAppForeground = true;
        this.restoreOri();
        this._onResume();
      }
    );
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'didBlur',
      () => {
        console.log("did blur");

        this.isPageForeGround = false;
        this._onPause();
        if (this.cameraRenderView != null) {
          this.cameraRenderView.hidesSurfaceView();
        }
      }
    );


    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.isAppForeground = true;// rnactivity调用了onresume
      this.restoreOri();
      this._onResume();
    });

    this.willPauseListener = PackageEvent.packageWillPause.addListener(() => {
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.isAppForeground = false;// rnactivity调用了onpause
      this._onPause();
    });

    if (Platform.OS == "ios") {
      this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.isPluginForeGround = true;// rnactivity调用了onresume
        this._onResume();
      });

      this.willDisappearListener = PackageEvent.packageViewWillDisappearIOS.addListener(() => {
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.toPortrait();// ios强制切换到竖屏去
        setTimeout(() => {
          this.isPluginForeGround = false;// rnactivity调用了onpause
          // console.log('testaaa', 'willDisappearListener', 'page: ', this.isPageForeGround, ' plugin: ', this.isPluginForeGround);
          this._onPause();
          
        }, 500);
      });
    } else {
      if (PackageEvent.packageWillStopAndroid) {
        this.willStopListener = PackageEvent.packageWillStopAndroid.addListener(() => {
          if (this.cameraRenderView != null) {
            // this.cameraRenderView.hidesSurfaceView();
          }
        });
      }

    }

    this.isDataUsageWarning = true;

    this.currentNetworkState = -1;
    this.isChangingNetwork = false;

    this.toStartTime = 0;// 暂存要播放的view的时间戳

    this.startTime = 0;
    this.endTime = 0;
    this.offset = 0;
    this.sessionId = 0;

    this.lastTimeItemEndTime = 0;// 从rdt捞过来的数据 的最后一个item的endtime
    this.penultimateStartTime = 0;

    this.isSetPlayTime = false;// 是否正在设置播放时间
    this.setPlayTimeMillis = 0;// 记录本次设置playback命令的时间
    this.lastTimestamp = 0;// 记录上次返回回调的时间戳
    // todo notify native side  whether in continue playback mode

    this.timelinePlaybackEndListener = DeviceEventEmitter.addListener(timelinePlaybackEndListenerName, () => {
      // 走到这里说明连续回放结束了
      console.log("时间轴模式下，从点播切换成了直播 要暂停camera");
      this.toSdcardEnd();// 停止播放
    });

    this.timelineView = null;
    this.cameraRenderView = null;
    this.scrollTimeout = null;
    this.scrollTimeout1 = null;
    this.isConnecting = false;
    this.mOri = "PORTRAIT";
    this.connRetry = 2;

    this.lastLoadDataTime = 0;
    this.isPlayingFinalItem = true;
    this.islastestFilePlayFailed = false;
    this.destroyed = false;
    this.selectedIndexArray = [0];
  }

  _onResume() {
    if (!this.isPageForeGround || !this.isAppForeground || !this.isPluginForeGround) { // 从后台回到前台，也会触发这里的行为。  需要保证插件在前台时才调用。
      return;
    }

    CameraPlayer.getInstance().bindConnectionCallback(this._connectionHandler);
    CameraPlayer.getInstance().bindP2pCommandCallback(this._ServerRespHandler);
    CameraPlayer.getInstance().bindNetworkInfoCallback(this._networkChangeHandler);
    CameraPlayer.getInstance().bindPowerOffCallback(this._powerOffHandler);

    self.enterPlaybackTime = new Date().getTime();
    // // 如果已经播放完成了 或者是播放error  或者是用户手动暂停的
    // if (this.state.showErrorView) {
    //   return;
    // }
    if (this.state.showPoweroffView) {
      return;
    }
    if (this.isUserPause) {
      return;
    }
    // 重新进来 要绑定一遍这些事件。

    this.onGetFiles();// 从其他页面回来 要刷新一遍数据，避免出现其他页面删了  这个页面还没有同步数据的情况。

    Service.miotcamera.bindTimelinePlaybackEndListener(timelinePlaybackEndListenerName);

    SdFileManager.getInstance().bindReceiveFilesListener(this._bindFilesHandler);

    this.queryNetworkJob();
  }

  _onPause() {
    this.cameraRenderView && !this.destroyed && this.cameraRenderView.stopRender();
    CameraPlayer.getInstance().bindConnectionCallback(null);
    CameraPlayer.getInstance().bindP2pCommandCallback(null);
    CameraPlayer.getInstance().bindPowerOffCallback(null);
    CameraPlayer.getInstance().bindNetworkInfoCallback(null);

    this._stopRecord();
    SdFileManager.getInstance().bindReceiveFilesListener(null);

    let playbackTime = (new Date().getTime() - self.enterPlaybackTime) / 1000;
    TrackUtil.reportResultEvent("Camera_Playback_Time", "Time", playbackTime); // Camera_Playback_Time
    if (this.state.showErrorView) {
      return;
    }
    if (this.state.showPoweroffView) {
      return;
    }

    this._startPlay(false);
  }

  restoreOri() {
    console.log(TAG, "restoreOri");
    if ("PORTRAIT" === this.mOri) {
      this.toPortrait();
    } else {
      this.toLandscape();
    }
  }


  toPortrait() {
    StatusBar.setHidden(false);
    console.log(TAG, "toPortrait");
    this.mOri = "PORTRAIT";
    CameraConfig.lockToPortrait();
  }

  toLandscape() {
    StatusBar.setHidden(true);
    console.log(TAG, "toLandscape");
    this.mOri = "LANDSCAPE";
    if (Platform.OS === "android") {
      Orientation.lockToLandscape();
    } else {
      Orientation.lockToLandscapeRight();
    }
  }

  componentWillUnmount() {
    // try {
    //   this._startPlay(false);
    // } catch (err) {

    // }
    this.destroyed = true;
    this.toPortrait();
    clearTimeout(this.scrollTimeout);
    clearTimeout(this.scrollTimeout1);

    CameraPlayer.getInstance().bindPlaybackTimestampCallback(null);
    CameraPlayer.getInstance().stopPlaybackTimestampInterval();// 防止崩溃

    Service.miotcamera.bindTimelinePlaybackEndListener(null);

    SdFileManager.getInstance().bindReceiveFilesListener(null);

    // this._onPause();
    Service.miotcamera.setTimelinePlaybackMode(false);

    this.didResumeListener.remove();
    this.willPauseListener.remove();
    if (this.willAppearListener) {
      this.willAppearListener.remove();
    }
    if (this.willDisappearListener) {
      this.willDisappearListener.remove();
    }
    this.willStopListener && this.willStopListener.remove();
    this.timelinePlaybackEndListener.remove();
    this.didFocusListener.remove();
    this.didBlurListener.remove();
    this.recordListener.remove();
    Orientation.removeOrientationListener(this._orientationListener);
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
    SdFileManager.getInstance().destroyInstance();// 走到这个页面的 都是这么弄的。
    Dimensions.removeEventListener('change', this.dimensionListener);
  }


  render() {
    return (

      <View style={styles.container}>
        <SafeAreaView style={{ backgroundColor: "black" }}></SafeAreaView>
        {this._renderVideoLayout()}
        {this._renderTimeLineView()}
        {this._renderBottomSelectView()}
        {this._renderPermissionDialog()}
        {this._renderResolutionDialog()}
        <SafeAreaView></SafeAreaView>
      </View>
    );
  }

  _renderResolutionDialog() {
    return (
      <ChoiceDialog
        modalStyle={{ marginLeft: this.state.fullScreen ? 100 : 0, width: this.state.fullScreen ? (this._getWindowPortraitHeight() - 100 * 2) : "100%" }}

        visible={this.state.dialogVisibility}
        title={LocalizedStrings["ptzc_play_back_speed_selection_title"]}
        options={[
          { "title": "x1", accessibilityLabel: DescriptionConstants.rp_13 },
          { "title": "x4", accessibilityLabel: DescriptionConstants.rp_13.replace('1', 4) },
          { "title": "x16", accessibilityLabel: DescriptionConstants.rp_13.replace('1', 16) }
        ]}
        selectedIndexArray={this.selectedIndexArray}
        onDismiss={(_) => this.setState({ dialogVisibility: false })}
        onSelect={(result) => {
          this.selectedIndexArray = result;
          this.updateSpeed(result[0]);
          this.setState({ dialogVisibility: false });
        }}
      />
    );
  }

  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 麦克风
    // 
    let message = "";
    if (this.state.permissionRequestState == 0) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_microphone"]);
    }
    return (

      <MessageDialog
        title={LocalizedStrings["tips"]}
        message={message}
        messageStyle={{
          fontSize: 14,
      }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );

  }

  _renderVideoLayout() {
    return (
      <View style={this.state.fullScreen ? styles.videoContainerFull : styles.videoContainerNormal}>
        {this._renderVideoView()}

        {this._renderPowerOffView()}
        {this._renderPauseView()}
        {this._renderErrorRetryView()}
        {this._renderLoadingView()}


        {this._renderTitleView()}
        {this._renderVideoControlView()}
        {this._renderSnapshotView()}
        {this._renderAngleView()}

        {this._renderRecordTimeView()}
        {this._renderTimeIndicatorView()}

      </View>
    );
  }

  _renderTimeIndicatorView() {
    return (
      <View
        style={{ width: "100%", height: "100%", position: "absolute", display: "flex", justifyContent: "center", alignItems: "center" }}
        pointerEvents={"none"}
      >
        <CenterTimeView
          ref={(ref) => {
            this.timeIndicatorView = ref;
          }}
        >

        </CenterTimeView>
      </View>
    );
  }

  _renderRecordTimeView() {
    if (!this.state.isRecording) {
      return null;
    }
    let containerHeight = 80;

    if (this.state.fullScreen) {
      containerHeight = 60;
    }
    let seconds = this.state.recordTimeSeconds;
    let second = Number.parseInt(seconds % 60);
    let minute = Number.parseInt(seconds / 60 % 60);
    let hour = Number.parseInt(seconds / 60 / 60 % 24);

    return (
      <View
        style={{ position: "absolute", top: containerHeight, display: "flex", alignItems: "center", justifyContent: "center", width: "100%" }}
      >
        <Text
          style={{ fontSize: 12, color: "red" }}
        >
          {`${hour > 9 ? hour : `0${hour}`}:${minute > 9 ? minute : `0${minute}`}:${second > 9 ? second : `0${second}`}`}
        </Text>

      </View>
    );
  }


  _renderPauseView() {
    if (!this.state.showPauseView || this.state.showPoweroffView) {
      return null;
    }
    return (
      <View styles={{ position: "absolute", width: "100%", height: "100%" }}>
        <View style={{ width: "100%", height: "100%", display: "flex", alignItems: "center", justifyContent: "center" }}

        >
          <ImageButton
            style={{ width: 64, height: 64 }}
            source={require("../../Resources/Images/home_icon_pause_normal.png")}
            onPress={() => {
              // StorageKeys.IS_DATA_USAGEE_WARNING = false //wifi下
              this.isDataUsageWarning = false;
              this.isUserPause = false;
              this._onResume();
            }}
          />
        </View>
      </View>
    );
  }

  _renderVideoView() {
    return (
      <CameraRenderView
        ref={(ref) => { this.cameraRenderView = ref; }}
        maximumZoomScale={6.0}
        style={styles.videoView}
        videoCodec={CameraConfig.getCameraCoderParam(Device.model, Device.lastVersion).videoCodec}
        audioCodec={CameraConfig.getCameraCoderParam(Device.model, Device.lastVersion).audioCodec}
        audioRecordSampleRate={CameraConfig.getCameraAudioSampleRate(Device.model)}
        audioRecordChannel={MISSAudioChannel.FLAG_AUDIO_CHANNEL_MONO}
        audioRecordDataBits={MISSDataBits.FLAG_AUDIO_DATABITS_16}
        fullscreenState={this.state.fullScreen}
        videoRate={15}
        correctRadius={CameraConfig.getCameraCorrentParam(Device.model).correctRadius}
        osdx={CameraConfig.getCameraCorrentParam(Device.model).osdx}
        osdy={CameraConfig.getCameraCorrentParam(Device.model).osdy}
        onVideoClick={this._onVideoClick.bind(this)}
        did={Device.deviceID}
        isFull={false}
        recordingVideoParam={CameraConfig.getRecordingVideoParam(Device.model)}
        onScaleChanged={this._onVideoScaleChanged.bind(this)}
      >
      </CameraRenderView>
    );
  }

  _renderPowerOffView() {
    // todo render poweroffview  full 
    if (!this.state.showPoweroffView) {
      return null;
    }
    return (
      <TouchableWithoutFeedback
        style={{ position: "absolute", width: "100%", height: "100%" }}
        onPress={() => { }}
      >
        <View style={{ backgroundColor: "black", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
        >
          <Image
            style={{ width: 54, height: 54 }}
            source={require("../../Resources/Images/icon_camera_sleep.png")} />
          <Text
            style={{ marginTop: 10, fontSize: 14, color: "#bfbfbf" }}>
            {LocalizedStrings["camera_power_off"]}
          </Text>
        </View>
      </TouchableWithoutFeedback>
    );
  }

  _renderErrorRetryView() {
    if (!this.state.showErrorView) {
      return null;
    }
    return (
      <View
        style={{ position: "absolute", backgroundColor: "black", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}>
        <TouchableOpacity
          style={{ display: "flex", alignItems: "center" }}
          onPress={() => {
            this.isUserPause = false;
            this.queryNetworkJob();
          }}// 走重新播放的逻辑,如果是断线了  会走重连的逻辑的}
        >
          <Image
            style={{ width: 54, height: 54 }}
            source={require("../../Resources/Images/icon_camera_fail.png")} />
          <Text
            style={{ marginTop: 10, fontSize: 14, color: "#bfbfbf" }}>
            {this.state.errTextString}
          </Text>
        </TouchableOpacity>
      </View>

    );
    // todo render errorRetryView not 
  }


  _renderLoadingView() {
    // todo render loading view 
    if (!this.state.showLoadingView) {
      return null;
    }
    return (
      <View

        style={{ position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <LoadingView
          style={{ width: 54, height: 54 }}
          color={"#ffffff"}
          size={"large"}

        />
        <Text
          style={{ marginTop: 10, fontSize: 12, color: "#ffffff" }}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );
  }

  _renderTitleView() {
    if (this.state.fullScreen) {
      return null;
    }

    // first change statusBar
    StatusBar.setBarStyle('light-content');
    if (Platform.OS == 'android') {
      // StatusBar.setTranslucent(true); // 测试过的机型几乎都无效：华为荣耀V9，红米Note4X，小米Mix2
    }

    // second get statusBar height;
    let containerHeight = StatusBar.currentHeight || 0;
    containerHeight += 65;
    const containerStyle = {
      position: "absolute",
      top: 0,
      backgroundColor: "#00000000",
      height: containerHeight,
      width: "100%",
      display: "flex",
      flexDirection: "row",
      alignItems: "center", // 垂直居中
      paddingTop: StatusBar.currentHeight,
      paddingLeft: 9,
      paddingRight: 9
    };

    const textContainerStyle = {
      flexGrow: 1,
      alignSelf: 'stretch', // 控制自己填充满父类的高度
      display: "flex",
      flexDirection: "column",
      justifyContent: 'center',
      alignItems: 'stretch', // 控制子类填充满本身的宽度
      marginHorizontal: 5
    };

    const titleTextStyle = {
      fontSize: 16,
      textAlignVertical: 'center',
      textAlign: 'center'
    };

    const darkTitleColor = '#ffffff'; // 深色背景下标题颜色
    const titleColor = { color: darkTitleColor };
    let iconSize = 40;


    const iconBack = require("../../Resources/Images/icon_back_black_nor_dark.png");

    const leftIcon = iconBack;

    const leftIconData = {
      source: leftIcon,
      onPress: this.onBackPress
    };

    return (

      <LinearGradient
        colors={['#00000099', '#00000000']}
        style={containerStyle}>
        <View
          style={{ width: iconSize, height: iconSize, position: "relative" }}>

          <ImageButton
            style={{ width: iconSize, height: iconSize, position: "absolute" }}
            source={leftIconData.source}
            onPress={leftIconData.onPress}
          />
        </View>

        <View style={textContainerStyle}>
          <Text
            numberOfLines={1}
            style={[titleTextStyle, titleColor]}
          >
            {LocalizedStrings["camera_playback"]}
          </Text>

        </View>

        <View
          style={{ width: iconSize, height: iconSize, position: "relative" }}>
          {/* 
          <ImageButton
            style={{ width: iconSize, height: iconSize, position: "absolute" }}
            source={rightIconData.source}
            onPress={rightIconData.onPress}
          /> */}
        </View>

      </LinearGradient>
    );
  }


  onBackPress = () => {
    this.props.navigation.goBack();
    this._onPause();
  }

  _renderVideoControlView() {

    const playIcons = [
      {
        source: require('../../Resources/Images/icon_camera_pause.png'),
        highlightedSource: null,
        onPress: () => {
          this.isUserPause = true;
          this._startPlay(false);
        }
      },
      {
        source: require('../../Resources/Images/icon_camera_play.png'),
        highlightedSource: null,
        onPress: () => {
          if (this.state.isRecording) {
            return;
          }
          this.isUserPause = false;
          this.queryNetworkJob();
        }
        // 开始播放
      }
    ];
    const audioIcons = [
      {
        source: require('../../Resources/Images/icon_camera_unmute_playback.png'),
        highlightedSource: require('../../Resources/Images/icon_camera_unmute_playback.png'),
        onPress: () => {
          if (this.state.isRecording) {
            Toast.success("camera_recording_block");
            return;
          }
          this._toggleAudio(true);
        }
      },
      {
        source: require('../../Resources/Images/icon_camera_mute_playback.png'),
        highlightedSource: require("../../Resources/Images/icon_camera_mute_playback.png"),
        onPress: () => {
          if (this.state.isRecording) {
            Toast.success("camera_recording_block");
            return;
          }
          this._toggleAudio(false);// 默认是这个状态
        }
      }
    ];
    const snapShotIcon = [{
      source: require('../../Resources/Images/icon_camera_screenshot_playback.png'),
      highlightedSource: require('../../Resources/Images/icon_camera_screenshot_playback.png'),
      onPress: () => this._startSnapshot()
    }];

    const recordIcon = [
      {
        source: require("../../Resources/Images/icon_camera_record_small.png"),
        onPress: () => this._startRecord()
      },
      {
        source: require("../../Resources/Images/icon_camera_recording_small.png"),
        onPress: () => this._stopRecord()
      }
    ];


    const fullScreenIcons = [
      {
        source: require('../../Resources/Images/icon_camera_fullscreen_playback.png'),
        highlightedSource: require('../../Resources/Images/icon_camera_fullscreen_playback.png'),
        onPress: () => { this.toLandscape(); }
      },
      {
        source: require('../../Resources/Images/icon_camera_fullscreen_exit_playback.png'),
        highlightedSource: require('../../Resources/Images/icon_camera_fullscreen_exit_playback.png'),
        onPress: () => { this.toPortrait(); }
      }
    ];
    let playIndex = this.state.isPlaying ? 0 : 1;
    let audioIndex = this.state.isMute ? 1 : 0;
    let recordIndex = this.state.isRecording ? 1 : 0;
    let resolutionText = "";
    switch (this.state.speed) {
      case 1:
        resolutionText = "1x";
        break;
      case 4:
        resolutionText = "4x";
        break;
      case 16:
        resolutionText = "16x";
        break;
      default:
        resolutionText = "1x";
        this.setState({ speed: 1 });
        break;
    }

    let gradientColors = ['#00000000', '#00000077'];
    if (this.state.fullScreen) {
      gradientColors = ["#00000077", "#00000000"];
    }
    if (this.state.showPlayToolBar) {
      return (
        <View style={[{ height: 70, width: "100%", position: "absolute" }, this.state.fullScreen ? { top: 0 } : { bottom: 0 }]}>
          <LinearGradient pointerEvents={"box-none"} colors={gradientColors} style={[{ position: "absolute", width: "100%", height: "100%" }]} />
          <View style={this.state.fullScreen ? styles.videoControlBarFull : styles.videoControlBar}>

            <View style={styles.videoControlBarItem}>

              <ImageButton
                onPress={playIcons[playIndex].onPress}
                style={styles.videoControlBarItemImg}
                source={playIcons[playIndex].source}
                highlightedSource={playIcons[playIndex].highlightedSource}
              />
            </View>

            <View style={styles.videoControlBarItem}>

              <ImageButton
                onPress={audioIcons[audioIndex].onPress}
                style={styles.videoControlBarItemImg}
                source={audioIcons[audioIndex].source}
                highlightedSource={audioIcons[audioIndex].highlightedSource}
              />
            </View>

            <View style={styles.videoControlBarItem}>
              <ImageButton
                onPress={snapShotIcon[0].onPress}
                style={styles.videoControlBarItemImg}
                source={snapShotIcon[0].source}
                highlightedSource={snapShotIcon[0].highlightedSource}
              />
            </View>

            <View style={styles.videoControlBarItem}>
              <ImageButton
                onPress={recordIcon[recordIndex].onPress}
                style={styles.videoControlBarItemImg}
                source={recordIcon[recordIndex].source}
                highlightedSource={recordIcon[recordIndex].highlightedSource}
              />
            </View>

            <View style={[styles.videoControlBarItem, { width: 40, height: 40 }]}>
              <Text
                style={{
                  fontSize: 10, textAlign: "center", paddingVertical: 2, paddingHorizontal: 4, color: "xm#FFFFFFCC", borderColor: "#FFFFFF", borderRadius: 3, borderWidth: 1, textAlignVertical: 'center'
                }}
                ellipsizeMode="tail"
                numberOfLines={2}
                onPress={() => {
                  if (this.state.isRecording) {
                    Toast.success("camera_recording_block");
                    return;
                  }
                  this.setState({ dialogVisibility: true });

                }}
              >
                {resolutionText}
              </Text>
            </View>

            <View style={styles.videoControlBarItem}>

              <ImageButton
                onPress={fullScreenIcons[this.state.fullScreen ? 1 : 0].onPress}
                style={styles.videoControlBarItemImg}
                source={fullScreenIcons[this.state.fullScreen ? 1 : 0].source}
                highlightedSource={fullScreenIcons[this.state.fullScreen ? 1 : 0].highlightedSource}
              />
            </View>
          </View>
        </View>
        


      );

    } else {
      return (null);
    }
  }

  _renderSnapshotView() {
    if (!this.state.screenshotVisiblity) {
      return null;
    }
    return (
      <ImageButton
        style={this.state.fullScreen ? styles.snapShotFull : styles.snapShot}
        source={(this.state.screenshotPath == "") ? null : ({ uri: `file://${Host.file.storageBasePath}/${this.state.screenshotPath}` })}
        fadeDuration={0}
        onPress={() => {
          if (!this.canStepOut()) {
            return;
          }
          clearTimeout(this.snapshotTimeout);
          this.setState({ screenshotVisiblity: false, screenshotPath: "" });// 点击后就消失。
          setTimeout(() => {
            if (this.isForVideoSnapshot) {
              console.log("点击了缩略图，跳转到视频页面333");
              setTimeout(() => {
                this.props.navigation.navigate("AlbumVideoViewPage", { preOri: this.state.fullScreen ? "landscape" : "portrait" });
              }, 100)


            } else {
              console.log("点击了缩略图，跳转到图片页面");
              this.props.navigation.navigate("AlbumPhotoViewPage", { preOri: this.state.fullScreen ? "landscape" : "portrait" });
            }
            this.isForVideoSnapshot = false;
          }, 500);
          // todo jump to album activity
        }}
      />
    );
  }

  canStepOut() {
    if (this.state.isRecording) {
      !this.destroyed && Toast.success("camera_recording_block");
      return false;
    }
    if (this.state.isCalling) {
      !this.destroyed && Toast.success("camera_speaking_block");
      return false;
    }
    if (this.state.isSleep) {
      !this.destroyed && Toast.success("camera_power_off");
      return false;
    }
    return true;
  }

  _renderTimeLineView() {
    // 如果这里设置为null，等切屏回来后，就生成了一个新的view。原先绑定的数据就没了。
    // if (this.state.fullScreen) {
    //   return null;
    // }
    return (// scaletimelineView自己处理宽高。
      <View style={[(this.state.isEmpty || (this.state.fullScreen && !this.state.showPlayToolBar)) ? { display: "none" } : (this.state.fullScreen ? { position: "absolute", bottom: 0, width: "100%", height: 84 } : { width: "100%", height: 84 })]}>
        <TimeScaleView
          ref={(ref) => { this.timelineView = ref; }}
          onCenterValueChanged={this._onCenterValueChanged}
          onScrolling={this._onScrolling}
          onScrollEnd={this._onCenterValueChanged}
          isDisabled={this.state.isRecording ? true : false}
        />

      </View>

    );
  }

  _renderBottomSelectView() {
    if (this.state.fullScreen) {
      return;
    }

    return (
      <View style={[this.state.isEmpty ? { display: "none" } : null, { width: "100%", flexGrow: 1, position: "relative" }]}>
        <TouchableOpacity
          style={{ position: "absolute", bottom: 0, width: "100%", height: 90, padding: 24 }}
          onPress={() => { this.onPressSeeAllVideo(); }}
        >
          <View
            style={{ width: "100%", height: "100%", backgroundColor: this.state.isRecording ? "#32bac040" : "#25a9af", borderRadius: 5, display: "flex", alignItems: "center", justifyContent: "center" }}
          >
            <Text
              style={{ color: "#ffffff", fontSize: 13 }}
            >
              {LocalizedStrings["all_playback_video"] + " "}
            </Text>
          </View>

        </TouchableOpacity>

      </View>
    );
  }

  _orientationListener = (orientation) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    if (!this.isPageForeGround || !this.isPluginForeGround) {
      return;
    }
    console.log(TAG, `device orientation changed :${orientation} want ${this.mOri}`);
    if ((Platform.OS == "ios" || (Host.isAndroid && Host.isPad)) && ((this.mOri === 'LANDSCAPE' && this.state.fullScreen) || (this.mOri === 'PORTRAIT' && !this.state.fullScreen))) {
      return;
    }
    if (this.mOri === orientation) {
      if (orientation === 'LANDSCAPE') {
        // do something with landscape layout
        this.setState({ fullScreen: true });
      } else {
        // do something with portrait layout
        this.setState({ fullScreen: false });
      }
    } else {
      // ios need restore for next lock
      this.restoreOri();
    }
  };

  componentDidMount() {
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }

    this.restoreOri();
    Orientation.addOrientationListener(this._orientationListener);

    Service.miotcamera.bindTimelinePlaybackEndListener(timelinePlaybackEndListenerName);

    SdFileManager.getInstance().bindReceiveFilesListener(this._bindFilesHandler);
    SdFileManager.getInstance().startRequestSdcardFilesRegularly();

    StorageKeys.IS_DATA_USAGEE_WARNING.then((res) => { // 是否使用流量保护  这个页面只需要进来一次就行了
      if (typeof (res) === "string") {
        StorageKeys.IS_DATA_USAGEE_WARNING = false;
        this.isDataUsageWarning = false;
      } else {
        this.isDataUsageWarning = res;
      }
      // this.queryNetworkJob();//这里sdcard回看时间轴不需要

    }).catch((error) => {
      console.log(error);
      StorageKeys.IS_DATA_USAGEE_WARNING = false;
      this.isDataUsageWarning = false;
      // this.queryNetworkJob();
    });

    this.recordListener = DeviceEventEmitter.addListener(kRecordTimeCallbackName, (data) => {
      if (data == null) {
        return;
      }
      let time = Number.parseInt(data.recordTime);// 要屏蔽倍速带来的影响
      this.setState({ recordTimeSeconds: time });
      console.log(data);// 录制时长。
    });

    Dimensions.addEventListener('change', this.dimensionListener);
  }

  _powerOffHandler = (isPowerOn, showSleepDialog, startVideo) => {
    if (this.cameraRenderView == null) {
      CameraPlayer.getInstance().bindPowerOffCallback(null);
      return;
    }
    // 电源属性状态发生了变化
    this.setState({ showPoweroffView: !isPowerOn, showPlayToolBar: isPowerOn, isPlaying: isPowerOn });
    if (!isPowerOn) { // 暂停
      this._startPlay(false);
    } else {
      this.queryNetworkJob();
    }
  }

  _networkChangeHandler = (networkState) => {
    if (this.cameraRenderView == null) {
      return;
    }
    // if (this.currentNetworkState != networkState) { // 网络状态不一样
    //   this.isChangingNetwork = true;
    // }
    // if (this.currentNetworkState != networkState) { // 网络状态不一样
    //   this.isChangingNetwork = true;
    // }
    console.log("处理网络变化");

    if (networkState == 0) { // 网络断开了连接 showError?
      CameraPlayer.getInstance().disconnectToDevice();// 去往其他注册了网络监听的页面，就不会走到这里了，如果走到这里，这里必须先执行断开的操作。
      this.setState({ showErrorView: true, showLoadingView: false, showPlayToolBar: false, showPauseView: false, errTextString: LocalizedStrings["common_net_error"], pstate: 0, error: 2 });
      return;
    }
    this.currentNetworkState = networkState;
    if (this.isPageForeGround) { // 有网络来了  发起重连吧
      this.setState({ showErrorView: false });
      setTimeout(() => {
        this.queryNetworkJob();
      }, 500);// 过一会再查询 ，那个查询网络类型的api有问题。
    }
  }

  _ServerRespHandler = ({ command, data }) => {
    // todo  处理文件播放的问题
    console.log("server response:", command, data);
    if (command == MISSCommand.MISS_CMD_PLAYBACK_RESP) {

      let dataJSON = Host.isAndroid ? JSON.parse(data) : data;
      let id = dataJSON["id"];
      if (id == null) {
        return;
      }
      if (id != this.sessionId) {
        return;
      }
      let status = dataJSON["status"];
      if (status == null) {
        return;
      }
      let startTime = dataJSON.starttime;
      let duration = dataJSON.duration;
      switch (status) {
        case "filefound":
          console.log(dataJSON);
          this.setState({ showLoadingView: false });
          this.endTime = startTime + duration;

          break;
        case "filenotfound":
          if ((VersionUtil.judgeIsV1(Device.model) || !VersionUtil.isFirmwareSupportCloud(Device.model)) && (this.sessionId * 1000 >= this.lastTimeItemStartTime) && (this.penultimateStartTime > 0)) { // 针对v1做一次纠正，部分固件播放最后一个没问题，部分固件播放最后一个有问题，这里做一次替换。
            this.toStartTime = this.penultimateStartTime;
            clearTimeout(this.timestampTimeout);
            CameraPlayer.getInstance().stopPlaybackTimestampInterval();
            this.islastestFilePlayFailed = true;
            this._startPlay(true);
          } else {
            this.setState({ progress: 0, showLoadingView: false, showErrorView: true, showPauseView: false, showPlayToolBar: false, errTextString: LocalizedStrings["camera_play_error_file"] });
            this._startPlay(false);
          }
          break;
        case "endoffile":

          if (this.islastestFilePlayFailed && (this.penultimateStartTime + 60000 - this.endTime * 1000 < 1500)) { // 上一次播放最后一个文件失败了，这一次播放到倒数第二个文件就不播放了。
            this.toSdcardEnd();
          }
          if (this.lastTimeItemEndTime != 0 && this.lastTimeItemEndTime - this.endTime * 1000 < 1500) {
            this.toSdcardEnd();
          }
          break;
        case "readerror":
          if ((VersionUtil.judgeIsV1(Device.model) || !VersionUtil.isFirmwareSupportCloud(Device.model)) && (this.sessionId * 1000 >= this.lastTimeItemStartTime) && (this.penultimateStartTime > 0)) { // 针对v1做一次纠正，部分固件播放最后一个没问题，部分固件播放最后一个有问题，这里做一次替换。
            this.toStartTime = this.penultimateStartTime;
            clearTimeout(this.timestampTimeout);
            CameraPlayer.getInstance().stopPlaybackTimestampInterval();
            this.islastestFilePlayFailed = true;
            this._startPlay(true);
          } else {
            this.setState({ progress: 0, showLoadingView: false, showErrorView: true, showPauseView: false, showPlayToolBar: false, errTextString: LocalizedStrings["camera_play_error_file"] });
            this._startPlay(false);
          }
          // 取消timestamp的定时请求
          break;
      }
    }
  }

  timestampCallback = (timestamp) => {
    if (this.timelineView == null) {
      return;
    }
    console.log(`lastTimeItemEndTime:${this.lastTimestamp}`, `currentTimestamp:${timestamp}`);

    // 这里每隔一秒就会触发一次 返回当前的时间戳
    if (timestamp == this.lastTimestamp) { // 没有发生更新
      return;
    }

    // if (this.lastTimeItemEndTime - timestamp * 1000 < 1500 ) {//接近文件末尾了 直接pause
    //   this.toSdcardEnd();
    //   return;
    // }
    this.toStartTime = timestamp * 1000;
    if (this.isSetPlayTime) {
      let diff = timestamp - this.startTime;
      // console.log(diff, timestamp, this.startTime);
      if (Platform.OS == "ios") {
        this.setState({ showLoadingView: false });
      }
      if (Math.abs(this.offset - diff) <= 20 || (new Date().getTime() - this.setPlayTimeMillis) > 6000) { // 刚刚设置了playback指令， 要消除loading
        // todo hide loading
        this.setState({ showLoadingView: false });
        this.isSetPlayTime = false;
      } else {
        return;// 不管，等后面来timestamp
      }
    }
    // if (this.timelineView.isTimeLinePressed()) { // 如果正在被按住
    //   return;
    // }
    this.lastTimestamp = timestamp;
    // console.log(`updated time:${ this.lastTimestamp }`);
    this.dateTime.setTime(timestamp * 1000);
    // console.log(`scroll to time:${ this.dateTime.getHours() }:${ this.dateTime.getMinutes() }:${ this.dateTime.getSeconds() }`);
    this.timelineView.scrollToTimestamp(this.lastTimestamp * 1000);
  }

  _startSnapshot() {
    if (Platform.OS === "android") {
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this._realStartSnapshot(false);
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            !this.destroyed && Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
          !this.destroyed && Toast.success("action_failed");
        });
    } else {
      // no ios's photos const use hardcode
      System.permission.request("photos").then((res) => {
        this._realStartSnapshot(false);
      }).catch((error) => {
        this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
      });
    }
  }

  _realStartSnapshot(isFromVideo) {
    AlbumHelper.snapShot(this.cameraRenderView)
      .then((path) => {
        console.log(path);
        this.isForVideoSnapshot = isFromVideo;
        this.setState({ screenshotVisiblity: true, screenshotPath: path });// show snapshotview
        this.snapshotTimeout = setTimeout(() => {
          this.isForVideoSnapshot = false;
          this.setState({ screenshotVisiblity: false, screenshotPath: null });
        }, 3000);
        // 文件路径。
      })
      .catch((error) => {
        console.log(JSON.stringify(error));
        !this.destroyed && Toast.success("action_failed");
      });
  }

  _toggleAudio(isMute) {

    if (isMute) {
      if (!this.state.isRecording) {
        Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {}).then((retCode) => {
          console.log("audio stop get send callback");
          console.log(retCode);
        });
      }
      this.cameraRenderView && !this.destroyed && this.cameraRenderView.stopAudioPlay();
      this.setState({ isMute: true });
      CameraConfig.setUnitMute(true);
      return;
    }
    if (this.state.speed > 1) {
      return;// 倍速模式下 不要播放声音
    }
    if (!this.state.isRecording) {
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
        console.log("audio stop get send callback");
        console.log(retCode);
      });
    }

    this.cameraRenderView && !this.destroyed && this.cameraRenderView.startAudioPlay();
    this.setState({ isMute: false });
    CameraConfig.setUnitMute(false);

  }

  // 进来先调用startPlay  startPlay里会check连接状态，如果是连接中的状态，直接发playback指令；如果不是连接中的状态，先连接，连接走到这里 如果连接成功，就会重新走startPlay流程
  _connectionHandler = (connectionState) => {
    if (connectionState == null) {
      return;
    }
    if (this.state.pstate == connectionState.state) {
      return;// 状态一样 没有必要通知
    }
    if (connectionState.state == 0) { // 断开连接
      this.isConnecting = false;
      // show error view
      // 网络连接断开了
      // if (this.isChangingNetwork && this.currentNetworkState >= 1) { // 切换网络后走到disconnect 这里应该尝试重连
      //   this.isChangingNetwork = false;
      //   this.setState((state) => {
      //     return {
      //       pstate: connectionState.state,
      //       error: connectionState.error

      //     };
      //   }, () => { // 数据塞给listview了，渲染还需要时间吧，延迟一会再执行scroll
      //     if (this.state.showPauseView) { // 已经显示了。
      //       return;
      //     }
      //     this.queryNetworkJob();

      //   });
      //   return;
      // }
      if (this.connRetry > 0) {
        this.connRetry = this.connRetry - 1;
        setTimeout(() => {
          this.queryNetworkJob();
        }, 300);
        console.log("connection retry");
        return;
      }
      this.cameraRenderView && !this.destroyed && this.cameraRenderView.stopAudioPlay();
      this.cameraRenderView && !this.destroyed && this.cameraRenderView.stopRender();
      // sync ui state
      this.setState({ isMute: CameraConfig.getUnitMute() });
      this._stopRecord();
      let errorCode = connectionState.error;
      let errorStr = ((errorCode == 36 || errorCode == MISSError.MISS_ERR_MAX_SESSION) && VersionUtil.judgeIsMiss(Device)) ? LocalizedStrings["max_client_exceed"] : (errorCode == -6 && !VersionUtil.judgeIsMiss(Device) ? LocalizedStrings["max_client_exceed"] : `${LocalizedStrings["camera_connect_error"]} ${errorCode}, ${LocalizedStrings["camera_connect_retry"]}`);
      this.setState({ showPlayToolBar: false, showErrorView: true, isPlaying: false, showLoadingView: false, errTextString: errorStr });

      return;
    }


    this.setState((state) => {
      return {
        pstate: connectionState.state,
        error: connectionState.error

      };
    }, () => {

      if (this.state.pstate == 2) { // 连接成功后就会重新开始播放
        if (!this.isConnecting) { // 处理tutk断线自动重连重复发送connected消息导致的问题。
          return;
        }
        this.connRetry = 2;
        this.isConnecting = false;
        // on connected
        this._startPlay(true);
      }
    });

  }

  _startPlay(isPlay) {

    if (isPlay) {
      if (this.lastTimeItemEndTime == 0) { // 没有数据
        return;
      }
      this.setState({ showPlayToolBar: true });
      // 开始寻找合适的item
      let startTime = this.toStartTime;
      if (startTime >= this.lastTimeItemEndTime) {
        startTime = SdFileManager.getInstance().getLastestItemStartTime();
      }
      if (startTime <= 0) {
        return;
      }
      this.dateTime.setTime(startTime * 1000);
      console.log(`开始播放 format time:${this.dateTime.getHours()}:${this.dateTime.getMinutes()}:${this.dateTime.getSeconds()}`);

      let timeItem;
      timeItem = SdFileManager.getInstance().getTimeItemClosest(startTime);
      if (timeItem != null) {
        this.startTime = Number.parseInt(timeItem.startTime / 1000);
        let duration = Number.parseInt(timeItem.duration / 1000);
        if (timeItem.startTime < startTime) {
          this.offset = Number.parseInt((startTime - timeItem.startTime) / 1000);
          if (this.offset >= duration) {
            this.offset = duration - 2;
          }
        } else {
          this.offset = 0;
        }
        this.isSetPlayTime = true;
        this.setPlayTimeMillis = new Date().getTime();
        // show loading

      } else { // 这里只可能是选中的时间比最后一个文件的endTime 还远一些。
        this.toSdcardEnd();
        return;
      }

      CameraPlayer.getInstance().stopPlaybackTimestampInterval();// 停止抓取

      console.log(`start play:${this.startTime}`);
      this.dateTime.setTime(this.startTime * 1000);
      console.log(`start play format time:${this.dateTime.getHours()}:${this.dateTime.getMinutes()}:${this.dateTime.getSeconds()}`);

      // 发送完回看命令后，再发送video-start，让miss做一个标记位。方便miss在回看结束的时候，自己再切换成直播帧。
      if (!this.state.isPlaying) { // 之前发过了暂停命令 这里要发一个start命令。

      }
      this.cameraRenderView && !this.destroyed && this.cameraRenderView.stopRender();// startBindVideo
      this.cameraRenderView && !this.destroyed && this.cameraRenderView.startRender();// startBindVideo
      CameraPlayer.getInstance().startPlayBack(this.startTime, this.offset, 0, this.state.speed)
        .then((res) => {
          Service.miotcamera.bindTimelinePlaybackEndListener(timelinePlaybackEndListenerName);
          //
          this.setState(() => { return { showErrorView: false, showLoadingView: true, isPlaying: true, showPauseView: false }; }, () => {
            this._toggleAudio(this.state.isMute);
            let position = (this.state.speed == 1 ? 0 : (this.state.speed == 4 ? 1 : 2));
            this.updateSpeed(position);
          });
          this.sessionId = this.startTime;
          if (!this.state.isMute) {
            this.cameraRenderView && !this.destroyed && this.cameraRenderView.stopAudioPlay();
            Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
              console.log("audio stop get send callback");
              console.log(retCode);
            });
            this.cameraRenderView && !this.destroyed && this.cameraRenderView.startAudioPlay();
          }

          this.timestampTimeout = setTimeout(() => {
            CameraPlayer.getInstance().getPlaybackTimetampInterval();
          }, 1000);

          CameraPlayer.getInstance().bindPlaybackTimestampCallback(this.timestampCallback);
          Service.miotcamera.setTimelinePlaybackMode(true);
        })
        .catch((err) => {
          console.log(err);
          this.cameraRenderView && !this.destroyed && this.cameraRenderView.stopRender();// startBindVideo
          if (err == -1 || err == -8888) { // - 8888重置本地连接，然后开始重连。
            CameraPlayer.getInstance().resetConnectionState();
            this.queryNetworkJob();
            return;
          }

          this.setState({ pstate: 0, showLoadingView: false, showErrorView: true, errTextString: `${LocalizedStrings["camera_connect_error"]} ${err} ${LocalizedStrings["camera_connect_retry"]}` });// 已经渲染过  直接跳过去

          Service.miotcamera.setTimelinePlaybackMode(false);
        });

    } else {
      CameraPlayer.getInstance().stopPlaybackTimestampInterval();// 停止抓取
      CameraPlayer.getInstance().bindPlaybackTimestampCallback(null);

      // stop播放
      this.setState({ isMute: CameraConfig.getUnitMute(), isPlaying: false, showPauseView: true, showPlayToolBar: false, showLoadingView: false });
      // stop video
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_VIDEO_STOP, {}).then((retCode) => {
        console.log("video stop");
        console.log(retCode);
      }).catch((err) => console.log(err));

      if (!this.state.isRecording) {
        // stop audio
        Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {}).then((retCode) => {
          console.log("audio stop");
          console.log(retCode);
        }).catch((err) => console.log(err));

      }

      this.cameraRenderView && !this.destroyed && this.cameraRenderView.stopAudioPlay();
      this.cameraRenderView && !this.destroyed && this.cameraRenderView.stopRender();

      Service.miotcamera.setTimelinePlaybackMode(false);
      // stop audio local resources

    }
  }

  updateSpeed(position) {
    if (this.state.isRecording) {
      return;
    }
    let speed = this.state.speed;
    switch (position) {
      case 0:
        speed = 1;
        break;
      case 1:
        speed = 4;
        break;
      case 2:
        speed = 16;
        break;
      default:
        speed = 1;
        break;
    }
    this.selectedIndexArray = [position];
    this.setState({ speed: speed });
    if (speed > 1) {
      this._toggleAudio(true);
    }
    if (!this.state.isPlaying) {
      return;
    }
    CameraPlayer.getInstance().changeSpeed(speed)
      .then(() => {
        if (speed == 1) {

        } else if (!this.state.isMute) {
          this._toggleAudio(true);
        }
      })
      .catch((err) => {
        !this.destroyed && Toast.fail("action_failed", err);
      });
  }

  _onVideoClick() {
    if (!CameraPlayer.getInstance().isConnected()) {
      return;
    }
    if (this.state.showPauseView) {
      return;
    }
    this.setState({
      showPlayToolBar: !this.state.showPlayToolBar
    });
    console.log("click video view");
  }

  // android返回键处理
  onBackHandler = () => {
    if (!this.isPageForeGround) {
      return false;
    }
    if (this.state.fullScreen) {
      this.toPortrait();
      return true;
    }
    if (this.state.isRecording) {
      !this.destroyed && Toast.success("camera_recording_block");
      return true;
    }

    if (this.state.isSelectMode) {
      this.onSelectAllChanged(false);
      this.setState({ isSelectMode: false });
      return true;
    } else {
      this._onPause();
      return false;// 不接管
    }
  }

  queryNetworkJob() {
    if (this.destroyed) {
      return;
    }
    if (this.isUserPause) {
      return;// 用户主动暂停的
    }
    if (!this.isDataUsageWarning) { // 之前点过暂停按钮 本地暂存这个状态
      this._startConnect();
      return;
    }
    CameraPlayer.getInstance().queryShouldPauseOn4G()
      .then(({ state, pauseOnCellular }) => {
        if (state === "CELLULAR" && pauseOnCellular && this.isDataUsageWarning) { // 普通网络 && 数据流量提醒
          !this.destroyed && Toast.success("nowifi_pause", true);
          this._onPause();
          return;
        }
        // 其他网络条件 走连接的步骤吧
        this._startConnect();// 开始连接
      })
      .catch(() => { // 获取网络状态失败 也直接走开始连接的流程
        this._startConnect();// 开始连接
      });
  }

  _startConnect() {
    if (!this.props.navigation.isFocused()) { // 当前页面已经不在前台了
      this.setState({ showLoadingView: false });
      return;
    }
    if (!this.state.showLoadingView) { // 如果没有loading
      this.setState({ showLoadingView: true });
    }
    if (this.state.showPauseView) {
      this.setState({ showPauseView: false });
    }
    // 开始连接
    if (CameraPlayer.getInstance().isConnected()) {
      // 如果已经连接成功 直接发送video_start
      this._startPlay(true);
      return;
    }
    this.isConnecting = true;
    CameraPlayer.getInstance().startConnect();
  }


  _stopRecord() {
    if (!this.state.isRecording) {
      return;
    }

    if (this.state.resolution != 3) { // 不是高清
      Service.miotcamera.sendP2PCommandToDevice(
        MISSCommand.MISS_CMD_STREAM_CTRL_REQ, { "videoquality": this.state.resolution })
        .then(() => { // 不修改这些信息。

        })
        .catch((err) => {
          console.log(err);
        });
    }

    if (this.state.isMute) { // 原本静音
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {}).then((retCode) => {

      });
    }

    this.cameraRenderView && !this.destroyed && this.cameraRenderView.stopRecord().then(() => {
      this.setState({ isRecording: false, recordTimeSeconds: 0 });
      if (this.videoRecordPath == null || this.videoRecordPath == "") {
        return;
      }
      setTimeout(() => {
        // 录制成功后 要把视频转存储到相册。
        AlbumHelper.saveToAlbum(this.videoRecordPath, true)
          .then((result) => {
            console.log(result);
          })
          .catch((err) => {
            console.log(err);
          });
        // 录屏完成后 截图一张。
        // this._realStartSnapshot(true);
        // 录屏完成后 截图一张。
        AlbumHelper.justSnapshot(this.cameraRenderView)
          .then(() => {
            this.isForVideoSnapshot = true;
            this.setState({ screenshotVisiblity: true, screenshotPath: AlbumHelper.getSnapshotName() });// show snapshotview
            this.snapshotTimeout = setTimeout(() => {
              this.setState({ screenshotVisiblity: false, screenshotPath: null });
            }, 3000);
          })
          .catch((error) => {
            console.log(JSON.stringify(error));
            !this.destroyed && Toast.success("action_failed");
          });
      }, 500);
    })
      .catch((error) => {
        this.setState({ isRecording: false, recordTimeSeconds: 0 });
        if (error == -2) {
          !this.destroyed && Toast.fail("record_video_failed_time_mini");
        } else {
          !this.destroyed && Toast.fail("record_video_failed");
        }
      });
    // 录屏完成后 截图一张。
  }

  _startRecord() {
    if (Platform.OS === "ios") {
      // no ios's photos const use hardcode
      System.permission.request("photos").then((res) => {
        this.realStartRecord();
      }).catch((error) => {
        this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
      });
    } else {

      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this.realStartRecord();
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            !this.destroyed && Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
          !this.destroyed && Toast.success("action_failed");
        });
    }
  }

  realStartRecord() {
    // 切换清晰度为高清
    if (this.state.resolution != 3) { // 不是高清
      Service.miotcamera.sendP2PCommandToDevice(
        MISSCommand.MISS_CMD_STREAM_CTRL_REQ, { "videoquality": 3 })
        .then(() => { // 不修改这些信息。
          // this.setState({ resolution: index })
          // StorageKeys.LIVE_VIDEO_RESOLUTION = index;
        })
        .catch((err) => {
          console.log(err);
        });
    }
    // 打开声音
    if (this.state.isMute) { // 不是有声音 开启声音 并且播放
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
        console.log("audio stop get send callback");
        console.log(retCode);
      });
      // this.cameraRenderView.startAudioPlay();//打开通道，不打开播放。
    }
    let path = AlbumHelper.getFileName(true);
    this.videoRecordPath = path;
    this.cameraRenderView && !this.destroyed && this.cameraRenderView.startRecord(`${Host.file.storageBasePath}/${path}`, kRecordTimeCallbackName)
      .then((retCode) => {
        console.log(`start record, retCode: ${retCode}`);
        this.setState({ isRecording: true });
      })
      .catch((err) => {
        console.log(err);
        !this.destroyed && Toast.success("action_failed");
      });
  }


  onPressSeeAllVideo() {
    if (this.state.isRecording) {
      Toast.success("camera_recording_block");
      return;
    }
    this.props.navigation.navigate("SdcardPage", { title: "sdcardPage" });
  }


  // 这里代表时间轴滚动了
  _onCenterValueChanged = (timestamp) => {

    this.dateTime.setTime(timestamp);
    console.log(`滑动结束 timestamp:${timestamp}`);
    // console.log(`${ this.dateTime.getHours() } ${ this.dateTime.getMinutes() } ${ this.dateTime.getSeconds() }`);

    if (this.timeIndicatorView == null) {
      return;
    }
    this.timeIndicatorView.setState({ centerTimestamp: 0 });

    this.toStartTime = timestamp;
    if (this.state.isPlaying) {
      this.queryNetworkJob();// 检测网络
    }
    // 如果没有播放  就不用管了

  }

  _onScrolling = (timestamp) => {
    console.log("滑动中");
    // console.log(timestamp);
    this.dateTime.setTime(timestamp);
    console.log(`${this.dateTime.getHours()} ${this.dateTime.getMinutes()} ${this.dateTime.getSeconds()}`);
    if (this.timeIndicatorView == null) {
      return;
    }
    this.timeIndicatorView.setState({ centerTimestamp: timestamp });
  }

  _bindFilesHandler = (status) => { // 收到文件列表的回调
    this.onGetFiles();
  }

  onGetFiles() {
    let timeItems = SdFileManager.getInstance().getTimeItems();
    if (timeItems == null || timeItems.length <= 0) {
      return;
    }

    this.setState({ isEmpty: false });
    // 这里把数据丢给timelineview
    this.timelineView.initData(timeItems);
    let lastTimeItem = timeItems[timeItems.length - 1];
    let penultimateItem = timeItems.length > 1 ? timeItems[timeItems.length - 2] : null;
    this.lastTimeItemEndTime = lastTimeItem.endTime;
    this.lastTimeItemStartTime = lastTimeItem.startTime;

    let lastTimeItemStartTime = lastTimeItem.startTime;
    if (!this.isFirstReceiveFiles) {
      return;
    }

    this.isFirstReceiveFiles = false;

    this.scrollTimeout1 = setTimeout(() => {
      if (this.timelineView == null) {
        return;
      }
      this.timelineView.scrollToTimestamp(lastTimeItemStartTime);// 不通知
      // 这里开始播放了.
      this.toStartTime = lastTimeItemStartTime;
      if (penultimateItem) {
        this.penultimateStartTime = penultimateItem.startTime;
      } else {
        this.penultimateStartTime = 0;
      }
      this.isPlayingFinalItem = true;
      this.queryNetworkJob();
    });

  }

  toSdcardEnd() {
    // todo jump to sdcard file end
    // todo  pauseCamera
    if (this.timelineView != null) {
      this.timelineView.scrollToTimestamp(this.lastTimeItemEndTime);
    }
    this._startPlay(false);
    this._stopRecord();
  }

  dimensionListener = (args) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    if (Platform.OS === 'ios') {
      if (args && args.screen && args.window) {
        if (args.screen.width !== args.window.width || args.screen.height !== args.window.height) {
          console.log('correction: setDimensionsIos: ', args);
          setTimeout(() => Dimensions.set({ 'window': args.screen }), 10);
        }
      }
    }
  }

  _getWindowPortraitHeight() {
    let winWidth = Dimensions.get('window').width;
    let winHeight = Dimensions.get('window').height;
    let width = Math.min(winWidth, winHeight);
    let height = Math.max(winWidth, winHeight);
    return height;
  }

  _onVideoScaleChanged(params) {

    let endTime = new Date().getTime();
    if ((endTime - this.startScaleTime) < 50) {
      return;
    }
    this.startScaleTime = endTime;
    if (params && params.nativeEvent && params.nativeEvent.scale) {
      let scale = Number(params.nativeEvent.scale);

      if (scale < 1) {
        scale = 1;
      }

      if (this.angleViewTimeout) {
        clearTimeout(this.angleViewTimeout);
        this.angleViewTimeout = null;
      }

      this.angleViewTimeout = setTimeout(() => {
        this.setState({ showCameraAngleView: false, angleViewShowScale: false });
      }, 1000);
      console.log(`_onVideoScaleChange${scale}`);
      if (!this.state.fullScreen) {
        this.videoPortraitScale = scale;// 保存竖屏下的videoScale
      }

      this.setState({ videoScale: scale, showCameraAngleView: true, angleViewShowScale: true, showPlayToolBar: scale > 1 ? false : true });
    }

  }

  // 这里是直播中的小窗口
  _renderAngleView() {
    if (!this.state.showCameraAngleView) {
      return (null);
    }

    let sPadding = 20;
    let bottom = this.state.fullScreen ? (kWindowHeight > 600 ? 250 : 180) : ((Platform.OS == "ios" ? 250 : 260));// 28 is angle view's height
    let left = this.state.fullScreen ? 55 : sPadding;
    let angleStyle = {
      position: "absolute",
      left: left,
      bottom: bottom

    };

    return (
      <View style={angleStyle}>
        <RectAngleView
          ref={(ref) => { this.angleView = ref; }}
          angle={this.state.angle}
          elevation={this.state.elevation}
          scale={this.state.videoScale}
          showScale={this.state.angleViewShowScale}
          accessibilityLabel={DescriptionConstants.zb_39.replace('1',this.state.videoScale)}
        />
      </View>
    );
  }
}


const styles = StyleSheet.create({

  container: {
    backgroundColor: "#ffffff",
    width: '100%',
    height: '100%',
    display: "flex",
    flexWrap: "nowrap"
  },

  main: {
    display: 'flex',
    flexDirection: 'column',
    flexWrap: 'nowrap',
    backgroundColor: 'white',
    height: "100%"
  },

  videoContainerNormal: {
    backgroundColor: 'black',
    width: "100%",
    height: Platform.OS == "ios" ? 350 : 380,
    position: "relative"// absolute以第一个非static的父类变量作为定位标准
  },

  videoContainerFull: {
    backgroundColor: 'black',
    width: "100%",
    height: "100%",
    position: "relative"// absolute以第一个非static的父类变量作为定位标准
  },

  videoView: {
    position: "absolute",
    width: "100%",
    height: "100%"
  },

  videoControl: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    height: 80
  },

  videoControlBar: {// 横向
    display: "flex",
    flexWrap: "nowrap",
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-around",
    paddingTop: 20,
  },
  videoControlBarFull: {
    display: "flex",
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end"

  },
  videoControlBarItem: {// 内容居中排列
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    width: 45
  },

  videoControlBarItemImg: {
    width: 40,
    height: 40
  },

  landscapeCallViewLayout: {
    width: "100%",
    paddingBottom: 10,
    paddingTop: 20,
    position: "absolute",
    bottom: 0
  },
  landscapeCallViewLayoutImg: {
    display: "flex",
    margin: "auto",
    width: "100%",
    flexDirection: "row",
    justifyContent: "center"
    // textAlign:"center"
  },

  callViewLayout: {
    flexGrow: 1,
    width: "100%",
    flexDirection: "column",
    display: "flex",
    alignItems: "center",
    justifyContent: "center"
  },

  bottomLayout: {
    display: "flex",
    width: "100%",
    height: 60,
    flexDirection: "row",
    flexWrap: 'nowrap'
  },

  bottomLayoutItem: {
    flexGrow: 1,
    height: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center"
  },

  whiteText: {
    fontSize: 10,
    textAlign: "center",
    padding: 4,
    color: "#ffffff",
    borderColor: "#FFFFFFCC",
    borderRadius: 3,
    borderWidth: 1
  },
  snapShot: {
    position: "absolute",
    bottom: 40,
    left: 5,
    margin: 6,
    width: 100,
    height: 66,
    borderRadius: 5
  },
  snapShotFull: {
    position: "absolute",
    bottom: 84,
    left: 35,
    margin: 6,
    width: 100,
    height: 66,
    borderRadius: 5
  }
});
