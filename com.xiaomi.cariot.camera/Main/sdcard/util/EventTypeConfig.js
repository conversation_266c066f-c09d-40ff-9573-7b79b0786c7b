import { DarkMode } from 'miot';

export const EVENT_TYPE2 = {
  Default: 1 << 0,
  ObjectMotion: 1 << 1,
  EmotionRecognition: 1 << 2,
  PeopleMotion: 1 << 3,
  Face: 1 << 4,
  BabyCry: 1 << 5,
  Pet: 1 << 6,
  AI: 1 << 7,
  CameraCalling: 1 << 8,
  LouderSound: 1 << 9,
  PeopleCough: 1 << 10,
  WokeUp: 1 << 11,
  Asleep: 1 << 12,
  FenceIn: 1 << 13,
  FenceOut: 1 << 14,
  KnownFace: 1 << 15,
  CoveredFace: 1 << 16
};

// export const EVENT_TYPE = {
//   Default: 1 << 0,
//   ObjectMotion: 1 << 1,
//   EmotionRecognition: 1 << 2,
//   LouderSound: 1 << 3,
//   PeopleMotion: 1 << 4,
//   Asleep: 1 << 5,
//   WokeUp: 1 << 6,
//   FenceIn: 1 << 7,
//   FenceOut: 1 << 8,
//   Face: 1 << 9,
//   KnownFace: 1 << 10,
//   Pet: 1 << 11,
//   ChildDetected: 1 << 12,
//   PeopleCough: 1 << 12,
//   BabyCry: 1 << 13,
//   CoveredFace: 1 << 14,
//   AI: 1 << 15,
//   CameraCalling: 1 << 16,
// };

export const EVENT_TYPE = {
  Default: 1 << 0,
  IgnoreEvent: 1 << 1,
  ObjectMotion: 1 << 2,
  Pet: 1 << 3,
  ChildDetected: 1 << 4

};

Object.freeze(EVENT_TYPE);
let isDark = DarkMode.getColorScheme() == "dark";

export const EVENT_TYPE_COLOR = {
  motionSelectedColor: "#FDC541",
  unselectedColor: isDark ? "#474747" : "#F7F7F7",
  peopleSelectedColor: "#44CECA",
  babyCrySelectedColor: "#9B91FF",
  cameraCallingSelectedColor: "#6395FF",
  faceSelectedColor: "#2DB0FF",
  aiSelectedColor: "#7DA6E0",
  petSelectedColor: "#75DF6E",
  loudSelectedColor: "#F29E60",
  ignoreEventColor: "#207CE8",
  childEventColor: "#877CFF",
};

Object.freeze(EVENT_TYPE_COLOR);


const SD_AI_TYPE = 50; // 云存会用到，sd卡无
const SD_Emotion = 41;
const SD_WokeUp = 40;
const SD_Asleep = 39;
const SD_CoveredFace = 38;
const SD_LOUDER_SOUND = 13;
const SD_PeopleCough = 24;
const SD_FencePass = 10;
const SD_FenceIn = 20;
const SD_FenceOut = 21;
const SD_ABNORMAL_SOUND = 9; // 异响
const SD_CAMERA_CALLING = 8;
const SD_BABY_CRY = 7;
const SD_KNOWN_FACE = 5;
const SD_FACE = 4;
const SD_PEOPLE_MOTION = 1;
const SD_OBJECT_MOTION = 0;
const SD_DEFAULT = -1;
const SD_CAT = 2;
const SD_DOG = 3;
const SD_CHILD = 42;
const SD_IGNORE_EVENT = 23;

export const SD_PRIORITY = {
  [SD_DEFAULT]: 0,
  [SD_OBJECT_MOTION]: 1,
  [SD_IGNORE_EVENT]: 1,
  [SD_Emotion]: 2,
  [SD_ABNORMAL_SOUND]: 3,
  [SD_LOUDER_SOUND]: 3,
  [SD_PEOPLE_MOTION]: 4,
  [SD_Asleep]: 5,
  [SD_WokeUp]: 6,
  [SD_FencePass]: 7,
  [SD_FenceOut]: 8,
  [SD_FenceIn]: 9,
  [SD_FACE]: 10,
  [SD_KNOWN_FACE]: 11,
  [SD_DOG]: 12,
  [SD_CAT]: 12,
  [SD_PeopleCough]: 13,
  [SD_BABY_CRY]: 14,
  [SD_CoveredFace]: 15,
  // [SD_ELDER_FALL]: 12,
  [SD_CHILD]: 16,
  [SD_AI_TYPE]: 49,
  [SD_CAMERA_CALLING]: 50
};
// 固件里的事件类型定义
export const TIMELINE_EVENT_TYPE = {
  "Default": SD_DEFAULT,
  "ObjectMotion": SD_OBJECT_MOTION,
  "PeopleMotion": SD_PEOPLE_MOTION,
  // cat 2  dog 3 skip;
  "Face": SD_FACE,
  "KnownFace": SD_KNOWN_FACE,
  // old man 6 skip
  "ChildDetected": SD_CHILD, // 儿童
  "IgnoreEvent": SD_IGNORE_EVENT, // IgnoreEvent
  "Pet": SD_DOG, // any one presents for pets is ok.
  "BabyCry": SD_BABY_CRY,
  "AI": SD_AI_TYPE, // 固件里并没有该类型，强制塞的一个
  "CameraCalling": SD_CAMERA_CALLING, // 固件里并没有该类型，强制塞的一个
  "AbnormalSound": SD_ABNORMAL_SOUND,
  "LouderSound": SD_LOUDER_SOUND,
  "PeopleCough": SD_PeopleCough,
  "FencePass": SD_FencePass,
  "FenceIn": SD_FenceIn,
  "FenceOut": SD_FenceOut,
  "WokeUp": SD_WokeUp,
  "Asleep": SD_Asleep,
  "EmotionRecognition": SD_Emotion,
  "CoveredFace": SD_CoveredFace
};
Object.freeze(TIMELINE_EVENT_TYPE);
// 固件里的事件类型定义
export const TIMELINE_EVENT_MAP = {
  [SD_OBJECT_MOTION]: "ObjectMotion",
  [SD_PEOPLE_MOTION]: "PeopleMotion",
  // cat 2  dog 3 skip;
  [SD_FACE]: "Face",
  [SD_KNOWN_FACE]: "KnownFace",
  // old man 6 skip
  [SD_CHILD] : "ChildDetected",
  [SD_DOG]: "Pet", // any one presents for pets is ok.
  [SD_CAT]: "Pet", // any one presents for pets is ok.
  [SD_BABY_CRY]: "BabyCry",
  [SD_AI_TYPE]: "AI", // 固件里并没有该类型，强制塞的一个
  [SD_CAMERA_CALLING]: "CameraCalling", // 固件里并没有该类型，强制塞的一个
  [SD_LOUDER_SOUND]: "LouderSound",
  [SD_PeopleCough]: "PeopleCough",
  [SD_FencePass]: "FencePass",
  [SD_FenceIn]: "FenceIn",
  [SD_FenceOut]: "FenceOut",
  [SD_WokeUp]: "WokeUp",
  [SD_Asleep]: "Asleep",
  [SD_Emotion]: "EmotionRecognition",
  [SD_CoveredFace]: "CoveredFace",
  [SD_IGNORE_EVENT]: "IgnoreEvent"
};

Object.freeze(TIMELINE_EVENT_MAP);

export default { SD_CAMERA_CALLING, SD_AI_TYPE, SD_BABY_CRY, SD_CHILD, SD_IGNORE_EVENT, SD_KNOWN_FACE, SD_FACE, SD_PEOPLE_MOTION,
  SD_OBJECT_MOTION, SD_DEFAULT, SD_CAT, SD_DOG, SD_LOUDER_SOUND, SD_PeopleCough, SD_FencePass, SD_FenceIn, SD_FenceOut, SD_Asleep, SD_WokeUp, SD_Emotion, SD_CoveredFace };