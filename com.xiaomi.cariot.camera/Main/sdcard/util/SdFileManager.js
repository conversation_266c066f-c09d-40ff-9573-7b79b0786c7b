import { Service, Device, Host, Package } from 'miot';
import base64js from 'base64-js';
import { DeviceEventEmitter, Platform } from 'react-native';
import NumberUtil from '../../util/NumberUtil';
import CameraConfig from '../../util/CameraConfig';
import { MISSAudioChannel, MISSSampleRate } from 'miot/ui/CameraRenderView';
import AlbumHelper, { SNAPSHOT_SETTING_IMG_PATH_V2 } from '../../util/AlbumHelper';
import VersionUtil from '../../util/VersionUtil';
import EventTypeConfig, { SD_PRIORITY } from './EventTypeConfig';
import RPC from '../../util/RPC';
import CameraPlayer from '../../util/CameraPlayer';
import LogUtil from '../../util/LogUtil';
import { InteractionManager } from 'react-native';
import SdcardEventLoader from "../../framework/sdcard/SdcardEventLoader";


let basePath = Host.file.storageBasePath;
let hasEndPath = basePath != null && basePath.lastIndexOf("/") == (basePath.length - 1);

const kRDTDataReceiveCallBackName = 'rdtDataReceiveCallBack';
const filepathPrefix = hasEndPath ? (`camera/${ Device.deviceID }/` + `sdcardVideoThumbs/`) : (`/camera/${ Device.deviceID }/` + `sdcardVideoThumbs/`);
const videoPathPrefix = hasEndPath ? "sdVideo" : "/sdVideo/";
const Tag = "SdFileManager";

export const DownloadVideoState = {
  DOWNLOADED: 1,
  FAILED: 2
};

Object.freeze(DownloadVideoState);

const TAG = "SdFileManager";

// 这种普通数据类 参考: IBluetoothLock
export default class SdFileManager {

  static sDowningProgress = "DOWNLOAD_PROGRESS"
  // 获取sd卡文件
  static getInstance() {
    if (!this.instance) {
      this.instance = new SdFileManager();
    }
    return this.instance;
  }

  constructor() {
    this.timeItems = [];// 放置timeItems的地方
    this.timestampList = [];// 存放要下载图片的timestamp list地方，通过unshift和pop模拟队列
    this.isDownloadingImg = false;
    this.sdcardFileThumbMaps = {};

    this.timeItemDayPositionMap = {};
    this.requestSdfilesTimeout = null;
    this._bindRdtFilesListener();

    this.rdtCommandList = [];// 存储下发指令的队列，依次从队列里取

    this.receiveFileListener = null;

    this.commandBuffer = null;
    this.commandSize = 0;
    this.currentDataLength = 0;
    this.commandOffset = 0;
    this.commandStatus = 0;
    this.rdtCommand = 0;
    this.isAllRdtDataReceived = false;
    this.downloadingImgTimestamp = 0;

    this.isSendingCmd = false;

    this.isOnCmdError = false;
    this.onErrorRetryTimeout = null;
    this.isHandleFileList = false;

    this.lastRequestSdcardStatusTime = 0;
    this.sdcardStatus = null;

    this.eventTypeAll = [];
    this.eventTypeTemp = [];

    this.downloadItemList = [];
    this.videoFileDownloadListener = null;

    this.sendEventTypeBeginTime = 0;
    this.requestSdcardFilesRegularly = false;
    this.progress = 0;

    this.rdtErrorTime = 0;
    this.channel = 0;

    this.connectionListener = CameraPlayer.getInstance()
      .addConnectionListener((connectionState) => {
        LogUtil.logOnAll(TAG, "p2p connect state", connectionState.state, connectionState.error);
        if (connectionState.state < 1) {
          LogUtil.logOnAll(TAG, "p2p连接断开，重置rdt相关状态");
          this.commandBuffer = null;
          this.commandSize = 0;
          this.commandOffset = 0;
          this.commandStatus = 0;
          this.rdtCommand = 0;
          this.isAllRdtDataReceived = false;
          this.isOnCmdError = true;
          this.progress = 0;
          //没有主动重置状态呀;
          this.handleErrorRetry(false);// 网络连接断开了。通知一下外面的人。暂时不做其他的处理了。
        }
      });
    
    this.longNoDataTimeout = null;

  }

  static getSdcardFilePath(timestamp, isVideo = false) {
    if (isVideo) {
      return `${ videoPathPrefix + Device.deviceID }/${ timestamp }.mp4`;
    } else {
      return `${ filepathPrefix + timestamp }.jpg`;
    }
  }

  clearSdcardFileList() {
    this.timeItems = [];// 清空了 进来呢 就会开始重新请求
  }

  _bindRdtFilesListener() {
    this.rdtListener = DeviceEventEmitter.addListener(kRDTDataReceiveCallBackName, ({ data }) => {
      this.handleRdtData(data);
    });
    Service.miotcamera.bindRDTDataReceiveCallback(kRDTDataReceiveCallBackName);
  }

  handleRdtData(data) {
    // LogUtil.logDebug("   ...");
    // here handle data
    if (data == null || data.length <= 0) {
      LogUtil.logOnAll(`收到数据为空: isHandleFileList:${ this.isHandleFileList } downloading sdcard video:${ this.downloadingVideoTimestamp } downloading img:${ this.downloadingImgTimestamp }`);
      return;
    }
    // here发一个消息吧，避免中间出问题了，例如p2p连接断开了，等等导致所有的消息都卡住不播放的问题。
    if (this.longNoDataTimeout != null) {
      clearTimeout(this.longNoDataTimeout);
      this.longNoDataTimeout = null;
    }
    this.longNoDataTimeout = setTimeout(() => {
      // LogUtil.logOnAll(TAG, "比较长的时间内一直没有收到数据");// 因为转存视频 需要比较多的时间  这里做检测也不合适。
    }, 1000);

    if (this.isOnCmdError) {
      let time = new Date().getTime();
      if (time - this.rdtErrorTime < 1000) {
        clearTimeout(this.onErrorRetryTimeout);
        this.onErrorRetryTimeout = setTimeout(() => {
          // LogUtil.logOnAll(TAG, `上一次收到rdt数据错误后，还在接收数据，在此期间收到的数据都不处理， 直到1000ms内都不收任何数据  再走错误处理。。: isHandleFileList:${ this.isHandleFileList } downloading sdcard video:${ this.downloadingVideoTimestamp } downloading img:${ this.downloadingImgTimestamp } isSendingCmd:${ this.isSendingCmd } onCmdError:${ this.isOnCmdError }`);
          this.handleErrorRetry();
        }, 1000);
      }
      this.rdtErrorTime = time;
      return;
    }

    this.currentDataLength = 0;
    try {
      let result = null;
      try {
        result = base64js.toByteArray(data);// uint8array
      } catch (exception) {
        this.isOnCmdError = true;
        LogUtil.logOnAll(TAG, `接收数据出错：${ JSON.stringify(exception) }${ exception.stack } data:${ data }`);
        clearTimeout(this.onErrorRetryTimeout);
        this.onErrorRetryTimeout = setTimeout(() => {
          LogUtil.logOnAll(TAG, `上一次收到rdt数据错误后，1000ms后都没有收到其他数据了  走错误处理。。: isHandleFileList:${ this.isHandleFileList } downloading sdcard video:${ this.downloadingVideoTimestamp } downloading img:${ this.downloadingImgTimestamp } isSendingCmd:${ this.isSendingCmd } onCmdError:${ this.isOnCmdError }`);
          this.handleErrorRetry();
        }, 1000);
        return;
      }
      if (result == null || result.length == 0) { // base64转换的时候有可能返回空
        this.isOnCmdError = true;
        clearTimeout(this.onErrorRetryTimeout);
        LogUtil.logOnAll(TAG, "接收数据为空");
        this.onErrorRetryTimeout = setTimeout(() => {
          LogUtil.logOnAll(TAG, `上一次收到rdt数据错误后，1000ms后都没有收到其他数据了  走错误处理。。: isHandleFileList:${ this.isHandleFileList } downloading sdcard video:${ this.downloadingVideoTimestamp } downloading img:${ this.downloadingImgTimestamp } isSendingCmd:${ this.isSendingCmd } onCmdError:${ this.isOnCmdError }`);
          this.handleErrorRetry();
        }, 1000);
        return;
      }
      this.currentDataLength = result.length;
      if (this.commandSize == 0 && this.rdtCommand == 0) {
        this.commandBuffer = null;
        this.commandSize = 0;
        this.commandOffset = 0;
        this.commandStatus = 0;
        this.rdtCommand = 0;
        this.isAllRdtDataReceived = false;
        if (this.rdtCommand == 6 || this.rdtCommand == 1) {
          this.timeoutRequestFile && clearTimeout(this.timeoutRequestFile);
        }
        let command = this.byteArrayToInt(result, 0);
        if (command <= 0 || command > 65535) {
          LogUtil.logOnAll(TAG, `不合理的命令:${ command }`);
          throw ("invalid command");

        }
        this.rdtCommand = command;

        let size = this.byteArrayToInt(result, 4);
        if (size === 0) {
          throw (`empty`);
        }
        // 24487180 2155307
        if (size <= 0 || size >= 734003200) {
          LogUtil.logOnAll(TAG, `不合理的size:${ size }`);
          throw (`invalid size:${ size }`);
        }
        // console.log("rdt size ", size,result[4],result[5],result[6],result[7],this.byteArrayToInt([0xF,0xF,0xF,0xF],0));
        this.commandSize = size;

        let status = this.byteArrayToInt(result, 8);
        this.commandStatus = status;

        let rawSize = size;
        let currentPacketDataSize = result.length - 12;

        this.commandOffset = currentPacketDataSize;
        this.commandBuffer = new Uint8Array(rawSize);
        this.commandBuffer.set(result.slice(12));
        if (currentPacketDataSize >= this.commandSize) {
          this.isAllRdtDataReceived = true;
        }

      } else {
        if (this.rdtCommand == 6 || this.rdtCommand == 1) {
          this.timeoutRequestFile && clearTimeout(this.timeoutRequestFile);
        }
        this.commandBuffer.set(result, this.commandOffset);
        this.commandOffset = this.commandOffset + result.length;
        if (this.commandOffset >= this.commandSize) {
          this.isAllRdtDataReceived = true;
        }
        if (this.rdtCommand == 1) {
          let progress = Number.parseInt(this.commandOffset * 100 / this.commandSize);
          if (progress - this.progress >= 25) { // 只有不同的时候才抛出去，避免抛太多消息出现的UI卡顿问题。。
            this.progress = progress;
            LogUtil.logOnAll(TAG, "current video download progress:" + this.progress);
            DeviceEventEmitter.emit(SdFileManager.sDowningProgress, { timestamp: this.downloadingVideoTimestamp, progress: progress });
          }
        }
      }

      if (this.rdtCommand > 0 && this.commandSize > 0 && this.isAllRdtDataReceived) {
        let isCommandComplete = true;
        if (this.rdtCommand == 9) {
          let tmpBuffer = new Uint8Array(this.commandBuffer);
          this.parseDeviceInfo(tmpBuffer);
        } else if (this.rdtCommand == 6) { // sdcard files
          let tmpBuffer = new Uint8Array(this.commandBuffer);
          this.parseSdcardFiles(tmpBuffer);
        } else if (this.rdtCommand == 5) {
          let tmpBuffer = new Uint8Array(this.commandBuffer);
          this.parsePicFile(tmpBuffer);
        } else if (this.rdtCommand == 11) {
          let tempBuffer = new Uint8Array(this.commandBuffer);
          isCommandComplete = this.parseEventTypes(tempBuffer);// sdcard event type要下发多次。
        } else if (this.rdtCommand == 1) {
          this.progress = 0;
          let tempBuffer = new Uint8Array(this.commandBuffer);
          this.parseVideoFile(tempBuffer);
          // isCommandComplete = false;//parseVideoFile是一个异步操作
        }
        this.commandBuffer = null;
        this.commandSize = 0;
        this.commandOffset = 0;
        this.commandStatus = 0;
        this.rdtCommand = 0;
        this.isAllRdtDataReceived = false;
        // if (isCommandComplete) {
        //   LogUtil.logOnAll(TAG, "一个rdt任务执行完毕，执行下一个");
        //   this.isSendingCmd = false;
        //   this.pollAndSendCommand();//下载完毕，继续执行下一个任务。
        // }
      }

    } catch (err) {
      LogUtil.logOnAll(TAG, `deal rdt data error:${ err } message stack:${ err.stack }`, "300ms run again");
      this.rdtErrorTime = new Date().getTime();
      this.handleRdtErr(err);
    }
  }

  handleRdtErr(err) { // 出错了就重置状态吧  不再重试和请求下一步了
    LogUtil.logOnAll(TAG, "rdt数据当前状态: command:", this.rdtCommand, "offset", this.commandOffset, ` currentDatalength:${ this.currentDataLength }`, ` cmdBuffer length:${ this.commandBuffer != null ? this.commandBuffer.length : 0 }`, " totalSize: ", this.commandSize, `${ ` downloadingVideoTimestamp:${ this.downloadingVideoTimestamp } downloadingImgTimestamp:${ this.downloadingImgTimestamp }` + " ishandleFileList:" }${ this.isHandleFileList }`);
    // if (this.command <= 0) {

    // 如果是size为0，属于正常无数据，不走后面的error，继续执行后续任务
    if (err === "empty" && this.rdtCommand == 11) {
      if (this.receiveFileListener != null) {
        this.receiveFileListener(true, false);
      }
      this.isHandleFileList = false;
      this.isSendingCmd = false;
      this.pollAndSendCommand();
      this.commandBuffer = null;
      this.commandSize = 0;
      this.commandOffset = 0;
      this.commandStatus = 0;
      this.rdtCommand = 0;
      this.isAllRdtDataReceived = false;
      return;
    } else if (err === "empty" && this.rdtCommand == 6) {
      // 循环每个40秒去取一次数据
      clearTimeout(this.requestSdfilesTimeout);
      if (this.requestSdcardFilesRegularly) {
        this.requestSdfilesTimeout = setTimeout(() => this.startRequestSdcardFiles(), 40000);
      }
      // 返回的列表没有数据 清空的目的是为了防止原来有数据，删除SD卡数据后没数据了
      this.timeItems = [];
      this.isHandleFileList = false;
      if (this.receiveFileListener != null) {
        LogUtil.logOnAll(TAG, `timeItemList updated: length->${ this.timeItems.length }`);
        this.receiveFileListener(true);
      }
      this.isSendingCmd = false;
      this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
      this.commandBuffer = null;
      this.commandSize = 0;
      this.commandOffset = 0;
      this.commandStatus = 0;
      this.rdtCommand = 0;
      this.isAllRdtDataReceived = false;
      return;
    }
    this.commandBuffer = null;
    this.commandSize = 0;
    this.commandOffset = 0;
    this.commandStatus = 0;

    this.isAllRdtDataReceived = false;
    this.isOnCmdError = true;
    this.progress = 0;
    this.rdtCommand = 0;
    if (err === 'empty' && this.isHandleFileList) {
      this.receiveFileListener && this.receiveFileListener(false);
    }

    clearTimeout(this.onErrorRetryTimeout);
    this.onErrorRetryTimeout = setTimeout(() => { // 错误以后隔3s再去从任务队列拉数据 并屏蔽此时的错误信息接收
      this.handleErrorRetry();
    }, 3000);

  }

  handleErrorRetry(needPollNextCommand = true) {
    LogUtil.logOnAll(TAG, "执行rdt命令出错了，置空状态");
    if (this.isHandleFileList) { // 文件列表
      clearTimeout(this.requestSdfilesTimeout);
      this.receiveFileListener && this.receiveFileListener(false);// 接收数据出错了。
      this.isHandleFileList = false;
      if (this.requestSdcardFilesRegularly) {
        this.requestSdfilesTimeout = setTimeout(() => this.startRequestSdcardFiles(), 40000);
      }
    }
    this.isOnCmdError = false;

    this.isSendingCmd = false;
    if (this.downloadingVideoTimestamp != 0) {

      this._notifySdcardVideoDownloadStatus(this.downloadingVideoTimestamp, false);
      this.downloadingVideoTimestamp = 0;
    }
    if (this.downloadingImgTimestamp != 0) {
      LogUtil.logOnAll(TAG, `下载视频缩略图失败：${ this.downloadingImgTimestamp }`);
      this._notifySdcardThumbDownloadStatus(this.downloadingImgTimestamp, false);
      this.downloadingImgTimestamp = 0;
    }
    if (needPollNextCommand) {
      this.pollAndSendCommand();
    }
  }

  resetRequestStatus() {
    if (this.isHandleFileList) { // 文件列表
      clearTimeout(this.requestSdfilesTimeout);
      this.receiveFileListener && this.receiveFileListener(false);// 接收数据出错了。
      this.isHandleFileList = false;
      if (this.requestSdcardFilesRegularly) {
        this.requestSdfilesTimeout = setTimeout(() => this.startRequestSdcardFiles(), 40000);
      }
    }
    this.isHandleFileList = false;
    this.isSendingCmd = false;
    this.commandBuffer = null;
    this.commandSize = 0;
    this.commandOffset = 0;
    this.commandStatus = 0;
    this.rdtCommand = 0;
    this.isAllRdtDataReceived = false;
    this.isOnCmdError = false;
  }

  parseDeviceInfo() {
    // ignore
  }

  parseVideoFile(data) {
    if (data == null || this.downloadingVideoTimestamp == 0) {
      throw "invalid final data";
    }
    
    LogUtil.logOnAll(TAG, "receive vidoe file");
    
    LogUtil.logOnAll(TAG, `download video:${ this.downloadingVideoTimestamp } success`);
    let filePath = SdFileManager.getSdcardFilePath(this.downloadingVideoTimestamp, true);

    this.splitDownloadDatas(data)
      .then(() => {
        // 分段转存完毕了。
        Service.miotcamera.convertG711VideoIntoAACVideo(Host.file.storageBasePath + filePath, { sampleRate: CameraConfig.is16K(Device.model) ? MISSSampleRate.FLAG_AUDIO_SAMPLE_16K : MISSSampleRate.FLAG_AUDIO_SAMPLE_8K, channel: MISSAudioChannel.FLAG_AUDIO_CHANNEL_MONO, bitRate: MISSAudioChannel.FLAG_AUDIO_BIT_RATE_16K })
          .then(() => {
            LogUtil.logOnAll(TAG, "convert file success");
            this._notifySdcardVideoDownloadStatus(this.downloadingVideoTimestamp, true);
            LogUtil.logOnAll(TAG, "sdcard video save && convert success:", this.downloadingVideoTimestamp);
            this.downloadingVideoTimestamp = 0;
            this.isSendingCmd = false;
            this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
          })
          .catch((error) => {
            LogUtil.logOnAll(TAG, `convert video file failed: ${JSON.stringify(error)}`);
            this._notifySdcardVideoDownloadStatus(this.downloadingVideoTimestamp, true);
            LogUtil.logOnAll(TAG, "sdcard video save && convert success:", this.downloadingVideoTimestamp);
            this.downloadingVideoTimestamp = 0;
            this.isSendingCmd = false;
            this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
          });
      })
      .catch((err) => {
        this._notifySdcardVideoDownloadStatus(this.downloadingVideoTimestamp, false);
        LogUtil.logOnAll(TAG, "传输&转换视频文件出错");
        this.isSendingCmd = false;
        this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
      });
  }

  splitDownloadDatas(data) { // 视频数据分十次写入。
    return new Promise((resolve, reject) => {
      InteractionManager.runAfterInteractions(() => {
        let filePath = SdFileManager.getSdcardFilePath(this.downloadingVideoTimestamp, true);

        try {
          let startTime = new Date().getTime();
          let base64Str = base64js.fromByteArray(data);
          LogUtil.logOnAll(TAG, `convert base64 str:${new Date().getTime() - startTime}`);
          startTime = new Date().getTime();
          Host.file.writeFileThroughBase64(filePath, base64Str)
            .then(() => {
              LogUtil.logOnAll(TAG, `send file cost:${new Date().getTime() - startTime}`);
              resolve();
            })
            .catch(() => {
              LogUtil.logOnAll(TAG, `send file cost:${new Date().getTime() - startTime}`);
              reject();
            });
        } catch (exception) {
          LogUtil.logOnAll(TAG, "error while append file:", JSON.stringify(exception));
          reject();
        }
      })
    });
    
    
  }

  tryInsertVideoIntoAlbum(filePath, videoTimestamp) {
    
    if (this.downloadItemList.indexOf(videoTimestamp) == -1) { // 返回
      return;
    }
    AlbumHelper.saveToAlbum(filePath, true)
      .then(() => {
        this._notifySdcardVideoDownloadStatus(this.downloadingVideoTimestamp, true);
        LogUtil.logOnAll(TAG, "save file success");
        this.downloadingVideoTimestamp = 0;
      })
      .catch((error) => {
        LogUtil.logOnAll(TAG, `save video to album failed: ${ JSON.stringify(error) }`);
        this._notifySdcardVideoDownloadStatus(this.downloadingVideoTimestamp, false);
        this.downloadingVideoTimestamp = 0;
      });
  }

  _notifySdcardVideoDownloadStatus(timestamp, isDownloadSuccess) {
    if (this.videoFileDownloadListener) {
      this.videoFileDownloadListener(timestamp, isDownloadSuccess);
    }
  }

  _notifySdcardThumbDownloadStatus(timestamp, isDownloadSuccess) {
    if (this.onFileDownloadedListener) {
      this.onFileDownloadedListener(timestamp, isDownloadSuccess);
    }
  }

  parsePicFile(data) {
    LogUtil.logOnAll(TAG, `下载视频缩略图成功，待转存到sdcard${ this.downloadingImgTimestamp }`);
    if (data == null || this.downloadingImgTimestamp == 0) {
      throw `invalid final data: is date null?${ data == null } downloadingImgTimestamp==0 ?${ this.downloadingImgTimestamp == 0 }`;
    }

    LogUtil.logOnAll(TAG, `download image:${ this.downloadingImgTimestamp } success`);
    let filePath = `${ filepathPrefix + this.downloadingImgTimestamp }.jpg`;
    let timestamp = this.downloadingImgTimestamp;
    // 这里究竟是一次写入 base64 还是分多次写入 写入性能怎么样
    Host.file.writeFileThroughBase64(filePath, base64js.fromByteArray(data)).then((isSuccess) => {
      if (isSuccess) {
        this.sdcardFileThumbMaps[timestamp] = filePath;
        this._notifySdcardThumbDownloadStatus(this.downloadingImgTimestamp, true);
        LogUtil.logOnAll(TAG, `下载缩略图:${ this.downloadingImgTimestamp } 成功并写入成功`);

        this.downloadingImgTimestamp = 0;
        this.isSendingCmd = false;
        this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
      } else {
        LogUtil.logOnAll(TAG, `下载缩略图:${ this.downloadingImgTimestamp } 成功但是写入失败`);
        this._notifySdcardThumbDownloadStatus(this.downloadingImgTimestamp, false);
        this.downloadingImgTimestamp = 0;
        this.isSendingCmd = false;
        this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
      }

    }).catch(() => {
      // 写入失败了
      this._notifySdcardThumbDownloadStatus(this.downloadingImgTimestamp, false);
      LogUtil.logOnAll(TAG, `下载缩略图:${ this.downloadingImgTimestamp } 成功但是转存失败了。`);

      this.downloadingImgTimestamp = 0;
      this.isSendingCmd = false;
      this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
    });

  }

  /**
   * 获取完整的实时流缩略图
   *
   * @param data
   */
  parseLivePicFile(data) {
    LogUtil.logOnAll(TAG, `下载视频缩略图成功`);
    if (data == null) {
      throw `invalid final data: is date null?${ data == null }`;
    }

    LogUtil.logOnAll(TAG, `download image: success`);
    let filePath = SNAPSHOT_SETTING_IMG_PATH_V2;
    Host.file.isFileExists(filePath)
      .then((result) => {
        if (result) {
          Host.file.deleteFile(filePath)
            .then(() => {
              this.saveImageToLocal(filePath,data);
            })
            .catch(() => {
              this.saveImageToLocal(filePath,data);
            });
        } else {
          this.saveImageToLocal(filePath,data);
        }
      })
      .catch(() => {
        Host.file.deleteFile(filePath)
          .then(() => {
            this.saveImageToLocal(filePath,data);
          })
          .catch(() => {
            this.saveImageToLocal(filePath,data);
          });
      });
  }

  saveImageToLocal(filePath,data){
    // 这里究竟是一次写入 base64 还是分多次写入 写入性能怎么样
    Host.file.writeFileThroughBase64(filePath, base64js.fromByteArray(data)).then((isSuccess) => {
      if (isSuccess) {
        VersionUtil.settingsImgPathV2 = SNAPSHOT_SETTING_IMG_PATH_V2;
      }

    }).catch((err) => {
      // 写入失败了
      console.log("")
    });
  }
  parseSdcardFiles(data) {

    this.timeItems = [];// 清空数据
    if (data == null) {
      throw "invalid file list data";
    }
    let pos = 0;
    while (pos < data.length) {
      let timeItem = this.parseTimeItem(data, pos);
      if ((timeItem.startTime != 0) && (timeItem.startTime <= timeItem.endTime)) {
        this.timeItems.push(timeItem);
      }
      pos += 8;
    }
    this.timeItems.sort((a, b) => { return a.startTime - b.startTime; });

    
    clearTimeout(this.requestSdfilesTimeout);
    if (this.requestSdcardFilesRegularly) {
      this.requestSdfilesTimeout = setTimeout(() => this.startRequestSdcardFiles(), 40000);
    }

    if (CameraConfig.shouldRequestEventType(Device.model)) {
      if (this.receiveFileListener != null) {
        LogUtil.logOnAll(Tag, `timeItemList updated: length->${ this.timeItems.length }`);
        // 收到了新的sd卡，先merge一次，再通知出去；
        this._mergeTimeItemEventTypes(this.eventTypeAll);
        this.receiveFileListener(true);
      } // 先通知刷新一下。
      // 这个事件的下载会阻塞车机缩略图的下载
      if (CameraConfig.debugShowEvent()) {
        this.startRequestSdcardFileEventTypes();
        this.sendEventTypeBeginTime = new Date().getTime();
      } else {
        if (Package.entryInfo.mobileType != 'car') {
          console.log("=============this is not car plugin");
          this.startRequestSdcardFileEventTypes();
          this.sendEventTypeBeginTime = new Date().getTime();
        } else {
          this.isHandleFileList = false;
          this.isSendingCmd = false;
          this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
        }
      }
    } else {
      this.isHandleFileList = false;
      if (this.receiveFileListener != null) {
        LogUtil.logOnAll(TAG, `timeItemList updated: length->${ this.timeItems.length }`);
        this.receiveFileListener(true);
      }
      this.isSendingCmd = false;
      this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
    }
  }

  parseEventTypes(data) {
    // LogUtil.logOnAll(Tag, "合并sdcard事件");
    let position = 4;
    let total = this.byteArrayToInt(data, position); // 4 - 7是total的索引文件个数
    position = position + 4;
    let index = this.byteArrayToInt(data, position); // 8 - 11 时第几个索引文件
    position = position + 4; // 12
    position = position + 32;// 模仿android的写法  
    // let length = this.byteArrayToInt(data, position);// 16 - 19 24字节之后的数据长度
    // position = position + 4;
    // let error = this.byteArrayToInt(data, position); // 20 -23 错误码
    // position = position + 4;

    // position = position + 128; // 24开始 头128个字节都是事件文件头
    
    let eventTypes = {};
    while (position <= data.length - 32) {
      let timestamp = this.byteArrayToLong(data, position);// 单位 s的事件戳
      
      let eventType = this.byteArrayToInt(data, position + 8);
      if (timestamp != 0) {
        let type = eventTypes[timestamp * 1000];
        if (type != null) {
          let existedPriority = SD_PRIORITY[type];
          let currentPriority = SD_PRIORITY[eventType];
          if (existedPriority < currentPriority) {
            eventTypes[timestamp * 1000] = eventType;
          }
        } else {
          eventTypes[timestamp * 1000] = eventType;
        }
      }
      // this._updateTimeItemEventType(timestamp * 1000, eventType);
      position = position + 32;
    }
    let array = [];
    let keys = Object.keys(eventTypes);
    for (let i = 0; i < keys.length; i++) {
      array.push({ startTime: keys[i], eventType: eventTypes[keys[i]] });
    }
    
    this.eventTypeTemp.push(...array);
    if (index >= total) { // 所有的事件索引均已接受完毕
      let mergeEventTypeStartTime = new Date().getTime();
      this.eventTypeTemp.sort((type1, type2) => {
        return type1.startTime - type2.startTime;
      });
      this.eventTypeAll = this.eventTypeTemp;
      this._mergeTimeItemEventTypes(this.eventTypeAll);
      if (this.receiveFileListener != null) {
        this.receiveFileListener(true, false);
      }
      this.isHandleFileList = false;
      this.isSendingCmd = false;
      this.pollAndSendCommand();// 下载完毕，继续执行下一个任务。
      return true;

    } else {
      return false;
    }
  }

  _mergeTimeItemEventTypes(eventTypes) {
    let eventTypeIndex = 0;
    let recordIndex = 0;
    while (eventTypeIndex < eventTypes.length && recordIndex < this.timeItems.length) {
      let eventType = eventTypes[eventTypeIndex];
      let timeItem = this.timeItems[recordIndex];
      if (eventType.startTime < timeItem.startTime) {
        eventTypeIndex++;
      } else if (eventType.startTime >= timeItem.endTime) {//解决上一个视频的endTime == 下一个视频的startTime，而且eventType 也是startTime，导致跳过当前视频，不设置标记为的问题
        recordIndex++;
      } else {
        if (timeItem.eventType == -1) {
          timeItem.eventType = eventType.eventType;
        } else {
          let existedPriority = SD_PRIORITY[timeItem.eventType]; // 已有的
          let currentPriority = SD_PRIORITY[eventType.eventType]; // 待合并的
          if (existedPriority <= currentPriority) {
            timeItem.eventType = eventType.eventType;
          }
        }
        eventTypeIndex++;
      }
    }
  }

  // sdcard filelist相关功能begin

  getTimeItems() {
    return this.timeItems;
  }

  getTimeItemDays() { // 按照日期、小时来做区分。
    let dateTime = new Date();
    this.timeItemDayPositionMap = {};// 清空
    let timeItemDays = [];
    if (this.timeItems == null || this.timeItems.length <= 0) {
      return null;
    }
    let timeItemDay = null;
    let timeItemHour = null;
    for (let i = 0; i < this.timeItems.length; i++) {
      let timeItem = this.timeItems[i];
      dateTime.setTime(timeItem.startTime);
      let year = dateTime.getFullYear();
      let month = dateTime.getMonth() + 1;// 0-11 要加1
      let day = dateTime.getDate();// 1-31 getday返回的是0-6 
      let hour = dateTime.getHours();
      let tag = `${ year }-${ month }-${ day }`;// 标识某一天的 作为key缓存下来
      if (timeItemDay != null && timeItemDay.day != day) {
        timeItemDay = null;
        timeItemHour = null;
      }
      if (timeItemHour != null && timeItemHour.hour != hour) {
        timeItemHour = null;
      }
      if (timeItemDay == null) {
        timeItemDay = {};
        timeItemDay.startTime = timeItem.startTime;
        timeItemDay.day = day;
        timeItemDay.tag = tag;
        timeItemDay.month = month;
        timeItemDay.timeItemHourList = [];
        timeItemDays.push(timeItemDay);
      }
      if (timeItemHour == null) {
        timeItemHour = {};
        timeItemHour.hour = hour;
        timeItemHour.startTime = timeItem.startTime;
        timeItemHour.eventType = timeItem.eventType;
        timeItemHour.timeItemList = [];
        timeItemDay.timeItemHourList.push(timeItemHour);
      }
      timeItemHour.timeItemList.push(timeItem);
    }
    if (timeItemDays != null) {
      for (let index = 0; index < timeItemDays.length; index++) {
        this.timeItemDayPositionMap[timeItemDays[index].tag] = timeItemDays[index];
      }
    }
    return timeItemDays;
  }

  getTimeItemDay(timeStamp) {
    return this.timeItemDayPositionMap[timeStamp];
  }

  // 按天、按小视频连续
  getTimeItemContinuous(ignoreSubTimeItemList = true) {

    // 按日期，连续视频归类为一个视频
    let dateTime = new Date();
    this.timeItemDayContinuousPositionMap = {};// 清空
    let timeItemDays = [];
    if (this.timeItems == null || this.timeItems.length <= 0) {
      return null;
    }

    let timeItemDay = null;
    let timeContinuous = null;
    let timeContinuousPre = null;
    let timeContinuousVideoDuration = 0;
    for (let i = 0; i < this.timeItems.length; i++) {
      let timeItem = this.timeItems[i];
      dateTime.setTime(timeItem.startTime);
      let year = dateTime.getFullYear();
      let month = dateTime.getMonth() + 1;// 0-11 要加1
      let day = dateTime.getDate();// 1-31 getday返回的是0-6
      let hour = dateTime.getHours();
      let tag = `${ year }-${ month }-${ day }`;// 标识某一天的 作为key缓存下来
      if (timeItemDay != null && timeItemDay.day != day) {
        timeItemDay = null;
        timeContinuous = null;
        timeContinuousPre = null;
        timeContinuousVideoDuration = 0;
      }

      if (timeContinuousPre != null) {
        let time = Math.abs(timeItem.startTime - timeContinuousPre.endTime);
        if (time > 2000) {
          // 开始时间与前一个的结束时间，连续一般是相等，若偏差在3秒内也仍为连续
          timeContinuous = null;
          timeContinuousVideoDuration = 0;

        }
      }

      // 按天分割
      if (timeItemDay == null) {
        timeItemDay = {};
        timeItemDay.startTime = timeItem.startTime;
        timeItemDay.day = day;
        timeItemDay.tag = tag;
        timeItemDay.month = month;
        timeItemDay.timeItemContinuousList = [];
        timeItemDays.push(timeItemDay);
      }

      if (timeContinuous == null) {
        timeContinuousVideoDuration = 0;
        timeContinuous = {};
        timeContinuous.duration = timeItem.duration;
        timeContinuous.startTime = timeItem.startTime;
        timeContinuous.eventType = timeItem.eventType;
        // 用于存储需要下载的视频缩略图
        timeContinuous.images = [];
        timeContinuous.timeItemList = [];
        timeItemDay.timeItemContinuousList.push(timeContinuous);
      }
      timeContinuousVideoDuration = timeContinuousVideoDuration + timeItem.duration;
      timeContinuous.duration = timeContinuousVideoDuration;
      timeContinuous.endTime = timeContinuous.startTime + timeContinuousVideoDuration;
      timeContinuousPre = timeItem;
      timeContinuous.timeItemList.push(timeItem);

    }


    // 遍历给添加images
    for (let i = 0; i < timeItemDays.length; i++) {
      let itemDay = timeItemDays[i];
      if (!ignoreSubTimeItemList) {
        itemDay.timeItemContinuousList.sort((a, b) => a.startTime - b.startTime);
      } else {
        itemDay.timeItemContinuousList.sort((a, b) => b.startTime - a.startTime);
      }

      for (let j = 0; j < itemDay.timeItemContinuousList.length; j++) {
        let item = itemDay.timeItemContinuousList[j];
        // 不连续，处理当前timeContinuous的图片数据
        // 根据时长看需要展示多少张缩略图
        let images = [];
        let eventType = -1;
        if (item != null && item.timeItemList.length > 0) {
          let duration = item.duration;
          let length = item.timeItemList.length;
          if (duration < 300000) {
            // 小于五分钟，2张缩略图，可能只有一张
            if (length == 1) {
              images = [item.timeItemList[0]];
            } else {
              images = [item.timeItemList[0],item.timeItemList[length - 1]];
            }
          } else if (duration < 3600000) {
            images = this.divideImages(item.timeItemList,4);
          } else {
            images = this.divideImages(item.timeItemList,10);
          }
          eventType = item.timeItemList.reduce((max, obj) => obj.eventType > max ? obj.eventType : max, item.timeItemList[0].eventType);

        }
        item.images = images;
        item.eventType = eventType;
        if (ignoreSubTimeItemList) {
          item.timeItemList = [];
        }

      }
    }
    // timeItemDays 按天分割的书
    return timeItemDays;


  }
  getTimeItemContinuousBak() {

    // 按日期，连续视频归类为一个视频
    let dateTime = new Date();
    this.timeItemDayContinuousPositionMap = {};// 清空
    let timeItemDays = [];
    if (this.timeItems == null || this.timeItems.length <= 0) {
      return null;
    }
    console.log("allData:",JSON.stringify(this.timeItems));

    let timeItemDay = null;
    let timeContinuous = null;
    let timeContinuousPre = null;
    let timeContinuousVideoDuration = 0;
    for (let i = 0; i < this.timeItems.length; i++) {
      let timeItem = this.timeItems[i];
      dateTime.setTime(timeItem.startTime);
      let year = dateTime.getFullYear();
      let month = dateTime.getMonth() + 1;// 0-11 要加1
      let day = dateTime.getDate();// 1-31 getday返回的是0-6
      let hour = dateTime.getHours();
      let tag = `${ year }-${ month }-${ day }`;// 标识某一天的 作为key缓存下来
      if (timeItemDay != null && timeItemDay.day != day) {
        timeItemDay = null;
        timeContinuous = null;
        timeContinuousPre = null;
        timeContinuousVideoDuration = 0;
      }
      // if (timeContinuous != null && timeContinuous.startTime != hour) {
      //   timeContinuous = null;
      // }
      if (timeContinuousPre != null) {
        let time = timeItem.startTime - timeContinuousPre.endTime;
        if (time > (60 * 1000 + 3000) || time < (60 * 1000 - 3000)) {
          // 判断是否与上一个视频连续
          // 两个视频之间的时间差，连续的话，理论上应该是1分钟
          // 正负偏差3秒 仍然认为是连续的
          timeContinuous = null;
          timeContinuousVideoDuration = 0;

        }
      }

      // 按天分割
      if (timeItemDay == null) {
        timeItemDay = {};
        timeItemDay.startTime = timeItem.startTime;
        timeItemDay.day = day;
        timeItemDay.tag = tag;
        timeItemDay.month = month;
        timeItemDay.timeItemContinuousList = [];
        timeItemDays.push(timeItemDay);
      }

      if (timeContinuous == null) {
        timeContinuousVideoDuration = 0;
        timeContinuous = {};
        timeContinuous.duration = timeItem.duration;
        timeContinuous.startTime = timeItem.startTime;
        timeContinuous.eventType = timeItem.eventType;
        // 用于存储需要下载的视频缩略图
        timeContinuous.images = [];
        timeContinuous.timeItemList = [];
        timeItemDay.timeItemContinuousList.push(timeContinuous);
      }
      timeContinuousVideoDuration = timeContinuousVideoDuration + timeItem.duration;
      timeContinuous.duration = timeContinuousVideoDuration;
      timeContinuousPre = timeItem;
      timeContinuous.timeItemList.push(timeItem);

    }

    // 遍历给添加images
    for (let i = 0; i < timeItemDays.length; i++) {
      let itemDay = timeItemDays[i];
      itemDay.timeItemContinuousList.sort((a, b) => b.startTime - a.startTime);

      for (let j = 0; j < itemDay.timeItemContinuousList.length; j++) {
        let item = itemDay.timeItemContinuousList[j];
        // 不连续，处理当前timeContinuous的图片数据
        // 根据时长看需要展示多少张缩略图
        let images = [];
        let eventType = -1;
        if (item != null && item.timeItemList.length > 0) {
          let duration = item.duration;
          let length = item.timeItemList.length;
          if (duration < 300000) {
            // 小于五分钟，2张缩略图，可能只有一张
            if (length == 1) {
              images = [item.timeItemList[0]];
            } else {
              images = [item.timeItemList[0],item.timeItemList[length - 1]];
            }
          } else if (duration < 3600000) {
            images = this.divideImages(item.timeItemList,4);
          } else {
            images = this.divideImages(item.timeItemList,10);
          }
          eventType = item.timeItemList.reduce((max, obj) => obj.eventType > max ? obj.eventType : max, item.timeItemList[0].eventType);

        }
        item.images = images;
        item.eventType = eventType;
      }
    }

    // timeItemDays 按天分割的书
    return timeItemDays;


  }

  divideImages(arr, count) {
    // 首尾各一张，中间取4张
    // 获取首尾元素
    const first = arr[0];
    const last = arr[arr.length - 1];

    // 获取中间部分
    const middle = arr.slice(1, -1);

    // 计算中间部分的长度
    const middleLength = middle.length;

    // 计算每段的长度
    const segmentLength = Math.floor(middleLength / count);

    // 初始化结果数组
    const images = [first];

    // 从中间部分均匀取出4个数据
    for (let i = 0; i < count; i++) {
      const startIndex = i * segmentLength;
      images.push(middle[startIndex]);
    }

    // 添加最后一个元素
    images.push(last);
    return images;
  }

  parseTimeItem(data, pos) {
    let startTime = this.byteArrayToInt(data, pos) * 1000;
    let duration = (data[pos + 4] & 0xFF) * 1000;
    let motion = data[pos + 5] & 0xFF;
    let save = data[pos + 6] & 0xFF;
    let isDeleting = data[pos + 7] & 0xFF;
    if (isDeleting != 0) {
      startTime = 0;
    }
    if (duration <= 0) {
      duration = 60000;
    }
    let timeItem = {};
    timeItem.startTime = startTime;
    timeItem.duration = duration;
    timeItem.save = save;
    timeItem.motion = motion == 1;
    timeItem.eventType = -1;
    if (VersionUtil.judgeIsV1(Device.model) || VersionUtil.judgeIs009(Device.model) || VersionUtil.judgeIs019(Device.model)) {
      if (motion == 1) {
        timeItem.eventType = EventTypeConfig.SD_OBJECT_MOTION;// 针对v1做一下特别的处理。
      }
    }
    timeItem.endTime = startTime + duration;
    return timeItem;
  }

  byteArrayToInt(data, position) {
    return (0xff & data[position]) | (0xff & data[position + 1]) << 8 | (0xff & data[position + 2]) << 16 | (0xff & data[position + 3]) << 24;
  }

  byteArrayToLong(data, position) {
    return (0xff & data[position] | (0xff & data[position + 1]) << 8 | (0xff & data[position + 2]) << 16 | (0xff & data[position + 3]) << 24 | (0xff & data[position + 4]) << 32 | (0xff & data[position + 5]) << 40 | (0xff & data[position + 6]) << 48 | (0xff & data[position + 7]) << 56);
  }

  intToByteArray(data) {
    return new Uint8Array([data & 0xff, (data >> 8) & 0xff, (data >> 16) & 0xff, (data >> 24) & 0xff]);
  }

  bindReceiveFilesListener(receiveFileListener) {
    this.receiveFileListener = receiveFileListener;
  }

  startRequestSdcardFilesRegularly() {
    // this.requestSdfilesInterval = setInterval(() => {
    //   this.startRequestSdcardFiles();
    // }, 40000)//每隔40s请求一次
    this.requestSdcardFilesRegularly = true;
    this.startRequestSdcardFiles();
  }

  startRequestSdcardFiles() {
    // LogUtil.logOnAll(TAG, "开始请求刷新sdcard列表");
    Service.miotcamera.bindRDTDataReceiveCallback(kRDTDataReceiveCallBackName);
    let data = [];
    data[0] = 6;
    data[1] = 4;
    this.pushCommand(data);// 往里面丢
    this.pollAndSendCommand();// 尝试消费
  }

  
  startRequestSdcardFileEventTypes() {

    LogUtil.logOnAll(Tag, '开始刷新sdcard事件类型');
    if (new Date().getTime() - this.lastMatchEventTime < 50000) {
      LogUtil.logOnAll(Tag, '请求sdcard事件类型 时间太短了。');
      this.isHandleFileList = false;
      this.isSendingCmd = false;//
      this.pollAndSendCommand();
      return;
    }
    LogUtil.logOnAll(Tag, '插入sdcard事件类型请求');

    this.eventTypeTemp = [];
    this.lastMatchEventTime = new Date().getTime();

    let sdcardDays = this.getTimeItemDays();
    let params = [];
    let dateTime = new Date();
    if (sdcardDays == null || sdcardDays.length == 0 || CameraConfig.isXiaomiCamera(Device.model) || CameraConfig.sdcardEventNoDate(Device.model)) {
      let param = {};
      param.mac = "F1F2F3F4F5F6";
      param.time = "";
      param.channel = this.channel;
      params.push(param);
    } else {// 如果没有本地缓存的该怎么办
      let dayTags = sdcardDays.map((item, index) => {
        let startTime = item.startTime;
        dateTime.setTime(startTime);
        let year = dateTime.getFullYear();
        let month = dateTime.getMonth() + 1;
        let day = dateTime.getDate();
        return year + "" + (month < 10 ? ("0" + month) : month) + "" + (day < 10 ? ("0" + day) : day);
      });
      params = dayTags.map((item, index) => {
        let param = {};
        param.mac = "F1F2F3F4F5F6";
        param.time = item;
        param.channel = this.channel;
        return param;
      });

    }
    // 遍历params，要优先让尾巴的事件先发出，由于先插入的数据，在后面，所以访问params 顺序访问插入队首即可。

    for (let i = 0; i < params.length; i++) {
      let data = [];
      data[0] = 11;
      data[1] = JSON.stringify(params[i]);
      this.insertCommandAtFront(data);
    }

    // let data = [];
    // data[0] = 11; // eventType
    // data[1] = JSON.stringify(param);

    
    // this.insertCommandAtFront(data);
    this.isSendingCmd = false;
    this.pollAndSendCommand();// 手动插入队列，并执行这条任务
  }

  startDeleteFiles(timeItems, useSpec = false) {
    return new Promise((resolve, reject) => {
      if (timeItems == null || timeItems.length == 0) {
        reject(-1);
      } else {
        this.doDeleteFiles(resolve, reject, timeItems, 0, useSpec);
      }
    });
  }


  doDeleteFiles(resolve, reject, timeItems, offset, useSpec = false) {// SD卡文件按ID单个删除

    if (offset >= timeItems.length) { // 删除完毕
      resolve(1);
      return;
    }

    let start = offset;
    let once = 60;
    let total = timeItems.length;
    let end = 0;
    if (total < once) {
      end = total;
      start = 0;
    } else {
      end = start + once;
      if (end >= total) {
        end = total;
      }
    }
    let subList = timeItems.slice(start, end);
    let cmd = subList[0].save != 0 ? "deleteSaveVideo" : "deleteVideo";
    let timeList = [];
    for (let i = 0; i < subList.length; i++) {
      timeList.push(subList[i].startTime / 1000);
    }
    if (useSpec) {
      // 车机端无法通过RPC删除
      // {did: 1, siid: 1, aiid: 1, in: [17,"shanghai"]}
      let value = {
        channel: 0,
        timeList: timeList
      };
      let inValue = {
        piid: 7,
        value: JSON.stringify(value)
      };
      let params = {
        did: Device.deviceID,
        siid: 4,
        aiid: 3,
        in: [inValue]
      };
      Service.spec.doAction(params).then((res) => {
        console.log("======= doAction delete success", res);
        this.deleteLocalTimeItems(timeList);
        offset = end;
        this.doDeleteFiles(resolve, reject, timeItems, offset, useSpec);// 继续删除
      }).catch((err) => {
        console.log("======== doAction delete fail:",err);
        reject(err);
      });
    } else {
      RPC.callMethod(cmd, timeList)
        .then(() => {
          this.deleteLocalTimeItems(timeList);
          offset = end;
          this.doDeleteFiles(resolve, reject, timeItems, offset);// 继续删除
        })
        .catch((err) => { // 删除失败
          console.log("========delete fail:",err);
          reject(err);
        });
    }

  }

  deleteLocalTimeItems(timeList) {
    for (let startTime of timeList) {
      for (let i = this.timeItems.length - 1; i >= 0; i--) {
        if (this.timeItems[i].startTime / 1000 == startTime) {
          this.timeItems.splice(i, 1);// 删除
        }
      }
    }
  }

  startSaveFiles(timeItems) {
    return new Promise((resolve, reject) => {
      if (timeItems == null || timeItems.length == 0) {
        reject(-1);
      } else {
        this.doSaveFiles(resolve, reject, timeItems, 0);
      }
    });
  }

  doSaveFiles(resolve, reject, timeItems, offset) {
    if (offset >= timeItems.length) { // 保存
      resolve(1);
      return;
    }

    let start = offset;
    let once = 50;
    let total = timeItems.length;
    let end = 0;
    if (total < once) {
      end = total;
      start = 0;
    } else {
      end = start + once;
      if (end >= total) {
        end = total;
      }
    }
    let subList = timeItems.slice(start, end);
    let cmd = "saveVideo";
    let timeList = [];
    for (let i = 0; i < subList.length; i++) {
      if (subList[i].save != 1) {
        timeList.push(subList[i].startTime / 1000);
      }
    }
    RPC.callMethod(cmd, timeList)
      .then(() => {
        this.saveLocalTimeItems(timeList);
        offset = end;
        this.doSaveFiles(resolve, reject, timeItems, offset);// 继续保存
      })
      .catch((err) => { // 保存失败
        reject(err);
      });
  }

  saveLocalTimeItems(timeList) {
    for (let startTime of timeList) {
      for (let i = this.timeItems.length - 1; i >= 0; i--) {
        if (this.timeItems[i].startTime / 1000 == startTime) {
          this.timeItems[i].save = 1;
        }
      }
    }
  }


  // sdcard filelist相关功能end



  // sdcard thumbnailFiles 相关功能begin

  // 开始下载视频缩略图
  startDownloadVideoThumbs(timestampList) { // 开放给外部的方法 用来添加任务
    // first 往任务队列的头部放
    for (let timestamp of timestampList) {
      if (typeof (timestamp) != "number") {
        // 继续找下一个
        continue;
      }
      if (this.sdcardFileThumbMaps[timestamp] == null) { // 内存里没有 再看硬盘的
        //LogUtil.logOnAll(TAG, `timestamp:${ timestamp }`);
        Host.file.isFileExists(`${ filepathPrefix + timestamp }.jpg`).then((isSuccess) => {
          if (!isSuccess) {
            //LogUtil.logOnAll(TAG, `timestamp:${ timestamp } file is not found`);
            this.doDownloadImage(timestamp);// 没有就强制下载
          } else {
            // 已经下载成功了
            //LogUtil.logOnAll(TAG, `${ timestamp } found in diskCache`);

            this._notifySdcardThumbDownloadStatus(timestamp, true);
            // 放到缓存里
            this.sdcardFileThumbMaps[timestamp] = (`${ filepathPrefix + timestamp }.jpg`);
          }
        }).catch(() => {
          // 读取状态失败  走强制下载吧
          this.doDownloadImage(timestamp);
        });
      } else { // 内存里已经有了 继续下载下一个

        // LogUtil.logOnAll(TAG, `begin download:${ timestamp } found in memCache`);
        this._notifySdcardThumbDownloadStatus(timestamp, true);
      }
    }
  }

  bindOnFileDownloadedListener(onFileDownloadedListener) { // 每次有文件下载完成  就通知一下外面刷新一次
    this.onFileDownloadedListener = onFileDownloadedListener;
  }

  async setImageFilePath(timestampList) { // 这里只从内存里读取  下载的时候再从硬盘里读取
    return new Promise(async(resolve, reject) => {
      console.log("==============0000===============", timestampList.length);
      for (let i = 0; i < timestampList.length; i++) {
        let oneDayList = timestampList[i].data;
        for (let j = 0; j < oneDayList.length; j++) {
          let images = oneDayList[j].images;
          let length = images.length;
          for (let k = length - 1; k >= 0; k--) {
            let timestamp = images[k].startTime;
            let isSuccess = await Host.file.isFileExists(`${ filepathPrefix + timestamp }.jpg`);
            if (isSuccess) {
              this.sdcardFileThumbMaps[timestamp] = (`${ filepathPrefix + timestamp }.jpg`);
            }
          }
        }
      }
      console.log("==============1111===============", timestampList.length);

    });

  }

  getImageFilePath(timestamp) { // 这里只从内存里读取  下载的时候再从硬盘里读取
    if (this.sdcardFileThumbMaps[timestamp] == null) { // 内存里没有 再看硬盘的
      return null;
    } else { // 内存里已经有了 继续下载下一个
      return this.sdcardFileThumbMaps[timestamp];
    }
  }

  doDownloadImage(timestamp) {
    Service.miotcamera.bindRDTDataReceiveCallback(kRDTDataReceiveCallBackName);
    let data = [];
    data[0] = 5;// download thumb timestamp  前32bit/4byte 放命令号 
    data[1] = parseInt(timestamp / 1000);// 32位的足够包裹下timestamp/1000
    data[3] = this.channel;
    this.pushCommand(data);// 放进cmd队列里去
    LogUtil.logOnAll(TAG, `提交下载sdcard视频缩略图任务：${ timestamp * 1000 }`);
    this.pollAndSendCommand();// 尝试下载
  }

  // sdcard thumbnail files end

  pushCommand(dataArray) {
    if (dataArray == null || dataArray.length <= 1 || this.rdtCommandList == null) {
      return;// 数据不合理
    }
    this.rdtCommandList.push(dataArray);// 放进去的是array 放在队尾 后面取用shift 从队列头部取
  }

  // 只有sdcardEventType才有这个需求，其他的都不行。
  insertCommandAtFront(dataArray) {
    if (dataArray == null || dataArray.length <= 1 || this.rdtCommandList == null) {
      return;// 入队失败。
    }
    this.rdtCommandList.unshift(dataArray);// 数据插入到队列头部。优先执行
  }

  shiftCommand() {
    if (this.rdtCommandList == null || this.rdtCommandList.length == 0) { // 发完了或者destroy了
      return null;
    }
    return this.rdtCommandList.shift();
  }

  pollAndSendCommand() {
    if (this.isSendingCmd) {
      LogUtil.logOnAll(TAG, `还在执行其他任务 : isHandleFileList:${ this.isHandleFileList } downloading sdcard video:${ this.downloadingVideoTimestamp } downloading img:${ this.downloadingImgTimestamp } isSendingCmd:${ this.isSendingCmd } onCmdError:${ this.isOnCmdError }`);
      return;// 正在下发指令  滚蛋
    }

    let dataArray = this.shiftCommand();// 数据体
    if (dataArray == null) {
      return;
    }

    this.isSendingCmd = true;
    this.progress = 0;
    if (dataArray[0] == 6) { // 发送filelist指令的
      this.isHandleFileList = true;
      // let buf = new ArrayBuffer(8);
      // let data = new Uint32Array(buf);
      // data[0] = dataArray[0];
      // data[1] = 0;
      let buf = new ArrayBuffer(24);//3*8=24
      let data = new Uint32Array(buf);

      data[0] = dataArray[0];//cmd_type:6
      let chn = this.channel;
      let array = NumberUtil.intToByteArray(chn);
      let byteArray = new Uint8Array(buf);
      byteArray.set(array, 16);// 把数据往这里放到16

      let base64Data = base64js.fromByteArray(new Uint8Array(buf));
      LogUtil.logOnAll(TAG, "开始同步视频列表。");
      this.timeoutRequestFile && clearTimeout(this.timeoutRequestFile);
      this.timeoutRequestFile = setTimeout(() => {
        LogUtil.logOnAll(TAG, "request file timeout");
        this.handleRdtErr("timeout error");
      }, 10000);
      Service.miotcamera.sendRDTCommandToDevice(base64Data)
        .then(() => {
          LogUtil.logOnAll(TAG, 'send file list rdt cmd ok');

        })
        .catch(() => {
          this.isSendingCmd = false;
          this.isHandleFileList = false;
          LogUtil.logOnAll(TAG, "开始同步视频列表。发送命令错误");
          clearTimeout(this.requestSdfilesTimeout);
          this.receiveFileListener && this.receiveFileListener(false);
          if (this.requestSdcardFilesRegularly) {
            this.requestSdfilesTimeout = setTimeout(() => this.startRequestSdcardFiles(), 40000);// 40s后往任务队列加一个
          }
          this.pollAndSendCommand();
        });
    } else if (dataArray[0] == 5) {
      this.isHandleFileList = false;
      let timestamp = dataArray[1];
      let array = NumberUtil.intToByteArray(timestamp);
      // let buf = new ArrayBuffer(8 + array.length);
      let buf = new ArrayBuffer(24);
      let data = new Uint32Array(buf);
      data[0] = dataArray[0];// download thumb timestamp  前32bit/4byte 放命令号 

      data[1] = array.length;// 32bit/4byte 放 data的byte长度 这里timestamp是32位整数 长度是4
      let byteArray = new Uint8Array(buf);
      byteArray.set(array, 8);// 把数据往这里放；

      let chn = this.channel;
      let array_chn = NumberUtil.intToByteArray(chn);
      byteArray.set(array_chn, 16);// 把数据往这里放；

      this.downloadingImgTimestamp = timestamp * 1000;// 这里除以1000了的  要还原
      LogUtil.logOnAll(TAG, `开始下载图片 时间戳：${ this.downloadingImgTimestamp }`);
      
      let base64Data = base64js.fromByteArray(byteArray);

      Service.miotcamera.sendRDTCommandToDevice(base64Data)
        .then((retCode) => {
          LogUtil.logOnAll(TAG, '开始下载图片, 发送命令成功:', this.downloadingImgTimestamp);
          
        })
        .catch(() => {
          // 下载错误 不再重试，继续下载下一个
          LogUtil.logOnAll(TAG, `下载视频缩略图失败 发送命令出错 :${ this.downloadingImgTimestamp }`);
          this._notifySdcardThumbDownloadStatus(this.downloadingImgTimestamp, false);
          this.downloadingImgTimestamp = 0;
          this.isSendingCmd = false;
          this.pollAndSendCommand();// 继续下载
        });
    } else if (dataArray[0] == 11) {
      let param = dataArray[1];
      let paramArray = NumberUtil.stringToUtf8ByteArray(param);
      let array = new Uint8Array(paramArray);
      let buf = new ArrayBuffer(8 + array.length);
      let data = new Uint32Array(buf);
      data[0] = dataArray[0];// download thumb timestamp  前32bit/4byte 放命令号 

      data[1] = array.length;// 32bit/4byte 放 data的byte长度 这里timestamp是32位整数 长度是4
      let byteArray = new Uint8Array(buf);
      byteArray.set(array, 8);// 把数据往这里放；
      let base64Data = base64js.fromByteArray(byteArray);
      // LogUtil.logOnAll(Tag, "开始拉取sdcard事件类型数据");
      Service.miotcamera.sendRDTCommandToDevice(base64Data)
        .then((retCode) => {

          // LogUtil.logOnAll(Tag, "发送 拉取sdcard事件类型数据 命令成功");
        })
        .catch((err) => {
          LogUtil.logOnAll(Tag, "发送 拉取sdcard事件类型数据 命令失败。");
          this.isHandleFileList = false;
          this.isSendingCmd = false;
          this.pollAndSendCommand();// 继续发送rdt指令
        });
    } else if (dataArray[0] == 1) { // 下载sd卡文件
      this.isHandleFileList = false;
      let timestamp = dataArray[1];
      let array = NumberUtil.intToByteArray(timestamp);
      let buf = new ArrayBuffer(8 + array.length);
      let data = new Uint32Array(buf);
      data[0] = dataArray[0];// download thumb timestamp  前32bit/4byte 放命令号 

      data[1] = array.length;// 32bit/4byte 放 data的byte长度 这里timestamp是32位整数 长度是4
      let byteArray = new Uint8Array(buf);
      byteArray.set(array, 8);// 把数据往这里放；
      this.downloadingVideoTimestamp = timestamp * 1000;// 这里除以1000了的  要还原


      let base64Data = base64js.fromByteArray(byteArray);
      this.timeoutRequestFile && clearTimeout(this.timeoutRequestFile);
      this.timeoutRequestFile = setTimeout(() => {
        LogUtil.logOnAll(TAG, "request video timeout");
        this.handleRdtErr("timeout error");
      }, 10000);
      LogUtil.logOnAll(TAG, `开始下载sdcard视频:${ this.downloadingVideoTimestamp } `);

      Service.miotcamera.sendRDTCommandToDevice(base64Data)
        .then((retCode) => {
          LogUtil.logOnAll(Tag, "发送 下载sdcard视频 命令成功");
        
        })
        .catch(() => {
          // 下载错误 不再重试，继续下载下一个
          LogUtil.logOnAll(TAG, `发送 下载sdcard视频 命令失败${ this.downloadingVideoTimestamp }`);
          this._notifySdcardVideoDownloadStatus(this.downloadingVideoTimestamp, false);
          this.downloadingVideoTimestamp = 0;
          this.isSendingCmd = false;
          this.pollAndSendCommand();// 继续下载
        });
    }

  }

  // 获取离timeStamp最近的video，如果处于中间的空白区，则选择后面一个；如果选择的位置在第一个视频的位置之前，则选择第一个
  getTimeItemClosest(timeStamp) {
    if (timeStamp == 0)
      return null;
    if (this.timeItems.length == 0)
      return null;
    if (this.timeItems[this.timeItems.length - 1].endTime <= timeStamp) {
      return null;
    }
    for (let i = this.timeItems.length - 1; i >= 0; i--) {
      if (this.timeItems[i].endTime <= timeStamp) {
        if (i != this.timeItems.length - 1) {
          return this.timeItems[i + 1];
        }
      }
    }
    return this.timeItems[0];
  }

  getTimeItemClosestForV1(timeStamp) {
    if (timeStamp == 0)
      return null;
    if (this.timeItems.length == 0)
      return null;
    if (this.timeItems[this.timeItems.length - 1].endTime <= timeStamp) {
      return null;
    }
    for (let i = this.timeItems.length - 2; i >= 0; i--) {
      if (this.timeItems[i].endTime <= timeStamp) {
        if (i != this.timeItems.length - 1) {
          return this.timeItems[i + 1];
        }
      }
    }
    return this.timeItems[0];
  }

  getLastestItemStartTime() {
    if (this.timeItems.length == 0) {
      return 0;
    }
    return this.timeItems[this.timeItems.length - 1].startTime;
  }

  getLastestItemEndTime() {
    if (this.timeItems.length == 0) {
      return 0;
    }
    return this.timeItems[this.timeItems.length - 1].endTime;
  }

  addDownloadSdcardVideo(timeList) {
    Service.miotcamera.bindRDTDataReceiveCallback(kRDTDataReceiveCallBackName);
    Promise.all(timeList.map((item) => {
      return new Promise((resolve, reject) => {
        Host.file.isFileExists(`${ videoPathPrefix + Device.deviceID }/${ item }.mp4`)
          .then((hasVideo) => {
            if (hasVideo) {
              // this.videoFileDownloadListener && this.videoFileDownloadListener(item, DownloadVideoState.DOWNLOADED);
              this._notifySdcardVideoDownloadStatus(item, true);
            }
            resolve({ item, hasVideo });
          })
          .catch(() => {
            resolve({ item });
          });
      });
    })).then((resultArray) => {
      for (let i = 0; i < resultArray.length; i++) {
        let { item, hasVideo } = resultArray[i];
        if (hasVideo) {
          let index = timeList.indexOf(item);
          timeList.splice(index, 1);
        }
      }
      if (timeList.length == 0) {
        return;
      }
      for (let i = 0; i < timeList.length; i++) {
        let startTime = timeList[i] / 1000;
        let data = [];
        data[0] = 1;// download thumb timestamp  前32bit/4byte 放命令号 
        data[1] = parseInt(startTime);// 32位的足够包裹下timestamp/1000
        data[3] = this.channel; // channel号，不加下载出会错
        this.pushCommand(data);// 放进cmd队列里去
      }
      LogUtil.logOnAll(TAG, "尝试添加下载视频的任务");
      this.pollAndSendCommand();
      
    }).catch((error) => { // ignore
      LogUtil.logOnAll(TAG, `提交下载任务出错：${ JSON.stringify(error) }`);
    });
  }

  // 从下载列表里移除timestmaps
  clearAllDownloadTask(timestamps) {
    // if (this.downloadItemList == null) {
    //   return;
    // }
    // this.downloadItemList = [];
    if (timestamps == null || timestamps.length <= 0) {
      return;
    }
    for (let i = 0; i < timestamps.length; i++) {
      let timestamp = timestamps[i];
      let index = this.downloadItemList.indexOf(timestamp);
      this.downloadItemList.splice(index, 1);
      let rdtCommand = this.rdtCommandList.filter((value) => {
        return value[1] == timestamp / 1000;
      });
      if (rdtCommand != null && rdtCommand.length >= 1) {
        index = this.rdtCommandList.indexOf(rdtCommand[0]);
        if (index != -1) {
          this.rdtCommandList.splice(index, 1);
        }
      }
    }
  }

  bindDownloadVideoFileListener(videoFileListener) {
    this.videoFileDownloadListener = videoFileListener;
  }

  destroyInstance() { // 避免内存泄漏
    // clearInterval(this.requestSdfilesInterval);
    clearTimeout(this.requestSdfilesTimeout);
    clearTimeout(this.onErrorRetryTimeout);
    this.requestSdcardFilesRegularly = false;
    this.timeItems = null;// 放置timeItems的地方
    this.timestampList = null;// 存放要下载图片的timestamp list地方，通过unshift和pop模拟队列
    this.isDownloadingImg = null;
    this.sdcardFileThumbMaps = null;

    this.timeItemDayPositionMap = null;
    this.rdtCommandList = null;

    this.rdtListener && this.rdtListener.remove();
    this.connectionListener && this.connectionListener.remove();
    this.timeoutRequestFile && clearTimeout(this.timeoutRequestFile);
    this.connecteDelayDoTask && clearTimeout(this.connecteDelayDoTask);
    this.downloadItemList = null;

    SdFileManager.instance = null;
  }

}