'use strict';

import React from 'react';
import { isIphoneX, getStatusBarHeight } from 'react-native-iphone-x-helper';
import {
  ScrollView,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  BackHandler,
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  PermissionsAndroid,
  Platform,
  DeviceEventEmitter,
  Dimensions,
  NativeModules,
  PanResponder,
  FlatList, Modal
} from 'react-native';

import LinearGradient from 'react-native-linear-gradient';

import { Device, Service, PackageEvent, Host, System, API_LEVEL, DarkMode } from 'miot';
import AlbumHelper from "../util/AlbumHelper";

import ImageButton from "miot/ui/ImageButton";

import Toast from '../components/Toast';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';

import Orientation from 'react-native-orientation';

import StorageKeys from '../StorageKeys';
import CenterTimeView from '../ui/CenterTImeView';
import TimeScaleView3 from '../ui/TimeScaleView3';
import { EVENT_TYPE, EVENT_TYPE_COLOR } from './util/EventTypeConfig';
import Video from 'react-native-video';
import CloudVideoUtil, { CLOUD_VIDEO_STATUS } from './util/CloudVideoUtil';
import StackNavigationInstance, {
  SD_CLOUD_FORCE_LOAD_TAB_DATA,
  SD_CLOUD_FORCE_REFRESH_TAB,
  SD_CLOUD_STACK_NAVIGATION_ONBACK,
  SD_CLOUD_STACK_NAVIGATION_ONPAUSE,
  SD_CLOUD_STACK_NAVIGATION_ONRESUME
} from '../StackNavigationInstance';
import CameraConfig from '../util/CameraConfig';
import { ChoiceDialog } from 'mhui-rn';
import { MessageDialog } from "mhui-rn";
import { PixelRatio } from 'react-native';
import LoadingView from '../ui/LoadingView';
import CommonMsgDialog from '../ui/CommonMsgDialog';
import CameraPlayer from '../util/CameraPlayer';
import { HostEvent } from 'miot/Host';
import VersionUtil from '../util/VersionUtil';

import MHLottieSnapToolButton, { MHLottieSnapToolBtnDisplayState } from '../ui/animation/lottie-view/MHLottieSnapToolButton';
import MHLottieAudioToolButton, { MHLottieAudioToolBtnDisplayState } from '../ui/animation/lottie-view/MHLottieAudioToolButton';
import MHLottieSpeedToolButton, { MHLottieSpeedToolBtnDisplayState } from '../ui/animation/lottie-view/MHLottieSpeedToolButton';
import MHLottieFullScreenToolButton, { MHLottieFullScreenToolBtnDisplayState } from '../ui/animation/lottie-view/MHLottieFullScreenToolButton';

import MHLottieSnapLandscapeButton, { MHLottieSnapLandscapeBtnDisplayState } from '../ui/animation/lottie-view/MHLottieSnapLandscapeButton';
import StatusBarUtil from '../util/StatusBarUtil';

import { DescriptionConstants } from '../Constants';
import TrackUtil from '../util/TrackUtil';
import API from '../API';
import LogUtil from '../util/LogUtil';
import Util, { EvArray, EvMap } from '../util2/Util';
import VipUtil from '../util/VipUtil';
import NetInfo from "@react-native-community/netinfo";
import EventListV3 from '../widget/EventListV3';
import { CldDldTypes } from '../framework/CloudEventLoader';
import { BaseStyles } from '../BasePage';
import Singletons from '../framework/Singletons';
import { Event } from '../config/base/CfgConst';
import Calendar from '../widget/Calendar';
import CoverLayer from '../widget/CoverLayer';
import dayjs from 'dayjs';
import PlayerToolbarV2 from "../alarmDetail/components/PlayerToolbarV2";
import TimeScaleView2_1 from '../ui/TimeScaleView2_1';
import { AbstractDialog } from "miot/ui/Dialog";
import DldMgr from "../framework/DldMgr";
import { DldStatus } from "../framework/EventLoaderInf";
import AlarmUtilV2 from "../util/AlarmUtilV2";
import SdcardCloudTimelinePageV2 from './SdcardCloudTimelinePageV2';
import BuyCloudIntroView from "../widget/BuyCloudIntroView";
import InputDlgEx from "../widget/InputDlgEx";
import CalendarDot from "../widget/CalendarDot";
import imageName from "../../Resources/Images/icon_sel_all_events2.png";
import NearHandMJDialog from "../ui/NearHandMJDialog";
import DynamicColor from "mhui-rn/dist/styles/DynamicColor";
import { handlerOnceTapWithToast } from "../util/HandlerOnceTap";

const cloudHeaderH = 54;
const CommonHeaderH = 46;
const kIsCN = Util.isLanguageCN();
const TAG = "CloudTimelinePlayerFragmentV2";
const kWindowHeight = Math.max(Dimensions.get("window").height, Dimensions.get("window").width);
const kWindowWidth = Math.min(Dimensions.get("window").height, Dimensions.get("window").width);
// const portraitVideoHeight = Number.parseInt(kWindowWidth / 1.78);
const portraitVideoHeight = kWindowWidth * 9 / 16;
const DefFilter = "Default";

let isDark = DarkMode.getColorScheme() == "dark";
//支持的最大播放倍速
const maxSpeed = 2;
const kPxScale = Math.min(kWindowHeight / 896, 1); // 以iphone11的高度为设计标准高度, 目前设计也以之为尺寸上限
// 用于埋点的映射
const motionIdxMap = {
  Default: 1, PeopleMotion: 2, Face: 3, EmotionRecognition: 4, FenceIn: 5, FenceOut: 6, BabyCry: 7, PeopleCough: 8, LouderSound: 9, ObjectMotion: 10, AI: 11
}

export default class CloudTimelinePlayerFragmentV2 extends React.Component {

  static navigationOptions = ({ navigation }) => {
    // if (true) {//不要导航条
    //   return null;
    // }

    let tabBarVisible = true;
    let param = navigation.state.params || {};
    if (param.isFullScreen) {
      tabBarVisible = false;
    }
    return {
      tabBarVisible
    };
  }

  state = {
    curDate: new Date(), // 当前选中的日期
    evFilterKey: SdcardCloudTimelinePageV2.eventType,
    showTimeLine: false,
    isEmpty: true,
    isVip: false,
    touchMovement: false,
    sdcardFiles: [],
    showPlayToolBar: false,
    videoExitedArray: [],
    fullScreen: false,
    isMute: CameraConfig.getUnitMute(),
    isSleep: false,
    resolution: 0,
    speed: 1, // 倍速
    isPlaying: true,

    screenshotVisiblity: false, // 截图是否可见

    showErrorView: false,
    deletedStr: null,
    showLoadingView: false,
    showLoadingSkeletonView: true,
    showPauseView: false,
    showFastSpeedView: false,
    errTextString: "", // 错误提示文案

    screenshotPath: null,
    recordTimeSeconds: 0,

    // eventTypeFlags: (EVENT_TYPE.Default | EVENT_TYPE.Pet | EVENT_TYPE.ChildDetected),
    eventTypeFlags: (EVENT_TYPE.Default | EVENT_TYPE.IgnoreEvent),

    videoPath: null,

    videoViewWidth: 0,
    videoViewHeight: 0,
    dialogVisibility: false,

    permissionRequestState: 0,
    showPermissionDialog: false,
    displayCloudList: true,
    showEmptyHint: false,
    item: null,
    showMoreDlg: false,
    showDownloadHint: false,
    // showCloudBuyView: AlarmUtilV2.showAlarmCloudBuy,
    showCloudBuyView: false,
    loadedCloudBuyStatus: true,
    errReportPermissionVisible: false,
    moreIconY: -1,
    sdcardCode: -1,
    mNoVideoEvent: null,
    showPlayEndView: false
  };


  constructor(props) {
    super(props);
    this.destroyed = false;
    this.fromPush = SdcardCloudTimelinePageV2.fromPush;
    this.isEventListItem = true;
    CameraConfig.isToUpdateVipStatue = true;
    this.mLoader = Singletons.CloudEventLoader;
    this.timeIndicatorView = null;
    this.dateTime = new Date();
    this.isFirstReceiveFiles = true;
    this.isUserPause = false;

    this.isEuropeServer = CameraConfig.getIsEuropeServer();
    this.isInternationalServer = CameraConfig.getInternationalServerStatus();
    this.isCloudServer = CameraConfig.getIsCloudServer();

    this.isPluginForeGround = true;// sdcard页面不存在跳到其他页面的情况
    this.isPageForeGround = true;
    this.isAppForeground = true; // 米家App是否在前台。

    this.duration = 0;
    this.mCurTime = 0;
    this.mNewPlay = true;
    this.mNewEvent = false;//user click in vip event list
    this.panResponderDownTime = 0;
    this.isLongVideoPress = false;
    this.oldSpeed = 1;
    this.mLstItm = null;
    this.moreIconY = -1;
    this.mEvFilter = [];
    // see https://blog.csdn.net/u011068702/article/details/83218639
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        console.log("will focus");

        this.isPageForeGround = true;
        this.isPluginForeGround = true;
        this.isAppForeground = true;
        this.restoreOri();
        this._onResume();
      }
    );
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'didBlur',
      () => {
        console.log("did blur");

        this.isPageForeGround = false;
        this._onPause();
      }
    );


    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.isAppForeground = true;// rnactivity调用了onresume
      this.restoreOri();
      this._onResume();
    });

    this.willPauseListener = PackageEvent.packageWillPause.addListener(() => {
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.isAppForeground = false;// rnactivity调用了onpause
      this._onPause();
    });

    if (Platform.OS == "ios") {
      this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.isPluginForeGround = true;// rnactivity调用了onresume
        this._onResume();
      });

      this.willDisappearListener = PackageEvent.packageViewWillDisappearIOS.addListener(() => {
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.toPortrait();// ios强制切换到竖屏去
        setTimeout(() => {
          this.isPluginForeGround = false;// rnactivity调用了onpause
          // console.log('testaaa', 'willDisappearListener', 'page: ', this.isPageForeGround, ' plugin: ', this.isPluginForeGround);
          this._onPause();

        }, 500);
      });
    }
    this.mDldL = DldMgr.addListener((aStatus) => {
      this.onDldProgress(aStatus);
    });
    this.isDataUsageWarning = true;

    this.currentNetworkState = -1;
    this.isChangingNetwork = false;

    this.toStartTime = 0;// 暂存要播放的view的时间戳

    this.startTime = 0;
    this.endTime = 0;
    this.offset = 0;
    this.sessionId = 0;

    this.lastTimeItemEndTime = 0;// 从rdt捞过来的数据 的最后一个item的endtime

    this.isSetPlayTime = false;// 是否正在设置播放时间
    this.setPlayTimeMillis = 0;// 记录本次设置playback命令的时间
    this.lastTimestamp = 0;// 记录上次返回回调的时间戳
    // todo notify native side  whether in continue playback mode
    this.timelineView = null;
    this.timelineViewFull = null;
    this.scrollTimeout = null;
    this.scrollTimeout1 = null;
    this.mOri = "PORTRAIT";
    this.connRetry = 2;
    this.sdTimeRetry = 0;

    this.videoItem = null;


    this.stackNavigationOnPauseListener = null;
    this.stackNavigationOnResumeListener = null;
    this.isParentPageBackground = false;
    this.mPermReq = false;
    this.mDldInfo = {};
    this.enterCloudStorageTabTime = 0; // 埋点--记录运存页面曝光时间
    if (this.stackNavigationOnPauseListener == null) {
      this.stackNavigationOnPauseListener = DeviceEventEmitter.addListener(SD_CLOUD_STACK_NAVIGATION_ONPAUSE, () => {
        // 父类收到了pause事件，传给了子控件。
        if (!this.isPageForeGround) { // 只有页面处于前台的时候，这个属性才奏效
          return;
        }
        this.isParentPageBackground = true;
        this.isPageForeGround = false;
        this._onPause();// 暂停咯
      });
    }
    if (this.stackNavigationOnResumeListener == null) {
      this.stackNavigationOnResumeListener = DeviceEventEmitter.addListener(SD_CLOUD_STACK_NAVIGATION_ONRESUME, () => {
        console.log("onresume");

        if (!this.isParentPageBackground) {
          return;
        }
        this.restoreOri();
        this.isParentPageBackground = false;
        this.isPageForeGround = true;
        this.isPluginForeGround = true;
        this._onResume();
      });
    }
    if (this.stacknavigationOnRebackListener == null) {
      this.stacknavigationOnRebackListener = DeviceEventEmitter.addListener(SD_CLOUD_STACK_NAVIGATION_ONBACK, () => {
        // 父类收到了pause事件，传给了子控件。
        // if (!this.isPageForeGround) {//只有页面处于前台的时候，这个属性才奏效
        //   return;
        // }
        console.log(TAG, "receive parents back event");
        this.isParentPageBackground = true;
        this.isPageForeGround = false;
        this._onPause();// 暂停咯
      });
    }
    CameraPlayer.getInstance().getSdcardStatus().then(({ sdcardCode }) => {
      this.setState({ sdcardCode: sdcardCode });
    }).catch(({ sdcardCode }) => {
      this.setState({ sdcardCode: sdcardCode });
    });

    this.selectedIndexArray = [0];

    this.displayCutoutTop = 0;

    this.showNetworkDisconnectTimeout = null;

    this.storedEvSelMap = {};
    this.EvSelMap = {};
    this.listCloudIsPlayEnd = false;
    this.mLastInactiveTime = 0;
    Service.getServerName()
      .then((serverBean) => {
        let countryCode = serverBean.countryCode;
        let serverCode = serverBean.serverCode;
        CameraConfig.updateCloudSupportCountry(serverCode.toUpperCase());
        this.isEuropeServer = CameraConfig.getIsEuropeServer();
        this.mIsSupportCloudCountry = CameraConfig.isSupportCloud();
        this.mIsInternationalServer = countryCode.toLowerCase() != "cn";
        CameraConfig.setIsInternationalServer(this.mIsInternationalServer);

        if (countryCode.toLowerCase() == "in") {
          CameraConfig.setIsIndiaServer(true);
        } else {
          CameraConfig.setIsIndiaServer(false);
        }
      });
  }

  _initEvFilter(firstInit = false) {
    let supportEvents = CameraConfig.pgSupportEvents();
    let list = EvArray.filter((item) => {

      if (this.state.showTimeLine && item.key == Event.Default) return null;

      if (!supportEvents.includes(item.key) && item.key != Event.Default) {
        return null;
      }

      // if (item.key == Event.CameraCalling && !CameraConfig.displayCameraCallingTimeline(Device.model)) return null;
      //
      // if (item.key == Event.Pet && !CameraConfig.displayPetInTimeline(Device.model)) return null;

      if (firstInit && item.key !== Event.Default) {
        this.storedEvSelMap[item.key] = true;
        this.EvSelMap[item.key] = true;
      }
      if (item.key == this.state.evFilterKey) {
        item.selected = true;
      } else {
        item.selected = false;
      }
      return item;
    });
    this.mEvFilter = list;
    // if (!firstInit) {
    //   // 直接return会导致切换到时间轴多一个全部事件选项
    //   this.evFilterGroup(list);
    //   return;
    // }
    // AlarmUtilV2.getALLAISettings().then(() => {
    //   this.evFilterGroup(list);
    // }).catch(() => {
    //   this.mEvFilter = list;
    // });
  }

  evFilterGroup(list = []) {
    let closedItems = [];
    list = list.filter((item, index) => {
      if (item.key == Event.Default) {
        return true;
      }
      if (!AlarmUtilV2.AI_EVENT_SETTING_MAP[item.key] && item.key != Event.AI) {
        console.log("close AI->",item.key)
        closedItems.push(item);
        return false;
      }
      return true;
    });
    list.push(...closedItems);
    this.mEvFilter = list;
  }

  _networkChangeHandler = (networkState) => {
    if (this.destroyed) {
      return;
    }
    if (!this.isPageForeGround) {
      return;
    }
    if (!this.state.displayCloudList) {
      return;
    }
    // if (this.currentNetworkState != networkState) { // 网络状态不一样
    //   this.isChangingNetwork = true;
    // }
    // if (this.currentNetworkState != networkState) { // 网络状态不一样
    //   this.isChangingNetwork = true;
    // }
    console.log("处理网络变化" + new Date().getTime());
    this.networkState = networkState;
    clearTimeout(this.showNetworkDisconnectTimeout);
    if (networkState == 0) { // 网络断开了连接 showError?
      this.showNetworkDisconnectTimeout = setTimeout(() => {
        this.setState({ showErrorView: true, showLoadingView: false, showPlayToolBar: false, showPauseView: false });
      }, 1300);
      return;
    }
    this.isMobileNetwork = networkState == 1;
    if (this.isPageForeGround) { // 有网络来了  发起重连吧
      this.setState({ showErrorView: false });
      setTimeout(() => {
        this._startQueryNetwork();
      }, 500);// 过一会再查询 ，那个查询网络类型的api有问题。
    }
  }

  _onResume() {
    this._initEvFilter(true);
    if (CameraConfig.isToUpdateVipStatue) {
      this.fetchVipStatus();
    }
    if (CameraConfig.isToUpdateEventList) {
      this.playTheFirstEvent = true;
      this.itemEventIsDeleteToPlayFirst = true;
      this.mEvList.mRefresh();
      CameraConfig.isToUpdateEventList = false;
    }
    this.cellPhoneNetworkStateChanged = HostEvent.cellPhoneNetworkStateChanged.addListener((networkInfo) => {
      this._networkChangeHandler(networkInfo.networkState);
    });
    DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');

    // if (!this.state.displayCloudList) {
    //   this.fetchVipStatus();//
    //   return;// 不是vip 直接返回，不应该走剩下的路线。
    // }

    Host.getPhoneScreenInfo()
      .then((result) => {
        this.displayCutoutTop = PixelRatio.roundToNearestPixel(result.displayCutoutTop / PixelRatio.get() || 0);
        if (isNaN(this.displayCutoutTop)) {
          this.displayCutoutTop = 0;
        }
        console.log(TAG, "result:", result);
      })
      .catch((error) => {

      });
    this.destroyed = false;
    // 请求SD卡回看视频最早的视频时间戳
    this.getSDEarliestTime();
    console.log(TAG, "_onResume", this.isUserPause);
    if (this.isUserPause) {
      return;
    }
    // 重新进来 要绑定一遍这些事件。
    this.onGetFiles();// 从其他页面回来 要刷新一遍数据，避免出现其他页面删了  这个页面还没有同步数据的情况。

    // TODO 注释掉这块逻辑
    let interval = new Date().getTime() - this.mLastInactiveTime;
    // if (this.state.videoPath && (Platform.OS == 'android' || (Platform.OS == 'ios' && interval < (1000 * 90)))) {
    //   this.onPlay(true);
    // } else {
    //   if (Platform.OS == "ios" && interval > (1000 * 90)) {
    //     let temp = this.state.videoPath;
    //     this.setState({ videoPath: null },() => {
    //       this.setState({ videoPath: temp });
    //     });
    //   }
    //   this._startQueryNetwork();
    // }
    console.log("++++++++++++++++++++++",interval);
    if (Platform.OS == "ios" && this.mLastInactiveTime > 0 && interval > (1000 * 90)) {
      this.mLastInactiveTime = 0;
      let temp = this.state.videoPath;
      this.setState({ videoPath: null },() => {
        this.setState({ videoPath: temp });
      });
    }
    // 切换到后台，再回来无法正常播放云存视频
    this._startQueryNetwork();

    if(!this.state.showCloudBuyView){
      this.enterCloudStorageTabTime = new Date().getTime();  // 记录进入页面的时间
    } else {
      // 埋点--云存推广页曝光
      TrackUtil.reportClickEvent("Monitoring_CloudStorageIntroduce_Show")
    }
  }

  _onPause() {
    this.mLastInactiveTime = new Date().getTime();
    this.cellPhoneNetworkStateChanged && this.cellPhoneNetworkStateChanged.remove();

    if (!this.state.displayCloudList) {
      return;
    }


    if (this.state.showErrorView) {
      return;
    }
    if (this.mPermReq && Host.isAndroid) {
      console.log("请求权限")
    } else {
      this._startPlay(false);
    }

    // 埋点--离开页面时上报曝光时间
    if(this.enterCloudStorageTabTime > 0 && !this.state.showCloudBuyView){
      let cloudStorageTime = (new Date().getTime() - this.enterCloudStorageTabTime) / 1000;
      TrackUtil.reportResultEvent("Monitoring_CloudStorageTab_Time", "Time", cloudStorageTime)
      // console.log("===================================埋点--离开运云存页面时上报曝光时间 _onPause=================", cloudStorageTime)
      this.enterCloudStorageTabTime = 0;
    }
  }

  restoreOri() {
    console.log(TAG, "restoreOri");
    if ("PORTRAIT" === this.mOri) {
      this.toPortrait();
    } else {
      this.toLandscape();
    }
  }


  toPortrait() {
    StatusBar.setHidden(false); // false 状态栏显示
    this._hidePlayToolBarLater();
    this._setNavigation(false);
    console.log(TAG, "toPortrait");
    // 解决第三方手机状态栏隐藏问题，如有深色模式可修改
    this.mOri = "PORTRAIT";
    if (Host.isPad && Platform.OS == "android") { // for android for the special page
      Service.miotcamera.enterFullscreenForPad(false);
    } else {
      // CameraConfig.lockToPortrait();
    }
    CameraConfig.lockToPortrait();

  }

  toLandscape() {
    StatusBar.setHidden(true); // true 状态栏隐藏
    this._hidePlayToolBarLater();
    this._setNavigation(true);

    console.log(TAG, "toLandscape");
    this.mOri = "LANDSCAPE";
    if (Platform.OS === "android") {
      Orientation.lockToLandscape();
      Service.miotcamera.enterFullscreenForPad(true);
    } else {
      if (Platform.OS == 'ios' && Host.isPad) {
        Service.miotcamera.enterFullscreenForPad(true);
      } else {
        Orientation.lockToLandscapeRight();
      }
    }
  }

  _hidePlayToolBarLater(ignoreFull = false) {
    if (this.destroyed) {
      return;
    }
    let tTimer = 5000;
    clearTimeout(this.showPlayToolBarTimer);
    this.showPlayToolBarTimer = setTimeout(() => {
      if (this.timelineViewFull && !this.timelineViewFull.isTimelineIdle() && this.state.fullScreen) {
        this._hidePlayToolBarLater();
        return;
      }
      this.setState({ showPlayToolBar: false, showPauseView: false });
    }, tTimer);
  }
  componentWillMount() {
    this.panResponder = PanResponder.create({
      onStartShouldSetPanResponder: () => true, // 刚开始的时候
      onMoveShouldSetPanResponder: (gestureState) => {
        // 解决父组件手势事件与子组件的点击事件冲突问题
        // 不响应子组件的点击时间，只响应父组件的手势事件
        const { dx, dy } = gestureState
        return (dx > 2 || dx < -2 || dy > 2 || dy < -2)
      },
      onShouldBlockNativeResponder: () => true,
      onPanResponderTerminationRequest: () => true, // 不允许其他人抢占。
      onPanResponderGrant: (evt) => {
        console.log("开始触摸")
        //开始按下视频区域的时间
        //已经在最高倍速，则不在进行长按快进播放
        if (this.state.speed != maxSpeed) {
          // if (this.state.speed != maxSpeed && this.state.isPlaying){
          this.panResponderDownTime = new Date().getTime();
          this.long_press_timeout = setTimeout(() => {
            if (!this.isLongVideoPress) {
              this.isLongVideoPress = true;
              //1、视频区域中部展示快进  2、在当前播放基础上翻倍播放
              console.log("快进播放开始");
              this.oldSpeed = this.state.speed;
              this.setState({ speed: 2, showFastSpeedView: true, fastPlay: true },() => {
                console.log("===========",this.state.isMute);
                if (!this.state.isMute) {
                  this._toggleAudio(true, false);
                }

              });
              // 埋点--云存长按加速
              TrackUtil.reportClickEvent("Monitoring_CloudStorageTab_LongPress_Num")
            }
          }, 1000);
        }
      },

      onPanResponderMove: (evt, gestureState) => {
        // 通过触摸开始坐标加上横纵方向的位移算出当前坐标位置，可以解决拖动时locationX和locationY跳变问题
        let x = this.touchBeginCoordX + gestureState.dx; // dx 从触摸操作开始时的累计横向位移
        let y = this.touchBeginCoordY + gestureState.dy;// dy 从触摸操作开始时的累计纵向位移


      },

      onPanResponderRelease: () => {
        console.log("onPanResponderRelease");
        clearTimeout(this.long_press_timeout);
        if (this.isLongVideoPress) {
          console.log("快进播放结束");
          this.isLongVideoPress = false;
          //重置播放倍速
          this.setState({ speed: this.oldSpeed, showFastSpeedView: false, fastPlay: false },() => {
            this._toggleAudio(CameraConfig.getUnitMute(), false);
          });
          this.oldSpeed = 1;
        } else {
          this._onVideoClick();
        }
      },

      onPanResponderTerminate: () => {
        console.log("onPanResponderTerminate")
        clearTimeout(this.long_press_timeout);
      }
    });

    this._panResponder = PanResponder.create({
      onStartShouldSetPanResponderCapture: (evt, gestureState) => {
        if (Platform.OS == "ios") {
          this.setState({ touchMovement: true });//手指移动，用来监听IOS
        }
      },
    });
  }

  componentWillUnmount() {
    this.destroyed = true;

    // 清理所有定时器
    this.clearAllTimers();
    
    // 移除所有监听器
    this.removeAllListeners();
    
    // 清理视频资源
    this.cleanupVideoResources();
    
    // 清理云存储相关
    this.cleanupCloudStorage();

    // 强制切换到竖屏
    this.toPortrait();

    console.log("CloudTimelinePlayerFragmentV2", "unmount");
    try {
      this._onPause();
    } catch (exception) {
      console.log(TAG, "unmount error", exception);
    }

    this.setState = () => false;
  }

  // 添加清理定时器的方法
  clearAllTimers() {
    const timers = [
      this.showNetworkDisconnectTimeout,
      this.scrollTimeout,
      this.scrollTimeout1,
      this.snapshotTimeout,
      this.initRetryTimer,
      this.showPlayToolBarTimer,
      this.hidePlayToolBarTimer,
      this.globalLoadingTimeout,
      this.long_press_timeout
    ];

    timers.forEach(timer => {
      if (timer) {
        clearTimeout(timer);
      }
    });
  }

  // 添加移除监听器的方法
  removeAllListeners() {
    const listeners = [
      this.cellPhoneNetworkStateChanged,
      this.stackNavigationOnPauseListener,
      this.stackNavigationOnResumeListener,
      this.stacknavigationOnRebackListener,
      this.didResumeListener,
      this.willPauseListener,
      this.willAppearListener,
      this.willDisappearListener,
      this.willStopListener,
      this.didFocusListener,
      this.didBlurListener
    ];

    listeners.forEach(listener => {
      if (listener && listener.remove) {
        listener.remove();
      }
    });

    // 移除方向监听
    Orientation.removeOrientationListener(this._orientationListener);
    
    // 移除返回键监听
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  // 添加清理视频资源的方法
  cleanupVideoResources() {

    if (this.isShareDownloading) {
      this.mLoader.cancelDownload(this.state.item, null);
    }
  }

  // 添加清理云存储相关的方法
  cleanupCloudStorage() {
    CloudVideoUtil.setCloudFilesReceivedCallback(null);
    CloudVideoUtil.setCloudDateListCallback(null);
    CloudVideoUtil.removeAllDatas();
    
    // 重置状态
    this.timelineView = null;
    this.isFirstReceiveFiles = true;
  }

  CoverLayerState(state) {
    console.log("CoverLayerState=", state);
  }

  render() {
    let showvideo = !this.state.showCloudBuyView;
    let loaded = this.state.loadedCloudBuyStatus;
    return (
      loaded ?
        <View {...this._panResponder.panHandlers} style={styles.container}>
          {/*<SafeAreaView style={{ backgroundColor: "#ffffff" }}></SafeAreaView>*/}

          {showvideo ? this._renderVideoLayout() : null}
          {showvideo ? this._renderTitleBar() : null}
          {showvideo ? this._renderTimeLineView() : null}
          {showvideo ? this._renderEventList() : null}
          {showvideo ? this._renderEventListSkeleton() : null}
          {/* {this._renderEventTypes()} */}
          {/* {this._renderBottomSelectView()} */}
          {this._renderResolutionDialog()}
          {this._renderPermissionDialog()}
          {this._renderCommentsDialog()}
          {this._renderMoreItemDialog()}
          {this._renderDownloadHint()}
          {this._renderDownloadConfirmDialog()}
          {this._renderDeleteConfirmDialog()}
          {this._renderEvFilterDialog()}
          {this.renderCommentDlg()}
          {!showvideo ? this.renderCloudBuyView() : null}
          {this._renderErrorReportDialog()}
          <CoverLayer CoverLayerState={this.CoverLayerState} ref={(ref) => this.coverLayer = ref} />

          {/*<SafeAreaView></SafeAreaView>*/}
        </View> : <View style={styles.container}></View>
    );
  }
  doEvFilter() {
    TrackUtil.reportClickEvent("TimeSlider_Motion_ClickNum");
    let eventFlags = EVENT_TYPE.Default;
    // 埋点 -- 云存事件筛选状态--时间轴
    let motionArr = [];
    this.isAllEvSelect() ? motionArr.push(1) : null;
    Object.keys(this.storedEvSelMap).forEach((item) => {
      if (this.storedEvSelMap[item]) {
        eventFlags = eventFlags | EVENT_TYPE[item];
        motionArr.push(motionIdxMap[item])
      }
    });
    console.log("==========doEvFilter", eventFlags);
    this.setState({ eventTypeFlags: eventFlags });
    TrackUtil.reportResultEvent("Monitoring_CloudStorageTab_Motion_Status_TimeSlider", "type", motionArr.sort((a, b) => { return a - b }).join(','));
  }

  isAllEvSelect() {
    if (!this.state.showTimeLine) {
      return false;
    }
    let flag = true;
    Object.keys(this.EvSelMap).every((item) => {
      if (!this.EvSelMap[item]) {
        flag = false;
        return flag;
      }
      return true;
    });
    return flag;
  }
  _renderEvFilterDialog() {
    let allSelected = this.isAllEvSelect();
    let buttons = [
      {
        text: LocalizedStrings["btn_cancel"],
        // titleColor: 'rgba(0, 0, 0, 0.8)',
        // backgroundColor: { bgColorNormal: 'rgba(0, 0, 0, 0.04)', bgColorPressed: "rgba(0, 0, 0, 0.08)" },
        callback: () => {
          this.EvSelMap = Object.assign({}, this.storedEvSelMap);
          this.setState({ showEvFilter: false });
        }
      },
      {
        text: LocalizedStrings["btn_confirm"],
        callback: () => {
          this.storedEvSelMap = Object.assign({}, this.EvSelMap);
          this.doEvFilter();
          this.setState({ showEvFilter: false });
        }
      }
    ];
    if (!this.state.showTimeLine) {
      buttons = [
        {
          text: LocalizedStrings["btn_cancel"],
          titleColor: 'rgba(0, 0, 0, 0.8)',
          backgroundColor: { bgColorNormal: isDark ? '#EEEEEE' : 'rgba(0, 0, 0, 0.04)', bgColorPressed: "rgba(0, 0, 0, 0.08)" },
          callback: () => {
            this.setState({ showEvFilter: false });
          }
        }
      ];
    }
    return <AbstractDialog
      visible={this.state.showEvFilter}
      title={LocalizedStrings["ev_filter_title"]}
      style={{ maxHeight: kWindowHeight * 0.85 }}
      showSubtitle={false}
      onDismiss={() => {
        this.EvSelMap = Object.assign({}, this.storedEvSelMap);
        this.setState({ showEvFilter: false });
      }}
      canDismiss={true}
      useNewTheme={true}
      buttons={buttons}>
      <ScrollView style={{marginBottom: 10}} contentContainerStyle={{flexGrow: 1}} showsVerticalScrollIndicator={true}>
        {
          this.state.showTimeLine ?
            <TouchableOpacity key={Event.Default}
              onPress={() => {
                Object.keys(this.EvSelMap).forEach((item) => {
                  this.EvSelMap[item] = !allSelected;
                });
                this.forceUpdate();
              }}>
              <View style={{ flexDirection: "row", alignItems: "center", justifyContent: "space-between", paddingTop: 10, paddingBottom: 10 }}>
                <View style={{ flexDirection: "row", alignItems: "center", marginStart: 30 }}>
                  <Image source={EvMap[Event.Default].icon.norm} style={{ width: 30, height: 30, marginEnd: 20 }}></Image>
                  <Text style={{ fontWeight: "bold", fontSize: 16 }}>{EvMap[Event.Default].des}</Text>
                </View>
                {allSelected ? <Image source={require("../../Resources/Images/icon_camera_panoram_angle_select.png")} style={{ width: 22, height: 22, marginRight: 30 }}></Image>
                  : <Image source={require("../../Resources/Images/icon_camera_panoram_angle_unselect.png")} style={{ width: 22, height: 22, marginRight: 30 }}></Image>}
              </View>

              <View style={{ height: 0.5, marginHorizontal: 30, marginVertical: 10, backgroundColor: Util.isDark() ? "xm#333333" : "rgba(0, 0, 0, 0.1)" }}></View>
            </TouchableOpacity> : null
        }
        {
          this.mEvFilter.map((item) => {
            let itemInfo = EvMap[item.key];
            if (!itemInfo) {
              return null;
            }
            return <TouchableOpacity key={item.key}
              onPress={() => {
                if (this.state.showTimeLine) {
                  this.EvSelMap[item.key] = !this.EvSelMap[item.key];
                } else {
                  // 埋点--云存事件筛选状态-列表
                  TrackUtil.reportResultEvent("Monitoring_CloudStorageTab_Motion_Status_List", "type", motionIdxMap[item.key])
                  // 看家列表请求切换事件后，播放第一个视频
                  this.playTheFirstEvent = true;
                  this.setState({ evFilterKey: item.key, showEvFilter: false }, () => {
                    this._initEvFilter();
                  });
                }
                this.forceUpdate();
              }}>
              <View style={[{ flexDirection: "row", alignItems: "center", justifyContent: "space-between", paddingTop: 15, paddingBottom: 15 }, !this.state.showTimeLine && item.selected ? { backgroundColor: isDark ? '#EEEEEE' : 'rgba(0, 0, 0, 0.05)' }:{}]}>
                <View style={{ flexDirection: "row", alignItems: "center", marginStart: 30 }}>
                  <Image source={itemInfo.icon.norm} style={{ width: 30, height: 30, marginEnd: 20 }}></Image>
                  <Text style={{ fontWeight: "bold", fontSize: 16 }}>{itemInfo.des}</Text>
                </View>
                {this.state.showTimeLine ?
                  this.EvSelMap[item.key] ? <Image source={require("../../Resources/Images/icon_camera_panoram_angle_select.png")} style={{ width: 22, height: 22, marginRight: 30 }}></Image>
                    : <Image source={require("../../Resources/Images/icon_camera_panoram_angle_unselect.png")} style={{ width: 22, height: 22, marginRight: 30 }}></Image>
                  : item.selected ? <Image source={require("../../Resources/Images/plugin_icon_customVoice_selectItem.png")} style={{ width: 22, height: 22, marginRight: 30 }}></Image> : null}
              </View></TouchableOpacity>;
          })
        }
      </ScrollView>
    </AbstractDialog>;
  }

  renderCloudBuyView() {
    if (!this.statCloudBuyExposeDone) {
      this.statCloudBuyExposeDone = true;
    }

    return (
      <BuyCloudIntroView
        onFreeCloudOpen={() => {
          StorageKeys.FREE_CLOUD = true;
          // 不需要再去请求一遍，可能会存在请求不回来正常的数据
          AlarmUtilV2.showAlarmCloudBuy = false;
          this.setState(() => {
            return { displayCloudList: true};
          }, () => {
            this._initData();
          });
        }}
        onBuyCloudPress={() => {
          CameraConfig.isToUpdateVipStatue = true;
        }}>

      </BuyCloudIntroView>
    );
  }

  _renderBuyCloudTips = () => {
    if (this.state.fullScreen) {
      return null;
    }
    if (!this.state.showBuyCloudVideoTip) {
      return null;
    }
    if (this.state.showLoadingSkeletonView) {
      return null;
    }
    if (!this.statCloudTipsExposeDone) {
      this.statCloudTipsExposeDone = true;
      // TrackUtil.oneTrackReport('Monitoring_player_Tips_Expose', [this.isInCloudWindow ? 1 : 0, this.ref_tip]);
    }
    let backgroundColor = Util.isDark() ? "#25A9AF32" : '#32BAC019';
    let channel = "videodetails_button";
    let tipsText = this.isEuropeServer ? LocalizedStrings['eu_camera.alarm.cloud.tip.fullvideo.not.vip_new'] : LocalizedStrings['camera.alarm.cloud.tip.fullvideo.not.vip_new'];
    this.mainLandNoFreeSVL = (Device.model == "chuangmi.camera.070a02" || Device.model == "chuangmi.camera.060a02" || Device.model == "chuangmi.camera.046d02") && !CameraConfig.isInternationalServer;
    if (this.mainLandNoFreeSVL) {
      tipsText = this.isEuropeServer ? LocalizedStrings['eu_camera.alarm.cloud.tip.fullvideo.not.vip'] : LocalizedStrings['camera.alarm.cloud.tip.fullvideo.not.vip'];
    }
    let tipsTextColor = Util.isDark() ? "#25A9AF" : "#32BAC0";

    let backIcon = require('../../Resources/Images/bar_tips_green.png');
    if (this.isInCloudWindow) {
      tipsText = this.isEuropeServer ? LocalizedStrings['eu_c_cloudvip_end_tip'] : LocalizedStrings['c_cloudvip_end_tip'];
      tipsTextColor = Util.isDark() ? "#DB8E0D" : "#F5A623";
      channel = "videodetails_button_expire";
      backgroundColor = Util.isDark() ? "#DB8E0D32" : '#F5A62319';
      backIcon = require('../../Resources/Images/bar_tips.png');
    }
    let style = { flexDirection: "row", flexWrap: 'nowrap', alignItems: "center", justifyContent: "space-between", backgroundColor: backgroundColor, paddingLeft: 16, paddingRight: 30, width: "100%", height: 54, borderRadius: 16 };
    let mBgStyleBase = { alignItems: "center", justifyContent: "center" };
    let mBgStyle = [mBgStyleBase, { marginVertical: 10, backgroundColor: Util.isDark() ? "#000000" : "#FFFFFF", marginHorizontal: 15, borderRadius: 16 }];
    return (
      <View style={mBgStyle}>
        <TouchableOpacity
          style={style}
          onPress={() => {
            TrackUtil.reportClickEvent('Monitoring_WholeVideo_ClickNum', true, [this.isInCloudWindow ? 1 : 0, this.ref_tip]);
            if (!Device.isOwner) {
              Toast.success("share_user_permission_hint");
              return;
            }
            this.toPortrait();
            Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: channel });
            CameraConfig.isToUpdateVipStatue = true;
            this.setState({ showBuyCloudVideoTip: false });
            // 埋点--云存视窗推广点击
            TrackUtil.reportClickEvent("Monitoring_CloudStorageTab_Introduce_ClickNum")
          }
          }
          ref={(ref) => this.mCloudBuyTip = ref}
          onLayout={(e) => {
            this.mCloudBuyTipWidth = e.nativeEvent.layout.width;
          }}
        // accessibilityLabel={!this.state.fullScreen ? DescriptionConstants.kj_2_5 : DescriptionConstants.kj_2_30}
        >
          <Text numberOfLines={3}
            style={[BaseStyles.text14, { paddingRight: 0, color: tipsTextColor, textAlign: "left", textAlignVertical: 'center', width: '100%' }]}>{tipsText}</Text>
          <ImageButton
            style={{ width: 22, height: 22 }}
            source={backIcon}
          />
        </TouchableOpacity>
      </View>
    );
  }
  _renderEventList() {

    let emptyFaq = "这里有个问题";
    // let mEmptyDes = EventTypeUtil.getEventEmptyTip(this.state.evFilterKey);
    let mEmptyDes = LocalizedStrings['camera.alarm.event.type.empty.all'];
    //pid 根据id，判断当前播放的是哪个事件
    let mId = 0;
    //if (this.videoItem){
    // mId = `${this.videoItem.fileId}-${this.videoItem.offset?this.videoItem.offset:this.offset}`
    //}
    mId = `${this.state?.item?.fileId}-${this.state?.item?.offset}`
    return (
      <View style={this.state.showTimeLine ? { display: "none" } : { flex: 1 }}>
        {this._renderBuyCloudTips()}
        <EventListV3
          ref={(aEvLst) => { this.mEvList = aEvLst; }}
          style={[{ backgroundColor: Util.isDark() ? this.context.theme?.colorWhite : this.context.theme?.colorWhite }]}
          isFullScreen={this.state.fullScreen}
          // eventHeaderView={this._renderBuyCloudTips}
          eventHeaderHeight={cloudHeaderH}
          type={CldDldTypes.Events}
          loader={this.mLoader}
          isSltDay={true}
          displayStatus={!this.state.showTimeLine}
          // events={this.props.navigation.state.params.items}
          playingId={mId}
          loadingSkeletonStatus={this.state.showLoadingSkeletonView}
          onGetDataDone={this.mGetDataDone}
          onInitDataDone={this.mGetInitDataDone}
          loaderArgs={{ startDate: this.state.curDate, filter: this.state.evFilterKey, sdcardCode: this.state.sdcardCode }}
          emptyAdjust={CommonHeaderH}
          contentContainerStyle={{ paddingHorizontal: 15, paddingTop: !this.state.showBuyCloudVideoTip ? 12 : 0 }}
          morePressed={(aItm) => {
            SdcardCloudTimelinePageV2.toSDCardPageTime = aItm.createTime;
            DeviceEventEmitter.emit(SD_CLOUD_FORCE_REFRESH_TAB, { gotoPage: "topPage2" });
          }}
          onEventPress={(aItm, aExtra) => {
            console.log(TAG, "onEventPress===", aItm);
            this.blockOnProgress = true;
            this.setState({ showLoadingView: true });
            Util.checkExist(aItm).then((result) => {
              console.log("checkExist", result.data.deleteStatus); // result.data.deleteStatus true : false
              if (!result.data.deleteStatus) {
                this.updatePlayItem(aItm);
                // 埋点--事件点击
                TrackUtil.reportClickEvent("Monitoring_CloudStorageTab_ViewList_HistoryVideo_ClickNum")
              } else {
                this.setState({ isPlaying: false, showLoadingView: false, deletedStr: LocalizedStrings["camera.alarm.video.deleted"] });
              }
            }).catch((err) => {
              console.log("checkExist onEventPress", err);
              if (err.indexOf('暂未收录该接口') != -1) {
                this.updatePlayItem(aItm);
              }
            });
            // this.updatePlayItem(aItm);
            // this._onCenterValueChanged(aItm.createTime);
          }}
          onEventLongPress={(aItm, aExtra) => {
            console.log(TAG, "======点击item===", aItm);
            // this.props.navigation.navigate("CloudVideoEditPage", {curItem: aItm, aExtra});
            if (Device.isReadonlyShared) {
              // 只读的分享用户
              return;
            }
            StackNavigationInstance.jumpToStackNavigationPage_forSDCloudPage("CloudVideoEditPage", { curItem: aItm, aExtra, startDate: this.state.curDate, filter: this.state.evFilterKey, loadingStatus: this.state.loadingStatus });
          }}

          emptyDes={mEmptyDes}
          extraEmptyDes={emptyFaq ?
            {
              value: emptyFaq.des, action: () => {
                this.naviTo("WebGuide", { uri: emptyFaq.uri, title: emptyFaq.title });
              }
            }
            : null}
        />

      </View>
    );
  }
  _renderEventListSkeleton() {
    if (this.state.showTimeLine || !this.state.showLoadingSkeletonView) {
      return null;
    }
    return (
      <View>
        {
          ['1', '2', '3', '4', '5', '6'].map((item) => {
            return <Image key={item} source={Util.isDark() ? require('../../Resources/Images/icon_skeleton_dark.png') : require('../../Resources/Images/icon_skeleton.png')} style={{width: '100%',paddingHorizontal: 15, height: 80}}/>
          })
        }
      </View>
    );
  }

  _renderTitleBar() {
    // let pTypes = [Event.PeopleMotion,Event.Pet];
    let pTypes = Util.getFiltedEvents(this.state?.item?.type);
    let mShowComments = pTypes.includes(Event.KnownFace) || pTypes.includes(Event.PeopleMotion) || pTypes.includes(Event.Pet) || pTypes.includes(Event.ChildDetected);
    let count = this._getCommentCounts(pTypes);
    let width = count == 1 ? 30: count == 2 ? 49 : 68;
    // let isDark = Util.isDark();

    let commentsStyleBase = { justifyContent: "flex-end", height: 30, borderRadius: 15, alignItems: "center", width: width };
    // let commentsStyle = count > 1 ? [commentsStyleBase, { backgroundColor: isDark ? "xm#333333" : "#FFFFFF" }] : commentsStyleBase;
    let commentsStyle = [commentsStyleBase, { backgroundColor: isDark ? "xm#333333" : "#F6F6F6" }];
    // let imgStyle = { width: 26, height: 26, borderRadius: 13, marginLeft: 2 };
    let imgStyle = { width: 26, height: 26 };
    let imgbgStyle = { width: 30, height: 30, borderRadius: 15, backgroundColor: isDark ? "xm#333333" : "#F6F6F6", justifyContent: 'center', alignItems: 'center' };
    let isTimelineEvAllSelect = this.isAllEvSelect();

    let modeSource = this.state.showTimeLine ? Util.isDark() ? require("../../Resources/Images/icon_timeline_mode_dark.webp") : require("../../Resources/Images/icon_timeline_mode.webp")
      : Util.isDark() ? require("../../Resources/Images/icon_list_mode_dark.webp") : require("../../Resources/Images/icon_list_mode.webp");

    // let eventSource = isTimelineEvAllSelect ? Util.isDark() ? require("../../Resources/Images/icon_event_choose_all_dark.png") : require("../../Resources/Images/icon_event_choose_all.png")
    //   : Util.isDark() ? require("../../Resources/Images/icon_event_choose_dark.png") : require("../../Resources/Images/icon_event_choose.png");
    let eventSource = isTimelineEvAllSelect ? require("../../Resources/Images/icon_event_timeline_default_all.png") : require("../../Resources/Images/icon_event_timeline_default.png");
    let bgColor = Util.isDark() ? "xm#333333" : "#F6F6F6";
    // let imageName = require("../../Resources/Images/icon_sel_all_events2.png");
    let imageName = require("../../Resources/Images/icon_sel_all_events.png");
    if (this.state.filter == Event.Default) {
      imageName = require("../../Resources/Images/icon_sel_all_events.png");
    }  else if (this.state.evFilterKey == Event.BabyCry) {
      imageName = require("../../Resources/Images/icon_sel_baby_cry.png");
    } else if (this.state.evFilterKey == Event.KnownFace) {
      imageName = require("../../Resources/Images/icon_sel_face.png");
    } else if (this.state.evFilterKey == Event.PeopleMotion) {
      imageName = require("../../Resources/Images/icon_sel_people_move.png");
    } else if (this.state.evFilterKey == Event.AI) {
      imageName = require("../../Resources/Images/icon_sel_ai.png");
    } else if (this.state.evFilterKey == Event.ObjectMotion) {
      imageName = require("../../Resources/Images/icon_sel_area_move.png");
    } else if (this.state.evFilterKey == Event.LOUD) {
      imageName = require("../../Resources/Images/icon_sel_sound.png");
    } else if (this.state.evFilterKey == Event.CoveredFace) {
      imageName = require("../../Resources/Images/icon_sel_covered_face.png");
    } else if (this.state.evFilterKey == Event.FenceIn) {
      imageName = require("../../Resources/Images/icon_sel_fence_in.png");
    } else if (this.state.evFilterKey == Event.FenceOut) {
      imageName = require("../../Resources/Images/icon_sel_fence_out.png");
    } else if (this.state.evFilterKey == Event.EmotionRecognition) {
      imageName = require("../../Resources/Images/icon_sel_emotion.png");
    } else if (this.state.evFilterKey == Event.PeopleCough) {
      imageName = require("../../Resources/Images/icon_sel_cough.png");
    } else if (this.state.evFilterKey == Event.Wakeup) {
      imageName = require("../../Resources/Images/icon_sel_wake_up.png");
    } else if (this.state.evFilterKey == Event.Asleep) {
      imageName = require("../../Resources/Images/icon_sel_asleep.png");
    } else if (this.state.evFilterKey == Event.ChildDetected) {
      imageName = require("../../Resources/Images/icon_sel_child.png");
    } else if (this.state.evFilterKey == Event.Pet) {
      imageName = require("../../Resources/Images/icon_sel_pet.png");
    } else if (this.state.evFilterKey == Event.IgnoreEvent) {
      imageName = require("../../Resources/Images/icon_sel_car_event.png");
    }
    return (
      <View style={{ display: "flex", flexDirection: "row", borderBottomColor: Util.isDark() ? "xm#333333" : "#E6E6E6", borderBottomWidth: 0.5, alignItems: 'center', paddingVertical: 15 }}>
        {this.dateView()}
        {mShowComments && !this.state.showTimeLine ?
          <TouchableOpacity
            style={[BaseStyles.row, commentsStyle]}
            onPress={() => {
              this.setState({ showCommentsDlg: true }); // 显示备注弹窗
            }}>
            {pTypes.includes(Event.KnownFace) ? <View style={[imgbgStyle, { position: 'absolute', right: count == 1 ? 0 : count == 2 ? 19 : 38, zIndex: 100 }]}>
              <Image style={imgStyle} source={this.state.faceUrl}></Image></View> : null}
            {pTypes.includes(Event.ChildDetected) ? <View style={[imgbgStyle, { position: 'absolute', right: count == 1 ? 0 : (count == 2 && pTypes.includes(Event.ChildDetected)) ? 0 : 19, zIndex: 99 }]}>
              <Image style={imgStyle} source={Util.getIconFromType(Event.ChildDetected)}></Image></View> : null}
            {pTypes.includes(Event.PeopleMotion) ? <View style={[imgbgStyle, { position: 'absolute', right: count == 1 ? 0 : (count == 2 && pTypes.includes(Event.KnownFace)) ? 0 : 19, zIndex: 99 }]}>
              <Image style={imgStyle} source={Util.getIconFromType(Event.PeopleMotion)}></Image></View> : null}
            {pTypes.includes(Event.Pet) ? <View style={[imgbgStyle, { position: 'absolute', right: 0, zIndex: 98 }]}>
              <Image style={imgStyle} source={Util.getIconFromType(Event.Pet)}></Image></View> : null}
          </TouchableOpacity>: null
        }

        <TouchableOpacity
          style={[{
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "flex-start",
            marginLeft: 30
          }]}
          onPress={() => {

            this.setState({ showTimeLine: !this.state.showTimeLine }, () => {
              console.log("showTimeLine", this.state.showTimeLine)
              if (!this.state.showTimeLine) {
                this.mEvList._getPlayingOffset(this.toStartTime, false, true);
              } else {
                !this.destroyed && this.timelineView && this.timelineView.scrollToTimestamp(this.state.curDate.getTime()+100);
                !this.destroyed && this.timelineViewFull && this.timelineViewFull.scrollToTimestamp(this.state.curDate.getTime()+100);
              }
              this._initEvFilter();
            });
            // 埋点--视图切换点击
            TrackUtil.reportClickEvent("Monitoring_CloudStorageTab_ViewSwitch_ClickNum")
            this.state.showTimeLine ?
              TrackUtil.reportClickEvent("Monitoring_CloudStorageTab_ViewList_Show") :
              TrackUtil.reportClickEvent("Monitoring_CloudStorageTab_ViewTimeSlider_Show");
          }}>

          <Image
            accessibilityLabel={DescriptionConstants.kj_1_8}
            style={{ width: 45, height: 45 }} source={modeSource} />
        </TouchableOpacity>
        {
          this.state.showTimeLine ?
            // <TouchableOpacity
            //   style={[{
            //     flexDirection: "row",
            //     alignItems: "center",
            //     justifyContent: "flex-start",
            //     marginLeft: 15,
            //     marginRight: 24
            //   }]}
            //   onPress={() => { this.setState({ showEvFilter: true }); }}>
            //
            //   <Image
            //     accessibilityLabel={DescriptionConstants.kj_1_8}
            //     style={{ width: 45, height: 45 }} source={eventSource} />
            // </TouchableOpacity>

            <TouchableOpacity
              style={{ backgroundColor: bgColor, borderRadius: 15, width: 45, height: 30, alignItems: "center", alignSelf: 'center', justifyContent:'center', marginLeft: 15, marginRight: 24 }}
              onPress={() => {
                this.setState({ showEvFilter: true });
              }}
            >
              <Image style={{ width: 18, height: 18 }} source={eventSource} />
            </TouchableOpacity>
            :
            <TouchableOpacity
              style={{ backgroundColor: bgColor, borderRadius: 15, width: 45, height: 30, alignItems: "center", alignSelf: 'center', justifyContent:'center', marginLeft: 15, marginRight: 24 }}
              onPress={() => {
                this.setState({ showEvFilter: true });
              }}
            >
              <Image style={{ width: 18, height: 18 }} source={imageName} />
            </TouchableOpacity>
        }



      </View>
    );
  }
  _renderCommentsDialog() {
    let pTypes = Util.getFiltedEvents(this.state?.item?.type);
    // let pTypes = Util.getFiltedEvents(Event.KnownFace);
    return (
      <AbstractDialog
        visible={this.state.showCommentsDlg}
        // title={'识别报错'}
        // subtitle={testTitle}
        showTitle={false}
        showSubtitle={false}
        useNewTheme={true}
        onDismiss={(_) => { this.setState({ showCommentsDlg: false }) }}
        buttons={[
          {
            text: LocalizedStrings['action_cancle'],
            colorType: "grayLayerBlack",
            callback: (_) => {
              this.setState({ showCommentsDlg: false });
            }
          }
        ]}>
        <View style={[{ flex: 1 }, { marginTop: 20 }]} >
          {!this.state.fullScreen && pTypes.includes(Event.KnownFace) ? this._renderCommentLine(Event.KnownFace, this.state?.item?.faceInfo) : null}
          {!this.state.fullScreen && pTypes.includes(Event.ChildDetected) ? this._renderCommentLine(Event.ChildDetected) : null}
          {!this.state.fullScreen && !this.mIsInternationalServer && pTypes.includes(Event.PeopleMotion) ? this._renderCommentLine(Event.PeopleMotion) : null}
          {!this.state.fullScreen && pTypes.includes(Event.Pet) ? this._renderCommentLine(Event.Pet) : null}
        </View>
      </AbstractDialog>
    )
  }
  _getCommentCounts(pTypes) {
    let count = 0;
    if (pTypes.includes(Event.PeopleMotion)) count++;
    if (pTypes.includes(Event.KnownFace)) count++;
    if (pTypes.includes(Event.Pet)) count++;
    if (pTypes.includes(Event.ChildDetected)) count++;
    return count;
  }

  dateView() {
    let dStr = Util.addDateDes(dayjs(this.state.curDate), dayjs(this.state.curDate).format(LocalizedStrings["mmdd"]));
    return (
      <TouchableOpacity
        style={[{ flexDirection: "row", flex: 1, alignItems: "center", justifyContent: "flex-start", paddingRight: 10, paddingLeft: 24 }]}
        onPress={() => {
          // 弹出日期选择弹框
          this.mShowPopupView();
          // this.setState({ showDateDialog: true });
        }}>
        <Text
          style={[BaseStyles.text12, { paddingLeft: 0, color: "#8C93B0", fontWeight: "bold" }]}>{dStr}</Text>
        <Image
          accessibilityLabel={DescriptionConstants.kj_1_8}
          style={{ width: 20, height: 20 }} source={require("../../resources2/images/icon_ev_sel.png")} />
      </TouchableOpacity>
    );
  }

  mSwitchOneDay(items) {
    console.log("mSwitchOneDay", items);
    this.playTheFirstEvent = true;
    let sltDate = new Date(Date.parse(`${items[0]}/${items[1]}/${items[4]}`));
    this.mCalendar.setDate(sltDate);
    this.coverLayer.hideWithoutAnimated();
    this.fromPush = false;
    if (!this.state.showTimeLine && this.mEvList) {
      if (this.mEvList.getEvents() != null && this.mEvList.getEvents().length > 0) {
        this.mEvList.scrollTo({ animated: true, index: 0, viewOffset: 0 });
      }
      // this.mEvList.scrollTo({ animated: true, index: 0, viewOffset: 0 });
      // this.mEvList.switchDay(sltDate, true);// remove all exist and refresh
    }
    this.setState({ curDate: sltDate, isPlaying: false, mNoVideoEvent: null, showLoadingView: true },() => {
      if (this.state.showTimeLine) {
        !this.destroyed && this.timelineView && this.timelineView.scrollToTimestamp(this.state.curDate.getTime()+100);
        !this.destroyed && this.timelineViewFull && this.timelineViewFull.scrollToTimestamp(this.state.curDate.getTime()+100);
      }
    });

    // 埋点--云存日期筛选点击
    TrackUtil.reportClickEvent("Monitoring_CloudStorageTab_Date_ClickNum")
  }

  mSwitchAllVideo = () => {
    console.log("get selected");
    this.playTheFirstEvent = true;
    let sltDate = new Date();
    this.mCalendar.setDate(sltDate);
    this.coverLayer.hideWithoutAnimated();
    this.setState({ curDate: sltDate, isPlaying: false, showLoadingView: true });
  }

  mShowPopupView = () => {
    this.coverLayer.showWithContent(
      () => {
        // let nowDate = new Date();
        // console.log('calendar input: ', this.state.curDate);
        let mWidth = Math.min(Dimensions.get("window").height, Dimensions.get("window").width);
        return (
          <View style={{ height: 530, width: mWidth }}>
            <Calendar
              ref={(hdl) => this.mCalendar = hdl}
              visible={this.showCalendar}
              y={this.state.curDate.getFullYear()}
              m={this.state.curDate.getMonth() + 1}
              d={this.state.curDate.getDate()}
              interval={this.rollingInterval}
              width={mWidth}
              onDateChanged={this.mSwitchOneDay.bind(this)}
              onCancel={() => { this.coverLayer.hideWithoutAnimated(); }}
              onAllVideo={this.mSwitchAllVideo}
              // dates={this.state.videoExitedArray}
              dates={null}

            >
            </Calendar>
          </View>
        );
      },
      () => this.coverLayer.hideWithoutAnimated(),
      CoverLayer.popupMode.bottom
    );
  }

  _renderCommentLine(eType, faceInfo = null) {
    let commentStr;
    if (eType == Event.KnownFace) {
      commentStr = faceInfo && (faceInfo.name || !Device.isOwner) ? Util.getDescFromType(eType, faceInfo) : LocalizedStrings["add_notes"];
    }
    return (
      <View style={[BaseStyles.row, { backgroundColor: "white", height: 74, paddingLeft: 24, paddingRight: 24 }]}>
        {(faceInfo != null) ?
          <TouchableOpacity style={[BaseStyles.row, { alignItems: "center", width: "auto" }]}
            onPress={() => {
              if (Device.isOwner) {
                // this.naviTo(AllNativePage.FaceRec);
                this.changed = false;
                this.mItemForFaceComment = this.state.item;
                this.setState({ commentDlg: true, defVal: faceInfo.name ? faceInfo.name : null, commentErr: null, showCommentsDlg: false });
              }
              TrackUtil.reportClickEvent('Monitoring_mark_ClickNum');
            }}>
            <Image style={{ width: 36, height: 36, borderRadius: 18 }} source={this.state.faceUrl}></Image>
            {/* 有名字的不需要+   非设备主人的不需要+*/}
            {faceInfo.name ? null : (Device.isOwner ? <Image style={{ width: 19, height: 19, position: "relative", left: -8, top: -5 }} source={require("../../resources2/images/icon_face_comment.png")} /> : null)}
            <View style={{ flexDirection: 'column', alignItems: "flex-start", justifyContent: "center" }}>
              {/* 有名字的 直接输出desc即可   没有名字的共享设备提示有人脸出现   没名字的非共享设备提示添加备注+*/}
              {<Text style={[BaseStyles.text15, { fontWeight: "bold", paddingLeft: 5 }]}>{commentStr}</Text>}
              {/* 设备主人 & 有名字才提示修改备注+*/}
              {Device.isOwner && faceInfo.name ? <Text style={[BaseStyles.text12, { paddingLeft: 5 }]}>{LocalizedStrings["modify_notes"]}</Text> : null}
            </View>
          </TouchableOpacity>
          :
          this._renderCommentsLineRight(eType)
        }
        <View style={{ flex: 1, marginLeft: 10, justifyContent: "flex-end", backgroundColor: "#00000000", flexDirection: "row" }}>
          <TouchableOpacity style={{ backgroundColor: 'rgba(50, 186, 192, 0.1)', borderRadius: 17, minHeight: 34, paddingHorizontal: 10 }}
            onPress={() => {
              this.mReportType = eType;
              this.setState({ errReportPermissionVisible: true, showCommentsDlg: false });
            }}
          >
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
              <Text style={[BaseStyles.text13, { color: "#32BAC0", fontWeight: '400' }]}>{LocalizedStrings["cloud_face_rec_error"]}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  _renderCommentsLineRight(eType) {
    let icon = Util.getIconByEventType(eType);

    return (
      <TouchableOpacity
        style={[BaseStyles.row, { alignItems: "center", width: "auto" }]}
        onPress={() => {
        }}>
        <Image
          style={BaseStyles.icon30} source={icon} />
        <Text
          style={[BaseStyles.text15, { fontWeight: "bold", paddingLeft: 15 }]}>{Util.getDescFromType(eType)}</Text>
      </TouchableOpacity>
    );
  }

  renderCommentDlg(aFaceInf) {
    return (
      <InputDlgEx
        visible={this.state.commentDlg}
        icon={this.state.faceUrl}
        listData={this.state.mAllFigureInf}
        onPressed={(aDat) => {
          this.setState({ defVal: aDat.name, commentErr: null });
        }}
        title={LocalizedStrings["cloud_comment_dlg_title"]}
        onDismiss={(_) => {
          this.renameItem = null;
          this.setState({ commentDlg: false, isRename: false, commentErr: null });
          this.mItemForFaceComment = null;
        }}
        inputWarnText={this.state.commentErr}
        inputs={[{
          onChangeText: (text) => {
            this.changed = true;
            let isEmoji = Util.containsEmoji(text);
            let length = text.length;
            if (isEmoji) {
              this.setState({ commentErr: LocalizedStrings["Special_symbol_input_not_supported_temporarily"] });
            } else if (length > 8) {
              this.setState({ commentErr: LocalizedStrings["input_name_too_long"]?.replace("6", "8")});
            } else {
              this.setState({ commentErr: null });
            }
          },
          textInputProps: {
            maxLength: 8,
            returnKeyType: "done",
            autoFocus: Util.isHeightPt() ? true : false
          },
          defaultValue: this.state.defVal,
          type: 'DELETE',
          isCorrect: this.state.commentErr == null
        }]}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: (_) => {
              this.renameItem = null;
              this.setState({ commentDlg: false, isRename: false, commentErr: null });
              this.mItemForFaceComment = null;
            }
            // ignore
          },
          {
            text: LocalizedStrings["csps_right"],
            disabled: this.state.commentErr != null || this.state.uploadingFace,
            callback: (result) => {
              let text = result.textInputArray[0].trim();
              console.log(this.tag, "input changed", text, text.length);
              if (!this.mItemForFaceComment?.faceInfo) {
                this.setState({ isPlaying: true, commentDlg: false, isRename: false, commentErr: null });
                return;
              }
              if (text.length == 0 && this.state.defVal?.length > 0 && !this.changed) {
                text = this.state.defVal;
              }
              if (text.length > 0 && !this.containsEmoji(text)) {
                let cmd = null;
                let faceInfo = this.mItemForFaceComment?.faceInfo;
                if (faceInfo?.name) {
                  cmd = Util.modifyFaceComment(faceInfo.figureId, faceInfo.faceId, text);
                } else {
                  cmd = Util.commentFace(text, faceInfo.faceId);
                }

                if (cmd) {
                  if (this.state.uploadingFace) {
                    return;
                  }
                  this.setState({ uploadingFace: true });
                  cmd.then((aRet) => {
                    this.mEvList.updateAllItems();
                    if (this.mItemForFaceComment?.offset == this.state.item?.offset) {
                      let faceInfoTemp = this.state.item.faceInfo;
                      faceInfoTemp.name = text;
                      if (aRet?.data?.figureId) {
                        faceInfoTemp.figureId = aRet.data.figureId;
                      }
                      this.setState((prevState) => ({
                        item: {
                          ...prevState.item,
                          faceInfo: faceInfoTemp,
                          desc: Util.fmtStr(LocalizedStrings['alarm_event_face_face'], text)
                        }
                      }));
                    } else {
                      faceInfo.name = text;
                      if (aRet?.data?.figureId) {
                        faceInfo.figureId = aRet.data.figureId;
                      }
                      this.mItemForFaceComment.desc = Util.fmtStr(LocalizedStrings['alarm_event_face_face'], text);
                    }
                    this.mFaceUpdateInfoCb && this.mFaceUpdateInfoCb();
                    this.setState({ isPlaying: true, commentDlg: false });
                    this.mItemForFaceComment = null;
                    console.log(this.tag, "comment success", aRet);
                    this.setState({ uploadingFace: false });
                  })
                    .catch((aErr) => {
                      this.setState({ uploadingFace: false });
                      this.setState({ commentDlg: false });
                      this.mItemForFaceComment = null;
                      let errCode = aErr.code;
                      // 400302 人物上限
                      let errMap = { 400302: "figure_max_tips", 400305: "face_max_tips" };
                      let err = errMap[errCode] || "action_failed";
                      Toast.fail(err, false, true);
                      console.log(this.tag, "comment failed", aErr);
                    });
                } else {
                  console.log(this.tag, "nothing changed");
                  this.renameItem = null;
                  this.setState({ commentDlg: false, isRename: false });
                  this.mItemForFaceComment = null;
                }

              } else {
                // Alert.alert(
                //   LocalizedStrings["lowpower_leave_msg_cant_null"],
                //   null,
                //   [{ text: LocalizedStrings["owldiff_wifi_ok"] }]
                // );
                if (this.containsEmoji(text)) {
                  this.setState({ commentErr: LocalizedStrings["no_emoij_tips"] });
                }
                else {
                  this.setState({ commentErr: LocalizedStrings["cloud_comment_null"] });
                }
              }
            }
          }
        ]}
      />);
  }

  isEmojiCharacterV2(codePoint) {
    return !((codePoint == 0x0) ||
        (codePoint == 0x9) ||
        (codePoint == 0xA) ||
        (codePoint == 0xD) ||
        ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) ||
        ((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) ||
        ((codePoint >= 0x10000))) ||
      (codePoint == 0xa9 || codePoint == 0xae || codePoint == 0x2122 ||
        codePoint == 0x3030 || (codePoint >= 0x25b6 && codePoint <= 0x27bf) ||
        codePoint == 0x2328 || (codePoint >= 0x23e9 && codePoint <= 0x23fa))
      || ((codePoint >= 0x1F000 && codePoint <= 0x1FFFF))
      || ((codePoint >= 0x2702) && (codePoint <= 0x27B0))
      || ((codePoint >= 0x1F601) && (codePoint <= 0x1F64F));
  }

  containsEmoji(str) {
    let length = str.length;
    for (let i = 0; i < length; ++i) {
      let c = str.charCodeAt(i);
      if (this.isEmojiCharacterV2(c)) {
        return true;
      }
    }
    return false;
  }

  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 麦克风
    // 
    let message = "";
    if (this.state.permissionRequestState == 0) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_microphone"]);
    }
    return (
      <MessageDialog
        title={LocalizedStrings["tips"]}
        message={message}
        messageStyle={{
          fontSize: 14,
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );

  }


  _renderVideoLayout() {
    return (
      <View style={this.state.fullScreen ? styles.videoContainerFull : [styles.videoContainerNormal, { height: portraitVideoHeight }]}
        onLayout={(event) => {
          let width = event.nativeEvent.layout.width;
          let height = event.nativeEvent.layout.height;
          this.setState({ videoViewWidth: width, videoViewHeight: height });
        }}
        {...this.panResponder.panHandlers}
      >
        {this._renderVideoView()}
        {/*{this._renderClickableView()}*/}
        {/* {this._renderPauseView()} 066放到左下角了，这里隐藏 */}
        {this._renderErrorRetryView()}
        {this._renderLoadingView()}
        {this._renderPlayEndBuyVipView()}
        {
          this.state.deletedStr != null ?
            <Text style={{ color: 'white', zIndex: 99, position: "absolute", alignSelf: "center", top: "50%", display: 'flex' }}>
              {this.state.deletedStr}</Text>
            : null
        }
        {/* {this._renderTitleView()} */}
        {this.state.showTimeLine ? this._renderVideoControlView() : this._renderVideoControlListView()}
        {this.state.fullScreen || Device.isReadonlyShared ? null : this._renderMoreView()}
        {this._renderSnapshotView()}

        {this._renderTimeIndicatorView()}

        {this._renderLandscapeTopButtons()}
        {/*时间轴全屏 右侧的截图也去掉*/}
        {/*{this._renderLandscapeRightButtons()}*/}
        {
          this.state.mNoVideoEvent != null ?
            <View style={{ flexDirection: "column", width: "100%", height: '100%', display: "flex", alignItems: "center", zIndex: 99, position: "absolute", alignSelf: "center", backgroundColor: 'black' }}>
              <View
                style={{ width: "100%", height: '100%' }}>
                <Image
                  style={{ alignSelf: "center", width: 56, height: 56, marginTop: '20%' }}
                  source={ require("../../resources2/images/icon_ev_empty_w.png") }/>
                <Text style={{ color: '#999999', fontSize: 12, alignSelf: "center", display: 'flex' }}>
                  {this.state.mNoVideoEvent}</Text>
              </View>
            </View> : null
        }
        {this._renderFastSpeedView()}
      </View>
    );
  }

  _renderClickableView() {
    return (
      <TouchableOpacity
        style={{ width: "100%", height: "100%", backgroundColor: "#00000000", position: "absolute", zIndex: 0 }}
        accessibilityLabel={DescriptionConstants.rp_62}
        onPress={() => {
          console.log("------------")
          this._onVideoClick();
        }}
      >
      </TouchableOpacity>
    );
  }
  // 这里是滑动时产生的时间条
  _renderTimeIndicatorView() {
    return (
      <View
        style={{ width: "100%", height: "100%", position: "absolute", display: "flex", justifyContent: "center", alignItems: "center", marginTop: -50 }}
        pointerEvents={"none"}
      >
        <CenterTimeView
          ref={(ref) => {
            this.timeIndicatorView = ref;
          }}
        >

        </CenterTimeView>
      </View>
    );
  }

  _renderLandscapeRightButtons() {
    if (!this.state.fullScreen) {
      return null;
    }
    if (!this.state.showTimeLine) {
      return null;
    }
    if (!this.state.showPlayToolBar) {
      return null;
    }

    let screenHeight = Dimensions.get("window").height;
    let screenWidth = Dimensions.get("window").width;
    if (screenWidth < screenHeight) { // mi 9 se上 第一次切换回来，获取的高度是竖屏下的高度，需要修改以下。
      screenHeight = screenWidth;
    }
    let viewHeight = screenHeight - 120;
    let StatusBarheight = null, haveStatus = false;
    if (Platform.OS === "android") {
      StatusBarheight = StatusBar.currentHeight - 16;
      if (StatusBarheight <= 0) {
        haveStatus = true;
        StatusBarheight = 15;
      }
      // -16 因为返回按钮的图片留白太大
    } else {
      StatusBarheight = StatusBarUtil._getInset("top") - 30;
    }
    return (
      <View style={{ position: "absolute", justifyContent: "space-around", bottom: 0, right: this.displayCutoutTop - 16, top: Host.isPad ? 80 : 50, height: viewHeight - 50, width: 80, alignItems: "center" }}>


        {/* 目前的 - Lottie动画按钮 */}
        {
          this.showSnapShot ?
            <MHLottieSnapLandscapeButton
              style={{ width: 50, height: 50 }}
              onPress={handlerOnceTapWithToast(() => {
                this._startSnapshot();
                this._hidePlayToolBarLater();
              })}
              displayState={MHLottieSnapLandscapeBtnDisplayState.NORMAL}
              accessibilityLabel={DescriptionConstants.rp_26}
            /> : null
        }


      </View>
    );
  }

  _renderLandscapeTopButtons() {

    if (!this.state.fullScreen) {
      return null;
    }

    if (!this.state.showPlayToolBar) {
      return null;
    }

    let screenHeight = Dimensions.get("window").height;
    let screenWidth = Dimensions.get("window").width;
    if (screenWidth < screenHeight) { // mi 9 se上 第一次切换回来，获取的高度是竖屏下的高度，需要修改以下。
      screenHeight = screenWidth;
    }

    let iconBack = require("../../Resources/Images/icon_back_black_nor_dark.png");
    let iconBackPre = require("../../Resources/Images/icon_back_black_nor_dark.png");


    let audioIndex = this.state.speed > 1 ? 2 : (this.state.isMute ? 1 : 0);
    let speedIndex = this.state.speed == 1 ? 0 : (this.state.speed == 2 ? 1 : (this.state.speed == 4 ? 2 : 3));

    let StatusBarheight = null, haveStatus = false;
    if (Platform.OS === "android") {
      StatusBarheight = StatusBar.currentHeight - 16;
      // -16 因为返回按钮的图片留白太大
      if (StatusBarheight <= 0) {
        haveStatus = true;
        StatusBarheight = 15;
      }
    } else {
      StatusBarheight = StatusBarUtil._getInset("top") - 16;
    }

    let speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
    switch (this.state.speed) {
      case 1:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
        break;
      case 2:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X2;
        break;
      case 4:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X4;
        break;
      case 16:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X16;
        break;
      default:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
        break;
    }
    // 这里是全屏的时候 返回键改这里
    return (
      <LinearGradient colors={['#00000077', '#00000000']} pointerEvents={"box-none"}>
        <View style={{ width: "100%", paddingStart: 0, paddingEnd: this.displayCutoutTop, display: "flex", flexDirection: "row" }}>
          <View style={{ flexGrow: 1, display: "flex", flexDirection: "row", justifyContent: "space-between", paddingLeft: 20, paddingTop: Host.isPad ? 35 : 20 }}>
            <ImageButton
              source={iconBack}
              highlightedSource={iconBackPre}
              style={styles.videoControlBarItemImg}
              onPress={() => {
                console.log("back To 竖屏");
                this.toPortrait();// 切换到竖屏
                this._hidePlayToolBarLater(true);
              }}
              accessibilityLabel={DescriptionConstants.rp_22}
            >
            </ImageButton>
            <View>
              <View style={{ display: "flex", flexDirection: "row", height: 50, justifyContent: 'flex-end' }}>
                {/* 目前的 - Lottie动画按钮 */}
                {
                  this.showSnapShot ?
                    <MHLottieSnapToolButton
                      style={Object.assign({ marginRight: 20 }, styles.videoControlBarItemImg)}
                      onPress={handlerOnceTapWithToast(() => {
                        TrackUtil.reportClickEvent("TimeSlider_ScreenShot_ClickNum");
                        this._startSnapshot();
                        // this._hidePlayToolBarLater();
                      })}
                      displayState={MHLottieSnapToolBtnDisplayState.NORMAL}
                      landscape={this.state.fullScreen}
                      accessibilityLabel={this.state.speed <= 1 ? DescriptionConstants.rp_1 : DescriptionConstants.rp_1_1}

                    />: null
                }
                <MHLottieAudioToolButton
                  style={{ width: 50, height: 50, marginRight: 20 }}
                  onPress={() => {
                    this._hidePlayToolBarLater();
                    if (this.state.isMute) {
                      // 默认是这个状态，去开启声音
                      if (this.state.isCalling) {
                        this.isAudioMuteTmp = false;
                      }
                      this._toggleAudio(false, true);
                    } else {
                      if (this.state.isCalling) {
                        this.isAudioMuteTmp = true;
                      }
                      this._toggleAudio(true, true);
                    }
                  }}
                  displayState={this.state.isMute ? MHLottieAudioToolBtnDisplayState.MUTED : MHLottieAudioToolBtnDisplayState.NORMAL}
                  landscape={true}
                  disabled={this.state.speed > 1}
                  accessibilityLabel={this.state.speed <= 1 ?
                    this.state.isMute ? DescriptionConstants.rp_3 : DescriptionConstants.rp_24 :
                    DescriptionConstants.rp_3} />
                <MHLottieSpeedToolButton
                  style={styles.videoControlBarItemImg}
                  onPress={() => {
                    this._hidePlayToolBarLater();
                    this.setState({ dialogVisibility: true });
                  }}
                  displayState={speedDisplayState}
                  landscape={true}
                  disabled={this.state.isRecording}
                  accessible={true}
                  accessibilityLabel={DescriptionConstants.rp_25.replace('1', this.state.speed)}
                  accessibilityState={{
                    disabled: this.state.isRecording
                  }}
                  testId={speedDisplayState} />

                {
                  Device.isReadonlyShared || (this.state.showTimeLine && this.state.fullScreen) ?
                    null : <ImageButton
                      source={require("../../Resources/Images/icon_more.png")}
                      highlightedSource={require("../../Resources/Images/icon_more.png")}
                      style={styles.videoControlBarItemImg}
                      onPress={() => {
                        this._hidePlayToolBarLater();
                        this.setState({ showMoreDlg: true });

                      }}
                      onLayout={(e) => {
                        NativeModules.UIManager.measure(e.target, (x, y, width, height, pageX, pageY) => {
                          this.moreIconY = pageY;
                        });
                      }}
                      accessibilityLabel={DescriptionConstants.rp_22} />
                }

              </View>
            </View>
          </View>
        </View>
      </LinearGradient>

    );
  }
  playPauseClick() {
    TrackUtil.reportClickEvent("TimeSlider_PlayPause_ClickNum");
    // todo pause
    console.log(TAG,"playPauseClick",this.state.displayCloudList,this.isUserPause);
    if (!this.state.displayCloudList) {
      return;
    }
    this.isUserPause = true;
    if (this.state.showTimeLine) {
      this._startPlay(false);
    } else {
      this.onPlay(false)
    }
  }
  playStartClick() {
    TrackUtil.reportClickEvent("TimeSlider_PlayPause_ClickNum");
    if (!this.state.displayCloudList) {
      return;
    }
    if (this.isMobileNetwork) {
      this.isDataUsageWarning = false;
    }
    this._hidePlayToolBarLater();
    this.isUserPause = false;
    if (this.state.showTimeLine) {
      this._onResume();
    } else {
      this.onPlay(true)
    }
  }

  _renderPauseView() {
    if (!this.state.showPauseView || this.state.fullScreen) {
      return null;
    }
    if (this.state.videoViewWidth == 0 || this.state.videoViewHeight == 0) {
      return null;
    }
    if (this.state.showLoadingView) {
      return null;
    }
    let pauseIcons = [
      {
        source: require("../../Resources/Images/camera_icon_center_pause_nor.png"),
        highlightedSource: require("../../Resources/Images/camera_icon_center_pause_press.png"),
        onPress: () => {
          this.playPauseClick();
        }
      },
      {
        source: require("../../Resources/Images/camera_icon_center_play_nor.png"),
        highlightedSource: require("../../Resources/Images/camera_icon_center_play_nor.png"),
        onPress: () => {
          this.playStartClick();
        }
      }
    ];
    let index = this.state.isPlaying ? 0 : 1;

    return (
      <View style={{ position: "absolute", width: 64, height: 64, top: "50%", left: "50%", marginTop: -32, marginLeft: -32 }}
      >
        <ImageButton
          style={{ width: 64, height: 64 }}
          source={pauseIcons[index].source}
          highlightedSource={pauseIcons[index].highlightedSource}
          onPress={pauseIcons[index].onPress}
          accessibilityLabel={this.state.isPlaying ?
            !this.state.fullScreen ? DescriptionConstants.rp_5 : DescriptionConstants.rp_23 :
            !this.state.fullScreen ? DescriptionConstants.hk_3_6 : DescriptionConstants.yc_16}

        />
      </View>
    );
  }

  _renderMoreView() {
    if (!this.state.showPlayToolBar) {
      return null;
    }
    if (this.state.showTimeLine) {
      return null;
    }
    if (this.state.videoViewWidth == 0 || this.state.videoViewHeight == 0) {
      return null;
    }

    return (
      <LinearGradient colors={['#00000088', '#00000000']} style={{ position: "absolute", width: "100%", top: 0, paddingBottom: 10 }}>
        <View style={{  flexGrow: 1, display: "flex", justifyContent: 'flex-end', alignItems: 'flex-end' }}>
          <ImageButton
            style={{ width: 40, height: 40, marginRight: 5, marginTop: 3 }}
            source={require("../../Resources/Images/icon_more.png")}
            onPress={() => {
              this.setState({ showMoreDlg: true })
              // 埋点--点击查看更多
              TrackUtil.reportClickEvent('Monitoring_CloudStorageTab_ViewList_ViewMore_ClickNum')
            }}
            accessibilityLabel={this.state.isPlaying ?
              !this.state.fullScreen ? DescriptionConstants.rp_5 : DescriptionConstants.rp_23 :
              !this.state.fullScreen ? DescriptionConstants.hk_3_6 : DescriptionConstants.yc_16}
            onLayout={(e) => {
              NativeModules.UIManager.measure(e.target, (x, y, width, height, pageX, pageY) => {
                this.moreIconY = pageY;
              });
            }}
          />
        </View>
      </LinearGradient>
    );
  }

  _renderMoreItemDialog() {
    let isAlarm = false;
    if (this.videoItem && this.videoItem.isAlarm != undefined) {
      isAlarm = this.videoItem.isAlarm;
    } else if (this.videoItem) {
      isAlarm = this.videoItem.duration ? this.videoItem.duration <= 12 : false;
    }
    // let modalStyle = this.state.fullScreen ? {width: kWindowHeight * 0.5, height: kWindowWidth * 0.6, alignSelf: 'center', bottom: kWindowWidth * 0.2, borderBottomLeftRadius: 20, borderBottomRightRadius: 20} : {}
    // 使用right绝对定位到右侧，ios会出现闪现左侧再显示到右侧问题
    let left = this.state.fullScreen ? kWindowHeight - 220 : kWindowWidth - 220;
    let width = this.state.fullScreen && Host.isPad && Host.isAndroid ? 320 : 200;
    let modalStyle = {
      width: width,
      top: this.moreIconY + 46,
      left: left,
      // right: 20,
      height: isAlarm ? 54 * 3 + 20 : 54 * 2 + 20,
      alignSelf: 'center',
      borderRadius: 16,
      paddingVertical: 10,
      backgroundColor: DarkMode.getColorScheme() == "dark" ? 'xm#1a1a1a' : 'xm#ffffff'

    };
    return (
      <NearHandMJDialog
        style={modalStyle}
        showTitle={false}
        visible={this.state.showMoreDlg}
        showButton={false}
        onDismiss={() => {
          this.setState({ showMoreDlg: false });
        }}
        canDismiss={true}
        useNewTheme={true}
      >
        <View style={{flexGrow: 1}}>
          <TouchableOpacity
            style={styles.moreItemContainer}
            onPress={() => {
              this._showConfirmTips(true);
              this.setState({ showMoreDlg: false });
            }}>
            <Text style={styles.moreItemText}>{LocalizedStrings['f_download']}</Text>
          </TouchableOpacity>
          { isAlarm ? <TouchableOpacity
            style={styles.moreItemContainer}
            onPress={() => {
              this.doDld(true);
              this.setState({ showMoreDlg: false });
            }}>
            <Text style={styles.moreItemText}>{LocalizedStrings['share_files']}</Text>
          </TouchableOpacity> : null }
          <TouchableOpacity
            style={styles.moreItemContainer}
            onPress={() => {
              this._showConfirmTips(false);
              this.setState({ showMoreDlg: false });
            }}>
            <Text style={[styles.moreItemText, { color: "#F43F31" }]}>{LocalizedStrings['f_delete']}</Text>
          </TouchableOpacity>
        </View>

      </NearHandMJDialog>
    );
  }

  _renderDownloadHint() {
    if (!this.state.showDownloadHint) {
      return null;
    }
    return (
      <View style={{
        position: "absolute",
        bottom: 0, height: 90, width: "100%", display: "flex", flexDirection: "row",
        justifyContent: "space-between",
        alignItems: "center",
        backgroundColor: "#ffffff",
        borderTopWidth: 1,
        borderTopColor: "rgba(0,0,0,0.15)"
      }}
      >
        <Text
          style={[BaseStyles.text16, { paddingHorizontal: 10, flex: 1, marginLeft: 28, marginRight: 10 }]}
        >
          {LocalizedStrings["video_already_download"]}
        </Text>

        <TouchableOpacity style={{ backgroundColor: 'rgba(50, 186, 192, 0.1)', borderRadius: 17, minHeight: 34, paddingHorizontal: 10, marginRight: 28,  justifyContent: 'center' }}
          onPress={() => {
            StackNavigationInstance.jumpToStackNavigationPage_forSDCloudPage("DldPage", { preOri: this.state.fullScreen ? "landscape" : "portrait" });
          }}
        >
          <Text style={[BaseStyles.text13, { color: "#32BAC0", fontWeight: '400', minWidth: 60, textAlign: 'center' }]}>{LocalizedStrings["check_view"]}</Text>
        </TouchableOpacity>
      </View>
    );
  }
  _renderDownloadConfirmDialog() {

    return (
      <MessageDialog
        visible={this.state.downloadVisible}
        canDismiss={false}
        modalStyle={{ width: "100%" }}
        message={this.state.mDlgTips}
        messageStyle={[BaseStyles.text17, { alignSelf: 'center', color: '#333333', fontWeight: '400' }]}
        onDismiss={() => {
          this.setState({ downloadVisible: false });
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: (_) => this.setState({ downloadVisible: false })
            // ignore
          },
          {
            text: LocalizedStrings["f_download"],
            callback: (_) => {
              this.setState({ downloadVisible: false });
              this.doDld(false);
            }
            // ignore
          }
        ]}
      />
    );
  }
  _renderDeleteConfirmDialog() {

    return (
      <MessageDialog
        visible={this.state.deleteVisible}
        canDismiss={false}
        modalStyle={{ width: "100%" }}
        message={this.state.mDlgTips}
        messageStyle={[BaseStyles.text17, { alignSelf: 'center', color: '#333333', fontWeight: '400' }]}
        onDismiss={() => {
          this.setState({ deleteVisible: false });
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: (_) => this.setState({ deleteVisible: false })
            // ignore
          },
          {
            text: LocalizedStrings["delete_confirm"],
            callback: (_) => {
              this.setState({ deleteVisible: false });
              this.deletePressed();
            }
            // ignore
          }
        ]}
      />
    );
  }

  _renderErrorReportDialog() {

    return (
      <MessageDialog
        visible={this.state.errReportPermissionVisible}
        canDismiss={false}
        title={LocalizedStrings["cloud_face_rec_error"]}
        message={LocalizedStrings["mydevice.camera.motion.report.permission.no.policy"]}
        messageStyle={[BaseStyles.text17, { alignSelf: 'center', color: '#333333', fontWeight: '400', lineHeight: 23 }]}
        onDismiss={() => {
          this.setState({ errReportPermissionVisible: false });
        }}
        buttons={[
          {
            text: LocalizedStrings["btn_cancel"],
            callback: (e) => {
              console.log('onCancel err report permission', e);
              this.setState({ errReportPermissionVisible: false });
            }
          },
          {
            text: LocalizedStrings["btn_confirm"],
            callback: (e) => {
              console.log('onConfirm err report permission', e, this.mReportType);
              this.setState({ errReportPermissionVisible: false });
              Util.reportFaceRecError(this.state.item, this.mReportType)
                .then((aRet) => {
                  if (aRet.code == 0) {
                    Toast.success("cloud_face_feed_thx");
                    return;
                  }
                  console.log(this.tag, "reportFaceRecError", aRet);
                  Toast.fail("action_failed");
                })
                .catch((aErr) => {
                  console.log(this.tag, "reportFaceRecError err", aErr, this.mReportType);
                  Toast.fail("action_failed");
                });
            }
          }
        ]}
      />
    );
  }

  _showConfirmTips(isDownload) {
    // 下载一个事件视频时直接下载，所以非云存看家和一个事件的看家视频直接下载，多个事件的则提示
    // 删除一个事件视频提示‘确认删除’， 多个事件提示多个事件删除
    let mDlgTips = isDownload ? LocalizedStrings['kanjia_download_tips_single'] : LocalizedStrings['kanjia_delete_tips_single'];
    let elist = this.mEvList.getEventListOfCurentFile(this.state?.item?.fileId);
    let count = elist.length;
    if (count > 1) {
      let mStr = isDownload ? LocalizedStrings['kanjia_download_tips'] : LocalizedStrings['kanjia_delete_tips'];
      mDlgTips = Util.fmtStr(mStr, count);
    } else {
      if (isDownload) {
        this.doDld(false);
        return;
      }
    }

    if (isDownload) {
      this.setState({ downloadVisible: true, mDlgTips });
    } else {
      this.setState({ deleteVisible: true, mDlgTips });
    }
  }

  checkStoragePerm() {
    if (this.destroyed) {
      return Promise.reject('Component unmounted');
    }

    this.mPermReq = true;
    return new Promise((resolve, reject) => {
      if (Platform.OS === "android") {
        PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
          .then((granted) => {
            if (granted === PermissionsAndroid.RESULTS.GRANTED) {
              this.mPermReq = false;
              resolve();
            } else {
              this.mPermReq = false;
              reject("camera_no_write_permission");
            }
          }).catch((error) => {
            this.mPermReq = false;
            reject("action_failed");
          });
      } else {
        System.permission.request("photos").then((res) => {
          this.mPermReq = false;
          resolve();
        }).catch((error) => {
          this.mPermReq = false;
          reject("unauthorized_album_permission");
        });
      }
    }).finally(() => {
      if (!this.destroyed) {
        this.mPermReq = false;
      }
    });
  }

  // 下载
  doDld(aForShare = false) {

    this.checkStoragePerm()
      .then(() => {
        if (aForShare && this.isShareDownloading) {
          Toast.loading('c_setting');
          return;
        }
        let basePath = Host.file.storageBasePath;
        let name = AlbumHelper.getDownloadTargetPathName(true);
        let path = `${basePath}/${name}`;
        this.mDldInfo["name"] = name;
        this.mDldInfo["path"] = path;
        this.mDldInfo["share"] = aForShare;
        if (aForShare) {
          Toast.loading('c_setting');
          // Stat.reportEvent(StatEV.SHARE, null);
          this.mLoader.download(this.state.item, path, this);
          this.isShareDownloading = true;
          this.isCalledForMonitoring && TrackUtil.reportClickEvent('Monitoring_Share_ClickNum');
        } else {
          // Toast.loading('c_download');
          // Stat.reportEvent(StatEV.DOWNLOAD, null);
          this.setState({ showDownloadHint: true });
          setTimeout(() => {
            this.setState({ showDownloadHint: false });
          }, 5000);
          //maybe should do
          // this.addInfoForPush();
          DldMgr.addDld([this.state.item], this.mLoader);
          this.isCalledForMonitoring ? TrackUtil.reportClickEvent('Monitoring_Save_ClickNum') : TrackUtil.reportClickEvent('Storage_CloudStorage_Download');
        }
      })
      .catch((err) => {
        console.log(this.tag, err);
        Toast.success(err);
      });
  }

  onDldProgress(aProgress) {
    console.log("cloudV2", "onDldProgress", aProgress, "for", this.mDldInfo["path"], "name", this.mDldInfo["name"]);
    if (this.mDldInfo["path"]) {
      switch (aProgress) {
        case DldStatus.Complete:
          if (this.mDldInfo["share"]) {
            this.isShareDownloading = false;
            if (this.isPageForeGround) {
              if (Platform.OS == 'android' && this.state.fullScreen) {
                this.toPortrait();
                this.playToolbar && this.playToolbar.updateOrientation(false);
              }
              Host.ui.openSystemShareWindow(this.mDldInfo["path"]);
            }
          } else {
            AlbumHelper.saveToAlbum(this.mDldInfo["name"], true)
              .then((result) => {
                Toast.success('save_success');
              })
              .catch((err) => {
                Toast.success('save_faild');
                console.log(err);
              });
          }
          this.mDldInfo = {};
          break;
        case DldStatus.Err:
          this.mDldInfo = {};
          Toast.fail('camera_play_error_file');
          break;
        default:
          break;
      }
    }
  }

  onDldCancelResult(fileId, state) {
    console.log('sharedownload is canceled', fileId, state);
  }
  deletePressed() {
    let item = this.state.item;
    // Stat.reportEvent(StatEV.DELETE, null);
    TrackUtil.reportClickEvent('Monitoring_Delete_ClickNum');
    this.mLoader.delete([item])
      .then((aRet) => {
        try {
          if (!this.mDelSet) {
            this.mDelSet = new Set();
          }
          this.mDelSet.add(item.fileId);
          console.log(this.tag, "delete  complete", aRet);
          let newPlay = this.mEvList.getNearVideoEvent(item);

          Toast.show("delete_success");
          this.mEvList.removeEvents((aItm) => { return aItm.fileId != item.fileId; });
          if (newPlay != null && newPlay.fileId != item.fileId) {
            this.setState({
              item: newPlay
            });
            this.isEventListItem = true;
            this._startQueryNetwork(newPlay);
            // this.startPlayer(newPlay);
            let logstr = `item fileid ${item.fileId}, newPlay fileid ${newPlay.fileId}`;
            Service.smarthome.reportLog(Device.model, logstr);
          } else {
            this.toPortrait();
            // this.pushBack();
            // this.props.navigation.goBack();
          }
        } catch (err) {
          LogUtil.logOnAll("AlarmVideoUI delete file err=", err);
          Toast.show("delete_success");
          this.toPortrait();
        }
      })
      .catch((aErr) => {
        console.log(this.tag, "delete", aErr);
        Toast.fail("delete_failed");
      });
  }

  _renderFastSpeedView() {
    if (!this.state.showFastSpeedView) {
      return null;
    }

    if (this.state.videoViewWidth == 0 || this.state.videoViewHeight == 0) {
      return null;
    }


    if (this.state.showLoadingView) {
      return null;
    }

    return (
      <View style={{
        opacity: 0.6, backgroundColor: "#000000", position: "absolute", width: 100, height: 64, top: "50%", left: "50%",
        marginTop: -32, marginLeft: -50, alignItems: "center", justifyContent: "center", borderRadius: 20
      }}>
        <Text style={{ fontSize: 34, color: "xm#ffffff" }}>x2</Text>
      </View>
    );
  }


  _renderVideoView() {
    if (this.state.videoPath == null) {
      return null;// 没有的时候 就这么滴
    }
    return (
      <Video
        ref={(ref) => { this.video = ref; }}
        style={{ position: 'absolute', top: 0, left: 0, bottom: 0, right: 0 }}
        source={{ uri: this.state.videoPath }}
        muted={this.state.isMute}
        paused={!this.state.isPlaying}
        resizeMode="contain"
        onEnd={this.onEnd}
        onLoad={this.onLoad}
        onError={this.onError}
        onBuffer={this.onBuffer}
        playInBackground={false}
        playWhenInactive={false}
        repeat={false}
        onProgress={this.onProgress}
        onSeek={this.onSeek}
        controls={false}
        onPress={this._onVideoClick}
        rate={this.state.speed}
        ignoreSilentSwitch={"ignore"}
      />
    );
  }

  _renderErrorRetryView() {
    if (!this.state.showErrorView) {
      return null;
    }
    let errorDesc = "";
    let errorButton = "";
    if (this.state.displayCloudList) {
      // 播放失败了
      errorDesc = this.state.showEmptyHint ? LocalizedStrings["sdcard_page_desc_empty"] : LocalizedStrings["common_net_error"];
      errorButton = this.state.showEmptyHint ? LocalizedStrings["camera_connect_retry"] : LocalizedStrings["reconnect_button_text"];
    } else {
      errorButton = this.isCloudServer ? LocalizedStrings["eu_click_buy_cloud"] : LocalizedStrings["go_buy_cloud"];
      if (this.isInExpireWindow) { // 過期窗口期內
        errorDesc = this.isCloudServer ? LocalizedStrings["eu_cloud_vip_end"] : LocalizedStrings["cloud_vip_end"];
      } else {
        errorDesc = this.isCloudServer ? LocalizedStrings["eu_cloud_vip_no_open"] : LocalizedStrings["cloud_vip_no_open"];
      }
    }

    let buttonReConnectItem = (
      <View
        style={{
          paddingTop: 6,
          paddingBottom: 6,
          paddingLeft: 17,
          paddingRight: 17,
          backgroundColor: "#249A9F",
          borderRadius: 20,
          marginTop: 10
        }}>
        <Text style={{
          color: "#fff",
          fontSize: kIsCN ? 12 : 10
        }}
        >{errorButton}</Text>
      </View>
    );

    let noNetworkItem = (
      <View style={{ display: "flex", flexDirection: "row" }}>
        <TouchableOpacity
          style={{ display: "flex", alignItems: "center" }}
          onPress={() => {
            // this.setState({ showErrorView: false });
            if (this.state.displayCloudList) {
              NetInfo.fetch().then((networkInfo) => {
                if (Platform.OS != 'android') { // ios返回obj  android返回type str
                  networkInfo = networkInfo.type.toUpperCase();
                }
                if (networkInfo == "NONE" || networkInfo == "UNKNOWN") {
                  Toast.show("common_net_error");
                  return;
                }
                this.setState({ showErrorView: false });
                Service.smarthome.reportLog(Device.model, "on error Retry");
                if (!this.isFileReceived) {
                  this._initData();//
                  return;
                }
                this._startPlay(false);
              }).catch((err) => {
                LogUtil.logOnAll(TAG, "_renderErrorRetryView", "err-=", JSON.stringify(err));
              });
            } else {
              if (!Device.isOwner) {
                Toast.success("share_user_permission_hint");
                return;
              }
              LogUtil.logOnAll(TAG, "云存储回看点击了购买云存按钮");
              API_LEVEL > 10068 ? Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: 'playback_button' }) : Service.miotcamera.showCloudStorage(true, false);
              CameraConfig.isToUpdateVipStatue = true;
            }
          }}>
          {buttonReConnectItem}
        </TouchableOpacity>

      </View>

    );

    const errIcons = [
      require("../../Resources/Images/icon_connection_failure.png"),
      require("../../Resources/Images/icon_camera_offline.png"),
      require("../../Resources/Images/icon_camera_fail.png")
    ];

    let errIconIndex = 0;
    if (!Device.isOnline) {
      errIconIndex = 1;
    }

    return (
      <View
        style={{ zIndex: 7, position: "absolute", bottom: 0, backgroundColor: "black", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <View
          style={{ display: "flex", alignItems: "center" }}
        >
          <Image
            style={{ width: 54, height: 54 }}
            source={errIcons[errIconIndex]} />
          <Text
            style={{ marginTop: 10, fontSize: kIsCN ? 12 : 10, color: "#999", paddingHorizontal: 10, textAlign: "center", width: kWindowWidth - 60 }}>
            {errorDesc}
          </Text>
          {/* {Device.isOnline ? null : powerOfflineText} */}
        </View>
        {noNetworkItem}
      </View>
    );
    // todo render errorRetryView not
  }


  _renderLoadingView() {
    // todo render loading view 
    if (!this.state.showLoadingView || this.state.deletedStr) {
      return null;
    }

    let bgColor = "transparent";
    let loadingViewStyle = {
      zIndex: 0,
      position: "absolute",
      width: "100%",
      height: "100%",
      bottom: 0,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: bgColor
    };

    return (
      <View
        style={loadingViewStyle}
      >
        <LoadingView
          style={{ width: 54, height: 54 }}
        />
        <Text
          style={{ marginTop: 10, fontSize: kIsCN ? 12 : 10, color: "#ffffff" }}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );
  }

  // 播放结束，非VIP购买
  _renderPlayEndBuyVipView() {
    if (this.isVip || this.state.showLoadingView || this.state.deletedStr) {
      return null;
    }

    if (!this.state.showPlayEndView) {
      return null;
    }

    return (
      <View
        pointerEvents={"box-none"}
        style={{ zIndex: 0, position: "absolute", bottom: 0, backgroundColor: "rgba(0, 0, 0, 0.7)", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}>
        <View
          style={{ display: "flex", alignItems: "center" }}>
          <Text
            style={{ fontSize: kIsCN ? 16 : 14, color: "#FFFFFF", paddingHorizontal: 10, textAlign: "center", width: kWindowWidth - 60 }}>
            {LocalizedStrings['play_end_cloud_buy_tips']}
          </Text>
        </View>
        <View style={{ display: "flex", flexDirection: "row" }}>
          <TouchableOpacity
            style={{ display: "flex", alignItems: "center" }}
            onPress={() => {
              if (!Device.isOwner) {
                Toast.success("share_user_permission_hint");
                return;
              }
              this.toPortrait();
              Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: 'videodetails_button' });
              CameraConfig.isToUpdateVipStatue = true;
              this.setState({ showBuyCloudVideoTip: false });
              // 埋点--云存视窗推广点击
              TrackUtil.reportClickEvent("Monitoring_CloudStorageTab_Introduce_ClickNum")
            }}>
            <View
              style={{
                paddingTop: 8,
                paddingBottom: 8,
                paddingLeft: 20,
                paddingRight: 20,
                backgroundColor: "#249A9F",
                borderRadius: 20,
                marginTop: 18
              }}>
              <Text style={{
                color: "#fff",
                fontSize: kIsCN ? 14 : 12
              }}
              >{LocalizedStrings['play_end_cloud_buy']}</Text>
            </View>
          </TouchableOpacity>

        </View>
      </View>
    );

  }


  onBackPress = () => {
    this.props.navigation.goBack();
    this._startPlay(false);// 测试
  }

  _renderVideoControlView() {
    if (this.state.fullScreen) {
      return null;
    }

    let speedIndex = this.state.speed == 1 ? 0 : (this.state.speed == 2 ? 1 : (this.state.speed == 4 ? 2 : 3));


    let speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
    switch (this.state.speed) {
      case 1:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
        break;
      case 2:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X2;
        break;
      case 4:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X4;
        break;
      case 16:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X16;
        break;
      default:
        speedDisplayState = MHLottieSpeedToolBtnDisplayState.X1;
        break;
    }

    if (this.state.showPlayToolBar) {
      return (

        <LinearGradient pointerEvents={"box-none"} colors={this.state.fullScreen ? ['#00000000', '#00000077'] : ['#00000000', '#00000077']} style={[{ position: "absolute", width: "100%" }, this.state.fullScreen ? { top: 0 } : { bottom: 0 }]}>

          <View style={this.state.fullScreen ? styles.videoControlBarFull : styles.videoControlBar}>
            {/* 目前的 - Lottie动画按钮 */}
            <View style={styles.videoControlBarItem}>
              <TouchableWithoutFeedback
                onPress={() => {
                  if (!this.state.isPlaying) {
                    this.playStartClick();
                  } else {
                    this.playPauseClick();
                  }
                }}>
                <Image style={styles.videoControlBarItemImg}
                  source={!this.state.isPlaying ? require("../../Resources/Images/icon_video_play.png") : require("../../Resources/Images/icon_video_pause.png")}>
                </Image>
              </TouchableWithoutFeedback>
            </View>
            {
              this.showSnapShot ?
                <View style={styles.videoControlBarItem}>

                  <MHLottieSnapToolButton
                    style={styles.videoControlBarItemImg}
                    onPress={handlerOnceTapWithToast(() => {
                      TrackUtil.reportClickEvent("TimeSlider_ScreenShot_ClickNum");
                      this._startSnapshot();
                      this._hidePlayToolBarLater();
                    })}
                    displayState={MHLottieSnapToolBtnDisplayState.NORMAL}
                    landscape={this.state.fullScreen}
                    accessibilityLabel={this.state.speed <= 1 ? DescriptionConstants.rp_1 : DescriptionConstants.rp_1_1}

                  />
                </View>

                : null
            }

            <View style={styles.videoControlBarItem}>
              <MHLottieSpeedToolButton
                style={styles.videoControlBarItemImg}

                onPress={() => {
                  this.setState({ dialogVisibility: true });
                }}
                displayState={speedDisplayState}
                landscape={false}

                disabled={this.state.isRecording}
                accessible={true}
                accessibilityLabel={!this.state.isRecording ? DescriptionConstants.rp_2.replace('1', this.state.speed) : DescriptionConstants.rp_2_1}
                testId={
                  speedDisplayState
                }
              />
            </View>

            <View style={styles.videoControlBarItem}>

              <MHLottieAudioToolButton
                style={styles.videoControlBarItemImg}

                onPress={this.mutePressed}

                displayState={this.state.isMute ? MHLottieAudioToolBtnDisplayState.MUTED : MHLottieAudioToolBtnDisplayState.NORMAL}
                landscape={this.state.fullScreen}
                disabled={this.state.speed > 1}
                accessibilityLabel={this.state.isMute ? DescriptionConstants.rp_3 : DescriptionConstants.rp_24}

              />
            </View>

            <View style={styles.videoControlBarItem}>

              <MHLottieFullScreenToolButton
                style={styles.videoControlBarItemImg}

                onPress={() => this.fullscreenPressed()}

                displayState={MHLottieFullScreenToolBtnDisplayState.NORMAL}
                accessibilityLabel={DescriptionConstants.rp_4}

              />
            </View>


          </View>
        </LinearGradient>
      );

    } else {
      return (null);
    }
  }
  _renderVideoControlListView() {
    // if (this.state.fullScreen) {
    //   return null;
    // }
    if (this.state.showPlayToolBar) {
      return (

        <LinearGradient pointerEvents={"box-none"} colors={this.state.fullScreen ? ['#00000000', '#00000077'] : ['#00000000', '#00000077']} style={[{ position: "absolute", width: "100%" }, this.state.fullScreen ? { bottom: 0 } : { bottom: 0 }]}>
          <View style={this.state.fullScreen ? styles.videoControlBarFull : styles.videoControlBar}>
            <PlayerToolbarV2
              ref={(ref) => { this.playToolbar = ref; }}
              duration={this.duration}
              isPlaying={this.state.isPlaying}
              isMute={this.state.isMute}
              startTime={Math.max(0, this.mCurTime)}
              isFullscreen={this.state.fullScreen}
              playPressed={(aPlaying) => {
                if (aPlaying) {
                  this.isUserPause = false;
                } else {
                  this.isUserPause = true;
                }
                this.onPlay(aPlaying);
              }}
              snapShotPressed={handlerOnceTapWithToast(() => {
                this._startSnapshot();
              })}
              mutePressed={this.mutePressed}
              fastPlayPressed={() => {
                this.setState({ dialogVisibility: true });
              }}
              onSeeked={this.onSeeked}
              fullscreenPressed={() => this.fullscreenPressed()}
              isFastPlay={this.state.fastPlay}
            />
          </View>
        </LinearGradient>
      );

    } else {
      return (null);
    }
  }
  onPlay(aPlaying) {
    if (this.isPlayError) {
      this._startQueryNetwork();
      return;
    }
    this.mShowBuyCloudTip = false;
    this.setState({ showPlayEndView: false });
    this._toggleAudio(this.state.speed > 1 || CameraConfig.getUnitMute());
    this.updatePlayState(aPlaying);
    this._hidePlayToolBarLater();
  }

  updatePlayState(aPlaying) {
    console.log(this.tag, "updatePlayState", aPlaying);
    this.setState({ isPlaying: aPlaying });

    if (this.playToolbar) {
      this.playToolbar.updatePlayState(aPlaying);
    }
  }
  mutePressed = (isMute) => {
    this._hidePlayToolBarLater();
    if (this.state.isMute) {

      // 默认是这个状态，去开启声音
      if (this.state.isCalling) {
        this.isAudioMuteTmp = false;
      }
      this._toggleAudio(false, true);
    } else {

      if (this.state.isCalling) {
        this.isAudioMuteTmp = true;
      }
      this._toggleAudio(true, true);
    }
  }

  fastPlayProessed = (speed) => {
    // TrackUtil.reportClickEvent("TimeSlider_PlaySpeed_ClickNum");
    // this.setState({ dialogVisibility: true });
    // this._hidePlayToolBarLater();
    let tempSpeed = 1;
    switch (this.state.speed) {
      case 1:
        tempSpeed = 2;
        break;
      case 2:
        tempSpeed = 1;
        break;
      default:
        tempSpeed = 1;
        break;
    }
    this.setState(() => { return { speed: tempSpeed, fastPlay: tempSpeed > 1 } }, () => {
      if (tempSpeed > 1) {
        this._toggleAudio(true, false);
      } else {
        this._toggleAudio(CameraConfig.getUnitMute(), false);
      }
    });
  }

  onSeeked = (aPos) => {
    let target = this.duration * aPos;
    console.log("seek end", "seek to ", aPos, "=>", target);
    this.mSeeking = true;
    if (this.duration == target) {
      // 拖动时间轴到最后
      target = target - 0.1;
      console.log("==============================",target);
    }
    this.offset = target;
    this.video && this.video.seek(target);
    let currentTime = aPos * 1000;
    let startTime = this.videoItem.startTime ? this.videoItem.startTime : this.videoItem.createTime;
    if (this.cloudItem) {
      this.toStartTime = this.cloudItem.startTime + currentTime;
    } else {
      this.toStartTime = startTime + currentTime;
    }
    this.refreshEventListTime = Date.now();
    this.onSeeked_NeedScroll = true;
    this.mEvList._getPlayingOffset(this.toStartTime, true);
  }

  fullscreenPressed() {
    this._hidePlayToolBarLater();
    if (!this.state.fullScreen) {
      this.toLandscape();
    } else {
      this.toPortrait();
    }
  }

  _renderSnapshotView() {
    if (!this.state.screenshotVisiblity) {
      return null;
    }

    let recordItem = (
      <LinearGradient colors={['#00000000', '#00000077']} style={{ position: 'absolute', bottom: 0, width: "100%", height: "30%",borderRadius: 4}}>
        <View style={{
          display: "flex",
          flexDirection: "row",
          bottom: 2,
          left: 6,
          position: "absolute",
          alignItems: "center"
        }}>
          <Image style={{ width: 10, height: 10 }} source={require("../../Resources/Images/icon_snapshot_camera_play.png")}></Image>
          <Text style={{ fontSize: kIsCN ? 12 : 10, fontWeight: "bold", color: "white", marginLeft: 5 }}>{this.lastRecordTime}</Text>
        </View>
      </LinearGradient>
    );

    let sWidth = 90;
    let sHeight = 55;
    let sPadding = 20;
    let leftPading = 15;
    if (this.state.fullScreen) {
      sPadding = 90;
      leftPading = leftPading + StatusBarUtil._getInset("top");

    } else {
    }

    let containerStyle;

    containerStyle = {
      position: "absolute",
      left: leftPading,
      top: sPadding,
      width: sWidth,
      height: sHeight,
      borderRadius: 4,
      borderWidth: 1.5,
      borderColor: "xm#ffffff",
      zIndex: 4
    };

    if (this.state.fullScreen) {
      if (Platform.OS == "ios" && isIphoneX()) {
        containerStyle.left = 60;
      }

      if (Host.isPad) {
        containerStyle.top = "50%";
        containerStyle.marginTop = -1 * sHeight / 2;
      }

    }

    return (
      <View style={containerStyle}>
        <ImageButton
          style={{ width: "100%", height: '100%', borderRadius: 2.5 }}
          source={(this.state.screenshotPath == "") ? null : ({ uri: `file://${Host.file.storageBasePath}/${this.state.screenshotPath}` })}
          fadeDuration={0}
          accessible={true}
          accessibilityLabel={!this.state.fullScreen ? DescriptionConstants.rp_42 : DescriptionConstants.rp_46}
          onPress={() => {
            if (!this.canStepOut()) {
              return;
            }
            clearTimeout(this.snapshotTimeout);
            this.setState({ screenshotVisiblity: false, screenshotPath: "", isWhiteVideoBackground: true });// 点击后就消失。
            if (this.isForVideoSnapshot) {
              console.log("点击了缩略图，跳转到视频页面");
              this.showLastVideo();
              // this.props.navigation.navigate("AlbumVideoViewPage");
            } else {
              console.log("点击了缩略图，跳转到图片页面");
              this.showLastImage();
              // this.props.navigation.navigate("AlbumPhotoViewPage");
            }

            this.isForVideoSnapshot = false;
            // todo jump to album activity
          }}
        />
        {this.isForVideoSnapshot ? recordItem : null}

      </View>
    );
  }


  showLastImage() {
    StackNavigationInstance.jumpToStackNavigationPage_forSDCloudPage("AlbumPhotoViewPage", { preOri: this.state.fullScreen ? "landscape" : "portrait" });
  }

  showLastVideo() {
    StackNavigationInstance.jumpToStackNavigationPage_forSDCloudPage("AlbumVideoViewPage", { preOri: this.state.fullScreen ? "landscape" : "portrait" });
  }

  canStepOut() {

    if (this.state.isSleep) {
      !this.destroyed && Toast.success("camera_power_off");
      return false;
    }
    return true;
  }

  _renderTimeLineView() {
    // 如果这里设置为null，等切屏回来后，就生成了一个新的view。原先绑定的数据就没了。
    // if (this.state.fullScreen) {
    // return null;
    // }
    // if (!this.state.showTimeLine) {
    //   return null;
    // }
    let containerStyle;
    if (this.state.fullScreen) {
      let padding = 0;
      if (Platform.OS == "ios") {
        if (isIphoneX()) {
          padding = this.statusBarHeight + 5;
        }
        padding = this.statusBarHeight + 15;
      }
      containerStyle = {
        position: "absolute",
        bottom: 0,
        paddingLeft: padding,
        paddingRight: padding,
        width: "100%"
      };
    } else {
      containerStyle = { width: "100%", flex: 1 };
    }
    const { touchMovement } = this.state;

    return (// scaletimelineView自己处理宽高。
      <View style={[!this.state.showTimeLine && !this.state.fullScreen ? { display: "none" } : containerStyle]}>
        <View style={this.state.showTimeLine && this.state.fullScreen && this.state.showPlayToolBar ? { flexGrow: 1 } : { display: "none" }}>
          <TimeScaleView2_1
            ref={(ref) => { this.timelineViewFull = ref; }}
            // onCenterValueChanged={this._onCenterValueChanged}
            onScrolling={this._onScrolling}
            onScrollEnd={this._onCenterValueChanged}
            onPlayStateChange={(playing) => {
              if (playing) {
                this.playStartClick();
              } else {
                this.playPauseClick();
              }
            }}
            landscape={this.state.fullScreen}
            isCloud={true}
            isPlaying={this.state.isPlaying}
            eventTypeFlags={this.state.eventTypeFlags}
            isDisabled={this.state.isEmpty ? true : false}
            touchMovement={touchMovement}
          />
        </View>
        <View style={!this.state.fullScreen ? { flexGrow: 1 } : { display: "none" }}>
          <TimeScaleView3
            ref={(ref) => { this.timelineView = ref; }}
            // onCenterValueChanged={this._onCenterValueChanged}
            onScrolling={this._onScrolling}
            onScrollEnd={this._onCenterValueChanged}
            landscape={this.state.fullScreen}
            isCloud={true}
            eventTypeFlags={this.state.eventTypeFlags}
            isDisabled={this.state.isEmpty ? true : false}
            touchMovement={touchMovement}
          />
        </View>
      </View>




    );
  }

  mGetDataDone = (count, allItems = []) => {
    // console.log("==========",count,allItems);
    if (this.state.showLoadingSkeletonView){
      this.setState({showLoadingSkeletonView: false});
    }
    if (count > 0 && allItems) {
      this.isFileReceived = true;
      let items = allItems;
      // 如果没有播放中的视频，或者是刚刚切换了日期，就开始播放第一个视频事件
      if (!this.state.showTimeLine && count > 0 && (!this.state.item || this.playTheFirstEvent) && items[0]?.cached != true) {
        this.playTheFirstEvent = false;
        let itm = items[0];
        console.log("{{{{{{{{{{{{{{{",items[0]);
        if (!itm?.isShowImg) {
          this.setState({ isPlaying: false, item: null, mEmptyDes: null, mNoVideoEvent: LocalizedStrings['sdcard_page_desc_empty'], loadingStr: null, showBackOnly: true });
          return;
        } else {
          if (this.fromPush) {
            let pushItm = SdcardCloudTimelinePageV2.item;
            Util.checkExist(pushItm).then((result) => {
              console.log("checkExist", result,result.data.deleteStatus); // result.data.deleteStatus true : false
              if (!result.data.deleteStatus) {
                this._doOnGetDataDone(pushItm);
              } else {
                if (this.itemEventIsDeleteToPlayFirst) {
                  // 首页显示最近3条模式，点击某个事件进入，长按删除后，回到看家云存页，视频已被删除，仍能播放
                  this.itemEventIsDeleteToPlayFirst = false;
                  this._doOnGetDataDone(itm);
                }else {
                  this.setState({ isPlaying: false, showLoadingView: false, deletedStr: LocalizedStrings["camera.alarm.video.deleted"] });
                }
              }
            }).catch((err) => {
              console.log("checkExist2", err);
              if (err.indexOf('暂未收录该接口') != -1) {
                this._doOnGetDataDone(pushItm);
              }
            });
          } else {
            this._doOnGetDataDone(itm);
          }
          // this.startPlayer(itm);
        }
      } else if (this.state.showTimeLine) {
        // 这里不应该有操作才对
        console.log("++++++++++++++do nothing++++++++++++++");
        let itm = items[allItems.length - 1];
        let timestamp = new Date(itm.createTime).setHours(0, 0, 0, 0) + 1;
        this.toStartTime = timestamp;
        this._onCenterValueChanged(this.toStartTime);
      }
    } else {
      this.setState({ isPlaying: false, item: null, mEmptyDes: LocalizedStrings['kanjia_nonotification_tips'], mNoVideoEvent: LocalizedStrings['sdcard_page_desc_empty'], loadingStr: null, showBackOnly: true });
      if (this.state.showTimeLine) {
        // 滚动到某天，更新时间轴显示
        !this.destroyed && this.timelineView && this.timelineView.scrollToTimestamp(this.state.curDate.getTime()+100);
        !this.destroyed && this.timelineViewFull && this.timelineViewFull.scrollToTimestamp(this.state.curDate.getTime()+100);
      }
    }

    // if (count > 0 && this.props.navigation.state.params.lstType == 'push') {
    //   let item = this.mEvList.findEvent(this.state.item);
    //   if (item) {
    //     // console.log("========> aaa 990, item", item.fileId, item.offset);
    //     this.setState({ item: item });
    //   }
    //   if (this.listType() == "list_vip") {
    //     this.getFaceUrl(item);
    //   }
    // }
    //
    // this.mLoader.getVideoDates().then((dates) => { // 云存和看家目前返回null
    //   if (dates != null && dates.length > 0) {
    //     this.mDatesWithData = dates;
    //   }
    // }).catch((err) => {
    //   console.log(`getVideoDates err: ${ err }`);
    // });
  }

  _doOnGetDataDone(itm) {
    this.setState({ item: itm });
    this.isEventListItem = true;
    this._startQueryNetwork(itm);
  }

  mGetInitDataDone = (itm, ignoreScroll) => {
    console.log("==========", itm);
    if (!itm) {
      return;
    }
    //找到当前时间轴时间点播放的对应事件
    this.setState({ item: itm }, () => {
      if (ignoreScroll) return;
      this.timeout = setTimeout(() => {
        //不添加延迟切换后无法滚动到指定位置
        if (this.mEvList) {
          this.mEvList._scrollToPlayingId();
        }
      }, 500)
    });
  }

  updatePlayItem(item) {
    let showImg = item?.isShowImg != null ? item.isShowImg : true;
    if (!showImg) { // 无视频事件
      this.setState({ isPlaying: false, item: null, mEmptyDes: null, mNoVideoEvent: LocalizedStrings['sdcard_page_desc_empty'], loadingStr: null, showBackOnly: true });
      Toast.fail('camera.alarm.cloud.tip.fullvideo.not.vip');
      return;
    }
    let isSameFile = false;
    if (this.state.item?.fileId == item.fileId) {
      isSameFile = true;
    }
    console.log("========> aaa 4", item);
    this.setState({ item: item, showBackOnly: false, mNoVideoEvent: null, mEmptyDes: null }, () => {
      if (this.state.isPlaying && isSameFile) {
        console.log("========> aaa 5");
        this.mNewEvent = true;
        this.video && this.video.seek(item.offset * 3 + 0.4);
        this.getFaceUrl(item);
        this.blockOnProgress = false;
      } else {
        this.setState({
          loadingStr: LocalizedStrings['camera_loading'],
        });
        console.log("========> aaa 6");
        this.isEventListItem = true;
        this._startQueryNetwork(item);
        // this.startPlayer(item, true);
      }
    });

    // this.videoItem = item;
    // this.offset = item.offset;

    // let startTime = this.videoItem.startTime ? this.videoItem.startTime : this.videoItem.createTime;
    // this.cloudItem = CloudVideoUtil.getItemByID(startTime, this.videoItem.fileId);
    // LogUtil.logOnAll(TAG, "updatePlayItem cloudItem===", this.cloudItem);
    // if (this.cloudItem) {
    //   this.toStartTime = this.cloudItem.startTime + this.offset * 3000;
    //   !this.destroyed && this.timelineView && this.timelineView.scrollToTimestamp(this.toStartTime);
    //   !this.destroyed && this.timelineViewFull && this.timelineViewFull.scrollToTimestamp(this.toStartTime);
    // }


    this.isCalledForMonitoring ? TrackUtil.reportClickEvent('Monitoring_Video_ClickNum') : TrackUtil.reportClickEvent('CloudStorage_VideoPlay_ClickNum');
  }

  getFaceUrl(aItm) {
    if (aItm?.faceInfo != null) {
      if (aItm.faceInfo.faceUrl != null) {
        this.setState({ faceUrl: aItm.faceInfo.faceUrl });
      } else {
        Util.getFaceImgUrl(aItm.faceInfo.faceId)
          .then((aRet) => {
            console.log(this.tag, "getFaceImgUrl", aRet);
            aItm.faceInfo.faceUrl = aRet;
            this.setState({ faceUrl: aRet });
          })
          .catch((aErr) => {

            console.log(this.tag, "getFaceImgUrl aErr", aErr);
          });
      }
    }
  }

  // 这里代表时间轴滚动了
  _onCenterValueChanged = (timestamp) => {
    if (!this.state.displayCloudList) {
      return;
    }
    TrackUtil.reportClickEvent("TimeSlider_Drop_ClickNum");

    console.log("滑动结束");
    this.setState({ touchMovement: false });
    this.dateTime.setTime(timestamp);
    // console.log(`timestamp:${timestamp}`);
    console.log(`${this.dateTime.getHours()} ${this.dateTime.getMinutes()} ${this.dateTime.getSeconds()}`);

    if (this.timeIndicatorView == null) {
      return;
    }

    this.timeIndicatorView.setState({ centerTimestamp: 0 });

    this.toStartTime = timestamp;
    this.isEventListItem = false;
    // if (this.state.isPlaying) {
    this.blockOnProgress = true;// 如果开始播放的过程中，滚动了时间轴，要暂时屏蔽onProgress的回调。
    // 1.5s后再处理progress的回调。避免拖拽失败。
    if (this.blockOnProgressTimeout) {
      clearTimeout(this.blockOnProgressTimeout);
    }
    this.blockOnProgressTimeout = setTimeout(() => {
      this.blockOnProgress = false;
    }, 1500);
    this._startQueryNetwork();
    // }
    // 如果没有播放  就不用管了

  }

  _renderBottomSelectView() {
    if (this.state.fullScreen || !this.state.showTimeLine) {
      return;
    }


    return (
      <View style={[this.state.isEmpty && !this.state.displayCloudList ? { display: "none" } : null, { width: "100%", position: "relative", height: 46, marginTop: 25 }]}>
        <TouchableOpacity
          style={{ position: "absolute", bottom: 0, width: "100%", height: 46, marginBottom: 20, marginTop: 8, paddingHorizontal: 24 }}
          onPress={() => {
            TrackUtil.reportClickEvent("TimeSlider_AllVedio_ClickNum");
            this._onPressSeeAllVideo();
          }}
        >
          <View
            style={{ width: "100%", height: "100%", backgroundColor: isDark ? "#474747" : "#F5F5F5", borderRadius: 23, display: "flex", alignItems: "center", justifyContent: "center" }}
          >
            <Text
              style={{ color: "#4C4C4C", fontSize: kIsCN ? 16 : 14, fontWeight: 'bold', marginHorizontal: 40, textAlignVertical: "center", textAlign: 'center' }}
            >
              {LocalizedStrings["all_playback_video"] + " "}
            </Text>
          </View>

        </TouchableOpacity>

      </View>
    );
  }

  _orientationListener = (orientation) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    if (!this.isPageForeGround || !this.isPluginForeGround || this.isParentPageBackground || !this.isAppForeground) {
      return;
    }
    console.log(TAG, `device orientation changed :${orientation} want ${this.mOri}`);
    if ((Platform.OS == "ios" || (Host.isAndroid && Host.isPad)) && ((this.mOri === 'LANDSCAPE' && this.state.fullScreen) || (this.mOri === 'PORTRAIT' && !this.state.fullScreen))) {
      return;
    }
    if (this.mOri === orientation) {
      if (orientation === 'LANDSCAPE') {
        this._setNavigation(true);
        if (Host.isPad) {
          Service.miotcamera.enterFullscreenForPad(true);
        }
      } else {
        StatusBar.setHidden(false);
        this._setNavigation(false);
        if (Host.isPad) {
          Service.miotcamera.enterFullscreenForPad(false);
        }
      }
    } else {
      // ios need restore for next lock
      this.restoreOri();
    }
  };

  _setNavigation(isFull) {
    this.props.navigation.setParams({ isFullScreen: isFull });
    this.playToolbar && this.playToolbar.updateOrientation(isFull);
    this.setState({ fullScreen: isFull });
  }

  componentDidMount() {
    TrackUtil.reportClickEvent("TimeSlider_CloudStorage_Num");
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');

    this.restoreOri();
    Orientation.addOrientationListener(this._orientationListener);

    StorageKeys.IS_DATA_USAGEE_WARNING.then((res) => { // 是否使用流量保护  这个页面只需要进来一次就行了
      if (typeof (res) === "string" || res == null) {
        StorageKeys.IS_DATA_USAGEE_WARNING = false;
        this.isDataUsageWarning = false;
      } else {
        this.isDataUsageWarning = res;
      }

    }).catch((error) => {
      console.log(error);
      StorageKeys.IS_DATA_USAGEE_WARNING = false;
      this.isDataUsageWarning = false;
    });
    // StorageKeys.IS_VIP_STATUS.then((res) => {
    //   this.isVip = res;
    //   StorageKeys.IN_CLOSE_WINDOW.then((res) => {
    //     this.isInExpireWindow = res;
    //     console.log("refreshContents-=-=-=-=-=003");
    //     this.refreshContents();
    //   });
    // });
    if (Platform.OS == "ios") {
      this.statusBarHeight = getStatusBarHeight();
    }

    this.showSnapShot = Util.isShowSnapShop();

    Util.getFaceAllFigure().then((res) => {
      this.setState({
        mAllFigureInf: res
      }, () => {
        this.getAllFaceurl(this.state.mAllFigureInf);
      });
    }).catch((err) => {
      LogUtil.logOnAll("CloudTimelinePlayerFragmentV2", "get allFigure failed" + err);
    });

    CameraPlayer.getInstance().queryShouldPauseOn4G()
      .then(({ state, pauseOnCellular }) => {

        if (state === "NONE" || state === "UNKNOWN") {
          this.networkState = 0;
          this.isMobileNetwork = false;
          this.setState({ showErrorView: true, showPlayToolBar: false, showLoadingView: false, showEmptyHint: false, loadedCloudBuyStatus: true });
        }
      });

  }

  getSDEarliestTime() {
    // 获取最早SD卡视频
    AlarmUtilV2.getSDEarliestTime().then((value) => {
      // 请求数据成功
      this.sdTimeRetry = 0;
    }).catch((error) => {
      // 失败后再执行一次请求
      if (this.sdTimeRetry === 0) {
        this.sdTimeRetry++;
        this.getSDEarliestTime();
      }else {
        this.sdTimeRetry = 0;
      }
    });
  }
  getAllFaceurl(figureInfos) {
    let lastNotifyTime = Date.now();
    for (let i = 0; i < figureInfos.length - 1; i++) {
      Util.getFaceImgUrl(figureInfos[i].faceId).then((res) => {
        let path = res;
        figureInfos[i].faceUrl = path;
        this.setState({
          mAllFigureInf: figureInfos
        });
      }).catch((err) => {
        console.log('err', err);
      });
      if (Date.now() - lastNotifyTime < 1000) {//距离上次全局刷新，不超过1s,return
        continue;
      }
      lastNotifyTime = Date.now();
    }
  }

  refreshContents() {
    this._initData();
  }


  _initData() {
    console.log("[[[[[[[[[[[[[[[[[[[[[[",this.state.displayCloudList);
    if (this.state.displayCloudList) {
      // showLoadingView: true,
      this.setState({ showCloudBuyView: false, loadedCloudBuyStatus: true });
    } else {
      this.setState({ isPlaying: false, showLoadingView: false, showCloudBuyView: true, loadedCloudBuyStatus: true, showErrorView: false, showPlayToolBar: false });// here hide
      return;
    }
    CloudVideoUtil.setCloudFilesReceivedCallback(this._bindFilesHandler);// 收到了数据  通知刷新
    CloudVideoUtil.fetchCloudVideoDataSerials(Device.deviceID, Device.model, this.state.displayCloudList);
  }

  _startSnapshot() {
    if (Platform.OS === "android") {
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this._realStartSnapshot(false);
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            !this.destroyed && Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
          !this.destroyed && Toast.success("action_failed");
        });
    } else {
      // no ios's photos const use hardcode
      System.permission.request("photos").then((res) => {
        this._realStartSnapshot(false);
      }).catch((error) => {
        this.setState({ showPermissionDialog: true, permissionRequestState: 0 });

      });
    }
  }

  _realStartSnapshot(isFromVideo) {
    clearTimeout(this.snapshotTimeout);
    this.setState({ screenshotVisiblity: false, screenshotPath: null });
    AlbumHelper.reactNativeSnapShot(this.video)
      .then((path) => {
        console.log(path);
        this.isForVideoSnapshot = isFromVideo;
        clearTimeout(this.snapshotTimeout);
        this.setState({ screenshotVisiblity: true, screenshotPath: path });// show snapshotview
        this.snapshotTimeout = setTimeout(() => {
          this.isForVideoSnapshot = false;
          this.setState({ screenshotVisiblity: false, screenshotPath: null });
        }, 3000);
        // 文件路径。
      })
      .catch((error) => {
        console.log(JSON.stringify(error));
        !this.destroyed && Toast.success("action_failed");
      });
  }

  _toggleAudio(isMute, changeUnitMute = false) {
    //false
    if (!isMute) {
      TrackUtil.reportClickEvent("TimeSlider_OpenVolume_ClickNum");
    }
    console.log('_toggleAudio isMute', isMute);// isisMute true 静音/ isisMute false 开启声音
    if (isMute) {
      this.setState({ isMute: true });
      if (changeUnitMute) {
        CameraConfig.setUnitMute(true);
      }
      return;
    }
    if (this.state.speed > 1) {
      console.log('倍速模式下 进来了吗 this.state.speed', this.state.speed);
      this.setState({ isMute: true });
      return;// 倍速模式下 不要播放声音
    }
    this.setState({ isMute: false });
    if (changeUnitMute) {
      CameraConfig.setUnitMute(false);
    }
  }

  _startQueryNetwork(item = null) {
    if (this.destroyed) {
      return;
    }
    console.log("_startQueryNetwork=====", item);
    CameraPlayer.getInstance().queryShouldPauseOn4G()
      .then(({ state, pauseOnCellular }) => {
        if (state === "CELLULAR") {
          this.isMobileNetwork = true;
          this.networkState = 1;
        } else {
          if (state === "NONE" || state === "UNKNOWN") {
            this.networkState = 0;
          } else {
            this.networkState = 2;
          }
          this.isMobileNetwork = false;
        }
        if (state === "CELLULAR" && pauseOnCellular && this.isDataUsageWarning) { // 普通网络 && 数据流量提醒
          if (this.state.isPlaying) {
            !this.destroyed && Toast.success("nowifi_pause");
          }
          this._startPlay(false);
          return;
        }
        if (state === "NONE" || state === "UNKNOWN") {
          this._networkChangeHandler(0);
          return;
        }

        if (!this.isFileReceived) {
          if (CameraConfig.isToUpdateVipStatue) {
            this.fetchVipStatus();//
          }
          return;
        }
        // 其他网络条件 走连接的步骤吧
        if (this.isEventListItem) {
          this._startPlay(true, item ? item : this.state.item);
        } else {
          this._startPlay(true);// 开始连接
        }
      })
      .catch(() => { // 获取网络状态失败 也直接走开始连接的流程
        if (this.isEventListItem) {
          this._startPlay(true, item ? item : this.state.item);
        } else {
          this._startPlay(true);// 开始连接
        }
      });
  }

  // to be continued
  _startPlay(isPlay, aItm = null) {
    // handle流量保护。
    LogUtil.logOnAll("_startPlay ---- this.isEventListItem=", this.isEventListItem,isPlay,this.toStartTime);
    if (isPlay) {
      this.setState({ deletedStr: null, showPlayEndView: false });
      let selectedItem = null;
      let offset = 0;
      if (!this.isEventListItem) {
        let lastestItem = CloudVideoUtil.getLastestVideo(this.toStartTime);
        if (lastestItem == null) { // 压根都没有最后一条数据；
          return;
        }
        if (lastestItem.endTime < this.toStartTime) {
          // console.log(" selectedItem selected 111");
          selectedItem = lastestItem;
          if (lastestItem.endTime - lastestItem.startTime > 20000) {
            offset = (lastestItem.endTime - lastestItem.startTime - 20000) / 1000;
          } else {
            offset = 0;
          }
        } else {
          // console.log(" selectedItem selected 222");
          selectedItem = CloudVideoUtil.searchNeareastVideoItem(this.toStartTime);
          if (selectedItem == null) {
            return;
          }
          if (selectedItem.startTime > this.toStartTime) {
            offset = 0;
          } else {
            offset = (this.toStartTime - selectedItem.startTime) / 1000;
          }
        }
        if (!this.state.showLoadingView) {
          this.setState({ showLoadingView: true });
        }
        this.cloudItem = selectedItem;
        this.setState({ mNoVideoEvent: null });
        if (this.videoItem != null && this.videoItem.fileId == selectedItem.fileId) {
          this.videoItem = selectedItem;
          this.offset = offset;
          // console.log(`state:${ JSON.stringify(this.state) }`);
          console.log(`state.isPlaying:${ this.state.isPlaying } isMute:${ CameraConfig.getUnitMute() }`);
          this._toggleAudio(this.state.speed > 1 || CameraConfig.getUnitMute());
          if (this.state.isPlaying) {
            this._toggleAudio(this.state.speed > 1 || CameraConfig.getUnitMute());
            this.setState({ isPlaying: true, showPlayToolBar: true, showPauseView: true, showLoadingView: false });
            !this.destroyed && this.video && this.video.seek(offset);
            this.scrollToTimestamp();
            return;
          } else {
            this._toggleAudio(this.state.speed > 1 || CameraConfig.getUnitMute());
            console.log("old data", this.state.videoPath, this.isPlayErrorForIos);
            if (Platform.OS == "ios" && this.isPlayErrorForIos) {
              this.isPlayErrorForIos = false;
              let tempVideoPath = this.state.videoPath;
              this.setState({ videoPath: null }, () => {
                this.setState({ videoPath: tempVideoPath, isPlaying: true, showPlayToolBar: true, showPauseView: true, showLoadingView: false });
                !this.destroyed && this.video && this.video.seek(offset);
                this.scrollToTimestamp();
              });
            } else {
              console.log("=======this is do==============",offset);
              this.setState({ isPlaying: true, showPlayToolBar: true, showPauseView: true, showLoadingView: false });
              !this.destroyed && this.video && this.video.seek(offset);
              this.scrollToTimestamp();
            }

            return;
          }
        }
      } else {
        if (!aItm) {
          LogUtil.logOnAll("_startPlay ---- play item is null");
          return;
        }
        selectedItem = aItm;
        offset = aItm.offset;
        let fileId = selectedItem.fileId;
        this.cloudItem = CloudVideoUtil.getItemByID(selectedItem.startTime, fileId);
        this.blockOnProgress = false;
        this.mLoader.eventRead(aItm);
        this.setState({ mNoVideoEvent: null });

        // if (this.videoItem != null && this.videoItem.fileId == selectedItem.fileId) {
        //   // console.log(`state:${ JSON.stringify(this.state) }`);
        //   this.videoItem = selectedItem;
        //   this.offset = offset;
        //   console.log(`state.isPlaying isEventListItem:${ this.state.isPlaying } isMute:${ CameraConfig.getUnitMute() }`);
        //
        //   this._toggleAudio(this.state.speed > 1 || CameraConfig.getUnitMute());
        //   this.playToolbar && this.playToolbar.updatePlayState(true);
        //   this.setState({ isPlaying: true, showPlayToolBar: true, showPauseView: true, showLoadingView: false });
        //   !this.destroyed && this.video && this.video.seek(offset * 3 + 0.4);
        //   this.scrollToTimestamp();
        //   return;
        // }
        if (!selectedItem.isAlarm) {
          LogUtil.logOnAll("startPlay isAlarm false")
          this.nextEv = null;
          if (this.listCloudIsPlayEnd || this.mLstItm != aItm) {
            LogUtil.logOnAll("startPlay this.mLstItm != aItm")
            this.mCurTime = aItm.offset * 3 + 0.4;
            this.mLstItm = aItm;
            this.mNewPlay = true;
            this.listCloudIsPlayEnd = false;
          } else if (this.mLstItm.offset != aItm.offset) {
            LogUtil.logOnAll("startPlay this.mLstItm.offset != aItm.offset")

            if (Platform.OS == "ios" && this.isPlayErrorForIos) {
              this.isPlayErrorForIos = false;
              let tempVideoPath = this.state.videoPath;
              this.setState({ videoPath: null }, () => {
                this.setState({ videoPath: tempVideoPath });
                !this.video && this.video.seek(aItm.offset * 3 + 0.4);
              });
            } else {
              this.video && this.video.seek(aItm.offset * 3 + 0.4);
            }
            return;
          }
        } else {
          LogUtil.logOnAll("startPlay isAlarm true")
          if (this.mLstItm != aItm) {
            this.mCurTime = 0;
            this.mLstItm = aItm;
            this.mNewPlay = true;
          }
        }
        if (this.playToolbar != null) {
          this.playToolbar.updateCurTime(Math.max(this.mCurTime, 0));
        }
        this.getFaceUrl(aItm);
      }

      this.videoItem = selectedItem;
      this.offset = offset;

      let fileId = selectedItem.fileId;
      let isAlarm = false;
      console.log(TAG, "cloud start play");
      if (this.videoItem && this.videoItem.isAlarm != undefined) {
        isAlarm = this.videoItem.isAlarm;
      } else if (this.videoItem) {
        isAlarm = this.videoItem.duration ? this.videoItem.duration <= 12 : false;
      }

      Service.miotcamera.getVideoFileUrl(fileId, isAlarm)
        .then((url) => {
          this.setState(() => { return { showErrorView: false, videoPath: url, isPlaying: true, showloadingview: false, showPlayToolBar: true, showPauseView: false }; }, () => {
            if (this.playToolbar) {
              this.playToolbar.updatePlayState(true);
            }
            this._toggleAudio(this.state.speed > 1 || CameraConfig.getUnitMute());
            // this.updateSpeed(this.state.speed);
          });
          this._hidePlayToolBarLater();

        })
        .catch((error) => {
          !this.destroyed && Toast.fail("action_failed", error);
        });
      this.scrollToTimestamp();
    } else {
      if (this.state.showErrorView) {
        return;
      }
      // stop播放
      this.setState({ isPlaying: false, showPauseView: true, showPlayToolBar: true, showLoadingView: false });
      clearTimeout(this.showPlayToolBarTimer);
    }
  }
  scrollToTimestamp() {
    let startTime = this.videoItem?.startTime ? this.videoItem.startTime : this.videoItem.createTime;
    if (this.isEventListItem) {
      this.toStartTime = startTime;
    } else {
      this.toStartTime = startTime + this.offset * 1000;
    }
    LogUtil.logOnAll("_startPlay ---- startTime=", startTime, " offset=", this.offset);
    LogUtil.logOnAll("_startPlay ---- this.toStartTime=", this.toStartTime);
    !this.destroyed && this.timelineView && this.timelineView.scrollToTimestamp(this.toStartTime);
    !this.destroyed && this.timelineViewFull && this.timelineViewFull.scrollToTimestamp(this.toStartTime);

    if (!this.state.showTimeLine) {
      // 请求某天的数据，云端返回的数据，是从后一天的开始的，这里更新日期会导致数据重新请求
      LogUtil.logOnAll(TAG, "no need to change date");
      return;
    }
    this.dateTime.setTime(this.toStartTime);
    if (this.state.curDate.getMonth() != this.dateTime.getMonth() || this.state.curDate.getDate() != this.dateTime.getDate()) {
      console.log(TAG, "update curDate for Calendar by scrollToTimestamp()");
      this.setState({ curDate: new Date(this.toStartTime) });
    }
  }

  updateSpeed(position) {
    let speed = 0;
    switch (position) {
      case 0:
        speed = 1;
        break;
      case 1:
        speed = 2;
        break;
      default:
        speed = 0;
        break;
    }
    this.selectedIndexArray = [position];
    this.setState(() => { return { speed: speed, fastPlay: speed > 1 }}, () => {
      if (speed > 1) {
        this._toggleAudio(true, false);
      } else {
        this._toggleAudio(CameraConfig.getUnitMute(), false);
      }
    });

  }

  // todo 需要使用一个view盖在videoview上。
  _onVideoClick() {

    if (!this.state.displayCloudList) {
      return;// 不是vip 直接返回，不应该显示这些页面。
    }

    this.setState((state) => {
      return {
        showPlayToolBar: !this.state.showPlayToolBar,
        showPauseView: !this.state.showPlayToolBar
      };
    }, () => {
      this._hidePlayToolBarLater();
    });

    console.log("click video view");
    // 埋点--查看更多的曝光
    if(!this.state.showPlayToolBar) TrackUtil.reportClickEvent("Monitoring_CloudStorageTab_ViewList_ViewMore_Num")
  }

  // android返回键处理
  onBackHandler = () => {
    if (!this.isPageForeGround) {
      return false;
    }
    if (this.state.fullScreen) {
      this.toPortrait();
      this._hidePlayToolBarLater(true);
      return true;
    }


    this._startPlay(false);
    return false;// 不接管
  }

  _onPressSeeAllVideo() {
    if (this.state.displayCloudList) { // inexpireWindow== closeWindow= true 代表已经彻底过期了。
      if (CameraConfig.shouldDisplayNewStorageManage(Device.model)) {
        StackNavigationInstance.jumpToStackNavigationPage_forSDCloudPage("AllStorage", { initPageIndex: 0, vip: this.isVip, isSupportCloud: CameraConfig.isSupportCloud(Device.model) });
      } else {
        LogUtil.logOnAll(TAG, "云存储回看点击了全部视频按钮跳云存列表");
        Service.miotcamera.showCloudStorage(true, false);// 这里负责跳转， 跳购买页面还是普通页面，某种程度来说，并不对。
        CameraConfig.isToUpdateVipStatue = true;
      }
    } else {

      // here nothing 
    }
  }

  _onScrolling = (timestamp) => {
    // console.log("_onScrolling");
    this.dateTime.setTime(timestamp);
    // console.log(`${this.dateTime.getHours()} ${this.dateTime.getMinutes()} ${this.dateTime.getSeconds()}`);
    if (this.timeIndicatorView == null) {
      console.log('');
      return;
    }
    this.timeIndicatorView.setState({ centerTimestamp: timestamp });
    if (this.state.curDate.getMonth() != this.dateTime.getMonth() || this.state.curDate.getDate() != this.dateTime.getDate()) {
      console.log(TAG, "update curDate for Calendar");
      this.setState({ curDate: new Date(timestamp) });
    }
  }

  _bindFilesHandler = (status) => { // 收到文件列表的回调
    if (this.destroyed) {
      return;
    }
    if (CLOUD_VIDEO_STATUS.FAIL == status || CLOUD_VIDEO_STATUS.EMPTY == status) {
      this.isFileReceived = false;
      // 渲染 common_net_error；
      CameraPlayer.getInstance().queryShouldPauseOn4G()
        .then(({ state, pauseOnCellular }) => {
          if (state === "NONE" || state === "UNKNOWN") {
            this.networkState = 0;
            this.isMobileNetwork = false;
            this.setState({ showErrorView: true, showPlayToolBar: false, showLoadingView: false, showEmptyHint: false });
          } else {
            if (status == CLOUD_VIDEO_STATUS.EMPTY) {
              this.setState({ showErrorView: false, isPlaying: false, item: null, mEmptyDes: null, mNoVideoEvent: LocalizedStrings['sdcard_page_desc_empty'], loadingStr: null, showBackOnly: true });
            } else {
              this.setState({ showErrorView: true, showPlayToolBar: false, showLoadingView: false, showEmptyHint: status == CLOUD_VIDEO_STATUS.EMPTY });
            }
          }
        });
      return;
    }
    this.setState({ showEmptyHint: false, videoExitedArray: CloudVideoUtil.videoExistArray });// 避免后面没有刷新
    this.isFileReceived = true;
    this.onGetFiles();
    if (this.state.displayCloudList) {
      return;
    }
    //不是vip，每次回来都要检测一遍是否是vip
  }



  // 收到了 云存的数据。
  onGetFiles() {
    if (this.timelineView == null) {
      return;
    }
    let video = CloudVideoUtil.getLastestVideo();
    if (video == null) {
      console.log("find cloud items failed");
      return;
    }
    this.setState({ isEmpty: false });
    console.log("收到了视频数据");
    if (this.videoItem && !this.cloudItem) {
      let startTime = this.videoItem?.startTime ? this.videoItem.startTime : this.videoItem.createTime;
      this.cloudItem = CloudVideoUtil.getItemByID(startTime, this.videoItem.fileId);
      LogUtil.logOnAll(TAG, "onGetFiles cloudItem===", this.cloudItem);
    }
    this.lastTimeItemEndTime = video.endTime;
    !this.destroyed && this.timelineView && this.timelineView.onReceiveCloudDatas();
    !this.destroyed && this.timelineViewFull && this.timelineViewFull.onReceiveCloudDatas();
    if (!this.isFirstReceiveFiles) {
      return;
    }

    this.isFirstReceiveFiles = false;
    // setTimeout(() => {
    //   let video = CloudVideoUtil.getLastestVideo();
    //   if (video == null) {
    //     console.log("find cloud items failed");
    //     return;
    //   }
    //   !this.destroyed && this.timelineView && this.timelineView.scrollToTimestamp(video.startTime);
    //   !this.destroyed && this.timelineViewFull && this.timelineViewFull.scrollToTimestamp(video.startTime);
    //   this.toStartTime = video.startTime;
    //   this._startQueryNetwork();
    // });
    // here startToPlay

  }

  toSdcardEnd() {
    if (!this.state.displayCloudList) {
      return;
    }
    // todo jump to sdcard file end
    // todo  pauseCamera
    !this.destroyed && this.timelineView && this.timelineView.scrollToTimestamp(this.lastTimeItemEndTime);
    !this.destroyed && this.timelineViewFull && this.timelineViewFull.scrollToTimestamp(this.lastTimeItemEndTime);
    // this.videoItem = null;
    this._startPlay(false);
  }


  onProgress = (data) => {
    if (this.destroyed) {
      return;
    }
    if (!this.isPageForeGround) {
      return;
    }
    if (this.blockOnProgress) { // 拖动了时间轴，1.5s内不响应onProgress
      return;
    }
    if (!this.state.showTimeLine) {
      //列表页相关操作
      this.onProgressList(data);
      return;
    }
    if (this.videoItem == null) {
      return;
    }
    this.progress = data.currentTime;
    if (data.currentTime > this.offset) {
      let currentTime = data.currentTime * 1000;
      let startTime = this.videoItem.startTime ? this.videoItem.startTime : this.videoItem.createTime;
      let isReachCurrentFileEnd = startTime + currentTime >= this.videoItem.endTime; //特定视频播放器返回的进度条信息不太对，导致直接跳文件末尾了，这里不这么弄；
      if (isReachCurrentFileEnd) {
        // this.toSdcardEnd();
        // 特定视频不太对，在这里直接跳过去。
        // LogUtil.logOnAll("CloudTimeline", "onProgress endReached", this.videoItem, currentTime);
        // this.reachCurrentFileEnd();
      } else {
        // console.log("onProgress:", currentTime);
        if (this.isEventListItem && this.cloudItem) {
          this.toStartTime = this.cloudItem.startTime + currentTime;
        } else {
          this.toStartTime = startTime + currentTime;
        }
        // console.log("onProgress this.toStartTime:", this.toStartTime);
        !this.destroyed && this.timelineView && this.timelineView.scrollToTimestamp(this.toStartTime);// 受到进度提示了。
        !this.destroyed && this.timelineViewFull && this.timelineViewFull.scrollToTimestamp(this.toStartTime);// 受到进度提示了。
      }

    } else {
      console.log("on seeking");
    }

  };

  onProgressList(data) {
    if (!this.mNewPlay && data.currentTime > this.mCurTime && !this.mNewEvent) {
      // if (this.listType() == "list_vip") {
      let event = this.mEvList.getAdjacentEventForMarkListitem(this.state.item);
      if (event !== this.nextEv) {
        this.nextEv && console.log("next event: ", this.nextEv.offset);
        this.nextEv = event;
      }

      if (this.nextEv && data.currentTime > (this.nextEv.offset * 3 + 0.4)) {
        this.mLstItm = this.state.item;
        this.setState({ item: this.nextEv });
        this.getFaceUrl(this.nextEv);
      }
      // }

      this.mCurTime = data.currentTime;
      if (this.playToolbar && !this.mSeeking) {
        if (data.hasOwnProperty("duration")) {
          this.duration = data["duration"];
        }
        this.playToolbar.updateCurTime(this.mCurTime, this.duration);
        this.setState({});
      }
      if (!this.videoItem) {
        return;
      }
      let currentTime = data.currentTime * 1000;
      let startTime = this.videoItem.startTime ? this.videoItem.startTime : this.videoItem.createTime;
      if (this.cloudItem) {
        this.toStartTime = this.cloudItem.startTime + currentTime;
      } else {
        this.toStartTime = startTime + currentTime;
      }
      if (this.state.item && this.toStartTime > this.state.item.createTime) {
        if (!this.refreshEventListTime || Date.now() - this.refreshEventListTime > 3000) {
          this.refreshEventListTime = Date.now();
          this.mEvList._getPlayingOffset(this.toStartTime, !this.onSeeked_NeedScroll);
          this.onSeeked_NeedScroll = false;
        }
        !this.destroyed && this.timelineView && this.timelineView.scrollToTimestamp(this.toStartTime);// 受到进度提示了。
        !this.destroyed && this.timelineViewFull && this.timelineViewFull.scrollToTimestamp(this.toStartTime);// 受到进度提示了。
      }
    } else {
      //console.log(this.tag, "play progress exp");
    }

  }

  // todo check 原生代码。
  onError = (error) => { // 修改进度条
    LogUtil.logOnAll("play onError", error);
    if (!this.state.displayCloudList) {
      return;
    }
    this.isPlayError = true;
    this.isPlayErrorForIos = true;
    if (this.networkState == 0) {
      this._networkChangeHandler(0);
      return;// 断网了
    }
    this._startPlay(false);
    this.setState({ showErrorView: true, showPlayToolBar: false });
    // 设置错误文案 以及retry button。
  }

  // todo  check原生代码
  onEnd = () => {
    // this._startPlay(false);//播放结束了
    this.mCurTime = 0;
    LogUtil.logOnAll("CloudTimeline", "onEnd");
    if (this.state.showTimeLine) {
      this.reachCurrentFileEnd();
    } else {
      if (!this.state.isPlaying) {
        // 非播放情况下，播放器执行了onEnd
        LogUtil.logOnAll("video onEnd", this.state.isPlaying);
      }
      // 云存播放，已经播放完某个云存视频，标记后面播放重新走播放流程
      this.listCloudIsPlayEnd = true;
      //停止播放
      this._startPlay(false);
      if (this.playToolbar) {
        this.playToolbar.updatePlayState(false);
      }
      setTimeout(() => {
        this.video && this.video.seek(0);
        this.playToolbar && this.playToolbar.seek(1);//0--->1 0会使得进度滑块与时间重叠
        this.playToolbar && this.playToolbar.updateCurTime(0);
      }, 10);
      if (!this.isVip) {
        this.setState({ showPlayEndView: true });
      }

    }

  }

  reachCurrentFileEnd() {
    if (!this.state.displayCloudList) {
      return;
    }
    if (this.videoItem != null) {
      console.log("Video onEnd:", this.videoItem);
      this.toStartTime = this.videoItem.endTime + 1000; // 播放完成后，+1000 让下一次寻找的时候找到下一个。
      let video = CloudVideoUtil.getLastestVideo();
      if (video == null) {
        return;
      }
      if (video.fileId == this.videoItem.fileId) {
        this.toSdcardEnd();
        return;
      }
      if (this.toStartTime > video.endTime) {
        this.toSdcardEnd();
        return;
      }
      this._startQueryNetwork();// 播放下一个。
    }
  }

  onLoad = (info) => {
    // 获取到duration后，需要刷新UI，避免动态增长的云存视频，由于本地没有刷新导致的问题
    this.isPlayError = false;
    if (this.mNewPlay) {
      this.mNewPlay = false;
    }
    let duration = info.duration;// seconds
    this.duration = duration;


    // this.updateTimeStr();
    // this.setState({ showLoadingView: false, showPauseView: true });// 移除loading
    // if (this.offset != null && this.offset >= 0) {
    //   !this.destroyed && this.video && this.video.seek(this.offset);
    // }

    LogUtil.logOnAll("video onLoad", info);
    // android端需要先暂停播放，再开始播放
    if (Platform.OS == "android") {
      this.setState(() => {
        return { isPlaying: false };
      }, () => {
        this.setState(() => { return { isPlaying: true }; }, () => {
          if (this.playToolbar) {
            this.playToolbar.updatePlayState(true);
          }
          if (!this.state.showTimeLine) {
            this.onLoadList(info);
          } else {
            if (this.offset == null || this.offset == 0) {
              this.setState({ showLoadingView: false, showPauseView: this.state.showPlayToolBar });// 移除loading
              // 不需要seek
            } else {
              LogUtil.logOnAll("CloudTimeline", "seek:" + this.offset);
              !this.destroyed && !isNaN(this.offset) && this.video && this.video.seek(this.offset);
            }
          }

        });
      });
    } else {
      if (!this.state.showTimeLine) {
        this.onLoadList(info);
      } else {
        if (this.offset == null || this.offset == 0) {
          this.setState({ showLoadingView: false, showPauseView: this.state.showPlayToolBar });// 移除loading
          // 不需要seek
        } else {
          !this.destroyed && this.video && this.video.seek(this.offset);
        }
      }
    }
    // if (this.state.showTimeLine) {
    //   let startTime = this.videoItem?.startTime ? this.videoItem.startTime : this.videoItem.createTime;
    //   this.toStartTime = startTime + this.offset * 1000;
    //   !this.destroyed && this.timelineView && this.timelineView.scrollToTimestamp(this.toStartTime);
    //   !this.destroyed && this.timelineViewFull && this.timelineViewFull.scrollToTimestamp(this.toStartTime);
    // }
  }

  onLoadList(info) {
    if (this.mCurTime >= info.duration - 3) {
      this.mCurTime = info.duration - 3;
    }
    if (this.playToolbar) {
      this.playToolbar.updateDuration(info.duration);
      this.playToolbar.updateCurTime(this.mCurTime < 0 ? 0 : this.mCurTime);
    }
    console.log("onLoadList", info?.currentTime, this.mCurTime);
    if (info?.currentTime != this.mCurTime && this.mCurTime > 0) {
      LogUtil.logOnAll("++++++",info.duration, "--", this.mCurTime);
      this.video && this.video.seek(this.mCurTime);
    } else {
      this.setState({ showLoadingView: false, showPauseView: this.state.showPlayToolBar });// 移除loading
    }
  }

  onBuffer = (info) => {
    console.log(info);
  }

  // 拖拽完毕； 这里要移除loading
  onSeek = (info) => {
    console.log("video onSeek", info)
    this.mSeeking = false;
    this.mCurTime = info.currentTime;
    this.setState({
      showLoadingView: false, showPauseView: this.state.showPlayToolBar
    });
    this.mNewEvent = false;
  }


  _renderResolutionDialog() {
    let height = this.state.fullScreen ? kWindowWidth : kWindowHeight;

    // let modalStyle = this.state.fullScreen ? {width: kWindowHeight * 0.5, height: Platform.OS == 'ios' ? kWindowWidth * 0.6 : kWindowWidth * 0.7, alignSelf: 'center', bottom: Platform.OS == 'ios' ? kWindowWidth * 0.2 : kWindowWidth * 0.15, borderBottomLeftRadius: 20, borderBottomRightRadius: 20} : {}
    let modalStyle = this.state.fullScreen ? {width: kWindowHeight * 0.5, alignSelf: 'center', bottom: Platform.OS == 'ios' ? kWindowWidth * 0.2 : kWindowWidth * 0.15, borderBottomLeftRadius: 20, borderBottomRightRadius: 20} : {};

    return (
      <AbstractDialog
        visible={this.state.dialogVisibility}
        title={LocalizedStrings["ptzc_play_back_speed_selection_title"]}
        style={modalStyle}
        showSubtitle={false}
        onDismiss={() => {
          this.setState({ dialogVisibility: false });
        }}
        canDismiss={true}
        useNewTheme={true}
        buttons={[
          {
            text: LocalizedStrings["btn_cancel"],
            titleColor: 'rgba(0, 0, 0, 0.8)',
            backgroundColor: { bgColorNormal: DarkMode.getColorScheme() === 'dark' ? '#EEEEEE' : 'rgba(0, 0, 0, 0.04)', bgColorPressed: "rgba(0, 0, 0, 0.08)" },
            callback: () => {
              this.setState({ dialogVisibility: false });
            }
          }
        ]}>
        <ScrollView style={{marginBottom: 10}} contentContainerStyle={{flexGrow: 1}} showsVerticalScrollIndicator={true}>

          {
            [
              { "title": "x1", accessibilityLabel: DescriptionConstants.rp_13 },
              { "title": "x2", accessibilityLabel: DescriptionConstants.rp_13.replace('1', 2) }
            ].map((item,index) => {
              return <TouchableOpacity key={`speed_${index}`}
                                       onPress={() => {
                                         this.selectedIndexArray = [index];
                                         this.updateSpeed(index);
                                         this.setState({ dialogVisibility: false });
                                       }}>
                <View style={[{ flexDirection: "row", alignItems: "center", justifyContent: "space-between", paddingTop: 15, paddingBottom: 15 }, this.selectedIndexArray[0] == index ? { backgroundColor: 'rgba(50, 186, 192, 0.1)' }:{}]}>
                  <View style={{ flexDirection: "row", alignItems: "center", marginStart: 30 }}>
                    <Text style={{ fontWeight: "bold", color: this.selectedIndexArray[0] == index ? "#32BAC0" : "#000000" }}>{item.title}</Text>
                  </View>
                  { this.selectedIndexArray[0] == index ? <Image source={require("../../Resources/Images/plugin_icon_customVoice_selectItem.png")} style={{ width: 22, height: 22, marginRight: 30 }}></Image> : null}
                </View>
              </TouchableOpacity>;
            })
          }
        </ScrollView>
      </AbstractDialog>
    );
  }

  _renderResolutionDialogV2() {
    return (
      <ChoiceDialog
        modalStyle={this.state.fullScreen ? {width: kWindowHeight * 0.5, height: kWindowWidth * 0.6, alignSelf: 'center', bottom: kWindowWidth * 0.2, borderBottomLeftRadius: 20, borderBottomRightRadius: 20} : {}}

        visible={this.state.dialogVisibility}
        title={LocalizedStrings["ptzc_play_back_speed_selection_title"]}
        options={[
          { "title": "x1", accessibilityLabel: DescriptionConstants.rp_13 },
          { "title": "x2", accessibilityLabel: DescriptionConstants.rp_13.replace('1', 2) }
        ]}
        selectedIndexArray={this.selectedIndexArray}
        onDismiss={(_) => this.setState({ dialogVisibility: false })}
        onSelect={(result) => {
          this.selectedIndexArray = result;
          this.updateSpeed(result[0]);
          this.setState({ dialogVisibility: false });
        }}
      />
    );
  }

  _getWindowPortraitHeight() {
    let winWidth = Dimensions.get('window').width;
    let winHeight = Dimensions.get('window').height;
    let width = Math.min(winWidth, winHeight);
    let height = Math.max(winWidth, winHeight);
    return height;
  }

  fetchVipStatus() {
    if (this.destroyed) {
      return;
    }
    
    CameraConfig.isToUpdateVipStatue = false;
    VipUtil.getVipStatus()
      .then((res) => {
        if (this.destroyed) {
          return;
        }
        this.isVip = res.isVip;
        this.isInExpireWindow = res.inCloseWindow;
        let data = res.data;
        let vipBindStatus = res.vipBindStatus;
        console.log("getVipStatus data = ", JSON.stringify(data));
        this.setState({ isVip: this.isVip });

        let days7 = 604800000;
        let rollingSaveInterval = data.rollingSaveInterval;
        this.rollingInterval = rollingSaveInterval > days7 ? rollingSaveInterval : (7 + 1) * 24 * 60 * 60 * 1000;
        console.log("fetchVipStatus=====", rollingSaveInterval, this.rollingInterval);
        if (!this.isVip && this.mIsSupportCloudCountry) { // 非国际服的才可以提示购买 非云存视频才可以购买。
          let showTips = true;
          if (this.mIsInternationalServer && this.isInExpireWindow && vipBindStatus === false) {
            showTips = false; // 海外 过期窗口期中，bindStatus为false 不展示。
          }
          this.setState({ showBuyCloudVideoTip: showTips });
        }
        this.refreshContents();
        // this._onResume();
      })
      .catch((err) => {
        if (this.destroyed) {
          return;
        }
        LogUtil.logOnAll("fetchVipStatus err=", JSON.stringify(err));
      });
  }

}


const styles = StyleSheet.create({

  container: {
    backgroundColor: Util.isDark() ? "xm#000000" : "#ffffff",
    width: '100%',
    height: '100%',
    display: "flex",
    flexDirection: "column",
    flexWrap: "nowrap"
  },

  main: {
    display: 'flex',
    flexDirection: 'column',
    flexWrap: 'nowrap',
    backgroundColor: 'white',
    height: "100%"
  },

  videoContainerNormal: {
    backgroundColor: 'black',
    width: kWindowWidth,
    position: "relative"// absolute以第一个非static的父类变量作为定位标准
  },

  videoContainerFull: {
    backgroundColor: 'black',
    width: "100%",
    height: "100%",
    position: "relative"// absolute以第一个非static的父类变量作为定位标准
  },

  videoView: {
    position: "absolute",
    width: "100%",
    height: "100%"
  },

  videoControl: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    height: 80
  },

  videoControlBar: {// 横向
    display: "flex",
    flexWrap: "nowrap",
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-around"
    // backgroundColor: '#FFF1'
  },
  videoControlBarFull: {
    // backgroundColor: '#FFF1',
    display: "flex",
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end"

  },
  videoControlBarItem: {// 内容居中排列
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    flexGrow: 1
  },

  videoControlBarItemImg: {
    width: 50,
    height: 50
  },

  landscapeCallViewLayout: {
    width: "100%",
    paddingBottom: 10,
    paddingTop: 20,
    position: "absolute",
    bottom: 0
  },
  landscapeCallViewLayoutImg: {
    display: "flex",
    margin: "auto",
    width: "100%",
    flexDirection: "row",
    justifyContent: "center"
    // textAlign:"center"
  },

  callViewLayout: {
    flexGrow: 1,
    width: "100%",
    flexDirection: "column",
    display: "flex",
    alignItems: "center",
    justifyContent: "center"
  },

  bottomLayout: {
    display: "flex",
    width: "100%",
    height: 60,
    flexDirection: "row",
    flexWrap: 'nowrap'
  },

  bottomLayoutItem: {
    flexGrow: 1,
    height: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center"
  },

  whiteText: {
    fontSize: kIsCN ? 10 : 8,
    textAlign: "center",
    padding: 4,
    color: "#ffffff",
    borderColor: "#FFFFFFCC",
    borderRadius: 3,
    borderWidth: 1
  },
  snapShot: {
    position: "absolute",
    bottom: 40,
    left: 5,
    margin: 6,
    width: 100,
    height: 66,
    borderRadius: 5
  },
  snapShotFull: {
    position: "absolute",
    bottom: 84,
    left: 35,
    margin: 6,
    width: 100,
    height: 66,
    borderRadius: 5
  },
  moreItemContainer: {
    display: "flex",
    flexDirection: 'column',
    justifyContent: 'center',
    minHeight: 54
  },
  moreItemText: {
    fontSize: 16,
    color: "#000000",
    fontWeight: "400",
    textAlignVertical: "center",
    paddingHorizontal: 28
  }
});
