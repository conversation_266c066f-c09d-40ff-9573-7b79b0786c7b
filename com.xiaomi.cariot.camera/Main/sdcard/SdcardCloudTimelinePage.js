import React, { PureComponent } from 'react';
import { createMaterialTopTabNavigator, createTabNavigator } from 'react-navigation';
import { Text, View, SafeAreaView, StatusBar, Platform, TouchableOpacity, Image, StyleSheet, DeviceEventEmitter, Dimensions } from "react-native";
import { Device} from 'miot';
import CloudTimelinePlayerFragment from './CloudTimelinePlayerFragment';
import SdcardTimelinePlayerFragment from './SdcardTimelinePlayerFragment';
import TabBarComponent from '../ui/TabBarComponent';
import { getStatusBarHeight } from 'react-native-iphone-x-helper';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import NoSdcardPage from '../setting/NoSdcardPage';
import SDCardSetting from '../setting/SDCardSetting';
import Orientation from 'react-native-orientation';
import StackNavigationInstance, { SD_CLOUD_STACK_NAVIGATION_ONPAUSE, SD_CLOUD_STACK_NAVIGATION_ONRESUME } from '../StackNavigationInstance';
import StorageKeys from '../StorageKeys';
import TrackUtil from '../util/TrackUtil';
import Toast from '../components/Toast';
import Util from '../util2/Util';
import CameraConfig from '../util/CameraConfig';
import CameraPlayer from '../util/CameraPlayer';

const kIsCN = Util.isLanguageCN();
const navigationHeigth = 68;
const stautsBarHeight = _getStatusBarHeight();
let currentIndex = 1;

function createTopTabNavigator(sdcardStatus, isVip, isShowSdcardPage, isSupportCloud) {
  let defaultPage = "topPage1";
  // if (!showNoSdcard && !isVip) {
  //   defaultPage = "topPage2";// 有sd卡，不是云存
  // }

  if (sdcardStatus == 0 && !isVip ) {
    defaultPage = "topPage2";// 有sd卡，不是云存
  }
  let sdPage = null;
  if (sdcardStatus == 0 || sdcardStatus == -1 || sdcardStatus == 2
    || sdcardStatus == CameraPlayer.SD_CARD_FILE_ERROR_CODE) { // sdcard状态获取失败  sdcard正常或者满了
    sdPage = SdcardTimelinePlayerFragment;
  } else if (sdcardStatus == 3 || sdcardStatus == 4 || sdcardStatus == CameraPlayer.SD_CARD_NEED_FORMAT_CODE || sdcardStatus == CameraPlayer.SD_CARD_TOO_SMALL_CODE
    || sdcardStatus == CameraPlayer.SD_CARD_INCOMPATIBLE_CODE) { // 格式化中
    sdPage = SDCardSetting;
  } else {
    sdPage = NoSdcardPage;
  }

  if (isShowSdcardPage != "" && isShowSdcardPage != null) {
    if (isShowSdcardPage) {
      defaultPage = "topPage2";
    } else {
      defaultPage = "topPage1";
    }
  }
  
  if (!isVip && (sdcardStatus == 1 || sdcardStatus == 5)) {//无卡 卡推出
    defaultPage = "topPage1";
  }
  if (isVip && (sdcardStatus == 1 || sdcardStatus == -1 || sdcardStatus == 5)) { // sdcard无 但是是vip  默认跳vip  或者sdcard状态获取失败-1也一样跳过去。
    defaultPage = "topPage1";
  }

  let mPages = {
    topPage1: {
      screen: CloudTimelinePlayerFragment,
      navigationOptions: {
        tabBarLabel: LocalizedStrings["storage_cloud"],
        tabBarOnPress: ({ defaultHandler }) => {
          if (StackNavigationInstance.isRecording) {
            Toast.fail("camera_recording_block");
            return;// 录制过程不跳转。
          }
          StorageKeys.IS_SHOW_SDCARD_PAGE = false;
          defaultHandler();
        },
      }
    },
    topPage2: {
      screen: sdPage,
      navigationOptions: ({ navigation }) => ({
        tabBarLabel: LocalizedStrings["eu_storage_sdcard"],
        tabBarOnPress: ({ defaultHandler }) => {
          StorageKeys.IS_SHOW_SDCARD_PAGE = true;
          defaultHandler();
        }
      })
    }
  };
  if (Device.permitLevel === 36 && (sdcardStatus == 3 || sdcardStatus == 4 || sdcardStatus == CameraPlayer.SD_CARD_NEED_FORMAT_CODE || sdcardStatus == CameraPlayer.SD_CARD_TOO_SMALL_CODE)) {
    defaultPage = "topPage1";
    mPages = {
      topPage1: {
        screen: CloudTimelinePlayerFragment,
        navigationOptions: {
          tabBarLabel: LocalizedStrings["storage_cloud"],
          tabBarOnPress: ({ defaultHandler }) => {
            if (StackNavigationInstance.isRecording) {
              Toast.fail("camera_recording_block");
              return;// 录制过程不跳转。
            }
            StorageKeys.IS_SHOW_SDCARD_PAGE = false;
            defaultHandler();
          },
        }
      }
    };
  }

  if (isVip && (sdcardStatus == 1 || sdcardStatus == -1 )) { // sdcard无 但是是vip  默认跳vip  或者sdcard状态获取失败-1也一样跳过去。
    defaultPage = "topPage1";
  }

  if (!isSupportCloud) {
    mPages = {
      topPage2: {
        screen: sdPage,
        navigationOptions: ({ navigation }) => ({
          tabBarLabel: LocalizedStrings["sd_title"],
          tabBarOnPress: ({ defaultHandler }) => {
            StorageKeys.IS_SHOW_SDCARD_PAGE = true;
            
            defaultHandler();
          }
        })
      }
    };
    defaultPage = "topPage2";
  }
  return createMaterialTopTabNavigator(
    mPages,
    {
      backBehavior: "none",
      animationEnabled: false,
      initialRouteName: defaultPage,
      swipeEnabled: false,
      tabBarComponent: TabBarComponent,
      lazy: true,
      initialLayout: { width: Dimensions.get("window").width, height: Dimensions.get("window").height },
      tabBarOptions:
      {
        showLabel: true,
       
        // labelStyle: { fontSize: 18,fontWeight:'bold'}, //这里设置不管用
        // 是否显示标签栏
        activeTintColor: '#333333', // 标签栏激活字体颜色
        inactiveTintColor: '#32BAC0', // 标签栏未激活字体颜色
        style: { backgroundColor: "#ffffff" } // 设置整个tabbar样式(背景颜色等)
      }

    }
  );
}


export default class SdcardCloudTimelinePage extends React.Component {

  static navigationOptions = (navigation) => {
    return {
      headerTransparent: true,
      header: null,
      headerShown: false,
      headerStyle: {
        height: 0
      }
    };
  }
  state = {
    screenSize: Dimensions.get('screen')
  }

  constructor(props) {
    super(props);
    this.name = "topPage1";
    if (this.props.navigation != null) {
      console.log("cloudsdcard", this.props.navigation.state.params);
      this.sdcardCode = this.props.navigation.getParam("sdcardCode");
      if (this.sdcardCode == null) {
        this.sdcardCode = -1;
      }
      this.isVip = this.props.navigation.getParam("isVip") || false;
      this.isShowSdcardPage = this.props.navigation.getParam("isShowSdcardPage");

      console.log("cloudsdcard", `sdcardCode:${ this.sdcardCode }`,);
    } else {
      this.sdcardCode = -1;
      this.isVip = false;
    }
    this.isPageToBackground = false;
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        console.log("sdcard cloud page did focus");
        // 只有先发送了 onpause，这里才允许发送onResume，避免乱了周期。
        if (!this.isPageToBackground) {
          return;
        }
        this.isPageToBackground = false;
        DeviceEventEmitter.emit(SD_CLOUD_STACK_NAVIGATION_ONRESUME);
      }
    );
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'willBlur',
      () => {
        console.log("sdcard cloud page did blur");
        this.isPageToBackground = true;
        DeviceEventEmitter.emit(SD_CLOUD_STACK_NAVIGATION_ONPAUSE);
      }
    );

    this.mLastScreenSize = this.state.screenSize;
    this.mSupportCloud = this.props.navigation.getParam('isSupportCloud');
    // let shouldShowNoSdcard = this.sdcardCode == 1 || this.sdcardCode == 5;//没有sd卡或者sd卡被推出
    this.RootTabs = createTopTabNavigator(this.sdcardCode, this.isVip, this.isShowSdcardPage, this.mSupportCloud);
  }

  componentDidMount() {
    // Orientation.addOrientationListener(this._orientationListener);
    // see https://blog.csdn.net/u011068702/article/details/83218639
    // https://stackoverflow.com/questions/46269595/react-navigation-navigating-from-child-component/51333660#51333660
    // https://stackoverflow.com/questions/57179865/how-can-a-tabnavigator-child-screen-refer-to-parent-stacknavigator
    

    this.enterPlaybackTime = new Date().getTime();
    Dimensions.addEventListener('change', this.dimensionListener);

  }

  dimensionListener = (args) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    if (Platform.OS === "ios") {
      if (args && args.screen && args.window) {
        if (args.screen.width !== args.window.width || args.screen.height !== args.window.height || this.mLastScreenSize.width != Dimensions.get('screen').width) {
          this.setState({ screenSize: Dimensions.get('screen') });
          this.mLastScreenSize = Dimensions.get('screen');
          console.log('Dimensions changed========3');
        }
      }
    }
  }

  componentWillUnmount() {
    // Orientation.removeOrientationListener(this._orientationListener);
    this.didBlurListener && this.didBlurListener.remove();
    this.didBlurListener && this.didBlurListener.remove();
    Dimensions.removeEventListener('change', this.dimensionListener);
    let playbackTime = (new Date().getTime() - this.enterPlaybackTime) / 1000;
    TrackUtil.reportResultEvent("Camera_Playback_Time", "Time", playbackTime); // Camera_Playback_Time
  }


  // https://reactnavigation.org/docs/2.x/common-mistakes/ 可以参考这个解决不同子页面无法跳转的问题。
  render() {
    let statusBarHeight = _getStatusBarHeight();
    let mW = '100%';
    let mH = '100%';
    if (Platform.OS == 'ios') {
      mW = this.state.screenSize.width;
      mH = this.state.screenSize.height;
    }

    return (
        <this.RootTabs />
    );

  }

}

class AppContainer extends PureComponent {
  render() {
    return (
      <View style={{ width: "100%", height: "100%" }}>

      </View>
    );
  }
}



function _getStatusBarHeight() {
  let statusBarHeight = 0;
  if (Platform.OS == 'android') {
    statusBarHeight = StatusBar.currentHeight || 0;
  } else {
    statusBarHeight = getStatusBarHeight();
  }
  return statusBarHeight;
}

const stackNavigationStyle = StyleSheet.create({
  statusBar: {
    height: navigationHeigth,
    paddingTop: stautsBarHeight,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff'
  },
  orderCompleteView: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  orderCompleteBackTouchView: {
    padding: 5
  },
  orderCompleteBackIcon: {
    width: 20,
    height: 20,
    marginLeft: 10
  },
  orderCompleteTouchView: {
    flex: 1,
    alignItems: 'flex-end',
    paddingTop: 12
  },
  orderCompleteTx: {
    fontSize: kIsCN ? 14 : 12,
    fontWeight: 'bold'
  },
  orderCompleteIndicatorView: {
    width: 30,
    height: 3,
    borderRadius: 8,
    marginTop: 8,
    marginRight: 6
  },
  orderAuditingTouchView: {
    flex: 1.2,
    alignItems: 'center',
    paddingTop: 12
  },

  orderAuditingTx: {
    fontSize: kIsCN ? 14 : 12,
    fontWeight: 'bold'
  },

  orderAuditingIndicatorView: {
    marginTop: 8,
    width: 30,
    height: 3,
    borderRadius: 8
  },
  orderAllTouchView: {
    flex: 1,
    paddingTop: 12
  },
  orderAllTx: {
    marginRight: 14,
    fontWeight: 'bold'
  },
  orderAllIndicatorView: {
    width: 30,
    height: 3,
    borderRadius: 8,
    marginTop: 8
  }
});
