'use strict';

import React from 'react';
import { StatusBar, BackHandler, View, Text, Image, StyleSheet, FlatList, Dimensions, TouchableOpacity, TouchableWithoutFeedback, PermissionsAndroid, Platform, NativeModules } from 'react-native';
import { SafeAreaView } from 'react-navigation';
import LinearGradient from 'react-native-linear-gradient';
import { Device, Service, PackageEvent, Host, System } from 'miot';
import NavigationBar from "miot/ui/NavigationBar";

import { MessageDialog } from "mhui-rn";
import ImageButton from "miot/ui/ImageButton";

import Toast from '../components/Toast';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';

import SdFileManager, { DownloadVideoState } from './util/SdFileManager';

import CameraRenderView from 'miot/ui/CameraRenderView';
import { MISSCodec, MISSSampleRate, MISSDataBits, MISSAudioChannel } from 'miot/ui/CameraRenderView';
import Slider from "react-native-slider";
import CameraPlayer from '../util/CameraPlayer';
import { MISSCommand, MISSConnectState, MISSError } from "miot/service/miotcamera";
import Orientation from 'react-native-orientation';

import StorageKeys from '../StorageKeys';

import AlbumHelper from "../util/AlbumHelper";
import CameraConfig, { IMG_DARKMODE_TINT } from '../util/CameraConfig';
import TrackConnectionHelper from '../util/TrackConnectionHelper';
import EventTypeConfig from './util/EventTypeConfig';
import CancelableProgressDialog from '../ui/CancelableProgressDialog';
import { ChoiceDialog } from 'mhui-rn';
import TrackUtil from '../util/TrackUtil';
import { isIphoneX, getStatusBarHeight } from 'react-native-iphone-x-helper';
import LoadingView from '../ui/LoadingView';
import RectAngleView from '../ui/RectAngleView';

import DldMgr from '../framework/DldMgr';
import SdcardEventLoader from '../framework/sdcard/SdcardEventLoader';
import { DldStatus } from '../framework/EventLoaderInf';
import CommonMsgDialog from '../ui/CommonMsgDialog';
import RPC from '../util/RPC';
import { DescriptionConstants } from '../Constants';
import { DarkMode } from 'miot/Device';
import SpecUtil from '../util/SpecUtil';
import { CardWidth, CardHeight } from '../widget/EventGridCard';
import Util from '../util2/Util';
import VersionUtil from '../util/VersionUtil';
import StatusBarUtil from '../util/StatusBarUtil';
import LogUtil from "../util/LogUtil"
import { handlerOnceTapWithToast } from "../util/HandlerOnceTap";



const statusBarHeight = StatusBarUtil._getInset("top");




const TAG = "SdcardPlayerPage";
const kWindowHeight = Math.max(Dimensions.get("window").height, Dimensions.get("window").width);
const kWindowWidth = Math.min(Dimensions.get("window").height, Dimensions.get("window").width);
const portraitVideoHeight = Number.parseInt(kWindowWidth / 1.78);
const navigationBarHeightFat = 53; // 导航栏高度，有副标题
const fixControlBarHeight = kWindowHeight > 600 ? 127 : 90;


export default class SdcardPlayerPage extends React.Component {

  static navigationOptions = () => {
    // const { titleProps } = navigation.state.params || {};
    // if (!titleProps) 
    return {
      headerTransparent: true,
      header: null
    };
    // return {
    //   header:
    //     <MultiSelectableNavigationBar
    //       {...titleProps}
    //     />
    // };
  };

  state = {
    pstate: -1,
    error: -1,
    isSelectAll: false,
    isSelectMode: false,
    index: 0,
    isEmpty: true,
    sdcardFiles: [],
    dialogVisible: false,
    disableSelect: false,


    showPlayToolBar: true,

    fullScreen: false,
    isMute: CameraConfig.getUnitMute(),
    isSleep: false,
    resolution: 0,
    speed: 1, // 倍速
    isPlaying: true,

    screenshotVisiblity: false, // 截图是否可见

    showErrorView: false,
    showLoadingView: false,
    showPoweroffView: false,
    showPauseView: false,
    errTextString: "", // 错误提示文案

    startTimeStr: "",
    endTimeStr: "",

    progress: 0,

    state: 0,

    currentPlayingIndex: 0,

    screenshotPath: null,
    dialogVisibility: false,
    showDownloadHint: false,

    permissionRequestState: 0,
    showPermissionDialog: false,
    duration: 60000,

    isSeeking: false,
    showNetworkDialog: false,
    isInSameLAN: true
  };


  constructor(props) {
    super(props);
    this.isDelete = false;
    this.dateTime = new Date();
    this.isUserPause = false;

    this.isPluginForeGround = true;// sdcard页面不存在跳到其他页面的情况
    this.isPageForeGround = true;
    this.isAppForeground = true;
    this.isFirst = true;
    // see https://blog.csdn.net/u011068702/article/details/83218639
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.isPageForeGround = true;
        this.isPluginForeGround = true;
        this.isAppForeground = true;
        this.restoreOri();

        this._onResume();
        // 视频下载回调。
        this.sdcardVideoDownload = DldMgr.addListener((status) => {
          console.log("download status:test", status);
          // this.videoFileListener();
        });
        this.fileListListener = SdcardEventLoader.getInstance().addListener(() => {
          // 刷新内容
          this._onGetData();
        });

      }
    );
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'willBlur',
      () => {
        this.sdcardVideoDownload && this.sdcardVideoDownload.remove();
        this.sdcardVideoDownload = null;
        this.fileListListener && this.fileListListener.remove();
        this.fileListListener = null;

        this.isPageForeGround = false;
        this._onPause();
        !this.destroyed && this.cameraRenderView && this.cameraRenderView.hidesSurfaceView(); //这个页面没有必要调用这个

      }
    );

    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.isAppForeground = true;// rnactivity调用了onresume
      this.restoreOri();
      this._onResume();
    });

    this.willPauseListener = PackageEvent.packageWillPause.addListener(() => {
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.iosRetryDone = false;
      this.isAppForeground = false;// rnactivity调用了onresume
      this._onPause();
    });

    if (Platform.OS == "ios") {
      this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.isPluginForeGround = true;// rnactivity调用了onresume
        this._onResume();
      });

      this.willDisappearListener = PackageEvent.packageViewWillDisappearIOS.addListener(() => {
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.toPortrait();// ios强制切换到竖屏去
        setTimeout(() => {
          this.isPluginForeGround = false;// rnactivity调用了onpause
          // console.log('testaaa', 'willDisappearListener', 'page: ', this.isPageForeGround, ' plugin: ', this.isPluginForeGround);
          this._onPause();
          
        }, 500);
      });
    } else {
      if (PackageEvent.packageWillStopAndroid) {
        this.willStopListener = PackageEvent.packageWillStopAndroid.addListener(() => {
          // this.cameraRenderView && this.cameraRenderView.hidesSurfaceView();
        });
      }
    }
    this.ignoreDataWarning = false;

    this.currentNetworkState = -1;
    this.isChangingNetwork = false;

    this.cameraRenderView = null;


    this.offset = 0;
    this.mOri = "PORTRAIT";

    this.showPlayToolBarTimer = null;
    this.isSupportDownload = CameraConfig.shouldDownloadSdcardFile(Device.model);

    this.downloadingVideoTimestamps = [];
    this.shouldDownload = false;
    this.isSupportNewStorage = CameraConfig.shouldDisplayNewStorageManage(Device.model);
    this.isMobileNetwork = false;
    this.destroyed = false;
    this.enterPlaybackTime = -1;
    //单独判断是否是V1 V3
    this.isCameraV1orV3 = CameraConfig.isCameraV1orV3(Device.model);

    if (this.isSupportNewStorage) {
      this.imgContainerStl = { width: CardWidth, height: CardHeight, marginLeft: 5, marginRight: 5 };
      this.imgStl = { width: CardWidth, height: CardHeight - 38 };
    } else {
      this.imgContainerStl = { width: CardWidth, height: 95, marginLeft: 3.5, marginRight: 3.5 };
      this.imgStl = { width: CardWidth, height: 65 };
    }
    this.selectCount = 0;
    this.iosRetryDone = false;
  }


  _onResume() {
    // byh@20250725 设置字体颜色白色
    StatusBar.setBarStyle("light-content");
    if (!this.isPluginForeGround || !this.isAppForeground) {
      return;
    }
    this.enterPlaybackTime = new Date().getTime();

    CameraPlayer.getInstance().bindConnectionCallback(this._connectionHandler);
    CameraPlayer.getInstance().bindP2pCommandCallback(this._ServerRespHandler);
    CameraPlayer.getInstance().bindNetworkInfoCallback(this._networkChangeHandler);
    CameraPlayer.getInstance().bindPowerOffCallback(this._powerOffHandler);

    if (!this.isPageForeGround) {
      return;
    }
    // 如果已经播放完成了 或者是播放error  或者是用户手动暂停的
    if (this.state.progress >= this.state.duration) {
      return;
    }
    if (this.state.showErrorView) {
      return;
    }
    if (this.state.showPoweroffView) {
      return;
    }
    if (this.isUserPause) {
      return;
    }
    this.queryNetworkJob();

  }

  _onPause() {
    if (this.enterPlaybackTime != -1) {
      let playbackTime = (new Date().getTime() - this.enterPlaybackTime) / 1000;
      TrackUtil.reportResultEvent("Camera_FIlePlayback_Time", "Time", playbackTime);
      this.enterPlaybackTime = -1;
    }


    CameraPlayer.getInstance().bindConnectionCallback(null);
    CameraPlayer.getInstance().bindP2pCommandCallback(null);
    CameraPlayer.getInstance().bindNetworkInfoCallback(null);
    CameraPlayer.getInstance().bindPowerOffCallback(null);

    if (this.state.showErrorView) {
      return;
    }
    if (this.state.showPoweroffView) {
      return;
    }
    if (this.isUserPause) {
      return;
    }
    this._startPlay(false);
  }

  restoreOri() {
    console.log(TAG, "restoreOri");
    if ("PORTRAIT" === this.mOri) {
      this.toPortrait();
    } else {
      this.toLandscape();
    }
  }

  videoFileListener = (timestamp, state) => {
  }


  toPortrait() {
    console.log(TAG, "toPortrait");
    this.mOri = "PORTRAIT";
    CameraConfig.lockToPortrait();
  }

  toLandscape() {
    console.log(TAG, "toLandscape");
    this.mOri = "LANDSCAPE";
    if (Platform.OS === "android") {
      Orientation.lockToLandscape();
    } else {
      Orientation.lockToLandscapeRight();
    }
    if (Host.isPad) {
      Service.miotcamera.enterFullscreenForPad(true);
    }
  }
  componentWillUnmount() {
    // try {
    //   this._startPlay(false);
    // } catch (err) {
    //   console.log(err); // 强制
    // }
    this.sdcardVideoDownload && this.sdcardVideoDownload.remove();
    this.sdcardVideoDownload = null;
    this.fileListListener && this.fileListListener.remove();
    this.fileListListener = null;

    if (this.enterPlaybackTime != -1) {
      let playbackTime = (new Date().getTime() - this.enterPlaybackTime) / 1000;
      TrackUtil.reportResultEvent("Camera_FIlePlayback_Time", "Time", playbackTime);
      this.enterPlaybackTime = -1;
    }
    try {
      this._onPause();
    } catch (exception) {

    }

    // 移除网络状态监听器
    if (this.networkStateListener) {
      this.networkStateListener.remove();
      this.networkStateListener = null;
    }
    
    // 清除超时定时器
    if (this.timeoutLocalPing) {
      clearTimeout(this.timeoutLocalPing);
      this.timeoutLocalPing = null;
    }

    Orientation.removeOrientationListener(this._orientationListener);
    CameraPlayer.getInstance().bindPlaybackTimestampCallback(null);
    CameraPlayer.getInstance().stopPlaybackTimestampInterval();// 防止崩溃
    this.didResumeListener.remove();
    this.willPauseListener.remove();
    if (this.willAppearListener) {
      this.willAppearListener.remove();
    }
    if (this.willDisappearListener) {
      this.willDisappearListener.remove();
    }
    this.didFocusListener.remove();
    this.didBlurListener.remove();
    this.toPortrait();
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.destroyed = true;
  }

  setNavigation(isSelectAll, isSelectMode, isDisableSelect) {
    console.log("cheng" + "setNavigation");
    if (Device.isReadonlyShared) {
      isDisableSelect = true;
    }
    this.setState({ disableSelect: isDisableSelect });
  }


  render() {
    return (

      <SafeAreaView style={[styles.container, this.state.fullScreen? { backgroundColor:  "black"} : null]}
        forceInset={{ top: 'never', bottom: "always" }}
      >
        {this._renderVideoLayout()}
        {this._renderDayFiles()}
        {this._renderEmptyLayout()}
        {this._renderBottomSelectView()}
        {this._renderDownloadHint()}
        {this._renderDialog()}
        {this._renderNetworkDialog()}
        {this._renderLoadingDialog()}
        {this._renderResolutionDialog()}
        {this._renderPermissionDialog()}
      </SafeAreaView>
    );
  }

  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 麦克风
    // 
    let message = "";
    if (this.state.permissionRequestState == 0) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_microphone"]);
    }
    return (
      <MessageDialog
        title={LocalizedStrings["tips"]}
        message={message}
        messageStyle={{
          fontSize: 14,
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );
  }

  _renderDownloadHint() {
    if (!this.state.showDownloadHint) {
      return null;
    }
    let bottom = 0;
    
    return (
      <TouchableOpacity style={{ width: "100%", backgroundColor: "#32BAC0", minHeight: 40, display: "flex", justifyContent: "center", alignItems: "center", paddingHorizontal: 10 }}
        onPress={() => {
          StatusBar.setBarStyle("dark-content");
          this.props.navigation.navigate("DldPage");
        }}
      >
        <Text
          style={{ color: "#ffffff", fontSize: 13, paddingHorizontal: 10 }}
        >
          {LocalizedStrings["download_hint"]}
        </Text>
      </TouchableOpacity>
    );
  }

  _renderLoadingDialog() {
    return (
      <CancelableProgressDialog
        ref={(ref) => {
          this.cancelableProgressDialog = ref;
        }}
        visible={this.state.loading}
        onCancelPress={() => {
          this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
          this.shouldDownload = false;
        }}
        cancelText={LocalizedStrings["action_cancel"]}
        loadingText={this.isDelete ? LocalizedStrings["c_setting"] : LocalizedStrings["action_downloading"]}
        disableOutsideCancel={true}
      />
    );
  }

  _renderVideoLayout() {
    return (
      <View style={this.state.fullScreen ? styles.videoContainerFull : styles.videoContainerNormal}>
        {this._renderTitleView()}

        {this._renderVideoView()}
        {/* {this._renderAngleView()} 与ios保持一致都不显示 */}
        {this._renderPowerOffView()}
        {this._renderPauseView()}
        {this._renderErrorRetryView()}
        {this._renderLoadingView()}


        {this._renderVideoControlView()}
        {this._renderSnapshotView()}
        {this._renderLandscapeTopView()}
      </View>
    );
  }
  // 全屏的标题栏在这里
  _renderLandscapeTopView() {
    if (!this.state.fullScreen) {
      return null;
    }
    if (!this.state.showPlayToolBar) {
      return null;
    }
    let iconBack = require("../../Resources/Images/icon_back_black_nor_dark.png");
    let iconBackPre = require("../../Resources/Images/icon_back_black_nor_dark.png");
    let isPad = Host.isPad

    return (
      <View style={{ position: "absolute", width: "100%", display: "flex", flexDirection: "row", justifyContent: "space-between", paddingLeft: 20, paddingRight: 15, paddingTop: isPad ? 35 : 10 }}>
        <ImageButton
          source={iconBack}
          highlightedSource={iconBackPre}
          style={{ width: 40, height: 40 }}
          onPress={() => {
            this.toPortrait();// 切换到竖屏
          }}
          accessibilityLabel={DescriptionConstants.sd_12}
        >
        </ImageButton>

        <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
          <Text style={{ color: "white", fontSize: 14 }} accessibilityLabel={DescriptionConstants.sd_13 + this.landscapeTitleValue}>{this.landscapeTitle}</Text>
        </View> 

        <View
          // source={iconBack}
          // highlightedSource={iconBackPre}
          style={[styles.videoControlBarItemImg]}
          onPress={() => {
            // this.toPortrait();//切换到竖屏
          }}
        >
        </View>
      </View>
    );
  }


  _renderPauseView() {
    if (this.state.isPlaying || this.state.showLoadingView) {
      return null;
    }
    if (this.state.showErrorView) {
      return null;
    }
    if (this.state.showPoweroffView) {
      return null;
    }
    
    return (
      <View styles={{ position: "absolute", width: "100%", height: "100%" }}>
        <View 
          style={{ width: "100%", height: "100%", display: "flex", alignItems: "center", justifyContent: "center" }}
        >
          <ImageButton
            accessibilityLabel={this.state.showLoadingView?DescriptionConstants.hk_3_5:DescriptionConstants.hk_3_6}
            style={{ width: 64, height: 64 }}
            source={require("../../Resources/Images/home_icon_pause_normal.png")}
            onPress={() => {
              // StorageKeys.IS_DATA_USAGEE_WARNING = false //wifi下
              if (this.networkType == "CELLULAR") {
                this.ignoreDataWarning = true;
              }
              this.isUserPause = false;
              this.queryNetworkJob();
            }}
          />
        </View>
      </View>
    );
  }

  _renderVideoView() {
    return (
      <CameraRenderView
        ref={(ref) => { this.cameraRenderView = ref; }}
        maximumZoomScale={6.0}
        style={styles.videoView}
        videoCodec={CameraConfig.getCameraCoderParam(Device.model, Device.lastVersion).videoCodec}
        audioCodec={CameraConfig.getCameraCoderParam(Device.model, Device.lastVersion).audioCodec}
        audioRecordSampleRate={CameraConfig.getCameraAudioSampleRate(Device.model)}
        audioRecordChannel={MISSAudioChannel.FLAG_AUDIO_CHANNEL_MONO}
        audioRecordDataBits={MISSDataBits.FLAG_AUDIO_DATABITS_16}
        fullscreenState={this.state.fullScreen}
        videoRate={15}
        correctRadius={CameraConfig.getCameraCorrentParam(Device.model).correctRadius}
        osdx={CameraConfig.getCameraCorrentParam(Device.model).osdx}
        osdy={CameraConfig.getCameraCorrentParam(Device.model).osdy}
        onVideoClick={this._onVideoClick.bind(this)}
        onScaleChanged={this._onVideoScaleChanged.bind(this)}
        did={Device.deviceID}
        recordingVideoParam={{ width: 1920, height: 1080 }}
        isFull={false}
      >
      </CameraRenderView>
    );
  }

  // 这里是直播中的小窗口
  _renderAngleView() {
    if (!this.state.showCameraAngleView) {
      return (null);
    }
    
    let sPadding = 20;
    let top=this.state.fullScreen ? 100 : 53 + statusBarHeight +28 // 28 is angle view's height
    let left = this.state.fullScreen ? 55 : sPadding;
    let angleStyle = {
      position: "absolute",
      left: left,
      top: top,
    };
  
    return (
      <View style={angleStyle}>
        <RectAngleView
          ref={(ref) => { this.angleView = ref; }}
          angle={this.state.angle}
          elevation={this.state.elevation}
          scale={this.state.videoScale}
          showScale={this.state.angleViewShowScale}
          accessible={true}
          accessibilityLabel={DescriptionConstants.zb_39.replace('1',this.state.videoScale)}
        />
      </View>
    );
  }

  _renderPowerOffView() {
    // todo render poweroffview  full 
    if (!this.state.showPoweroffView) {
      return null;
    }
    return (
      <TouchableWithoutFeedback
        style={{ position: "absolute", width: "100%", height: "100%" }}
        onPress={() => {}}
        accessibilityState={{
          selected: true
        }}
        testID={'1'}
      >
        <View style={{ backgroundColor: "black", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
        >
          <Image
            style={{ width: 54, height: 54 }}
            source={require("../../Resources/Images/icon_camera_sleep.png")} />
          <Text
            style={{ marginTop: 10, fontSize: 14, color: "#bfbfbf" }}>
            {LocalizedStrings[this._getPowerOffString()]}
          </Text>
        </View>
      </TouchableWithoutFeedback>
    );
  }

  _getPowerOffString() {
    return CameraConfig.isSupportPhysicalCover(Device.model) ? "camera_physical_covered" : "camera_power_off";
  }

  _renderErrorRetryView() {
    if (!this.state.showErrorView) {
      return null;
    }
    return (
      <View
        style={{ position: "absolute", backgroundColor: "black", width: "100%", height: "100%" }}>
        <View style={{ display: "flex", justifyContent: "center", alignItems: "center", width: "100%", height: "100%" }} >
          <TouchableOpacity
            style={{ display: "flex", alignItems: "center" }}
            onPress={() => {
              this.isUserPause = false;
              this.queryNetworkJob();
            }// 走重新播放的逻辑,如果是断线了  会走重连的逻辑的}
            }
          >
            <Image
              style={{ width: 54, height: 54 }}
              source={require("../../Resources/Images/icon_camera_fail.png")} />
            <Text
              style={{ marginTop: 10, fontSize: 14, color: "#bfbfbf", paddingHorizontal: 10  }}>
              {this.state.errTextString}
            </Text>
          </TouchableOpacity>
        </View>

      </View>

    );
    // todo render errorRetryView not 
  }


  _renderLoadingView() {
    // todo render loading view 
    if (!this.state.showLoadingView) {
      return null;
    }
    return (
      <View

        style={{ position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <LoadingView
          style={{ width: 54, height: 54 }}
          color={"#ffffff"}
          size={"large"}

        />
        <Text
          style={{ marginTop: 10, fontSize: 12, color: "#ffffff" }}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );
  }



  _renderTitleView() {
    if (this.state.fullScreen) {
      return null;
    }
    // byh@20250725 删除在view中设置字体颜色setBarStyle方法

    this.dateTime.setTime(this.startTime);
    let hour = this.dateTime.getHours();
    let minutes = this.dateTime.getMinutes();
    let startTime = `${hour > 9 ? `${hour}` : `0${hour}`}:${minutes > 9 ? `${minutes}` : `0${minutes}`}`;
    this.dateTime.setTime(this.endTime);
    hour = this.dateTime.getHours();
    minutes = this.dateTime.getMinutes();
    let endTime = `${hour > 9 ? `${hour}` : `0${hour}`}:${minutes > 9 ? `${minutes}` : `0${minutes}`}`;

    let originalTitle = `${startTime}-${endTime}`;
    let originalTitleValue = startTime + DescriptionConstants.rp_66 + endTime;
    this.landscapeTitle = originalTitle;
    this.landscapeTitleValue = originalTitleValue;
    let title = this.state.isSelectMode ? (this.selectCount <= 0 ? LocalizedStrings["action_select"] : LocalizedStrings["selected_count"].replace("%1$d", this.selectCount)) : originalTitle;

    // second get statusBar height;
    let statusBarH = statusBarHeight;
    if (Host.isPad && Platform.OS == 'ios' && !this.state.fullScreen) {
      statusBarH = 10;
    }
    const containerStyle = {
      position: "absolute",
      height: 53 + statusBarH,
      width: "100%",
      display: "flex",
      flexDirection: "row",
      alignItems: "center", // 垂直居中
      paddingBottom: 0,
      paddingLeft: 9,
      paddingRight: 9,
      top: 0,
      backgroundColor: "#00000000",
      zIndex: 10
    };

    const textContainerStyle = {
      flexGrow: 1,
      alignSelf: 'stretch', // 控制自己填充满父类的高度
      display: "flex",
      flexDirection: "column",
      justifyContent: 'center',
      alignItems: 'stretch', // 控制子类填充满本身的宽度
      marginHorizontal: 5
    };

    const titleTextStyle = {
      fontSize: 18,
      textAlignVertical: 'center',
      textAlign: 'center',
      fontWeight: '500'
    };

    const darkTitleColor = '#ffffff'; // 深色背景下标题颜色
    const titleColor = { color: darkTitleColor };
    let iconSize = 40;
    let isDisable = this.state.disableSelect;

    let isSelectMode = isDisable ? false : this.state.isSelectMode;

    const iconBack = require("../../Resources/Images/icon_back_black_nor_dark.png");
    const iconCancle = require("../../Resources/Images/icon_cancle_white.png");
    const iconSelect = this.state.isSelectAll ? require("../../resources2/images/icon_select_active.png") : require("../../Resources/Images/icon_select_white.png");
    const iconEdit = require("../../Resources/Images/icon_edit_white.png");

    const leftIcon = isSelectMode ? iconCancle : iconBack;
    const rightIcon = isSelectMode ? iconSelect : iconEdit;

    const leftIconData = {
      source: leftIcon,
      accessibilityLabel: !isSelectMode ? DescriptionConstants.sd_1 : DescriptionConstants.hk_3_14,
      onPress: this.state.isSelectMode ?
        this.onExitSelectMode :
        this.onBackPress
    };

    const rightIconData = {
      source: rightIcon,
      accessibilityLabel: !isSelectMode ? DescriptionConstants.hk_3_3 : DescriptionConstants.hk_3_15,
      onPress: !this.state.isSelectMode ?
        this.onEnterSelectMode :
        (() => {
          if (!this.state.isSelectAll) {
            // 就选择50个，产品们脑子有坑
            let selectedCount = 0;
            for (let file of this.state.sdcardFiles) {
              if (file.isSelected) {
                selectedCount++;
              }
            }
            let allCount = this.state.sdcardFiles.length;
            let sdcardCopy = this.state.sdcardFiles;
            if (allCount > 50) {
              let diff = 50 - selectedCount;
              for (let i = 0; i < sdcardCopy.length && diff > 0; i++) {
                if (!sdcardCopy[i].isSelected) {
                  diff--;
                  sdcardCopy[i].isSelected = true;
                }
              }
              this.selectCount = 50;
              Toast.show("max_select_noti");
              this.setState({ sdcardFiles: this.state.sdcardFiles, isSelectAll: true });
              return;
            }
            
            this.onSelectAllChanged(!this.state.isSelectAll);
          } else {
            this.onSelectAllChanged(!this.state.isSelectAll);// 全部取消
          }
          

        })
    };
    return (

      <View
        style={containerStyle}>
        <View style={{position: "relative", marginTop: statusBarH, height: 53, width: "100%", flexDirection:"row", alignItems: "center"}}>
          <View
            style={{ width: iconSize, height: iconSize, position: "relative" }}>

            <ImageButton
              style={{ width: iconSize, height: iconSize, position: "absolute" }}
              source={leftIconData.source}
              onPress={leftIconData.onPress}
              accessibilityLabel={leftIconData.accessibilityLabel}
            />
          </View>

          <View style={textContainerStyle}>
            <Text
              numberOfLines={1}
              style={[titleTextStyle, titleColor]}
              accessibilityLabel={this.state.isSelectMode?title: DescriptionConstants.hk_3_2+this.landscapeTitleValue}
            >
              {title}
            </Text>

          </View>

          <View
            style={{ width: iconSize, height: iconSize, position: "relative" }}>
            {
              isDisable ?
                null :
                <ImageButton
                  style={{ width: iconSize, height: iconSize, position: "absolute" }}
                  source={rightIconData.source}
                  onPress={rightIconData.onPress}
                  accessibilityLabel={rightIconData.accessibilityLabel}
                />
            }

          </View>
        </View>
        

      </View>
    );
  }

  onExitSelectMode = () => {
    this.onSelectAllChanged(false);// 将isSelected重置
    this.setNavigation(false, false, false);
    this.setState({ isSelectMode: false });
  }

  onBackPress = () => {
    this._onPause();
    this.props.navigation.goBack();
  }

  onEnterSelectMode = () => {
    this.setState({ isSelectAll: false, isSelectMode: true });
  }


  _renderVideoControlView() {
    if (!this.state.showPlayToolBar) {
      return null;
    }
    const playIcons = [
      {
        source: require('../../Resources/Images/icon_camera_pause.png'),
        highlightedSource: null,
        onPress: () => { this.isUserPause = true; this._startPlay(false); },
        accessibilityLabel: this.state.isPlaying?DescriptionConstants.hk_3_5:DescriptionConstants.hk_3_6
      },
      {
        source: require('../../Resources/Images/icon_camera_play.png'),
        highlightedSource: null,
        onPress: () => {

          if (this.networkType == "CELLULAR") {
            this.ignoreDataWarning = true;
          }

          this.isUserPause = false;
          this.queryNetworkJob();
        }, // 开始播放
        accessibilityLabel:this.state.showLoadingView?DescriptionConstants.hk_3_5:DescriptionConstants.hk_3_6
      }
    ];
    const audioIcons = [
      {
        source: require('../../Resources/Images/icon_camera_unmute_playback.png'),
        highlightedSource: require('../../Resources/Images/icon_camera_unmute_playback.png'),
        onPress: () => this._toggleAudio(true),
        accessibilityLabel: this.state.isMute ? DescriptionConstants.hk_3_10 : DescriptionConstants.hk_3_10_1

      },
      {
        source: require('../../Resources/Images/icon_camera_mute_playback.png'),
        highlightedSource: require("../../Resources/Images/icon_camera_mute_playback.png"),
        onPress: () => this._toggleAudio(false), // 默认是这个状态
        accessibilityLabel: this.state.isMute ? DescriptionConstants.hk_3_10 : DescriptionConstants.hk_3_10_1

      },
      {
        source: require("../../Resources/Images/icon_camera_mute_playback_dis.png"),
        accessibilityLabel: this.state.isMute ? DescriptionConstants.hk_3_10 : DescriptionConstants.hk_3_10_1
      }
    ];
    const snapShotIcon = [{
      source: require('../../Resources/Images/icon_camera_screenshot_playback.png'),
      highlightedSource: require('../../Resources/Images/icon_camera_screenshot_playback.png'),
      onPress: handlerOnceTapWithToast(() => this._startSnapshot()),
      accessibilityLabel: DescriptionConstants.hk_3_11 

    }];


    const fullScreenIcons = [
      {
        source: require('../../Resources/Images/icon_camera_fullscreen_playback.png'),
        highlightedSource: require('../../Resources/Images/icon_camera_fullscreen_playback.png'),
        onPress: () => { this.toLandscape(); },
        accessibilityLabel:DescriptionConstants.hk_3_13

      },
      {
        source: require('../../Resources/Images/icon_camera_fullscreen_exit_playback.png'),
        highlightedSource: require('../../Resources/Images/icon_camera_fullscreen_exit_playback.png'),
        onPress: () => { this.toPortrait(); },
        accessibilityLabel:DescriptionConstants.sd_21

      }
    ];
    let playIndex = this.state.isPlaying ? 0 : 1;
    let audioIndex = this.state.speed > 1 ? 2 : (this.state.isMute ? 1 : 0);
    let resolutionText = "";
    let replacedSpeed = 1;
    switch (this.state.speed) {
      case 1:
        resolutionText = "1x";
        break;
      case 4:
        resolutionText = this.isCameraV1orV3 ? "2x" : "4x";
        replacedSpeed = this.isCameraV1orV3 ? 2 : 4;
        break;
      case 16:
        resolutionText = this.isCameraV1orV3 ? "3x" : "16x";
        replacedSpeed = this.isCameraV1orV3 ? 3 : 16;
        if (CameraConfig.support8xSpeed(Device.model) && !this.isCameraV1orV3) {
          resolutionText = "8x";
          replacedSpeed = 8;
        }
        break;
      default:
        resolutionText = "1x";
        this.setState({ speed: 1 });
        break;
    }
    let isDark = DarkMode.getColorScheme() == "dark";
    return (

      <View style={this.state.showPlayToolBar ? { bottom: 0, position: "absolute", width: "100%" } : { display: "none" }}>

        <View style={{ display: "flex", flexDirection: "row", flexWrap: "nowrap", alignItems: "center", backgroundColor: "#00000099", height: 40, paddingHorizontal: 5 }}>

          <View style={styles.videoControlBarItem}>

            <ImageButton
              onPress={playIcons[playIndex].onPress}
              style={styles.videoControlBarItemImg}
              source={playIcons[playIndex].source}
              highlightedSource={playIcons[playIndex].highlightedSource}
              accessibilityLabel={playIcons[playIndex].accessibilityLabel}
              accessibilityState={{
                selected: this.state.isPlaying
              }}
              testID={this.state.isPlaying ? '1' : '0'}
            />
          </View>

          <View style={{ flexGrow: 11, display: "flex", flexDirection: "row", height: "100%", alignItems: "center" }}>
            <Text style={{ fontSize: 10, color: "#ffffff" }}
              accessibilityLabel={DescriptionConstants.hk_3_7+"00:"+this.state.startTimeStr || "00:00"}
            >
              {this.state.startTimeStr ? ` ${ this.state.startTimeStr } ` : " 00:00 "}
            </Text>
            <Slider
              style={{ flexGrow: 5, height: 30, marginLeft: 5, marginRight: 5 }}
              maximumValue={Number.parseInt(this.state.duration / 1000)}
              minimumValue={0}
              step={1}
              minimumTrackTintColor={"#32BAC0"}
              maximumTrackTintColor={"xm#ffffff"}
              value={this.state.progress}
              onSlidingComplete={(value) => this.onSlidedProgress(value)}
              onValueChange={(progress) => {
                console.log("==================",progress);
                this.setState({ isSeeking: true },() => {
                  this._updateTimeStr(this.startTime + progress * 1000);
                })
              }}
              thumbTintColor={"xm#ffffff"}
              accessible={true}
              accessibilityLabel={DescriptionConstants.hk_3_8}

            />

            <Text style={{ fontSize: 10, color: "white" }}
              accessibilityLabel={ DescriptionConstants.hk_3_9+"00:"+this.state.endTimeStr || "00:00" }

            >
              {this.state.endTimeStr ? ` ${ this.state.endTimeStr } ` : " 00:00 "}
            </Text>

          </View>


          <View style={styles.videoControlBarItem}>

            <ImageButton
              onPress={audioIcons[audioIndex].onPress}
              disabled={this.state.speed > 1 ? true : false}
              style={styles.videoControlBarItemImg}
              source={audioIcons[audioIndex].source}
              highlightedSource={audioIcons[audioIndex].highlightedSource}
              accessibilityLabel={audioIcons[audioIndex].accessibilityLabel}
              accessibilityState={{
                selected: this.state.isMute
              }}
              testID={this.state.isMute ? '1' : '0'}
            />
          </View>
          <View style={styles.videoControlBarItem}>

            <ImageButton
              onPress={snapShotIcon[0].onPress}
              style={styles.videoControlBarItemImg}
              source={snapShotIcon[0].source}
              highlightedSource={snapShotIcon[0].highlightedSource}
              accessibilityLabel={snapShotIcon[0].accessibilityLabel}
            />
          </View>
          <View style={[styles.videoControlBarItem]}>
            <Text
              style={{
                fontSize: 10, textAlign: "center", paddingVertical: 2, paddingHorizontal: 4, color: "white", borderColor: Util.isDark() ? "xm#FFFFFF" : "xm#FFFFFFCC", borderRadius: 3, borderWidth: 1, textAlignVertical: 'center'
              }}
              ellipsizeMode="tail"
              numberOfLines={2}
              onPress={() => this.setState({ dialogVisibility: true })}
              accessibilityLabel={ DescriptionConstants.hk_3_12.replace('1', replacedSpeed)}
              testID={resolutionText}
            >
              {resolutionText}
            </Text>
          </View>

          <View style={styles.videoControlBarItem}>

            <ImageButton
              onPress={fullScreenIcons[this.state.fullScreen ? 1 : 0].onPress}
              style={styles.videoControlBarItemImg}
              source={fullScreenIcons[this.state.fullScreen ? 1 : 0].source}
              highlightedSource={fullScreenIcons[this.state.fullScreen ? 1 : 0].highlightedSource}
              accessibilityLabel={fullScreenIcons[this.state.fullScreen ? 1 : 0].accessibilityLabel}

            />
          </View>
        </View>
      </View>

    );
  }

  _renderSnapshotView() {


    if (!this.state.screenshotVisiblity) {
      return null;
    }
    let sWidth = 90;
    let sHeight = 55;
    let sPadding = 20;
    let leftPadding = 15;
    if (this.state.fullScreen) {
      sPadding = 90;
      leftPadding = leftPadding + StatusBarUtil._getInset("top");
    } else {
      sPadding = sPadding + statusBarHeight + 65;
    }


    let containerStyle = {
      position: "absolute",
      left: leftPadding,
      top: sPadding,
      width: sWidth,
      height: sHeight,
      borderRadius: 4,
      borderWidth: 1.5,
      borderColor: "xm#ffffff",
      zIndex: 10
    };
    if (this.state.fullScreen) {
      if (Platform.OS == "ios" && isIphoneX()) {
        containerStyle.left = 60;
      }
      if (Host.isPad) {
        containerStyle.top = "50%";
        containerStyle.marginTop = -1 * sHeight / 2;
      }
    }

    return (
      <View style={containerStyle}
        accessibilityLabel={DescriptionConstants.hk_3_4 }
      >
        <ImageButton
          style={{ width: "100%", height: "100%", borderRadius: 4 }}
          source={(this.state.screenshotPath == "") ? null : ({ uri: `file://${Host.file.storageBasePath}/${this.state.screenshotPath}` })}
          fadeDuration={0}
          onPress={() => {
            clearTimeout(this.snapshotTimeout);
            this.setState({ screenshotVisiblity: false, screenshotPath: "", isWhiteVideoBackground: true });// 点击后就消失。

            if (this.isForVideoSnapshot) {
              console.log("点击了缩略图，跳转到视频页面111");
              setTimeout(() => {
                this.showLastVideo();
              }, 100)

              // this.props.navigation.navigate("AlbumVideoViewPage");
            } else {
              console.log("点击了缩略图，跳转到图片页面");
              this.showLastImage();
              // this.props.navigation.navigate("AlbumPhotoViewPage");
            }
            this.isForVideoSnapshot = false;

            // todo jump to album activity
          }}
        />

      </View>
    );

  }

  _renderDialog() {
    let title = this.isDelete ? LocalizedStrings["delete_title"] : LocalizedStrings["save_title"];
    let btn = this.isDelete ? LocalizedStrings["delete_confirm"] : LocalizedStrings["action_confirm"];
    return (
      <MessageDialog 
        // title={LocalizedStrings["tips"]}
        message={title}
        messageStyle={{
          textAlign: "center"
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              console.log('onCancel');
              this.isDelete = false;
              this.setState({ dialogVisible: false });
            }
          },
          {
            text: btn,
            callback: () => {
              if (this.isDelete) {
                this.onConfirmDelete();
              } else {
                this.onConfirmSave();
              }
              this.setState({ dialogVisible: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ dialogVisible: false });
        }}
        cancelable={true}
        visible={this.state.dialogVisible} />
    );
  }

  _renderNetworkDialog() {
    return (
      <MessageDialog
        message={LocalizedStrings["lan_is_different"]}
        buttons={[
          {
            text: LocalizedStrings["offline_divice_ok"],
            colorType: "grayLayerBlack",
            callback: () => {
              this.setState({ showNetworkDialog: false });
              if (this.state.fullScreen) {
                // 切换到竖屏
                this.toPortrait();
                StatusBar.setBarStyle("light-content");
                StatusBar.setHidden(false);
              }
              // 导航到 AllStorage 页面而不是返回上一级
              this.props.navigation.pop(2);
              // setTimeout(() => {
              //   // 导航到 AllStorage 页面而不是返回上一级
              //   this.props.navigation.pop(2);
              // }, 100);

            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showNetworkDialog: false });
          // 导航到 AllStorage 页面而不是返回上一级
          if (this.state.fullScreen) {
            // 切换到竖屏
            this.toPortrait();
            StatusBar.setBarStyle("light-content");
            StatusBar.setHidden(false);
          }
          this.props.navigation.pop(2);

        }}
        cancelable={true}
        visible={this.state.showNetworkDialog}
      />
    );
  }

  // hasNotch() {
  //   let screenW = Dimensions.get('window').width;
  //   let screenH = Dimensions.get('window').height;
  //   return Platform.OS === 'ios' && ((screenH >= 812 && screenW >= 375) ||
  //   (screenH >= 375 && screenW >= 812));
  // }

  _renderBottomSelectView() {
    if (!this.state.isSelectMode || this.state.fullScreen) {
      return;
    }

    return (
      <View style={{ width: "100%", height: 69, borderTopColor: "#e5e5e5", borderTopWidth: 1, display: "flex", flexDirection: "row", justifyContent: "center", alignItems: "center" }}>
        <TouchableOpacity
          // accessibilityLabel={DescriptionConstants.hk_3_19}
          style={{ width: 90, display: "flex", alignItems: "center" }}
          onPress={() => { this.onPressSave(); }}
        >
          <Image
            style={{ width: 25, height: 25 }}
            source={Util.isDark() ? require("../../resources2/images/icon_videorecord_download_w.png") : require("../../resources2/images/icon_videorecord_download_b.png")} />
          <Text
            style={{ color: "#000000", fontSize: 11 }}
          >
            {LocalizedStrings["save_files"]}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          // accessibilityLabel={DescriptionConstants.hk_3_20}
          style={{ marginLeft: 30, width: 90, display: "flex", alignItems: "center" }}
          onPress={() => { this.onPressDelete(); }}
        >
          <Image
            style={{ width: 25, height: 25 }}
            source={Util.isDark() ? require("../../resources2/images/icon_videorecord_delete_w.png") : require("../../resources2/images/icon_videorecord_delete_b.png")} />
          <Text
            style={{ color: "#000000", fontSize: 11 }}
          >
            {LocalizedStrings["delete_files"]}
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  _renderDayFiles() {
    if (this.state.isEmpty) {
      return null;
    }
    return (
      // <View style={{ flexGrow: 1, marginTop: 22, marginHorizontal: 16, backgroundColor:"blue" }}>
      <FlatList
        ref={(ref) => { this.flatListRef = ref; }}
        style={this.state.fullScreen ? { display: "none" }: [{ flex: 1, paddingTop: 22, paddingHorizontal: 16 }, this.state.fullScreen ? {} : { borderTopColor: "#e5e5e5", borderTopWidth: 0.5 }]}
        data={this.state.sdcardFiles}
        renderItem={({ item, index }) => this._renderDayFile(item, index)}
        numColumns={3}
        ListFooterComponent={<View style={{ height: 20 }}></View>}
        keyExtractor={(item, index) => index}
        getItemLayout={(data, index) => {
          let ret = { length: 110, offset: 110 * Math.floor(index % 3) + 42, index };
          return ret;
        }}
      />
      // </View>

    );

  }

  _renderDayFile(item, index) {
    let marginHorizontal = 3.5;
    let screenWidth = self.windowWidth;
    let containerWidth = (screenWidth - 33 - marginHorizontal * 6) / 3;
    this.dateTime.setTime(item.startTime);
    let hour = this.dateTime.getHours();
    let minutes = this.dateTime.getMinutes();
    let startTime = `${hour > 9 ? `${hour}` : `0${hour}`}:${minutes > 9 ? `${minutes}` : `0${minutes}`}`;
    this.dateTime.setTime(item.endTime);
    hour = this.dateTime.getHours();
    minutes = this.dateTime.getMinutes();
    let endTime = `${hour > 9 ? `${hour}` : `0${hour}`}:${minutes > 9 ? `${minutes}` : `0${minutes}`}`;
    let path = SdFileManager.getInstance().getImageFilePath(item.startTime);
    let source = (path == null) ? require("../../Resources/Images/icon_camera_file_loading.png") : ({ uri: `file://${Host.file.storageBasePath}${path}` });
    let playingBackground = {
      borderColor: "#32BAC0",
      borderWidth: 2,
      borderRadius: 6
    };
    return (
      <TouchableOpacity
        style={[{ width: containerWidth, height: 95, paddingBottom: 10, marginLeft: 3.5, marginRight: 3.5 }, this.imgContainerStl]}
        onPress={() => this._onPressVideoFileView(index)}

      >

        <View
          accessibilityState={{
            selected: item.isSelected
          }}

          style={[{ width: "100%", height: 60, marginBottom: 3, position: "relative" }, this.state.currentPlayingIndex == index ? playingBackground : null, this.imgStl]} >
          <Image style={{ width: "100%", height: "100%", borderRadius: 5, resizeMode: "stretch" }}
            source={source}
          >
          </Image>
          {
            this.state.isSelectMode ?
              <Image
                accessibilityLabel={item.isSelected ? DescriptionConstants.selected: DescriptionConstants.unSelected}
                style={{ width: 20, height: 20, position: "absolute", bottom: 4, right: 4 }}
                source={item.isSelected ? require("../../Resources/Images/icon_selected.png") : require("../../Resources/Images/icon_unselected.png")}
              /> :
              null
          }

          {
            item.save == 1 ?
              <Image
                style={{ width: 16, height: 16, position: "absolute", bottom: 4, left: 4 }}
                source={require("../../Resources/Images/icon_lock.png")}
              />
              :
              null
          }

        </View>
        <View
          style={{ display: "flex", flexDirection: "row", alignItems: "center" }}
        >
          {this._renderFileFlagIcons(item)}

          <Text style={{ fontSize: 11, color: "#000000" }} accessibilityLabel={DescriptionConstants.rp_67 + startTime + DescriptionConstants.rp_66 + endTime}
          >
            {` ${startTime}-${endTime} `}
          </Text>

        </View>


      </TouchableOpacity>
    );
  }

  _renderFileFlagIcons(item) {
    let eventType = item.eventType;
    if (eventType < EventTypeConfig.SD_OBJECT_MOTION) {
      return;
    }


    let source = null;
    if (eventType == EventTypeConfig.SD_PEOPLE_MOTION) {
      source = require("../../Resources/Images/icon_event_type_people_run.png");
    } else if (eventType == EventTypeConfig.SD_BABY_CRY) {
      source = require("../../Resources/Images/icon_event_type_baby_cry.png");
    } else if (eventType == EventTypeConfig.SD_DOG || eventType == EventTypeConfig.SD_CAT) {
      source = require("../../Resources/Images/icon_event_type_pet.png");
    } else if (eventType == EventTypeConfig.SD_FACE) {
      source = require("../../Resources/Images/icon_event_type_unknown_people.png");
    } else if (eventType == EventTypeConfig.SD_KNOWN_FACE) {
      source = require("../../Resources/Images/icon_event_type_known_people.png");
    } else if (eventType == EventTypeConfig.SD_CAMERA_CALLING) {
      source = require("../../Resources/Images/icon_event_type_camera_calling.png");
    } else if (eventType == EventTypeConfig.SD_LOUDER_SOUND) {
      source = require("../../Resources/Images/icon_event_type_louder.png");
    } else if (eventType == EventTypeConfig.SD_OBJECT_MOTION) {
      source = require("../../Resources/Images/icon_event_type_object_motion.png");
    } else if (eventType == EventTypeConfig.SD_CHILD) {
      source = require("../../Resources/Images/icon_event_type_child.png");
    }
    if (source == null) {
      return null;
    }
    return (
      <Image style={{ width: 14, height: 14, marginLeft: 5 }}
        source={source} />
    );
  }

  _renderEmptyLayout() {
    if (this.state.isEmpty) {
      return (
        <View
          style={{ width: "100%", flexGrow: 1, display: "flex", justifyContent: "center", alignItems: "center" }}
        >
          <Image
            // accessibilityLabel={DescriptionConstants.hk_3_21}
            source={require("../../Resources/Images/icon_camera_empty_files.png")}
            style={{ width: 79, height: 79 }}
          />
          <Text
            style={{ fontSize: 14, color: "#808080" }}
          >
            {LocalizedStrings["no_files"]}
          </Text>
        </View>
      );
    } else {
      return null;
    }

  }

  componentDidMount() {
    self.windowWidth = Dimensions.get("window").width;
    self.windowHeight = Dimensions.get("window").height;
    if (self.windowHeight < self.windowWidth) {
      let sw = self.windowWidth;
      self.windowWidth = self.windowHeight;
      self.windowHeight = sw;
    }
    this.setNavigation(false, false, false);
    this.setState({ index: 1 });

    this.dayStartTime = this.props.navigation.getParam("dayBeginTime");
    this.hourStartTime = this.props.navigation.getParam("hourBeginTime");
    this.hour = this.props.navigation.getParam("hour");
    this.tag = this.props.navigation.getParam("tag");// 年月日的形式
    this.startTime = this.props.navigation.getParam("startTime");
    this.lastTimestamp = this.startTime / 1000;
    this.setState({ duration: 60000 });
    this.endTime = this.startTime + 60000;
    this.sessionId = 0;
    this.slideProgress = 0;
    

    this.updateSdcardFiles();
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }

    this.restoreOri();
    Orientation.addOrientationListener(this._orientationListener);

    // 添加网络状态监听器
    this.networkStateListener = CameraPlayer.getInstance().addNetworkListener(this.checkLocalNetwork.bind(this));
    
    // 初始检查局域网状态
    this.checkLocalNetwork();
    
    this.isTryConnecting = false;
  }


  _orientationListener = (orientation) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    orientation === 'LANDSCAPE' ? StatusBar.setHidden(true) : StatusBar.setHidden(false);
    if (!this.isPageForeGround || !this.isPluginForeGround || !this.isAppForeground) {
      return;
    }
    console.log(TAG, `device orientation changed :${orientation} want ${this.mOri}`);
    if ((Platform.OS == "ios" || (Host.isAndroid && Host.isPad)) && ((this.mOri === 'LANDSCAPE' && this.state.fullScreen) || (this.mOri === 'PORTRAIT' && !this.state.fullScreen))) {
      return;
    }
    if (this.mOri === orientation) {
      if (orientation === 'LANDSCAPE') {
        // if (Host.isPad) { // 直播页面也需要调用
        //   Service.miotcamera.enterFullscreenForPad(true);
        // }
        // do something with landscape layout
        this.setState({ fullScreen: true });
        StatusBar.setBarStyle("light-content");
        StatusBar.setHidden(true);
      } else {
        // do something with portrait layout
        // Service.miotcamera.enterFullscreenForPad(false);
        this.setState({ fullScreen: false });
        StatusBar.setBarStyle("light-content");
        StatusBar.setHidden(false);
      }
    } else {
      // ios need restore for next lock
      this.restoreOri();
    }
  };
  onSlidedProgress(progress) {
    CameraPlayer.getInstance().stopPlaybackTimestampInterval();
    this.setState((state) => {
      return {
        progress: progress,
        isSeeking: false
      };
    }, () => { // 数据塞给listview了，渲染还需要时间吧，延迟一会再执行scroll
      this.queryNetworkJob();
    });

  }

  _powerOffHandler = (isPowerOn, showSleepDialog, startVideo) => {
    // 电源属性状态发生了变化
    this.setState({ showPoweroffView: !isPowerOn, showPlayToolBar: isPowerOn, isPlaying: isPowerOn });
    if (!isPowerOn) { // 暂停
      this._startPlay(false);
    } else {
      this.queryNetworkJob();
    }
  }

  _networkChangeHandler = (networkState) => {
    // if (this.currentNetworkState != networkState) { // 网络状态不一样
    //   this.isChangingNetwork = true;
    // }

    clearTimeout(this.showNetworkDisconnectTimeout);
    if (networkState == 0 || networkState == -1) { // 网络断开了连接 showError?
      Service.smarthome.reportLog(Device.model, "网络异常" + networkState);
      // CameraPlayer.getInstance().disconnectToDevice();// 去往其他注册了网络监听的页面，就不会走到这里了，如果走到这里，这里必须先执行断开的操作
      this.showNetworkDisconnectTimeout = setTimeout(() => {
        this.handleDisconnected(MISSError.MISS_ERR_CLOSE_BY_LOCAL);// 刷新UI，避免出现异常。  
      }, 1300);
      return;
    }

    this.currentNetworkState = networkState;
    if (this.isPageForeGround) { // 有网络来了  发起重连吧
      this.setState({ showErrorView: false });
      setTimeout(() => {
        this.queryNetworkJob();
      }, 500);// 过一会再查询 ，那个查询网络类型的api有问题。
    }
  }

  _hidePlayToolBarLater() {
    if (this.state.fullScreen) {
      return;
    }
    let tTimer = 5000;
    clearTimeout(this.showPlayToolBarTimer);
    this.showPlayToolBarTimer = setTimeout(() => {
      this.setState({ showPlayToolBar: false });
    }, tTimer);
  }

  _ServerRespHandler = ({ command, data }) => {
    // todo  处理文件播放的问题
    if (command == MISSCommand.MISS_CMD_PLAYBACK_RESP) {
      let dataJSON = Host.isAndroid ? JSON.parse(data) : data;
      LogUtil.logOnAll(TAG, `server response:${JSON.stringify(data)} sessionId:${this.sessionId}`);
      let id = dataJSON["id"];
      if (id == null) {
        return;
      }
      if (id != this.sessionId) {
        return;
      }
      let status = dataJSON["status"];
      if (status == null) {
        return;
      }
      switch (status) {
        case "filefound":
          // console.log(dataJSON);
          // what todo ?  
          this.setState({ showLoadingView: false });
          // 开始请求timestamp
          setTimeout(() => {
            CameraPlayer.getInstance().getPlaybackTimetampInterval();
            CameraPlayer.getInstance().bindPlaybackTimestampCallback(this.timestampCallback);
          }, 500);// 延迟一会

          break;
        case "filenotfound":
          this.setState({ progress: 0, showErrorView: true, showLoadingView: false, errTextString: LocalizedStrings["camera_play_error_file"] });
          this._startPlay(false);
          break;
        case "endoffile":
          this.endoffile();
          // 取消timestamp的定时请求
          break;
        case "readerror":
          this.setState({ progress: 0, showLoadingView: false, showErrorView: true, errTextString: LocalizedStrings["camera_play_error_file"] });
          this._startPlay(false);
          // 取消timestamp的定时请求
          break;
      }
    }
  }
  endoffile() {
    this.isUserPause = true;
    this.setState({ showLoadingView: false,progress: Number.parseInt(this.state.duration / 1000)},()=>{//结束后进度条自动跳转到最后
      setTimeout(()=>{
        this.setState({progress: 0})
      },1000)
    });
    this._updateTimeStr(this.startTime);// reset sdcardplayer startTime;
    this._startPlay(false);
    this.setState({ showPauseView: true });
  }
  // 每隔一秒调用一次 在拖拽到最后的时候 拖拽成功 但是传入的timestamp又会继续返回前2秒的数据
  timestampCallback = (timestamp) => {
    // 这里每隔一秒就会触发一次 返回当前的时间戳
    // first if in touching slider  should ignore
    // but i dont know whether slider is sliding
    console.log("currentTime:" + timestamp);
    console.log('lastTimestamp:' + this.lastTimestamp);
    console.log('real offset:' + (timestamp - this.startTime / 1000) + " expected offset:" + this.offset);
    if (timestamp == this.lastTimestamp) { // 没有发生更新
      return;
    }
    if (timestamp > (this.startTime / 1000 + 61) || (this.startTime / 1000) > timestamp) { // v3回看 每个视频1分钟 超过了就是有问题的
      return;
    }

    if (this.startTime / 1000 + this.offset > timestamp) { // 拉到的不是预期的。
      return;
    }
    this.lastTimestamp = timestamp;
    if (this.state.isSeeking) {
      return;// 拖拽中 不允许修改progress
    }

    this._updateTimeStr(timestamp * 1000);


    let tmp = timestamp - this.startTime / 1000;

    this.setState({ progress: tmp });// 更新进度条
  }

  _startSnapshot() {
    if (Platform.OS === "android") {
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this._realStartSnapshot(false);
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            !this.destroyed && Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
          !this.destroyed && Toast.fail("action_failed", error);
        });
    } else {
      // no ios's photos const use hardcode
      System.permission.request("photos").then((res) => {
        this._realStartSnapshot(false);
      }).catch((error) => {
        this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
      });

    }
  }

  _realStartSnapshot(isFromVideo) {
    clearTimeout(this.snapshotTimeout);
    this.setState({ screenshotVisiblity: false, screenshotPath: null });
    AlbumHelper.snapShot(this.cameraRenderView)
      .then((path) => {
        console.log(path);
        this.isForVideoSnapshot = isFromVideo;
        this.setState({ screenshotVisiblity: true, screenshotPath: path });// show snapshotview
        clearTimeout(this.snapshotTimeout);
        this.snapshotTimeout = setTimeout(() => {
          this.isForVideoSnapshot = false;
          this.setState({ screenshotVisiblity: false, screenshotPath: "" });
        }, 3000);
        // 文件路径。
      })
      .catch((error) => {
        console.log(JSON.stringify(error));
        !this.destroyed && Toast.success("action_failed");
      });
  }

  _toggleAudio(isMute, changeUnitMute = true) {
    if (!isMute) {
      TrackUtil.reportClickEvent("Playback_OpenVolume_ClickNum")
    }
    if (isMute) {
      if (this.state.isPlaying) {
        Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {}).then((retCode) => {
          console.log("audio stop get send callback");
          console.log(retCode);
        });

        !this.destroyed && this.cameraRenderView && this.cameraRenderView.stopAudioPlay();
      }
      this.setState({ isMute: true });
      if (changeUnitMute) {
        CameraConfig.setUnitMute(true);
      }
      return;
    }
    if (this.state.speed > 1) {
      return;// 倍速模式下 不要播放声音
    }
    if (this.state.isPlaying) {
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_START, {}).then((retCode) => {
        console.log("audio stop get send callback");
        console.log(retCode);
      });
    }
    if (this.state.isPlaying) {
      !this.destroyed && this.cameraRenderView && this.cameraRenderView.startAudioPlay();
    }
    this.setState({ isMute: false });
    if (changeUnitMute) {
      CameraConfig.setUnitMute(false);
    }

  }

  // 进来先调用startPlay  startPlay里会check连接状态，如果是连接中的状态，直接发playback指令；如果不是连接中的状态，先连接，连接走到这里 如果连接成功，就会重新走startPlay流程
  _connectionHandler = (connectionState) => {
    console.log("_connectionHandler sdcard ", connectionState);
    if (connectionState == null) {
      return;
    }
    if (this.state.pstate == connectionState.state) {
      return;// 状态一样 没有必要通知
    }
    if (connectionState.state == 0) { // 断开连接
      if (connectionState.error == 1 && Platform.OS == 'ios' & !this.iosRetryDone) {
        this.iosRetryDone = true;
        this.queryNetworkJob();
      } else {
        this.isTryConnecting = false;
        this.handleDisconnected(connectionState.error);
      }
      return;
    }

    this.setState((state) => {
      return {
        pstate: connectionState.state,
        error: connectionState.error
      };
    }, () => {

      if (connectionState.state == MISSConnectState.MISS_Connection_Connected) {
        if (Platform.OS === "ios") {
          // fix MIIO-40295 需要使用命令 MISS_CMD_VIDEO_START启动底层 avReceiveFrame
          if (VersionUtil.judgeIsMiss(Device)) {//miss不需要手动启动video-start
            if (!this.isTryConnecting) {
              return;
            }
            this.isTryConnecting = false;

            this._startPlay(true);
          } else { //tutk才需要
            Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_VIDEO_START, {})
              .then((retCode) => {
                if (this.isUserPause) { // 如果是用户手动暂停的，回来后，重连 不做这个事情。  重连是谁发起的？
                  return;
                }
                this._startPlay(true);// 处理因为网络变化走到这里的重连  要区分是wifi还是4g
              })
              .catch((err) => {
                if (err == -1) {
                  this.queryNetworkJob();
                  return;
                }// 打开摄像头失败  给一个提示
                this.setState({ showErrorView: true, showLoadingView: false, errTextString: `${LocalizedStrings["camera_connect_error"]},${LocalizedStrings["camera_connect_retry"]}` });
                console.log(err);
              });
          }

        } else { // fangzhi chonglian
          if (!this.isTryConnecting) {
            return;
          }
          this.isTryConnecting = false;

          this._startPlay(true);
        }
      }
    });


  }

  handleDisconnected(errorCode) {

    !this.destroyed && this.cameraRenderView && this.cameraRenderView.stopAudioPlay();
    !this.destroyed && this.cameraRenderView && this.cameraRenderView.stopRender();
    // sync ui state
    this.setState({ isMute: CameraConfig.getUnitMute() });
    // this._stopRecord();

    if (!Device.isOnline) {
      return;
    }
    this.setState({ showPlayToolBar: false, showErrorView: true, isPlaying: false, showLoadingView: false, errTextString: `${LocalizedStrings["camera_connect_error"]} ${errorCode}, ${LocalizedStrings["camera_connect_retry"]}` });
  
  }

  _startPlay(isPlay) {
    if (isPlay) {
      // 开始播放
      if (this.state.progress > this.state.duration) {
        return;// 不合法
      }

      let offset = this.state.progress;
      let startTime = this.startTime / 1000;
      let endTime = this.endTime / 1000;
      if (this.state.duration / 1000 - offset <= 0) {
        // 快要到文件末尾了
        // offset = this.state.duration / 1000;
       this.endoffile();
       return;
      }
      this.offset = offset;

      // todo： 更新起始和终止时间 
      // todo
      this.sessionId = startTime;
      !this.destroyed && this.cameraRenderView && this.cameraRenderView.stopRender();// 重新开启播放之前 先暂停播放。
      !this.destroyed && this.cameraRenderView && this.cameraRenderView.startRender();// startBindVideo
      CameraPlayer.getInstance().startPlayBack(startTime, offset, endTime, this.state.speed)
        .then(() => {
          //
          this.setState(() => { return { showErrorView: false, showLoadingView: true, isPlaying: true, showPauseView: false }; }, () => {
            let position = (this.state.speed == 1 ? 0 : (this.state.speed == 4 ? 1 : 2));
            this.updateSpeed(position);
          });
        })
        .catch((err) => {
          !this.destroyed && this.cameraRenderView && this.cameraRenderView.stopRender();// 重新开启播放之前 先暂停播放。
          if (err == -1 || err == -8888) { // - 8888重置本地连接，然后开始重连。
            CameraPlayer.getInstance().resetConnectionState();
            this.queryNetworkJob();
            return;
          }

          this.setState({ pstate: 0, showLoadingView: false, showErrorView: true, errTextString: `${LocalizedStrings["camera_connect_error"]} ${err} ${LocalizedStrings["camera_connect_retry"]}` });// 已经渲染过  直接跳过去

          console.log(err);
        });

      this._updateTimeStr(this.startTime + this.state.progress * 1000);

    } else {
      // stop播放
      if (!this.destroyed) {
        this.setState({ isMute: CameraConfig.getUnitMute(), isPlaying: false, showPlayToolBar: true });
      }
      clearTimeout(this.showPlayToolBarTimer);
      // stop video
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_VIDEO_STOP, {}).then((retCode) => {
        console.log("video stop");
        console.log(retCode);
      }).catch((err) => console.log(err));

      // stop audio
      Service.miotcamera.sendP2PCommandToDevice(MISSCommand.MISS_CMD_AUDIO_STOP, {}).then((retCode) => {
        console.log("audio stop");
        console.log(retCode);
      }).catch((err) => console.log(err));
      !this.destroyed && this.cameraRenderView && this.cameraRenderView.stopAudioPlay();
      !this.destroyed && this.cameraRenderView && this.cameraRenderView.stopRender();
      CameraPlayer.getInstance().stopPlaybackTimestampInterval();


      // stop audio local resources

    }
  }

  _updateTimeStr(startTime) {
    let endTime = this.endTime;
    this.dateTime.setTime(startTime);
    let startMinutes = this.dateTime.getMinutes();
    let startSeconds = this.dateTime.getSeconds();
    let beginStr = `${startMinutes > 9 ? startMinutes : (`0${startMinutes}`)}:${startSeconds > 9 ? startSeconds : (`0${startSeconds}`)}`;
    this.dateTime.setTime(endTime);
    startMinutes = this.dateTime.getMinutes();
    startSeconds = this.dateTime.getSeconds();
    let endStr = `${startMinutes > 9 ? startMinutes : (`0${startMinutes}`)}:${startSeconds > 9 ? startSeconds : (`0${startSeconds}`)}`;
    this.setState({ startTimeStr: beginStr, endTimeStr: endStr });
  }

  updateSpeed(position) {
    let speed = 1;

    switch (position) {
      case 0:
        speed = 1;
        break;
      case 1:
        speed = 4;
        break;
      case 2:
        speed = 16;
        break;
      default:
        speed = 1;
        break;
    }

    this.setState({ speed: speed });
    this.selectedIndexArray = [position];
    if (!this.state.isPlaying) { // 如果没有播放，需要先把UI切换一下，后面播放的时候再走一遍吧
      if (speed != 1) {
        this._toggleAudio(true, false);
      } else {
        this._toggleAudio(CameraConfig.getUnitMute(), false);
      }
      return;
    }
    CameraPlayer.getInstance().changeSpeed(speed)
      .then(() => {
        if (speed == 1) {
          this._toggleAudio(CameraConfig.getUnitMute(), false);
        } else if (!this.state.isMute) {

          this._toggleAudio(true, false);
        }
        this.setState({ speed: speed });
      })
      .catch((err) => {
        !this.destroyed && Toast.fail("action_failed", err);
      });

  }

  _onVideoClick(e) {
    LogUtil.logOnAll(TAG, "sdcard player page onVideoClick");
    TrackUtil.reportClickEvent("Playback_VideoPlay_ClickNum");
    // if (!CameraPlayer.getInstance().isConnected()) {
    //   return;
    // }
    // if (this.state.showPauseView) {
    //   return;
    // }
    this.setState({
      showPlayToolBar: !this.state.showPlayToolBar
    });
    console.log("click video view");
  }
  // 倍数改变时的函数
  _onVideoScaleChanged(params) {
    let scale = params.nativeEvent?.scale;
    // 当返回有倍数时 清除定时器 并更新倍数 相当于防抖操作 一直触发事件就一直清空定时器
    if (scale) {
      clearTimeout(this.videoScaleTimer);
        
      this.videoScaleTimer = setTimeout(() => {
        console.log("tick"+ scale);
        this._updateScale(scale); // 更新倍数
      }, 0);  
    }
    // 进行节流操作 
    let endTime = Date.now();
    if ((endTime - this.startScaleTime) < 50) {
      console.log('_onVideoScaleChanged', scale)
      return;
    }
    this.startScaleTime = endTime;

    this._updateScale(scale);
  }

  _updateScale(scale) {
    if (scale) {
      scale = Number(scale);

      if (scale < 1) {
        scale = 1;
      }

      if (this.angleViewTimeout) {// 隔一段时间就需要隐藏
        clearTimeout(this.angleViewTimeout);
        this.angleViewTimeout = null;
      }

      this.angleViewTimeout = setTimeout(() => {
        this.setState({ showCameraAngleView: false, angleViewShowScale: false });
      }, 3000);
      if (!this.state.fullScreen) {
        this.videoPortraitScale = scale;// 保存竖屏下的videoScale
      }
      this.angleView?.setScale(scale);
      if (!this.state.showCameraAngleView) {
        this.setState(() => { return { showCameraAngleView: true, angleViewShowScale: true }}, () => {
          this.angleView?.setScale(scale);
          if (scale > 1 && this.state.showPlayToolBar) {
            this.setState({ showPlayToolBar: false });
          } else if (scale == 1 && !this.state.showPlayToolBar) {
            this.setState({ showPlayToolBar: true });
          }
        });
      }
      this.setState({ videoScale: scale, showCameraAngleView: true, angleViewShowScale: true, showPlayToolBar: scale > 1 ? false : true });
      if ((Date.now() - this.startScaleReportTime) > 1000) {
        this.startScaleReportTime = Date.now();
        if (scale == 1) {
          TrackUtil.reportClickEvent('camera_ZoomOutFull_Num');
        } else if (scale > this.tmpScale) {
          TrackUtil.reportClickEvent('camera_ZoomIn_Num');
        } else {
          TrackUtil.reportClickEvent('camera_ZoomOut_Num');
        }
      } else {
        this.tmpScale = scale;
      }
    }
  }
  
  // android返回键处理
  onBackHandler = () => {
    if (!this.isPageForeGround) {
      return false;
    }
    if (this.state.fullScreen) {
      this.toPortrait();
      return true;
    }

    if (this.state.isSelectMode) {
      this.onSelectAllChanged(false);
      this.setNavigation(false, false, false);
      this.setState({ isSelectMode: false });
      return true;
    } else {
      this._onPause();
      return false;// 不接管
    }
  }

  _onPressVideoFileView(index) {
    if (!this.state.isSelectMode) {

      let sdcardFile = this.state.sdcardFiles[index];
      if (sdcardFile != null) {
        this.startTime = sdcardFile.startTime;
        this.endTime = sdcardFile.endTime;
        this.setState((state) => {
          return {
            currentPlayingIndex: index,
            progress: 0,
            duration: sdcardFile.duration
          };
        }, () => { // 数据塞给listview了，渲染还需要时间吧，延迟一会再执行scroll
          this.isUserPause = false;// fix bug: chuangmi-7442
          this.queryNetworkJob();

        });
        CameraPlayer.getInstance().stopPlaybackTimestampInterval();// 停止抓取
      }
    } else {
      let sdcardFile = this.state.sdcardFiles[index];
      let selectedCount = 0;
      for (let file of this.state.sdcardFiles) {
        if (file.isSelected) {
          selectedCount++;
        }
      }
      if (selectedCount >= 50 && !sdcardFile.isSelected) { // 勾选没有选中的  超过了50 给出提示
        Toast.fail('max_select_noti');
        return;
      }
      sdcardFile.isSelected = !sdcardFile.isSelected;
      if (sdcardFile.isSelected) {
        selectedCount++;
      } else {
        selectedCount--;
      }
      if (selectedCount == 0) {
        this.onSelectAllChanged(false);
      } else if (selectedCount == this.state.sdcardFiles.length) {
        this.onSelectAllChanged(true);
      } else {
        this.setState({ sdcardFiles: this.state.sdcardFiles });// 刷新页面 状态不要保留在ui控件里
        this.selectCount = selectedCount;
        this.setState(() => { return { isSelectAll: false }; }, () => { 
          this.setNavigation(this.state.isSelectAll, this.state.isSelectMode, false);
        });
      }

    }
  }


  onSelectAllChanged(isSelectAll) {
    this.selectCount = isSelectAll ? this.state.sdcardFiles.length : 0;
    this.setNavigation(isSelectAll, this.state.isSelectMode, false);
    this.setState({ index: isSelectAll ? 0 : 1 });
    for (let timeItem of this.state.sdcardFiles) {
      timeItem.isSelected = isSelectAll ? true : false;
    }
    this.setState({ sdcardFiles: this.state.sdcardFiles, isSelectAll: isSelectAll });

  }

  _onGetData() {
    // 当前选中的是哪个日期
    this.updateSdcardFiles();
  }

  scrollToSelectItem() {
    let num = Math.floor(this.state.currentPlayingIndex / 3);
    let offset = num * 110;
    if (num !== 0) {
      offset += 12;
    }
    console.log("scrollToSelectItem",this.state.currentPlayingIndex,this.state.sdcardFiles.length,num,offset)

    this.flatListRef && this.flatListRef.scrollToOffset({ offset: offset, animated: false });
  };

  updateSdcardFiles() {
    if (this.tag == null || this.hour < 0) {
      this.setNavigation(false, false, true);// 禁用选择功能
      this.setState({ isEmpty: true, isSelectMode: false });
      return;
    }

    let timeItemDays = SdFileManager.getInstance().getTimeItemDays();
    if (timeItemDays == null || timeItemDays.length <= 0) {
      this.setNavigation(false, false, true);// 禁用选择功能
      this.setState({ isEmpty: true, isSelectMode: false });
      this.props.navigation.goBack();
      return;
    }
    let timeItemDay = null;
    for (let item of timeItemDays) {
      if (item.tag == this.tag) {
        timeItemDay = item;
        break;
      }
    }
    if (!this.state.isSelectMode) {
      this.onSelectAllChanged(false);
    }
    if (timeItemDay == null || timeItemDay.timeItemHourList == null || timeItemDay.timeItemHourList.length == 0) {
      this.setNavigation(false, false, true);// 禁用选择功能
      this.setState({ isEmpty: true, isSelectMode: false });
      this.props.navigation.goBack();
      return;
    }
    let hourTimeItem = null;

    for (let timeItem of timeItemDay.timeItemHourList) {
      if (timeItem.hour == this.hour) { // 搞到小时的那个东西 （防止删除了头部元素 timesamp值跟之前保存的timeStart值不一样了）
        hourTimeItem = timeItem;
        break;
      }
    }

    if (hourTimeItem == null || hourTimeItem.timeItemList == null || hourTimeItem.timeItemList.length == 0) {
      this.setNavigation(false, false, true);// 禁用选择功能
      this.setState({ isEmpty: true, isSelectMode: false });
      this.props.navigation.goBack();
      return;
    }

    let timeItemList = [];
    for (let j = 0; j < hourTimeItem.timeItemList.length; j++) {
      let timeItem = hourTimeItem.timeItemList[j];
      for (let timeItemLocal of this.state.sdcardFiles) { // 遍历所有的正在展示的内容
        if (timeItem.startTime === timeItemLocal.startTime) {
          timeItem.isSelected = timeItemLocal.isSelected;
          break;
        }
      }
      timeItemList.push(timeItem);
    }
    if (!this.state.isSelectMode) {
      this.onSelectAllChanged(false);// 去除全选
    }

    this.setNavigation(this.state.isSelectAll, this.state.isSelectMode, false);// 禁用选择功能
    // 倒序显示
    timeItemList.sort((a, b) => { return b.startTime - a.startTime; });
    this.setState({ sdcardFiles: timeItemList, isEmpty: false, isSelectMode: this.state.isSelectMode });// 这里是有数据的  所以不用管isEmpty了。
    // 开始下载图片

    let timestampList = [];

    for (let index = 0; index < timeItemList.length; index++) {
      let sdcardFile = timeItemList[index];
      let startTime = sdcardFile.startTime;
      if (this.startTime == startTime) {
        this.setState({ currentPlayingIndex: index, duration: sdcardFile.duration },() => {
          console.log("==================index",index,this.isFirst);
          if (this.isFirst) {
            this.timeout && clearTimeout(this.timeout);
            this.timeout = setTimeout(()=>{
              this.isFirst = false;
              this.scrollToSelectItem();
            },300);
          }
        });
        this.endTime = sdcardFile.endTime;
      }
      let path = SdFileManager.getInstance().getImageFilePath(startTime);
      if (path == null) {
        timestampList.push(startTime);
      }
    }
    if (timestampList.length > 0) {
      this.downloadThumbs(timestampList);
    }
  }

  async downloadThumbs(timestampList) {
    if (timestampList.length > 0) {
      let lastNotifyTime = Date.now();
      for (let i = 0; i < timestampList.length; i++) {
        try {
          await SdcardEventLoader.getInstance().getThumb({ imgStoreId: timestampList[i] });
          if (Date.now() - lastNotifyTime < 1000) {
            continue;
          }
          lastNotifyTime = Date.now();
          
          // if (i % 5 == 0) { // 下载成功 通知一下。
          this.onReceiveFile(timestampList[i]);
          // }
        } catch (err) {

        }
      }
      this.onReceiveFile(timestampList[0]);// 下载完毕后，通知刷新。
    }
  }

  onReceiveFile = (timestamp, status) => {
    // checkTimestampRange
    let files = this.state.sdcardFiles;
    if (files == null || files.length == 0) {
      return;
    }
    if (files[0].startTime <= timestamp && files[files.length - 1].startTime >= timestamp) { // 只有在这中间的才刷新
      this.setState({ index: (this.state.index > 100 ? 0 : this.state.index + 1) });// 刷新页面
    }
  }

  onPressSave() {
    let timeItems = [];
    for (let timeItem of this.state.sdcardFiles) { // 遍历所有的正在展示的内容
      if (timeItem.isSelected) {
        timeItems.push(timeItem);
      }
    }

    if (timeItems == null || timeItems.length == 0) {
      !this.destroyed && Toast.success("bottom_action_tip");
      return;
    }

    if (timeItems.length > 50) {
      Toast.fail("max_select_noti");
      return;
    }


    if (this.isSupportDownload) {
      if (!CameraConfig.isShowDownloadHint) {
        CameraConfig.isShowDownloadHint = true;
        Toast.success("playback_download_hint");
      }
      this._startDownload(timeItems);
      return;
    }

    for (let i = timeItems.length - 1; i >= 0; i--) {
      if (timeItems[i].save == 1) {
        timeItems.splice(i, 1);
      }
    }
    if (timeItems.length == 0) {
      !this.destroyed && Toast.success("bottom_save_tip");
      return;
    }

    this.isDelete = false;
    this.setState({ dialogVisible: true });

  }

  _startDownload(selectedTimeItems) {
    TrackUtil.reportClickEvent("Storage_MemoryCard_Download");
    this.setState((state) => {
      return {
        isSelectMode: false
      };
    }, () => { // 数据塞给listview了，渲染还需要时间吧，延迟一会再执行scroll
      this.onSelectAllChanged(false);
    });

    if (Platform.OS === "android") {
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this._realStartDownloadFiles(selectedTimeItems);
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            !this.destroyed && Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
          !this.destroyed && Toast.success("action_failed");
        });
    } else {
      // no ios's photos const use hardcode
      System.permission.request("photos").then((res) => {
        this._realStartDownloadFiles(selectedTimeItems);
      }).catch((error) => {
        !this.destroyed && this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
      });
    }

  }


  _realStartDownloadFiles(timeItems) {

    if (this.isSupportNewStorage) {
      let times = timeItems.map((item) => {
        let startTime = item.startTime;
        let localUrls = SdcardEventLoader.getInstance().getThumbUrlByImgStoreId(startTime);
        let path = localUrls.thumbUrl;
        let source = (path == null) ? null : (`${Host.file.storageBasePath}${path}`);
        return { duration: 60, fileId: startTime, mediaType: "sdcard", createTime: startTime, imgStoreUrl: source, videoUrl: localUrls.videoUrl, playCfg: { loader: SdcardEventLoader.getInstance() } };// 缩略图，下载路径，创建时间，fileId
      });
      this.setState({ showDownloadHint: true });
      setTimeout(() => {
        this.setState({ showDownloadHint: false });
      }, 5000);
      DldMgr.addDld(times, SdcardEventLoader.getInstance());
    } else {
      this.cancelableProgressDialog && this.cancelableProgressDialog.show();
      let times = timeItems.map((item) => {
        return item.startTime;
      });
      this.downloadingVideoTimestamps = times;
      this.shouldDownload = true;
      this.tryDownloadWithDirectWay();
    }
  }


  async tryDownloadWithDirectWay() {

    for (let i = 0; i < this.downloadingVideoTimestamps.length && this.shouldDownload; i++) {
      let timestamp = this.downloadingVideoTimestamps[i];
      try {
        await new Promise((resolve, reject) => {
          SdcardEventLoader.getInstance()
            .download({ fileId: timestamp }, null, {
              onDldProgress: (state) => {
                if (state == DldStatus.Complete) {
                  resolve();
                } else if (state == DldStatus.Err) {
                  reject();
                }
              }
            });
        });
        let { videoUrl } = SdcardEventLoader.getInstance().getThumbUrlByImgStoreId(timestamp);
        if (!this.shouldDownload) {
          break;
        }
        await AlbumHelper.saveToAlbum(videoUrl, true);

      } catch (err) {
        console.log(`download video and save error:${timestamp}`);
        !this.destroyed && Toast.fail("save_faild", err);
        this.cancelableProgressDialog.hide();
        this.shouldDownload = false;
        break;
      }

    }
    if (this.shouldDownload) {
      !this.destroyed && Toast.success("save_success");
      this.shouldDownload = false;
    }
    this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
  }

  setItemAfterDelete(indexes) {
    let length = this.state.sdcardFiles.length;
    let min = length;
    for (let index of indexes) {
      if (index < min) {
        min = index;
      }
    }
    let indexesSet = new Set(indexes);
    let lefts = [];
    for (let i = 0; i < length; i++) {
      if (!indexesSet.has(i)) {
        lefts.push(i);
      }
    }

    let target = -1;
    for (let index of lefts) {
      if (index > min) {
        target = index;
        break;
      }
    }
    if (target == -1) {
      for (let index of lefts) {
        if (index < min && index > target) {
          target = index;
        }
      }
    }
    if (target != -1 && indexesSet.has(this.state.currentPlayingIndex)) {
      this.startTime = this.state.sdcardFiles[target].startTime;
      this.endTime = this.state.sdcardFiles[target].endTime;
      this.setState({ duration: this.state.sdcardFiles[target].duration });
      // this.setState({ currentPlayingIndex: target });
    }

  }

  onConfirmDelete() {
    let timeItems = [];
    let indexes = [];
    for (let i = 0, len = this.state.sdcardFiles.length; i < len; i++) {
      let timeItem = this.state.sdcardFiles[i];
      if (timeItem.isSelected) {
        timeItems.push(timeItem);
        indexes.push(i);
      }
    }
    this.setItemAfterDelete(indexes);

    SdFileManager.getInstance().startDeleteFiles(timeItems)
      .then(() => {
        this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
        this._onGetData();
        this.offset = 0;
        this.setState({ progress: 0 });
        this.queryNetworkJob();

        this.setState((state) => {
          return {
            isSelectMode: false
          };
        }, () => { // 数据塞给listview了，渲染还需要时间吧，延迟一会再执行scroll

          this._onGetData();
          this.offset = 0;
          this.queryNetworkJob();
        });
        Toast.success("delete_success");
        this.isDelete = false;
      })
      .catch((err) => {
        this.cancelableProgressDialog && this.cancelableProgressDialog.hide();
        !this.destroyed && Toast.fail("delete_failed", err);
        this.isDelete = false;
      });

  }

  onPressDelete() {
    let timeItems = [];
    for (let timeItem of this.state.sdcardFiles) { // 遍历所有的正在展示的内容
      if (timeItem.isSelected) {
        timeItems.push(timeItem);
      }
    }
    if (timeItems == null || timeItems.length == 0) {
      !this.destroyed && Toast.success("bottom_action_tip");
      return;
    }

    if (timeItems.length > 50) {
      Toast.fail("max_select_noti");
      return;
    }
    this.isDelete = true;
    this.setState({ dialogVisible: true });
  }

  onConfirmSave() {
    let timeItems = [];
    for (let timeItem of this.state.sdcardFiles) { // 遍历所有的正在展示的内容
      if (timeItem.isSelected) {
        timeItems.push(timeItem);
      }
    }
    if (timeItems == null || timeItems.length == 0) {
      return;
    }
    for (let i = timeItems.length - 1; i >= 0; i--) {
      if (timeItems[i].save == 1) {
        timeItems.splice(i, 1);
      }
    }
    if (timeItems.length == 0) {
      return;
    }

    SdFileManager.getInstance().startSaveFiles(timeItems)
      .then(() => {
        this.setState((state) => {
          return {
            isSelectMode: false
          };
        }, () => { // 数据塞给listview了，渲染还需要时间吧，延迟一会再执行scroll
          this.onSelectAllChanged(false);
          this._onGetData();
        });
        !this.destroyed && Toast.success("save_success");
      })
      .catch((err) => {
        !this.destroyed && Toast.fail("save_faild", err);
      });

  }

  queryNetworkJob() {
    if (this.isUserPause) {
      return;// 用户主动暂停的
    }
    if (this.destroyed) {
      return;
    }

    // 检查是否在同一局域网
    this.checkLocalNetwork();

    CameraPlayer.getInstance().queryShouldPauseOn4G()
      .then(({ state, pauseOnCellular }) => {
        this.networkType = state;
        if (state === "CELLULAR" && pauseOnCellular && !this.ignoreDataWarning) { // 普通网络 && 数据流量提醒
          !this.destroyed && Toast.success("nowifi_pause", true);
          this._startPlay(false);
          return;
        }
        // 其他网络条件 走连接的步骤吧
        this._startConnect();// 开始连接
      })
      .catch((err) => { // 获取网络状态失败 也直接走开始连接的流程
        this._startConnect();// 开始连接
      });
  }

  checkLocalNetwork() {
    console.log("检查局域网状态");
    // 设置超时，防止 localPing 在非局域网内执行时间过长
    this.timeoutLocalPing && clearTimeout(this.timeoutLocalPing);
    this.timeoutLocalPing = setTimeout(() => {
      CameraConfig.isLocalNetwork = false;
      if (this.state.isInSameLAN) {
        this.setState({ 
          isInSameLAN: false,
          showNetworkDialog: true
        });
        // 非局域网弹框弹出时暂停播放
        this.isUserPause = true;
        this._startPlay(false);
      }
    }, 3000);
    
    Device.getDeviceWifi().localPing().then((response) => {
      console.log("localPing 结果:", response);
      this.timeoutLocalPing && clearTimeout(this.timeoutLocalPing);
      CameraConfig.isLocalNetwork = response;
      if (!response && this.isPageForeGround) {
        // 不在同一局域网，显示提示弹窗并暂停播放
        this.setState({ 
          isInSameLAN: false,
          showNetworkDialog: true
        });
        // 非局域网弹框弹出时暂停播放
        this.isUserPause = true;
        this._startPlay(false);
      } else if (response && !this.state.isInSameLAN) {
        // 恢复到同一局域网
        this.setState({
          isInSameLAN: true,
          showNetworkDialog: false
        });
        this.isUserPause = false;
        // 重新尝试连接
        this.queryNetworkJob();
      }
    }).catch(error => {
      console.log("localPing 错误:", error);
      if (this.state.isInSameLAN && this.isPageForeGround) {
        CameraConfig.isLocalNetwork = false;
        this.setState({
          isInSameLAN: false,
          showNetworkDialog: true
        });
        // 非局域网弹框弹出时暂停播放
        this.isUserPause = true;
        this._startPlay(false);
      }
      this.timeoutLocalPing && clearTimeout(this.timeoutLocalPing);
    });
  }

  _startConnect() {

    if (!this.props.navigation.isFocused()) { // 当前页面已经不在前台了
      this.setState({ showLoadingView: false });
      return;
    }

    if (!this.state.showLoadingView) { // 如果没有loading
      this.setState({ showLoadingView: true });
    }
    if (this.state.showPauseView) {
      this.setState({ showPauseView: false });
    }
    if (this.state.showErrorView) {
      this.setState({ showErrorView: false });
    }
    // 开始连接
    if (CameraPlayer.getInstance().isConnected()) {
      // 如果已经连接成功 直接发送video_start
      this._startPlay(true);
      return;
    }
    this.isTryConnecting = true;
    this.setState({ pstate: 0, error: 1 });// 发起重连，需要把本地状态清空
    CameraPlayer.getInstance().startConnect();
  }


  showLastImage() {
    this.props.navigation.navigate("AlbumPhotoViewPage", { preOri: this.state.fullScreen ? "landscape" : "portrait" });
  }

  showLastVideo() {
    this.props.navigation.navigate("AlbumVideoViewPage", { ipad: Host.isPad, preOri: this.state.fullScreen ? "landscape" : "portrait" });
  }

  _renderResolutionDialog() {
    return (
      <ChoiceDialog
        modalStyle={{ marginLeft: this.state.fullScreen ? 100 : 0, width: this.state.fullScreen ? (this._getWindowPortraitHeight() - 100 * 2) : "100%" }}
        visible={this.state.dialogVisibility}
        title={LocalizedStrings["ptzc_play_back_speed_selection_title"]}
        options={this.isCameraV1orV3 ? [
          { "title": "x1", accessibilityLabel: DescriptionConstants.rp_13 },
          { "title": "x2", accessibilityLabel: DescriptionConstants.rp_13.replace('1', 2) },
          { "title": "x3", accessibilityLabel: DescriptionConstants.rp_13.replace('1', 3) }
        ] : [
          { "title": "x1", accessibilityLabel: DescriptionConstants.rp_13 },
          { "title": "x4", accessibilityLabel: DescriptionConstants.rp_13.replace('1', 4) },
          { "title": CameraConfig.support8xSpeed(Device.model) ? "x8" : "x16", accessibilityLabel: CameraConfig.support8xSpeed(Device.model) ? DescriptionConstants.rp_13.replace('1', 8) : DescriptionConstants.rp_13.replace('1', 16) }
        ]}
        selectedIndexArray={this.selectedIndexArray}
        onDismiss={(_) => this.setState({ dialogVisibility: false })}
        onSelect={(result) => {
          this.selectedIndexArray = result;
          this.updateSpeed(result[0]);
          this.setState({ dialogVisibility: false });
        }}
      />
    );
  }
  _getWindowPortraitHeight() {
    let winWidth = Dimensions.get('window').width;
    let winHeight = Dimensions.get('window').height;
    let width = Math.min(winWidth, winHeight);
    let height = Math.max(winWidth, winHeight);
    return height;
  }
}

const styles = StyleSheet.create({

  container: {
    backgroundColor: "#ffffff",
    width: '100%',
    height: '100%',
    display: "flex",
    flexWrap: "nowrap"
  },

  main: {
    display: 'flex',
    flexDirection: 'column',
    flexWrap: 'nowrap',
    backgroundColor: 'white',
    height: "100%"
  },

  videoContainerNormal: {
    backgroundColor: 'black',
    width: kWindowWidth,
    height: Host.isAndroid ? 380 : 410,
    position: "relative"// absolute以第一个非static的父类变量作为定位标准
  },

  videoContainerFull: {
    backgroundColor: 'black',
    width: "100%",
    height: "100%",
    position: "relative"// absolute以第一个非static的父类变量作为定位标准
  },

  videoView: {
    position: "absolute",
    width: "100%",
    height: "100%",
    // marginTop:Platform.OS == "ios" ? 30 : 0
  },

  videoControl: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    height: 80
  },

  videoControlBar: {// 横向
    backgroundColor: '#FFF1',
    display: "flex",
    flexWrap: "nowrap",
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    paddingTop: 20
  },
  videoControlBarFull: {
    backgroundColor: '#FFF1',
    display: "flex",
    width: "100%",
    flexDirection: "row",
    justifyContent: "flex-end"
  },
  videoControlBarItem: {// 内容居中排列
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    flexGrow: 1
  },
  videoControlSeekbar: {
    flexGrow: 1,
    alignItems: "center"
  },

  videoControlBarItemImg: {
    width: 40,
    height: 40
  },

  landscapeCallViewLayout: {
    width: "100%",
    paddingBottom: 10,
    paddingTop: 20,
    position: "absolute",
    bottom: 0
  },
  landscapeCallViewLayoutImg: {
    display: "flex",
    margin: "auto",
    width: "100%",
    flexDirection: "row",
    justifyContent: "center"
    // textAlign:"center"
  },

  callViewLayout: {
    flexGrow: 1,
    width: "100%",
    flexDirection: "column",
    display: "flex",
    alignItems: "center",
    justifyContent: "center"
  },

  bottomLayout: {
    display: "flex",
    width: "100%",
    height: 60,
    flexDirection: "row",
    flexWrap: 'nowrap'
  },

  bottomLayoutItem: {
    flexGrow: 1,
    height: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center"
  },

  whiteText: {
    fontSize: 10,
    textAlign: "center",
    padding: 4,
    color: "#ffffff",
    borderColor: "#FFFFFFCC",
    borderRadius: 3,
    borderWidth: 1
  },
  snapShot: {
    position: "absolute",
    bottom: 40,
    left: 5,
    margin: 6,
    width: 100,
    height: 66,
    borderRadius: 5,
    zIndex: 10
  },
  snapShotFull: {
    position: "absolute",
    bottom: 84,
    left: 35,
    margin: 6,
    width: 100,
    height: 66,
    borderRadius: 5,
    zIndex: 10
  }
});
