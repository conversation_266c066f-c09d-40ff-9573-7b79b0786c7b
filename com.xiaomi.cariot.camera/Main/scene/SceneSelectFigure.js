import React from 'react';
import { Package } from 'miot';
import TitleBar from 'miot/ui/TitleBar';
import { ScrollView, View, Button, Platform, StyleSheet, Text, FlatList, TouchableOpacity, Image } from 'react-native';
import { Device } from 'miot/device';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { NavigationBar } from 'mhui-rn';
import Util from "../util2/Util";
import { CHUANGMI_AI2_AI_SCENE_SIID } from '../util/AlarmUtil';
export default class SceneSelectFigure extends React.Component {
  constructor(props, context) {
    super(props, context);
    this.state = {
      isLoading: false,
      figureInfos: []
    };
    // console.log("native传过来的参数为：", JSON.stringify(Package.entryInfo), Device.deviceID, Device.model);
  }

  render() {
    return (<View style={{ display: "flex", height: "100%", width: "100%", flex: 1, flexDirection: "column", backgroundColor: "#FFFFFF", alignItems: "center" }}>
      {this.state.figureInfos.length <= 0 ?
        <View style={{ display: "flex", height: "100%", width: "100%", flexDirection: "column", justifyContent: "center", alignItems: "center" }}>
          <Image
            style={{ marginTop: -100, width: 110, height: 110 }} source={Util.isDark() ? require("../../resources2/images/icon_ev_empty_w.png") : require("../../resources2/images/icon_ev_empty.png")} />

          <Text style={{ color: 'gray', textAlign: "center", paddingHorizontal: 40 }}> {LocalizedStrings.no_registered_face_tip} </Text>
        </View> 
        : <FlatList
          style={{ width: "100%" }}
          data = { this.state.figureInfos }
          renderItem = {(data) => this.renderItemView(data.item, data.index)}
          contentContainerStyle={[{ paddingBottom: 50, flexGrow: 1, paddingHorizontal: 12 }]}

          refreshing={this.state.isLoading}
          onRefresh={() => {
            this.loadData(); 
          }}></FlatList>}
    </View>);
  }

  renderItemView(item, index) {
    console.log(item, index);
    // {"figureId":"65461652547961088","figureInfo":"肥嘟嘟","coverFaceId":"65461216704201472","updateTime":1635420803495,"figureName":"肥嘟嘟"}
    return (
      <View style={{ display: "flex", flexDirection: "column" }}>
        <TouchableOpacity style={{ display: "flex", flexDirection: "column", flexGrow: 1 }}
          onLongPress={() => this.onItemLongClick(item) }
          onPress={() => { 
            this._save(item);
          }}>
          <Text style={{ padding: 10, fontSize: 18 }}>{item.figureName}</Text>
          <View style={{ backgroundColor: "#FFFFFF" }}>
            <View style={{ height: 0, borderTopWidth: StyleSheet.hairlineWidth, borderColor: '#bdbdbd', opacity: 0.7, margin: StyleSheet.hairlineWidth }} />
          </View>
        </TouchableOpacity>
      </View>
    );
  }

  loadData() {
    Util.getAllFigures().then((res) => {
      console.log("getAllFigures=====res=", res);
      if (res.code == 0) {
        this.setState({ figureInfos: res.data.figureInfos });
      }
    }).catch((err) => {
      console.log("getAllFigures=====err=", err);
    });
  }

  componentDidMount() {
    this.props.navigation.setParams({
      title: Device.name,
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { Package.exit(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        // this._getSetting();
      }
    );
    this.loadData();
  }

  // 保存数据
  _save(item) {
    // 回传数据给米家原生App
    // 如果是在扩展程序中创建if条件，分为属性上报触发自动化和事件上报触发自动化两种情况
    // 示例1：属性值的范围为[0,100],当属性值大于10的时候通过属性上报触发自动化，value赋值如下
    // Package.entryInfo.payload.value = {
    //   min: 10,
    //   max: 100
    // };
    // 示例2：事件中包含的属性值的范围为[0,100],当该属性值大于10的时候通过事件上报触发自动化，value赋值如下
    Package.entryInfo.payload.name = item.figureName;
    Package.entryInfo.payload.value = {
      sub_props: {
        attr: [{
          key: `prop.${ Device.model }.${ CHUANGMI_AI2_AI_SCENE_SIID }.1`, // miot.light.t0915 为设备model，3.4表示siid为3，piid为4的属性，也就是spec中event上报的参数
          value: item.figureId
        }],
        express: 0
      }
    };
    // 如果是在扩展程序中创建then执行动作，这个value值会透传给固件去进行解析并执行，示例如下
    // Package.entryInfo.payload.value = {
    //   text: this.state.text, // 此处text和type都是在假设固件需要的情况下传的，需要跟固件的同学先沟通需要传何值
    //   type: "PLAY_USER_TTS"
    // };
    console.log("传回native的参数为：", JSON.stringify(Package.entryInfo));
    Package.exit(Package.entryInfo);
  }

}