import React from 'react';
import { Package, Service } from 'miot';
import TitleBar from 'miot/ui/TitleBar';
import { ScrollView, View, Button, Platform, StyleSheet, Text, FlatList, TouchableOpacity, Image } from 'react-native';
import { Device } from 'miot/device';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { NavigationBar } from 'mhui-rn';
import Util from "../util2/Util";
import AlarmUtil, { CHUANGMI_AI2_AI_SCENE_SIID } from '../util/AlarmUtil';
import { Base64 } from '../util/Base64';
import AlarmUtilV2, { AIID_SCENE_CALL_USER, SIID_VOIP } from "../util/AlarmUtilV2";
import DeviceSettingUtil from "../util/DeviceSettingUtil";
export default class SceneSelectContact extends React.Component {
  constructor(props, context) {
    super(props, context);
    this.state = {
      isLoading: false,
      contactsData: []
    };
    // console.log("native传过来的参数为：", JSON.stringify(Package.entryInfo), Device.deviceID, Device.model);
  }

  render() {
    return (<View style={{ display: "flex", height: "100%", width: "100%", flex: 1, flexDirection: "column", backgroundColor: Util.isDark() ? "#xm000000" : "#FFFFFF", alignItems: "center" }}>
      {this.state.contactsData.length <= 0 ?
        <View style={{ display: "flex", height: "100%", width: "100%", flexDirection: "column", justifyContent: "center", alignItems: "center" }}>
          <Image
            style={{ marginTop: -100, height: 60, width: 92 }} source={require("../../Resources/Images/icon_share_no.webp")} />

          <Text style={{ color: 'gray', textAlign: "center", paddingHorizontal: 40 }}> {LocalizedStrings['no_contact']} </Text>
        </View>
        : <FlatList
          style={{ width: "100%" }}
          data = { this.state.contactsData }
          renderItem = {(data) => this.renderItemView(data.item, data.index)}
          contentContainerStyle={[{ paddingBottom: 50, flexGrow: 1, paddingHorizontal: 12 }]}

          refreshing={this.state.isLoading}
          onRefresh={() => {
            this.loadData(); 
          }}></FlatList>}
    </View>);
  }

  renderItemView(item, index) {
    console.log(item, index);
    return (
      <View style={{ display: "flex", flexDirection: "column" }}>
        <TouchableOpacity style={{ display: "flex", flexDirection: "column", flexGrow: 1 }}
          onPress={() => {
            this._save(item);
          }}>
          <Text style={{ padding: 10, fontSize: 18 }}>{ item.callName }</Text>
          <View style={{ backgroundColor: "#FFFFFF" }}>
            <View style={{ height: 0, borderTopWidth: StyleSheet.hairlineWidth, borderColor: '#bdbdbd', opacity: 0.7, margin: StyleSheet.hairlineWidth }} />
          </View>
        </TouchableOpacity>
      </View>
    );
  }

  loadData() {
    //
    DeviceSettingUtil.getDeviceSettingByKey(DeviceSettingUtil.clickCallSetting).then(res => {
      if (res.code == 0) {
        let settingsData = res.result.settings;
        if (settingsData && settingsData.call_setting) {
          let data = JSON.parse(settingsData.call_setting);
          this.callSettingData = JSON.parse(JSON.stringify(data));
          let stateProps = {};
          let contacts = [];
          Object.keys(data).forEach((key) => {
            if (key.indexOf('key') != -1) {
              let item = data[key];
              item.key = key;
              contacts.push(item);
            }
          });

          // 先去重
          let hash = {};
          contacts = contacts.reduce((item:any, next:any)=>{
            // hash[next['mijia']] ? '' : ((hash[next['mijia']] = true) && item.push(next));
            if (!hash[next['mijia']]) {
              hash[next['mijia']] = true;
              item.push(next)
            }
            return item;
          }, []);

          contacts.map((item, index) => {
            if (item.mijia == Service.account.ID) {
              contacts.unshift(contacts.splice(index, 1)[0]);
            }
          });

          stateProps.contactsData = contacts;
          this.setState(stateProps);
        }
      }
    }).catch(error => {
      console.log("======= error: ", error);
    });
  }

  componentDidMount() {
    this.props.navigation.setParams({
      title: Device.name,
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { Package.exit(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        // this._getSetting();
      }
    );
    this.loadData();
  }

  // 保存数据
  async _save(item) {
    let res = await AlarmUtilV2.getSpecAiidParams(SIID_VOIP, AIID_SCENE_CALL_USER);
    console.log("============",res);
    Package.entryInfo.payload.name = `${ item.callName }`;
    // Package.entryInfo.payload.value = { siid: 2, aiid: 6, in: [{ piid: 3, value: 80 }] };
    Package.entryInfo.payload.value = {
      did: Device.deviceID,
      siid: res[0]?.siid,
      aiid: res[0]?.aiid,
      // value: item.mijia
      in: [{ piid: 1, value: `${item.mijia}` }]
    };
    console.log("传回native的参数为：", JSON.stringify(Package.entryInfo));
    Package.exit(Package.entryInfo);
  }

}