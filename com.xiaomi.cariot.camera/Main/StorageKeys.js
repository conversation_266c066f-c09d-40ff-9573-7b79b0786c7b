import { Device, Host } from 'miot';

export class _StorageKeys {
  constructor() {
  }

  initValueIfNull() {
  }

  get _prifix() {
    return 'storagekeys_prifix_';
  }
  _set(key, value) {
    console.log('set 一个值', key, value)
    Host.storage.set(this._prifix + key, value);
  }
  _get(key) {
    // return Host.storage.get(this._prifix + key)
    return new Promise((resolve, reject) => {
      Host.storage.get(this._prifix + key).then((res) => {
        resolve(res);
      }).catch((e) => {
        console.log('请检查key', JSON.stringify(e));
        reject(e);
      });
    });
  }

  /**
     * @description 流量保护 是否开启 Bool
     */
  get IS_DATA_USAGEE_WARNING() { return this._get('is_data_usage_warning'); }
  set IS_DATA_USAGEE_WARNING(value) { this._set('is_data_usage_warning', value); }

  /**
     * @description 畸变矫正是否开启 Bool
     */
  get IS_LENS_DISTORTION_CORREECTION() { return this._get('is_lens_distortion_correction'); }
  set IS_LENS_DISTORTION_CORREECTION(value) { this._set('is_lens_distortion_correction', value); }

  /**
     * @description 是否有时间水印 Bool
     */
  get IS_WATERMARK_OPEN() { return this._get('is_watermark_open'); }
  set IS_WATERMARK_OPEN(value) { this._set('is_watermark_open', value); }

  /**
     * @description 镜头翻转 属否开启 Bool
     */
  get IS_IMAGE_FLIP() { return this._get('is_image_flip'); }
  set IS_IMAGE_FLIP(value) { this._set('is_image_flip', value); }

  get IS_SHOW_SDCARD_PAGE_ALL_STORAGE() { return this._get('show_all_storage_sdcard'); }
  set IS_SHOW_SDCARD_PAGE_ALL_STORAGE(value) { this._set('show_all_storage_sdcard', value); }

  /**
     * @description 镜头旋转 Int
     */
  get IMAGE_ROTATION() { return this._get('image_rotation'); }
  set IMAGE_ROTATION(value) { this._set('image_rotation', value); }

  /**
     * @description 截图的所有路径 array
     */
  get SNAPSHOT_PATHS() { return this._get('snapshot_paths'); }
  set SNAPSHOT_PATHS(value) { this._set('snapshot_paths', value); }

  /**
     * @description 视频罗略图路径 string
     */
  get THUMB_PATH() { return this._get('thumb_path'); }
  set THUMB_PATH(value) { this._set('thumb_path', value); }

  /**
    * @description 全景图路径 string
    */
  get PANORAMA_IMAGE_PATH() { return this._get('panorama_path'); }
  set PANORAMA_IMAGE_PATH(value) { this._set('panorama_path', value); }

  /**
    * @description 全景图参数 string
    */
  get PANORAMA_PARAM() { return this._get('panorama_param'); }
  set PANORAMA_PARAM(value) { this._set('panorama_param', value); }

  /**
    * @description 是否显示方向键 bool
    */
  get IS_SHOW_DIRECTION_VIEW() { return this._get('is_show_direction_view'); }
  set IS_SHOW_DIRECTION_VIEW(value) { this._set('is_show_direction_view', value); }

  /**
    * @description 是否显示过休眠对话框 bool
    */
  get SHOWN_SLEEP_DIALOG() { return this._get('show_sleep_dialog'); }
  set SHOWN_SLEEP_DIALOG(value) { this._set('show_sleep_dialog', value); }

  /**
    * @description 是否显示过看家助手
    */
  get IS_ALARM_GUIDE_SHOWN() { return this._get('is_alarm_guide_shown'); }
  set IS_ALARM_GUIDE_SHOWN(value) { this._set('is_alarm_guide_shown', value); }

  /**
     * @description 是否是vip
     */
  get IS_VIP_STATUS() { return this._get('is_vip_status'); }
  set IS_VIP_STATUS(value) { this._set('is_vip_status', value); }
  /**
     * @是否显示看家弹窗
     */
  get IS_WATCH_HOUST() { return this._get("is_watch_houst"); }
  set IS_WATCH_HOUST(value) { this._set("is_watch_houst", value); }
  /**
     * @是否显示看家弹窗
     */
  get IS_FLOAT_BAR() { return this._get("floatBar"); }
  set IS_FLOAT_BAR(value) { this._set("floatBar", value); }
  /**
   * @description 是否在云存过期窗口期内
   */
  get IN_CLOSE_WINDOW() { return this._get("is_in_close_window"); }
  set IN_CLOSE_WINDOW(value) { this._set("is_in_close_window", value); }

  /**
   * @description 是否在云存过期窗口期内
   */
  get HIDE_CLOUD_BUY_TIP() { return this._get("hide_cloud_buy_tip"); }
  set HIDE_CLOUD_BUY_TIP(value) { this._set("hide_cloud_buy_tip", value); }
  /**
   * @description 是否第一次显示预置位
   */
  get NEED_SHOW_PRE_POSITION_DIALOG() { return this._get("NEED_SHOW_PRE_POSITION_DIALOG"); }
  set NEED_SHOW_PRE_POSITION_DIALOG(value) { this._set("NEED_SHOW_PRE_POSITION_DIALOG", value); }

  /**
     * @description 是否power on
     */
  get IS_POWER_ON() { return this._get('is_power_on'); }
  set IS_POWER_ON(value) { this._set('is_power_on', value); }

  /**
     * @description 存储首页视频清晰度的 默认是0 自动
     */
  get LIVE_VIDEO_RESOLUTION() { return this._get('live_video_resolution'); }
  set LIVE_VIDEO_RESOLUTION(value) { this._set('live_video_resolution', value); }

  /**
     * @是否弹过隐私弹框
     */
  get IS_PRIVACY_NEEDED() { return this._get("privacy_needed"); }
  set IS_PRIVACY_NEEDED(value) { this._set("privacy_needed", value); }

  /**
   * @是否弹过人脸个保法弹窗
   */
  get IS_FACE_PRIVACY_GBF_NEEDED() { return this._get("face_privacy_gbf_needed"); }
  set IS_FACE_PRIVACY_GBF_NEEDED(value) { this._set("face_privacy_gbf_needed", value); }

  /**
     * @是否宽动态范围模式
     */
  get WDR_MODE() { return this._get("wdr-mode"); }
  set WDR_MODE(value) { this._set("wdr-mode", value); }

  /**
    * @记录上次退出时的视频缩放比例
    */
  get VIDEO_SCALE() { return this._get("video_scale"); }
  set VIDEO_SCALE(value) { this._set("video_scale", value); }

  get IS_SHOW_SDCARD_PAGE() { return this._get("show_sd_card_page"); }
  set IS_SHOW_SDCARD_PAGE(value) { this._set("show_sd_card_page", value); }

  get IS_PTZ_ROTATION_ENABLE() { return this._get("ptz_rotation_enable"); }
  set IS_PTZ_ROTATION_ENABLE(value) { this._set("ptz_rotation_enable", value); }

  get OPERATION_BANNER_ITEM() { return this._get("operation_banner_item"); }
  set OPERATION_BANNER_ITEM(value) { this._set("operation_banner_item", value); }

  get OPERATION_CLICKED_KEY() { return this._get("operation_clicked_key"); }
  set OPERATION_CLICKED_KEY(value) { this._set("operation_clicked_key", value); }

  /**
   * @description 是否是vip
   */
  get VIP_DETAIL() { return this._get('vip_detail'); }
  set VIP_DETAIL(value) { this._set('vip_detail', value); }

  /**
   * @下载列表
   */
  get PENDING_DLD() { return this._get("pending_dld"); }
  set PENDING_DLD(value) { this._set("pending_dld", value); }

  /**
    * @是否显示过定向推送
    */
  get IS_NIGHT_MODE_PUSH_SHOWN() { return this._get("is_night_mode_push_shown"); }
  set IS_NIGHT_MODE_PUSH_SHOWN(value) { this._set("is_night_mode_push_shown", value); }

  get IS_TARGET_PUSH_SHOWN() { return this._get("is_target_push_shown"); }
  set IS_TARGET_PUSH_SHOWN(value) { this._set("is_target_push_shown", value); }

  get CURRENT_RECORD_MODE() { return this._get("current_record_mode"); }
  set CURRENT_RECORD_MODE(value) { this._set("current_record_mode", value); }

  /**
   * 强制密码弹框需求相关。
   */
  get IS_PINCODE_SETTING_FORCE() { return this._get("is_pincode_setting_force"); }
  set IS_PINCODE_SETTING_FORCE(value) { this._set("is_pincode_setting_force", value); }


  /**
   * @是否显示过访问记录弹窗
   */
  get IS_VISIT_PUSH_SHOWN() { return this._get('is_visit_push_shown') }
  set IS_VISIT_PUSH_SHOWN(value) { this._set('is_visit_push_shown', value) }


  /**
  * @是否显示过设置安全密码推送
  */
  get IS_SECURITY_CODE_TIP_SHOWN() { return this._get("is_security_code_tip_shown"); }
  set IS_SECURITY_CODE_TIP_SHOWN(value) { this._set("is_security_code_tip_shown", value); }

  /** 
    * @是否打开AI画框
    */
  get IS_AI_FRAME_OPEN() { return this._get("is_ai_frame_open"); }
  set IS_AI_FRAME_OPEN(value) { this._set("is_ai_frame_open", value); }

  /** 
      * @是否使用人脸管理
      */
  get IS_AI_FACE_OPEN() { return this._get("is_ai_face_open"); }
  set IS_AI_FACE_OPEN(value) { this._set("is_ai_face_open", value); }

  /** 
      * @是否显示过人脸管理弹窗
      */
  get IS_AI_FACE_OPEN_TOAST() { return this._get("is_ai_face_open_toast"); }
  set IS_AI_FACE_OPEN_TOAST(value) { this._set("is_ai_face_open_toast", value); }

  /**
   * 上次选择的全景绘制角度
   */
  get LAST_PANO_ANGLE() {
    return this._get("last_pano_angle");
  }
  set LAST_PANO_ANGLE(value) {
    this._set("last_pano_angle", value);
  }

  /**
   * @是否打开自动化模式
   */
  get AUTOMATIC_MODE() {
    return this._get("automatic_mode");
  }
  set AUTOMATIC_MODE(value) {
    this._set("automatic_mode", value);
  }
  /**
   * @SD卡容量过小弹窗
   */
  get SDCARD_SMALL_CAPACITY() { return this._get('SDCARD_SMALL_CAPACITY'); }
  set SDCARD_SMALL_CAPACITY(value) { this._set('SDCARD_SMALL_CAPACITY', value); }
  /**
   * @S格式化弹窗弹窗
   */
  get SDCARD_FORMAT_DIALOG_3() { return this._get('SDCARD_FORMAT_DIALOG_3'); }
  set SDCARD_FORMAT_DIALOG_3(value) { this._set('SDCARD_FORMAT_DIALOG_3', value); }
  /**
   * @S格式化弹窗弹窗
   */
  get SDCARD_FORMAT_DIALOG_8() { return this._get('SDCARD_FORMAT_DIALOG_8'); }
  set SDCARD_FORMAT_DIALOG_8(value) { this._set('SDCARD_FORMAT_DIALOG_8', value); }
  /**
   * @S格式化弹窗弹窗
   */
  get SDCARD_FORMAT_DIALOG_10() { return this._get('SDCARD_FORMAT_DIALOG_10'); }
  set SDCARD_FORMAT_DIALOG_10(value) { this._set('SDCARD_FORMAT_DIALOG_10', value); }
  /**
   * @S格式化弹窗弹窗
   */
  get SDCARD_FORMAT_DIALOG_11() { return this._get('SDCARD_FORMAT_DIALOG_11'); }
  set SDCARD_FORMAT_DIALOG_11(value) { this._set('SDCARD_FORMAT_DIALOG_11', value); }
  /**
   * @SD卡可用空间不足弹窗
   */
  get SDCARD_FULL_DIALOG() { return this._get('SDCARD_FULL_DIALOG'); }
  set SDCARD_FULL_DIALOG(value) { this._set('SDCARD_FULL_DIALOG', value); }

  /**
   * @微信VOIP license有效期，单位s
   */
  get WX_LICENSE() { return this._get('WX_LICENSE'); }
  set WX_LICENSE(value) { this._set('WX_LICENSE', value); }

  /**
   * @微信授权提醒框是否弹出过
   */
  get WX_WARNING_DIALOG() { return this._get(`WX_WARNING_DIALOG_${Device.deviceID}`); }
  set WX_WARNING_DIALOG(value) { this._set(`WX_WARNING_DIALOG_${Device.deviceID}`, value); }

  /**
   * @微信授权提醒框是否去授权过
   */
  get WX_WARNING_LIST_DIALOG() { return this._get('WX_WARNING_LIST_DIALOG'); }
  set WX_WARNING_LIST_DIALOG(value) { this._set('WX_WARNING_LIST_DIALOG', value); }

  /**
   * @音视频通话提示条
   */
  get WX_EMPOWER_TIP() { return this._get('WX_EMPOWER_TIP'); }
  set WX_EMPOWER_TIP(value) { this._set('WX_EMPOWER_TIP', value); }

  /**
   * @音视频通话提示条
   */
  get IS_LIVE_ALARM_GUIDE_SHOWN() { return this._get('IS_LIVE_ALARM_GUIDE_SHOWN'); }
  set IS_LIVE_ALARM_GUIDE_SHOWN(value) { this._set('IS_LIVE_ALARM_GUIDE_SHOWN', value); }

  /**
   * @微信通话服务提醒
   */
  get WX_SERVER_SHOWN() { return this._get('WX_SERVER_SHOWN'); }
  set WX_SERVER_SHOWN(value) { this._set('WX_SERVER_SHOWN', value); }

  /**
   * @本地存储开关状态
   */
  get CAR_STORAGE() { return this._get('CAR_STORAGE'); }
  set CAR_STORAGE(value) { this._set('CAR_STORAGE', value); }

  /**
   * @记忆用户行为
   */
  get CAR_TO_SHOW_CLOUD() { return this._get('CAR_TO_SHOW_CLOUD'); }
  set CAR_TO_SHOW_CLOUD(value) { this._set('CAR_TO_SHOW_CLOUD', value); }


}

const StorageKeys = new _StorageKeys();
export default StorageKeys;
