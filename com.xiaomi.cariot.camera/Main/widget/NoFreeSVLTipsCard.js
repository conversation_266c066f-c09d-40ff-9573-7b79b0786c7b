import React from 'react';
import { StyleSheet, View, Text, Image, TouchableOpacity } from 'react-native';
import { Device, Service } from 'miot';
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import CameraConfig from '../util/CameraConfig';
import StorageKeys from '../StorageKeys';
import { BaseStyles } from '../BasePage';
import dayjs from 'dayjs';
import { DarkMode } from "miot/Device";
import Util from '../util2/Util';

const TAG = "NoFreeSVLTipsCard";
export default class NoFreeSVLTipsCard extends React.Component {
  _renderCloudBannerViewDetial() { 

    let isPostNoFreeSVL = CameraConfig.isPostNoFreeSVL(); // 到这里来的都是合法的no post free svl 分子，只有无免费过渡期期间和之后之分
    let noFreeSVLWillEndDays = 0;
    if (!isPostNoFreeSVL) {
      noFreeSVLWillEndDays = dayjs(CameraConfig.noFreeSVLCfg().endTime).diff(dayjs(new Date().getTime()), 'day') + 1;
      if (noFreeSVLWillEndDays > 30) {
        return null; // 从结束前30天开始显示提示
      }
    }

    let isDark = DarkMode.getColorScheme() == "dark";

    let cloudBannerRenewStyle = {
      display: "flex",
      flexDirection: "row",
      position: "relative",
      height: 62,
      marginHorizontal: 0,
      marginBottom: 10,
      borderRadius: 12,
      backgroundColor: isDark ? "#DB8E0D32" : "#F5A62319"
    };

    let renewVipTextStyle = [BaseStyles.text13, {
      width: "80%",
      left: 20,
      color: isDark ? "#DB8E0D" : "#F5A623",
      fontWeight: "400",
      textAlignVertical: 'center'
    }];

    let tipStr = CameraConfig.isIndiaServer ? LocalizedStrings["post_nofreesvl_tips_in"] : LocalizedStrings["post_nofreesvl_tips"];
    if (noFreeSVLWillEndDays > 0) {
      tipStr = CameraConfig.isIndiaServer ? Util.fmtStr(LocalizedStrings["pre_nofreesvl_tips_in"], noFreeSVLWillEndDays) : Util.fmtStr(LocalizedStrings["pre_nofreesvl_tips"], noFreeSVLWillEndDays);
    }
    let iconSize = 26;
    let iconStyle = { position: "absolute", width: 50, height: "100%", display: "flex", justifyContent: "center", alignContent: "center", right: 0 };
    let iconSrc = isPostNoFreeSVL ? 
      (isDark ? require('../../Resources/Images/nofree_svl_tip_close_dark.png') : require('../../Resources/Images/nofree_svl_tip_close.png')) 
      :
      (isDark ? require('../../Resources/Images/coud_banner_player_tip_inwindow_dark.png') : require('../../Resources/Images/coud_banner_player_tip_inwindow.png'));

    return (
      <View style={cloudBannerRenewStyle}>

        <TouchableOpacity
          activeOpacity={1.0}
          style={{ display: "flex", width: "100%", height: "100%", alignContent: "center", justifyContent: "center" }}
          onPress={() => {
            this.showExpireTips();
          }}>

          <Text style={renewVipTextStyle}>
            {tipStr}
          </Text> 

          <TouchableOpacity
            style={iconStyle}
            activeOpacity={0.5}
            onPress={() => {
              if (isPostNoFreeSVL) {
                let item = this.props.item;
                if (item) {
                  item.closeNoFreeSVLTip = true;
                  this.props?.cardPressed && this.props.cardPressed(item);
                }
              } else {
                this.showExpireTips();
              }
            }}
          >
            <Image style={{ width: iconSize, height: iconSize }}
              source={ iconSrc }
            >
            </Image>
          </TouchableOpacity>

        </TouchableOpacity>

      </View>
    );
  }

  render() {
    return this._renderCloudBannerViewDetial();
  }

  showExpireTips() {
    let h5 = CameraConfig.noFreeSVLDetailLink();
    if (h5) {
      Service.miotcamera.showCloudStorage(true, true, Device.deviceID, h5, true);
    }
  }
}

const styles = StyleSheet.create({
  timeView: {
    flexDirection: 'row',
    alignItems: 'flex-start'
  },
  pressedTimeLabel: {
    color: 'gray'
  },
  imgView: {
    width: 106,
    height: 64,
    borderRadius: 9
  }
});
