import React from 'react';
import { StyleSheet, View, Text, FlatList, Image, Platform } from "react-native";
import {  Device, Service } from 'miot';
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import EventCardV3 , { CardHeight, CardMB } from "./EventCardV3";
import { BaseStyles } from "../BasePage";
import PropTypes from 'prop-types';
import { DldStatus, Order } from "../framework/EventLoaderInf";
import { CldDldTypes } from "../framework/CloudEventLoader";
import Util from "../util2/Util"
import { DescriptionConstants } from '../Constants';
export const LoadStatus = {
  Idle: "Idel",
  Finish: "Finish",
  Failed: "Failed",
  Loading: "Loading"
};
const TAG = "EventListV3";
export const DefFilter = "Default";
const ListFooterH = Platform.OS == "ios" ? 20 : 10;

export const ChangeType = {
  Delete: "Delete",
  Modify: "Modify"
};

export function buildChange(aType, ...args) {
  switch (aType) {
    case ChangeType.Delete:
      return {
        action: "removeEvents",
        args: [(aEv) => {
          console.log(TAG, "action removeEvents  check ", aEv.fileId, "vs", args[0], "ret", args[0].indexOf(aEv.fileId) < 0);
          return args[0].indexOf(aEv.fileId) < 0;
        }]
      };
    case ChangeType.Modify:
      return { action: "applyAction2Events", args: args };
    default:
      return null;
  }
}

export function applyChange(aLst, actions) {
  let actFilter = [];
  for (let act of actions) {
    if (act.id != null) {
      if (actFilter.indexOf(act.id) < 0) {
        aLst[act.action](...act.args);
      }
      actFilter.push(act.id);
    } else {
      aLst[act.action](...act.args);
    }
  }
}

export default class EventListV3 extends React.Component {
  static propTypes = {
    onEventPress: PropTypes.func,
    onEventLongPress: PropTypes.func,
    morePressed: PropTypes.func,
    loaderArgs: PropTypes.object
  }
  static defaultProps = {
    eventHeaderView: () => { return null; },
    eventHeaderHeight: 0
  }
  constructor(aProps) {
    super(aProps);
    let ev = this.props.events || [];
    if (this.props.dataFilter) {
      ev = this.applyFilter(ev, this.props.dataFilter);
    }
    // let initIdx = this.getInitScroll();

    let startDate = null;
    let nextDate = null;
    startDate = this.props.loaderArgs.startDate;
    nextDate = this.props.loaderArgs.nextDate;
    let sdcardCode = this.props.loaderArgs.sdcardCode;
    if (startDate) {
      this.bgn = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate(), 0, 0, 0);
      this.end = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate(), 23, 59, 59);
    }

    let loadingStatus = LoadStatus.Idle;
    if (ev != null && ev.length > 0 && null == nextDate) {
      loadingStatus = LoadStatus.Finish;
    }
    this.state = {
      loadingStatus,
      nextDate,
      startDate,
      events: ev,
      isP2pLostEmpty: false,
      sdcardCode
    };
    console.log(TAG, "init with state", loadingStatus, ev ? ev.length : 0, nextDate, this.props.loader.constructor.name);
    this.mLoader = this.props.loader;
    this.mActive = false;
    this.mLst = null;
    this.mTopItmIdx = -1;
    this.sltDay = this.props.loaderArgs.sltDay;
    this.localSelectDays = false;
    this.mSltDayMore = false;
    this.mAutoRefresh = this.props.autoRefresh == null ? true : this.props.autoRefresh;
    this.toFindTime = 0;
    this.toFindNextData = true;
    console.log(TAG, "set sltDay: ", this.props.loaderArgs.sltDay);
  }

  getInitScroll() {
    let initIdx = 0;
    let ev = this.state.events;
    if (this.playingId) {
      let stop = false;
      for (let i = 0; i < ev.length && !stop; ++i) {
        let event = ev[i];
        if (`${ event.fileId }-${ event.offset }` === this.playingId) {
          console.log("======i",i,`${ event.fileId }-${ event.offset }`,this.playingId)
          return { animated: false, index: i, viewOffset: 100 };
        }
      }
    }
    return null;
  }

  componentDidMount() {
    console.log(TAG, "componentDidMount");
    this.mActive = true;
    this.mListener = this.mLoader.addListener(() => { this.mAutoRefresh && this.mRefresh(); });
    if (0 === this.state.events.length) {
      this.mRefresh();
    } else {
      // need a delay to do the scroll
      setTimeout(() => {
        let scroll = this.getInitScroll();
        console.log(TAG, "scroll", scroll);
        if (this.mActive && scroll != null) {
          this.scrollTo(scroll);
        }
      }, 100);
    }
  }

  _getPlayingOffset(time, ignoreScroll = false, focusScroll = false) {
    if (!time || time > this.end || time < this.bgn || !this.state.events || this.state.events?.length <= 0) {
      return;
    }

    let lastIndex = this.state.events.length - 1;
    console.log("_getPlayingOffset time ==", time,this.state.events[lastIndex].createTime);
    if (time < this.state.events[lastIndex].createTime && this.state.loadingStatus !== LoadStatus.Finish) {
      // 当前列表中不存在，拉取下一页数据,已经拉取到最后一页，还是没有不能继续拉取
      this.toFindTime = time;
      this.toFindNextData = true;
      this.mOnEnd();
      return;
    }
    this.toFindNextData = false;
    let item = null;
    for (let evt in this.state.events) {
      let eventTime = this.state.events[evt].createTime;
      if (time >= eventTime) {
        item = this.state.events[evt];
        break;
      }
    }
    if (item == null) {
      return;
    }
    console.log("_getPlayingOffset this.playingId ==", this.playingId,`${ item.fileId }-${ item.offset }`);
    // if (this.playingId != `${ item.fileId }-${ item.offset }`) {
    //   this.playingId = `${ item.fileId }-${ item.offset }`;
    if (this.props.playingId != `${ item.fileId }-${ item.offset }` || focusScroll) {
      this.playingId = `${ item.fileId }-${ item.offset }`;

      console.log("_getPlayingOffset==", this.playingId);
      if (this.props.onInitDataDone) {
        this.props.onInitDataDone(item, ignoreScroll);
      }
    }
  }

  _scrollToPlayingId() {
    this.forceUpdate();
    let scroll = this.getInitScroll();
    console.log(TAG, "scroll", scroll);
    if (this.mActive && scroll != null) {
      this.scrollTo(scroll);
    }
  }

  getAdjacentEventForMarkListitem(aEv) {
    if (this.state.events && this.state.events.length > 0 && this.state.events[0].hasOwnProperty("data")) {
      for (let j = 0; j < this.state.events.length; j++) {
        let events = this.state.events[j].data;
        let curOffset = aEv.offset;
        let fileId = aEv.fileId;
        for (let i = events.length - 1; i > -1; --i) {
          if (fileId == events[i].fileId && events[i].offset > curOffset) {
            return events[i];
          }
        }
      }
    }
    return null;
  }
  _playItemChange(item) {
    if (!item) {
      return;
    }

    if (this.playingId != `${ item.fileId }-${ item.offset }`) {
      this.playingId = `${ item.fileId }-${ item.offset }`;
      this.forceUpdate();
      let scroll = this.getInitScroll();
      console.log(TAG, "scroll", scroll);
      if (this.mActive && scroll != null) {
        this.scrollTo(scroll);
      }
      console.log("_getPlayingOffset==", this.playingId);
    }
  }

  componentWillUnmount() {
    this.mActive = false;
    this.mListener.remove();
    this.setState = () => false;
  }


  scrollTo(aLoc) {
    console.log(TAG, "scrollTo", this.constructor.name);
    if (this.mLst) {
      this.mLst.scrollToIndex(aLoc);
    }
  }


  async getData(date, event, isMore = false, aOrder = Order.Desc, type = this.props.type) {

    console.log("landing4");
    let data = await this.mLoader.getEventList(date, event, isMore, type);
    return data;
  }

  appendEvents(aOldEv, aNewEv, aOrder = Order.Desc) {
    if (Order.Desc == aOrder) {
      return aOldEv.concat(aNewEv);
    } else {
      return aNewEv.concat(aOldEv);
    }

  }

  updateAllItems() {
    console.log(this.tag, 'reload all items');
    // 清空数据，再去拉取，会先显示无数据页面，这里不在清空，直接拉取(后续会进行数据整理)
    this.mRefresh();
  }

  async getEventList(date, event, isMore = false, aOrder = Order.Desc) {
    let now = Date.now();
    let reNonce = `${now}${Math.random(now)}`;
    this.mReNonce = reNonce;
    let events = this.state.events;
    // console.log("events1111111111111111111111111111111111:",this.state.events);
    if (!isMore) {
      events = [];
    }
    let loadingStatus = this.state.loadingStatus;
    let nextDate = this.state.nextTime;// ????? this.state.nextState
    let keepRet = false;
    let status = DldStatus.Complete;
    try {

      console.log("landing2");

      let data = await this.getData(date, event, isMore, aOrder);
      Service.smarthome.reportLog(Device.model, "getEventListData:" + data);
      // console.log("data:", data);
      console.log("landing3");
      console.log(TAG, "getData ", date, "with", event, "datas", data.items ? data.items.length : 0, "nextDate", data.nextTime);
      keepRet = this.mReNonce === reNonce || !isMore;
      
      if (keepRet) {
        nextDate = data.nextTime;
        loadingStatus = data.hasMore ? LoadStatus.Idel : LoadStatus.Finish;
        if (data.items && data.items.length > 0) {
          console.log("landing301");
          events = this.appendEvents(events, data.items, aOrder);
          console.log("landing302");
          this.downloadFileThump(data.items);
        } else {
          loadingStatus = LoadStatus.Finish;
          status = data.status;
        }
      } else {
        console.log(TAG, "drop ret");
      }
    } catch (err) {
      console.log(TAG, "got error", err);
      keepRet = this.mReNonce === reNonce || !isMore;
      loadingStatus = LoadStatus.Failed;
    } finally {
      if (keepRet) {
        this.setState({
          loadingStatus,
          events,
          nextDate,
          isP2pLostEmpty: status == DldStatus.p2pLost
        }, () => {
          if (this.props.onGetDataDone) {
            let dates = [];
            // console.log("[[[[[[[[",events)
            for (let evt in events) {
              dates.push(events[evt]['date']);
            }
            this.props.onGetDataDone(events.length, events, dates);
          }
          console.log("=========",keepRet)
          if (this.toFindNextData && this.toFindTime > 0) {
            console.log("=========findNextData", this.toFindNextData, this.toFindTime);
            this._getPlayingOffset(this.toFindTime);
          }
        });
        
      }
      console.log(TAG, "getEventList finish with keepRet", keepRet, "nextDate", nextDate);
      // this._getPlayingOffset();
    }
    return keepRet;
  }



  async downloadFileThump(items) {
    // load from new to old
    // 如果图片返回太快，就慢一些刷新，避免频繁刷新导致的UI卡顿问题，如果
    let lastNotifyTime = Date.now();
    for (let i = 0; i < items.length && this.mActive; ++i) {
      try {
        let item = items[i];
        // console.log(this.tag, "get thumb", item.createTime, i, item);
        item.imgStoreUrl = await this.mLoader.getThumb(item);
        // 3 thumb per refresh
        if (Date.now() - lastNotifyTime < 1000) {//距离上次全局刷新，不超过1s,return
          continue;
        }
        lastNotifyTime = Date.now();
        // if (i % 4 == 3 || i == items.length - 1) {
          this.setState({});
        // }
      } catch (err) {
        console.log(this.tag, "getthumb", err);
      }
    }
    this.setState({});
  }

  onTopItemChange(aOldIdx, aNewIdx) {

  }


  render() {
    if (this.props.isFullScreen) {
      return null;
    }
    // console.log(TAG, "render loadingStatus", this.state.loadingStatus);
    let hvf = this.props.eventHeaderView;
    let hh = hvf != null ? this.props.eventHeaderHeight : 1;
    let cardH = CardHeight + CardMB;
    // console.log(TAG, "cardH hh", cardH, hh);
    // console.log("landing9");
    return (
      <FlatList
        data={this.state.events}
        style={this.props.style}
        ref={(ref) => { this.mLst = ref; }}
        contentContainerStyle={[this.props.contentContainerStyle, this.props.loadingSkeletonStatus ? {flexGrow: 1} : { paddingBottom: 60, flexGrow: 1 }]}
        showsVerticalScrollIndicator={false}
        renderItem={
          ({ item, index }) => {
            if (this.props.evCard) {
              return this.props.evCard(item);
            } else {
              return (
                // <View>
                <EventCardV3
                  style={styles.item} item={item}
                  isPlaying={this.props.playingId === `${ item.fileId }-${ item.offset }`}
                  sdcardCode={this.state.sdcardCode}
                  morePressed={(aItem) => {
                    this.props.morePressed(aItem);
                  }}
                  cardPressed={(aItm) => {

                    // this.naviTo('AlarmDetail', { item: item, event: this.state.selectedEventKey });
                    item.isRead = true;
                    let events = this.state.events;
                    let nextDate = this.state.nextDate;
                    this.props.onEventPress(aItm, { events, nextDate });
                  }}
                  cardLongPressed={(aItm)=>{
                    let events = this.state.events;
                    let nextDate = this.state.nextDate;
                    this.props.onEventLongPress(aItm,{events,nextDate})
                  }}/>
                // <Text style={{position:"absolute", left:0, top:0, fontSize:20, color:"red"}} >{index}</Text>
                // </View>
              );
            }
          }
        }

        onScroll={({ nativeEvent }) => {
          // console.log(TAG, "onScroll", nativeEvent);
          let offY = nativeEvent.contentOffset.y;
          let topIdx = Math.floor((offY - hh) / cardH);
          if (topIdx != this.mTopItmIdx) {
            // console.log(TAG, "onTopItemChange", topIdx);
            this.onTopItemChange(this.mTopItmIdx, topIdx);
            this.mTopItmIdx = topIdx;
          }
          if (this.props.onScroll != null) {
            this.props.onScroll(nativeEvent.contentOffset);
          }
        }}
        ListEmptyComponent={this.mEmptyV()}
        ListHeaderComponent={hvf ? () => { return hvf(); } : null}
        ListFooterComponent={this.props.listFooter ? this.props.listFooter : this.mFooter}
        keyExtractor={(item, index) => index.toString()}
        refreshing={LoadStatus.Loading === this.state.loadingStatus}
        onRefresh={this.mRefresh}
        onEndReached={this.mOnEnd}
        onEndReachedThreshold={0.1}
        getItemLayout={(data, index) => {

          let ret = { length: cardH, offset: cardH * index + hh, index };
          // console.log(TAG, "getItemLayout", data.length, index, ret);
          return ret;
        }}
      />
    );
  }

  componentDidUpdate(aPrevProps) {
    if (this.props.loaderArgs.startDate.getTime() !== aPrevProps.loaderArgs.startDate.getTime()
      || this.props.loaderArgs.filter !== aPrevProps.loaderArgs.filter
      || this.props.loaderArgs.sdcardCode !== aPrevProps.loaderArgs.sdcardCode) {
      console.log("=================",this.props.loaderArgs,aPrevProps.loaderArgs);
      this.setState({ events: [], loadingStatus: LoadStatus.Idel, startDate: this.props.loaderArgs.startDate, sdcardCode: this.props.loaderArgs.sdcardCode }, () => {
        let startDate = this.state.startDate;
        this.bgn = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate(), 0, 0, 0);
        this.end = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate(), 23, 59, 59);
        this.mRefresh(true);
      });
    }
  }

  getEventFilter() {
    let filter = this.props.loaderArgs.filter;
    if (null == filter) {
      filter = DefFilter;
    }
    return filter;
  }
  // 获取当前事件所在视频，前一个视频或者后一个视频（前一个视频不存在的情况下）
  getNearVideoEvent(aEv) {
    if (!this.state.events) {
      return null;
    }
    let evs = this.state.events;

    let lastIdx = -1;
    let nextIdx = -1;
    let finded = false;
    for (let i = 0; i < evs.length; i++) {
      if (!finded && aEv.fileId == evs[i].fileId) {
        finded = true;
      }
      if (!finded) {
        lastIdx = i;
      }
      if (finded && aEv.fileId != evs[i].fileId) {
        nextIdx = i;
        break;
      }
    }
    let fid = lastIdx >= 0 ? lastIdx : nextIdx >= 0 ? nextIdx : 0;
    return evs[fid];
  }

  removeEvents(aFilter) {
    let newEv = this.applyFilter(this.state.events, aFilter);
    console.log(TAG, 'removeEvents', newEv.length);
    this.setState({ events: newEv });
    if (0 == newEv.length || this.props.typeTab === 1) {
      this.mRefresh();
    } else {
      this.mOnEnd();
    }
  }

  switchDay(aDat, sltDay, localSelectDays) {
    let newEv = [];
    this.sltDay = sltDay;
    this.localSelectDays = localSelectDays;
    let oldDat = this.state.startDate;
    this.setState({ events: newEv, startDate: aDat, nextDate: null, LoadStatus: LoadStatus.Idle }, () => {
      if (oldDat && oldDat.getTime() == aDat.getTime()) {
        this.mRefresh();
      }
    });
  }

  applyFilter(aEvs, aFilter) {
    return aEvs.filter(aFilter);
  }

  getAdjacentEvent(aEv) {
    let evs = this.state.events;
    let idx = evs.findIndex((aItm) => {
      return aItm.fileId == aEv.fileId;
    });
    let ret = null;
    if (idx != -1) {
      if (idx == evs.length - 1) {
        if (evs.length != 1) {
          ret = evs[idx - 1];
        }
      } else {
        ret = evs[idx + 1];
      }
    }
    return ret;
  }

  mEmptyV = () => {
    let emptyMT = 0;
    if (this.props.eventHeaderHeight) {
      emptyMT = -this.props.eventHeaderHeight / 2;
    }
    return (this.state.loadingStatus === LoadStatus.Finish || this.state.loadingStatus === LoadStatus.Failed ?
      <View
        accessibilityLabel={DescriptionConstants.kj_1_17}
        style={{ height: "100%",  alignItems:"center" }}>
        <View style={{flex: 0.38}}></View>
        <Image
          // style={{ alignSelf: "center", width: 138, height: 138 }} source={Util.isDark() ? require("../../resources2/images/icon_ev_empty_w.png") : require("../../resources2/images/icon_ev_empty.png")} />
          style={{ alignSelf: "center", width: 92, height: 60 }} source={Util.isDark() ? require("../../Resources/Images/icon_home_empty_d.webp") : require("../../Resources/Images/icon_home_empty.webp")} />
        <Text style={{ color: 'gray', textAlign: "center", paddingHorizontal: 40, marginTop: 12 }}
          numberOfLines={2}
        >
          {
            this.state.isP2pLostEmpty ? LocalizedStrings.device_not_cont_sdcard_page_desc_empty : this.state.loadingStatus === LoadStatus.Failed ? LocalizedStrings['c_get_fail']:(this.props.emptyDes ? this.props.emptyDes : (this.getEventFilter() === DefFilter ? LocalizedStrings.sdcard_page_desc_empty : LocalizedStrings.all_event_empty))
          }
        </Text>
        <View style={{flex: 0.5}}></View>
      </View>
      : null);
  }

  /**
   * 相同fileId对应的事件数量
   * @param fileId
   */
  getEventListOfCurentFile(fileId) {
    let res = [];
    if (this.state.events && this.state.events.length > 0) {
      for (let j = 0; j < this.state.events.length; j++) {
        let events = this.state.events[j];
        if (fileId == events.fileId) {
          res.push(events);
        }
      }
    }
    return res;
  }

  mRefresh = () => {
    console.log("landing7");
    this.getEventList(this.state.startDate, this.getEventFilter(), false);
  }

  mFooter = () => {
    let text = null;
    if (this.props.type == CldDldTypes.Events)
      switch (this.state.loadingStatus) {
        case LoadStatus.Finish:
          if (!(this.state.events == null || 0 == this.state.events.length)) {
            text = LocalizedStrings.alarm_none_data;
          }
          break;
        case LoadStatus.Loading:
          // text = LocalizedStrings.alarm_loading_data;
          break;
        case LoadStatus.Failed:
          // text = LocalizedStrings.alarm_loading_failed;
          break;
        default:
          break;
      }
    return text ?
      <View style={{ height: ListFooterH, alignItems: 'center', marginTop: 10 }}>
        <Text style={{ color: 'gray' }}>{text}</Text>
      </View>
      : null;
  }


  mOnEnd = () => {
    if (this.localSelectDays) { //判断是本地视频，同时是用户点击某一天，不再多次调用接口，否则会出现视频重复（历史逻辑原因，用面向过程思想为最低成本改动）
      return false;
    }
    if (!this.props.displayStatus) {
      console.log(TAG, "this.props.displayStatus=", this.props.displayStatus);
      return false;
    }
    console.log(TAG, "mOnEnd");
    if (LoadStatus.Finish === this.state.loadingStatus || LoadStatus.Loading === this.state.loadingStatus) {
      console.log(TAG, "onEndReached skip loading for status", this.state.loadingStatus);
      if (this.props.onGetDataDone) {
        this.props.onGetDataDone(this.state.events.length);
      }
      if (this.props.isSltDay && this.props.onSwitchSltDayMore && !this.mSltDayMore) {
        console.log("landing6 2");
        this.props.onSwitchSltDayMore();
        this.mSltDayMore = true;
        console.log("landing6 1");
        this.setState({ loadingStatus: LoadStatus.Loading });
        setTimeout(() => {
          this.localSelectDays = false;
          let date = new Date(this.state.startDate);
          console.log('mNextDate: ', date, 'startDate: ', this.state.startDate);
          this.getEventList(date, this.props.loaderArgs.filter, true);
        }, 100);
      }
    } else {
      console.log("landing6");
      this.getEventList(this.state.nextDate, this.props.loaderArgs.filter, true);
      this.setState({ loadingStatus: LoadStatus.Loading });
    }
  }

  mSwitchSltDayMore(isMore) {
    this.mSltDayMore = isMore;
  }

  getEvents() {
    return this.state.events;
  }
}


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BaseStyles.mainBg.backgroundColor
  },
  listContainer: {
    borderRadius: 10,
    backgroundColor: 'white',
    marginTop: 10,
    marginHorizontal: 10
  },
  item: {
    height: 44
  },
  emptyView: {
    height: "50%",
    backgroundColor: "blue",
    justifyContent: 'center',
    alignItems: 'center'
  }
});
