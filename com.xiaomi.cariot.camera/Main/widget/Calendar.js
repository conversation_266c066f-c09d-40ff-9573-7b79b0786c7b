import React from 'react';
import { View, Text, ViewPropTypes, StyleSheet, ScrollView, TouchableOpacity, Platform, Image } from 'react-native';

import { PropTypes } from 'victory-core/es';
import CalendarPanel from './calendar/CalendarPanel';
import util from './calendar/util';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { DarkMode, Host } from "miot";
import ImageButton from 'miot/ui/ImageButton';
import dayjs from 'dayjs';
import Util from "../util2/Util";
const MAXMOTH_DETION = 40;  //最小的滑动距离

class Calendar extends React.Component {
  static propTypes = {
    y: PropTypes.number,
    m: PropTypes.number,
    d: PropTypes.number,
    visible: PropTypes.bool,
    showTwoBottomButton: PropTypes.bool,
    onDateChanged: PropTypes.func,
    toastStr: PropTypes.string,
    ...ViewPropTypes
  };

  static defaultProps = {
    showTwoBottomButton: true
  };


  constructor(props) {
    super(props);
    let currentDate = new Date(Date.parse(`${ this.props.y }/${ this.props.m }/${ this.props.d }`));
    let nextMD = util.getNextMonthDate(currentDate);
    let preMD = util.getPreMonthDate(currentDate);
    console.log('calendar input: ', this.props.y, this.props.m, this.props.d, 'currentDate: ', currentDate);
    this.nowDate = new Date();
    this.chooseItem = [this.props.y, this.props.m, 0, 0, this.props.d];
    this.state = {
      y: this.props.y, // same as currentDate
      mon: this.props.m,
      day: this.props.d,
      clickNum: undefined,
      today: new Date(),
      currentDate: currentDate,
      nextMonthDate: nextMD,
      preMonthDate: preMD,
      firsttime: true,
      interval: this.props.interval,
      isCurrentMonth: currentDate.getFullYear() == this.nowDate.getFullYear() && currentDate.getMonth() == this.nowDate.getMonth()
    };
    this.scrollView = null;
    this.mCldPnl_cur = null;
    this.mCldPnl_pre = null;
    this.mCldPnl_nxt = null;
  }


  componentWillMount() {

  }
  handleScrollBeginDrag = (event) => {
    this.startX = event.nativeEvent.contentOffset.x;
  };

  handleScrollEndDrag = (event) => {
    this.endX = event.nativeEvent.contentOffset.x;
    this.determineScrollDirection();
  };
  determineScrollDirection = () => {
    // console.log("this.endX- this.startX",this.endX- this.startX)
    // if (this.endX > this.startX) {
    //   // console.log('swipe 加载下个月');
    //   this.direction = 'right';
    // } else {
    //   // console.log('swipe 加载上个月');
    //   this.direction = 'left';
    // }



    if (Math.abs(this.endX - this.startX) > MAXMOTH_DETION) {
      if (this.endX > this.startX) {
        this.direction = 'right';
      } else {
        this.direction = 'left';
      }
    } else {
      this.direction = null;
      this.scrollView?.scrollTo({ x: this.props.width - this.paddingHorizontal * 2, animated: true });
      this.startX = 0;
      this.endX = 0;
      return
    }
    // console.log("this.direction",this.direction)
    if (this.direction == 'left') {
      this.scrollEndLeft();
    } else if (this.direction == 'right') {
      this.scrollEndRight();
    }

    this.direction = null;
    this.scrollView?.scrollTo({ x: this.props.width - this.paddingHorizontal * 2, animated: true });
    this.startX = 0;
    this.endX = 0;
  };

  scrollEndLeft() {
    const { currentDate } = this.state;
    let changeDate = currentDate;
    changeDate = util.getPreMonthDate(currentDate);
    this.setState({
      currentDate: changeDate,
      nextMonthDate: util.getNextMonthDate(changeDate),
      preMonthDate: util.getPreMonthDate(changeDate)
    });
    this.scrollView.scrollTo({ x: this.props.width - this.paddingHorizontal * 2, animated: false });
  }

  scrollEndRight() {
    const { currentDate } = this.state;
    let changeDate = currentDate;
    changeDate = util.getNextMonthDate(currentDate);
    this.setState({
      currentDate: changeDate,
      nextMonthDate: util.getNextMonthDate(changeDate),
      preMonthDate: util.getPreMonthDate(changeDate)
    });
    this.scrollView.scrollTo({ x: this.props.width - this.paddingHorizontal * 2, animated: false });
  }

  scrollEnd(event) {
    const scrollView = this.scrollView;
    const { width } = this.props;
    const { currentDate } = this.state;

    let changeDate = currentDate;
    // console.log("landing1231",currentDate);

    let tWidth = width;
    const offsetX = event.nativeEvent.contentOffset.x;
    if (Platform.OS === 'ios') {
      if (offsetX < width) {
        // 上个月
        changeDate = util.getPreMonthDate(currentDate);
      } else if (offsetX > width) {
        // 下个月
        changeDate = util.getNextMonthDate(currentDate);
      }
    } else {
      if (this.state.firsttime) {
        if (offsetX == 0) {
          // 上个月
          changeDate = util.getPreMonthDate(currentDate);
        } else {
          // 下个月
          changeDate = util.getNextMonthDate(currentDate);
          if (this.state.firsttime) {
            tWidth = 2 * width;
          }
        }
      } else if (offsetX < width) {
        // 上个月
        changeDate = util.getPreMonthDate(currentDate);
      } else {
        // 下个月
        changeDate = util.getNextMonthDate(currentDate);
      }
    }
    offsetX !== tWidth && scrollView.scrollTo({ x: tWidth, animated: false });
    // console.log("landing123---------", "width", width, "tWidth", tWidth, "firsttime", this.state.firsttime,  "offsetX", offsetX, "current: ", changeDate, 'next:', util.getNextMonthDate(changeDate), 'pre:', util.getPreMonthDate(changeDate));
    if (this.state.firsttime) {
      this.setState({
        currentDate: changeDate,
        nextMonthDate: util.getNextMonthDate(changeDate),
        preMonthDate: util.getPreMonthDate(changeDate),
        firsttime: false,
        isCurrentMonth: changeDate.getFullYear() == this.nowDate.getFullYear() && changeDate.getMonth() == this.nowDate.getMonth()

      });
    } else {
      this.setState({
        currentDate: changeDate,
        nextMonthDate: util.getNextMonthDate(changeDate),
        preMonthDate: util.getPreMonthDate(changeDate),
        isCurrentMonth: changeDate.getFullYear() == this.nowDate.getFullYear() && changeDate.getMonth() == this.nowDate.getMonth()
      });
    }

  }

  setDate(date) {
    this.setState({
      currentDate: date,
      nextMonthDate: util.getNextMonthDate(date),
      preMonthDate: util.getPreMonthDate(date)
    });
  }

  render() {

    console.log("render: ", this.state.preMonthDate, this.state.currentDate, this.state.nextMonthDate);
    let isZH = Host.locale.language == 'zh' || Host.locale.language == 'zh_tw' || Host.locale.language == 'zh_hk';
    let format = LocalizedStrings["yyyymm"];
    let yearMM = dayjs(this.state.currentDate).locale(Util.getLanguage()).format(format);
    return (
      <ScrollView
        showsVerticalScrollIndicator={false}
        bounces={false}
        style={ { flex: 1, width: '100%' } }
        contentContainerStyle={{flexGrow: 1}}>

        <View style={ styles.container }>
          <View style={ {
            justifyContent: 'center',
            flexDirection: 'row',
            height: 22,
            alignItems: 'center',
            marginTop: 25
          } }>
            <Text style={ {
              fontSize: 18,
              color: '#000000',
              fontWeight: '500'
            } }> { LocalizedStrings['calendar_sel_date'] } </Text>
          </View>
          <View
            style={ {
              justifyContent: 'center',
              flexDirection: 'row',
              height: 19,
              alignItems: 'center',
              marginTop: 17
            } }>
            <TouchableOpacity
              style={ { marginRight: 5 } }
              onPress={ () => {
                this.changeMonth(false);
              } }>
              <Image
                source={ DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/icon_preview_month_dark.png') : require('../../Resources/Images/icon_preview_month.png') }
                style={ { width: 22, height: 22 } }/>
            </TouchableOpacity>
            <Text style={ {
              fontSize: 16,
              color: '#666666',
              textAlign: 'center',
              minWidth: 138,
              fontWeight: '500'
            } }> { yearMM } </ Text>
            <TouchableOpacity
              style={ { marginLeft: 5 } }
              onPress={ () => {
                this.changeMonth(true);
              } }>
              <Image
                source={ DarkMode.getColorScheme() == "dark" ? require('../../Resources/Images/icon_next_month_dark.png') : require('../../Resources/Images/icon_next_month.png') }
                style={ { width: 22, height: 22 } }/>
            </TouchableOpacity>
          </View>
          {
            !this.state.isCurrentMonth ?
              <TouchableOpacity
                style={ {
                  width: 45,
                  height: 45,
                  position: 'absolute',
                  top: 25,
                  right: 25,
                  backgroundColor: 'rgba(50, 186, 192, 0.1)',
                  borderRadius: 22.5,
                  justifyContent: 'center',
                  alignContent: 'center'
                } }
                onPress={ () => {
                  this.change2Today();
                  // this.props.onAllVideo();
                  // this.props.onCancel();
                } }
                accessible={ true }
                accessibilityLabel={ LocalizedStrings['calendar_today'] }>
                <Text style={ {
                  fontSize: isZH ? 18 : 8,
                  color: '#32BAC0',
                  textAlign: 'center',
                  fontWeight: '500',
                  textAlignVertical: 'center',
                  paddingHorizontal: 6
                } }> { LocalizedStrings['calendar_today'] } </ Text>
              </TouchableOpacity> : null
          }


          <ScrollView
            horizontal={ true }
            showsHorizontalScrollIndicator={ false }
            style={{paddingBottom: 75}}
            onMomentumScrollEnd={ (event) => this.scrollEnd(event) }
            // onScrollBeginDrag={this.handleScrollBeginDrag}
            // onScrollEndDrag={this.handleScrollEndDrag}
            ref={ (scrollView) => this.scrollView = scrollView }
            contentOffset={ { x: this.props.width } }>
            {
              Platform.OS == 'ios' ?
                <CalendarPanel
                  visible={ this.showCalendar }
                  y={ this.state.preMonthDate.getFullYear() }
                  m={ this.state.preMonthDate.getMonth() }
                  d={ this.state.preMonthDate.getDate() }
                  cy={ this.state.y }
                  cm={ this.state.mon }
                  cd={ this.state.day }
                  date={ this.state.preMonthDate }
                  interval={ this.state.interval }
                  ref={ (hdl) => this.mCldPnl_pre = hdl }
                  onDateChanged={ this.change2SomeDay }
                  onCancel={ this.props.onCancel }
                  onAllVideo={ this.props.onAllVideo }
                  width={ this.props.width }
                  dates={ this.props.dates }
                  toastStr={ this.props.toastStr }
                /> : (
                  this.state.firsttime ? null :
                    <CalendarPanel
                      visible={ this.showCalendar }
                      y={ this.state.preMonthDate.getFullYear() }
                      m={ this.state.preMonthDate.getMonth() }
                      d={ this.state.preMonthDate.getDate() }
                      cy={ this.state.y }
                      cm={ this.state.mon }
                      cd={ this.state.day }
                      date={ this.state.preMonthDate }
                      interval={ this.state.interval }
                      ref={ (hdl) => this.mCldPnl_pre = hdl }
                      onDateChanged={ this.change2SomeDay }
                      onCancel={ this.props.onCancel }
                      onAllVideo={ this.props.onAllVideo }
                      width={ this.props.width }
                      dates={ this.props.dates }
                      toastStr={ this.props.toastStr }
                    />)
            }

            <CalendarPanel
              visible={ this.showCalendar }
              y={ this.state.currentDate.getFullYear() }
              m={ this.state.currentDate.getMonth() }
              d={ this.state.currentDate.getDate() }
              cy={ this.state.y }
              cm={ this.state.mon }
              cd={ this.state.day }
              date={ this.state.currentDate }
              interval={ this.state.interval }
              ref={ (hdl) => this.mCldPnl_cur = hdl }
              onDateChanged={ this.change2SomeDay }
              onCancel={ this.props.onCancel }
              onAllVideo={ this.props.onAllVideo }
              width={ this.props.width }
              dates={ this.props.dates }
              toastStr={ this.props.toastStr }
            />
            <CalendarPanel
              visible={ this.showCalendar }
              y={ this.state.nextMonthDate.getFullYear() }
              m={ this.state.nextMonthDate.getMonth() }
              d={ this.state.nextMonthDate.getDate() }
              cy={ this.state.y }
              cm={ this.state.mon }
              cd={ this.state.day }
              date={ this.state.nextMonthDate }
              interval={ this.state.interval }
              ref={ (hdl) => this.mCldPnl_nxt = hdl }
              onDateChanged={ this.change2SomeDay }
              onCancel={ this.props.onCancel }
              onAllVideo={ this.props.onAllVideo }
              width={ this.props.width }
              dates={ this.props.dates }
              toastStr={ this.props.toastStr }
            />

          </ScrollView>
          {
            this.props.showTwoBottomButton ?
              <View style={ {
                position: 'absolute',
                width: '87%',
                flexDirection: 'row',
                height: 46,
                bottom: 25,
                justifyContent: 'space-between'
              } }>
                <TouchableOpacity
                  style={ {
                    width: '46%',
                    height: 46,
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRadius: 23,
                    backgroundColor: '#F5F5F5'
                  } }
                  onPress={ () => {
                    console.log('cancel clicked');
                    this.props.onCancel();
                  } }>
                  <Text style={ { fontSize: 17, color: '#4C4C4C' } }> { LocalizedStrings['btn_cancel'] } </ Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={ {
                    width: '46%',
                    height: 46,
                    justifyContent: 'center',
                    borderRadius: 23,
                    backgroundColor: '#32BAC0',
                    alignItems: 'center'
                  } }
                  onPress={ () => { // cancel
                    console.log('cancel clicked');
                    this.props.onCancel();
                    this.props.onDateChanged(this.chooseItem);
                  } }>
                  <Text style={ { fontSize: 17, color: '#FFFFFF' } }> { LocalizedStrings['btn_confirm'] } </ Text>
                </TouchableOpacity>
              </View> :
              <View style={ {
                position: 'absolute',
                width: '87%',
                height: 46,
                borderRadius: 23,
                bottom: 25,
                backgroundColor: '#F5F5F5'
              } }>
                <TouchableOpacity
                  style={ { width: '100%', height: 46, justifyContent: 'center', alignItems: 'center' } }
                  onPress={ () => { // cancel
                    console.log('cancel clicked');
                    this.props.onCancel();
                  } }>
                  <Text style={ { fontSize: 17, color: '#4C4C4C' } }> { LocalizedStrings['btn_cancel'] } </ Text>
                </TouchableOpacity>
              </View>
          }

        </View>
      </ScrollView>

    );
  }

  changeMonth(isNext = true) {
    const { currentDate } = this.state;
    let changeDate = isNext ? util.getNextMonthDate(currentDate) : util.getPreMonthDate(currentDate);
    this.setState({
      currentDate: changeDate,
      nextMonthDate: util.getNextMonthDate(changeDate),
      preMonthDate: util.getPreMonthDate(changeDate),
      isCurrentMonth: changeDate.getFullYear() == this.nowDate.getFullYear() && changeDate.getMonth() == this.nowDate.getMonth()
    });
  }

  change2Today() {
    let changeDate = new Date();
    this.setState({
      y: changeDate.getFullYear(),
      mon: changeDate.getMonth() + 1,
      day: changeDate.getDate(),
      currentDate: changeDate,
      nextMonthDate: util.getNextMonthDate(changeDate),
      preMonthDate: util.getPreMonthDate(changeDate),
      isCurrentMonth: changeDate.getFullYear() == this.nowDate.getFullYear() && changeDate.getMonth() == this.nowDate.getMonth()
    });
    this.chooseItem = [changeDate.getFullYear(), changeDate.getMonth() + 1, 0, 0, changeDate.getDate()];
  }

  change2SomeDay = (item) => {
    let changeDate = new Date(Date.parse(`${ item[0] }/${ item[1] }/${ item[4] }`));
    this.chooseItem = item;
    console.log("+++++++++++++++++", item);
    this.setState({
      y: changeDate.getFullYear(),
      mon: changeDate.getMonth() + 1,
      day: changeDate.getDate(),
      currentDate: changeDate,
      nextMonthDate: util.getNextMonthDate(changeDate),
      preMonthDate: util.getPreMonthDate(changeDate),
      isCurrentMonth: changeDate.getFullYear() == this.nowDate.getFullYear() && changeDate.getMonth() == this.nowDate.getMonth()
    });

    // this.props.onDateChanged(item);

  };

  onDateChanged(item) {
    let changeDate = new Date(Date.parse(`${ item[0] }/${ item[1] }/${ item[4] }`));
    this.setState({
      currentDate: changeDate,
      nextMonthDate: util.getNextMonthDate(changeDate),
      preMonthDate: util.getPreMonthDate(changeDate)
    });
    this.props.onDateChanged(item);
  }
}

export default Calendar;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    alignItems: 'center'
  },
  imgstyle: {
    width: 360,
    height: 442
  }
});