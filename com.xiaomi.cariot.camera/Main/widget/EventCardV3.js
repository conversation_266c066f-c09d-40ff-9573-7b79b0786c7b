import React from 'react';
import { BaseStyles } from "../BasePage";
import Util from "../util2/Util";
import PropTypes from 'prop-types';
import { StyleSheet, View, Text, Image, TouchableOpacity } from 'react-native';
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import { DescriptionConstants } from '../Constants';
export const CardHeight = 88;
export const CardMB = 12;
import { SCREEN_WIDTH } from '../util2/Const';
import CameraConfig from '../util/CameraConfig';
import { Device } from 'miot';
import AlarmUtilV2 from "../util/AlarmUtilV2";

const TAG = "EventCardV3";
export default class EventCardV3 extends React.Component {
  static propTypes = {
    cardPressed: PropTypes.func,
    cardLongPressed: PropTypes.func,
    morePressed: PropTypes.func,
    item: PropTypes.object,
    sdcardCode: PropTypes.number,
    isPlaying: PropTypes.bool
  }
  constructor(props) {
    super(props);
    this.state = {
      hasSDCard: this.props.sdcardCode == 0 || this.props.sdcardCode == 2
    };
  }
  componentDidUpdate(aPrevProps) {
    if (this.props.sdcardCode !== aPrevProps.sdcardCode) {
      this.setState({ hasSDCard: this.props.sdcardCode == 0 || this.props.sdcardCode == 2 });
    }
  }
  render() {
    let imgStoreUrl = this.props.item.imgStoreUrl;
    let imgSource = imgStoreUrl != null ? { uri: `file://${imgStoreUrl}` } : null;
    const type = this.props.type;
    let iconSource = Util.getIconFromType(this.props.item.type, this.props.item.faceInfo ? this.props.item.faceInfo.name : null);
    let actColor = Util.getActiveColorFromType(this.props.item.type);
    // console.log("EVENTCARD render", this.props.item.type);
    // nomal 0 read 1 active 2
    let styleIdx = this.props.item.isRead ? 1 : 0;
    let backgroundColor ={backgroundColor: Util.isDark() ? "xm#000000" : "#ffffff"};
    if (this.props.isPlaying) {
      styleIdx = 0;
      backgroundColor ={backgroundColor: "#F6F6F6"};
    }
    let timeStyle = [[BaseStyles.text12, { marginTop: 2, color: "#7F7F7F" }],
    [BaseStyles.text12, { marginTop: 2, color: "#7F7F7F" }],
    [BaseStyles.text12, { marginTop: 2, color: actColor }]
    ];

    let descStyle = [[BaseStyles.text16, { fontWeight: "bold" }],
    [BaseStyles.text16, { color: "#7F7F7F", fontWeight: "bold" }],
    [BaseStyles.text16, { color: actColor, fontWeight: "bold" }]
    ];
    // console.log(TAG, "render", this.props.item.desc, "evt", this.props.item.eventTime, "type", this.props.item.type);
    let textW = SCREEN_WIDTH - 176 - 30;
    let showImg = true;
    if (typeof(this.props.item.isShowImg) === 'boolean') {
      showImg = this.props.item.isShowImg;
    }

    let showMore = false;
    // if (AlarmUtilV2.SD_EARLIEST_TIME != 0 && this.props.item.createTime / 1000 >= AlarmUtilV2.SD_EARLIEST_TIME) {
    //   showMore = true;
    // }
    return (
      <TouchableOpacity style={[BaseStyles.row, { height: CardHeight, paddingLeft: 18, paddingRight: 12, marginBottom: CardMB, borderRadius: 12 },backgroundColor]}
        onPress={() => { 
          if (showImg) {
            this.props.cardPressed(this.props.item);
          } else {
            // Toast.success("c_cloudvip_need");
          }
          }}
        onLongPress={()=>{
          console.log("长按，删除")
          this.props.cardLongPressed(this.props.item);
        }}>
        <Image
          style={[BaseStyles.icon30, { marginRight: 13 }]}
          source={iconSource}
          accessibilityLabel={DescriptionConstants.kj_1_9}
        >

        </Image>
        <View style={[BaseStyles.column, { flex: 1, alignItems: "flex-start", justifyContent: "space-between", marginRight: 20 }]} accessibilityLabel={ this.props.item.desc.trim() + this.props.item.eventTime }>
          <Text
            style={descStyle[styleIdx]}
            numberOfLines={3}
            ellipsizeMode={"tail"}
          >{this.props.item.desc.trim()}
          </Text>
          <Text
            style={timeStyle[styleIdx]}>
            {this.props.item.eventTime}
          </Text>
        </View>

        {
          showImg ? 
            this.props.isPlaying && this.state.hasSDCard && showMore ?
              <TouchableOpacity
                onPress={() => {
                  this.props.morePressed(this.props.item);
                }}>{this._renderImageView(imgSource,showMore)}</TouchableOpacity>
              : this._renderImageView(imgSource,showMore)
            : null
        }
      </TouchableOpacity>
    );
  }

  _renderImageView(imgSource,showMore) {
    return <View styles={styles.imgView}>
      {
        imgSource ? <Image
          accessibilityLabel={!this.props.isPlaying ? DescriptionConstants.kj_2_17 : DescriptionConstants.kj_2_18}
          style={[styles.imgView, { alignSelf: "center", marginRight: 12, resizeMode: "stretch" }]} source={imgSource} /> : <View style={[styles.imgView, { alignSelf: "center", marginRight: 12, resizeMode: "stretch", backgroundColor: "#EEEEEE", borderRadius: 9 }]}/>
      }
      {
        this.props.isPlaying ?
          <View style={[styles.imgView, { position: "absolute", backgroundColor: "rgba(0,0,0,0.55)", alignItems: "center", justifyContent: "center" }]}>
            {
              this.state.hasSDCard && showMore ?
                <Text style={[BaseStyles.text12, { fontWeight: "bold", color: "#ffffff", paddingHorizontal: 6 }]}>{LocalizedStrings["view_more"]}</Text>
                : showMore ? null : <Text style={[BaseStyles.text12, { fontWeight: "bold", color: "#ffffff", paddingHorizontal: 6 }]}>{LocalizedStrings["storage_item_playing"]}</Text>
            }
          </View>
          : null
      }
    </View>;
  }
}

const styles = StyleSheet.create({
  timeView: {
    flexDirection: 'row',
    alignItems: 'flex-start'
  },
  pressedTimeLabel: {
    color: 'gray'
  },
  imgView: {
    width: 106,
    height: 64,
    borderRadius: 9
  }
});
