
import dayjs from 'dayjs';

export default {
  getMonthDays(year, month) {
    let thisDate = new Date(year, month, 0); // 当天数为0 js自动处理为上一月的最后一天
    return thisDate.getDate();
  },

  getPreMonthDate(date) {
    let month = dayjs(date).add(-1, 'month');

    if (date.getDate() > this.getMonthDays(month.year(), month.month() + 1)) {
      return new Date(`${ month.year() }/${ month.month() + 1 }/${ month.daysInMonth() }`);
    } else {
      return new Date(`${ month.year() }/${ month.month() + 1 }/${ date.getDate() }`);
    }
  },
  getNextMonthDate(date) {
    let month = dayjs(date).add(+1, 'month');

    if (date.getDate() > this.getMonthDays(month.year(), month.month() + 1)) {
      return new Date(`${ month.year() }/${ month.month() + 1 }/${ month.daysInMonth() }`);
    } else {
      return new Date(`${ month.year() }/${ month.month() + 1 }/${ date.getDate() }`);
    }
  },
  dateCover(num) {
    return num < 10 ? `0${ num }` : num;
  },
  
  getFirstDateInMonth(date) {
    return new Date(date.getFullYear(), date.getMonth(), 1);
  },
  getDateNumber(date) {
    const year = date.getFullYear();
    return [31,
      (year % 100 === 0 && year % 400 === 0) || (year % 100 !== 0 && year % 4 === 0) ? 29 : 28,
      31, 30, 31, 30, 31, 31, 30, 31, 30, 31][date.getMonth()];
  },
  getDateArr(date) {
    const dateNumber = this.getDateNumber(date);
    const dateArr = [];
    console.log(dateNumber);
    for (let i = 0; i < dateNumber; i++) {
      dateArr.push(new Date(date.getFullYear(), date.getMonth(), i + 1));
    }
    return dateArr;
  }
};
