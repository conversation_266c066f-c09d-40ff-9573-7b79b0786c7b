import ImageButton from 'miot/ui/ImageButton';
import React from 'react';
import { View, Text, Image, Modal, Easing, ViewPropTypes, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import CameraConfig from '../../util/CameraConfig';


import LinearGradient from 'react-native-linear-gradient';
import Toast from 'react-native-root-toast';
import { PropTypes } from 'victory-core/es';
import { localStrings as LocalizedStrings } from '../../MHLocalizableString';
import { Host } from "miot";
import dayjs from 'dayjs';
import Util from "../../util2/Util";

let signArr = [];

// const nowDate = new Date();
const nowDate = new Date(new Date().setHours(0, 0, 0, 0));

const colors = {
  themecolor: "gray",
  selected: "gray",
  normal: "light"
};
const Metrics = {
  CH: 100,
  CW: 100
};
class CalendarPanel extends React.Component {
    static propTypes = {
      y: PropTypes.number,
      m: PropTypes.number,
      d: PropTypes.number,
      cy: PropTypes.number,
      cm: PropTypes.number,
      cd: PropTypes.number,
      visible: PropTypes.bool,
      toastStr: PropTypes.string,
      onDateChanged: PropTypes.func,
      ...ViewPropTypes
    };


    constructor(props) {
      super(props);
      this.isEuropeServer = CameraConfig.getIsEuropeServer();
      this.initState(this.props.y, this.props.m, this.props.d, this.props.selDay);
    }

    initState(y, m, d) {
      this.setState({
        y: y,
        m: m,
        d: d,
        clickNum: undefined
      });
      // console.log('calendar panel input: ', this.props.y, this.props.m + 1, this.props.d);
    }

    // 获得上一个月的第一天
    DateStr() {
      let rr = this.props.m + 1;
      return `${ this.props.y }-${ rr }-${ 1 }`;
    }
    
    MonthInfo() {
      // console.log('转化成字符串后的时间', this.DateStr())
      let Nowdate = (this.DateStr()).split("-"), // 获取某一日期
        NowYear = parseInt(Nowdate[0]), // 某年份
        NowMonth = parseInt(Nowdate[1]), // 某月份
        Nowday = parseInt(Nowdate[2]), // 某天
        date = new Date();
        // console. log( '改变的月份', NowMonth)
        // 顶部标题样式
      let title = `${ NowYear } - ${ NowMonth }`;
      // 获取当前月份第一天为周几
      date.setFullYear(NowYear); // 把当前年份设为某年
      date.setMonth(NowMonth - 1); // 把当前月份设为某月
      date.setDate(Nowday);
      let weeks = [0, 1, 2, 3, 4, 5, 6];
      // getDay获取当前月份为星期几
      let date1 = weeks[date.getDay()];

      // 获取当前月份天数
      let days = this.getMonthDays(NowYear, NowMonth);
      return [title, date1, days];
    }

    getMonthDays(year, month) {
      // new Date(year, month, 0).getDate()返回的是上个月的最后一天的日期
      // month 0-11
      let thisDate = new Date(year, month, 0);
      return thisDate.getDate();
    }

    componentWillMount() {

    }

    // 获取当前日期
    getD = () => {
      let date = new Date();
      let d = date.getDate();
      return d;
    }

    dateArr = () => {
      console.log('get dateArr', this.props.y, this.props.m, this.props.d);
      let arr = [];
      let arr2 = [];
      let lastM = this.props.m == 0 ? 11 : this.props.m - 1;
      let nextM = this.props.m == 11 ? 0 : this.props.m + 1;
      let lastY = this.props.m === 0 ? this.props.y - 1 : this.props.y;
      let nextY = this.props.m === 11 ? this.props.y + 1 : this.props.y;
      let lastMDs = this.getMonthDays(lastY, lastM + 1);
      let nextMDs = this.getMonthDays(nextY, nextM + 1);
      // console.log("this "+ this.props.y + '-' + (this.props.m+1) + ' monthinfo: ' + this.MonthInfo()[1]);
      // console.log("landing last"+ lastY + '-' + (lastM + 1) + ' : '+lastMDs );
      // console.log("landing next "+ nextY + '-' + (nextM + 1) + ' : '+nextMDs );
      // 当期月份的第一天是周几，此处加入的是上个月的数据
      // lastMDs上一个月的总天数
      // setSelected(day) 标记此天是否被选中
      // 最后一位是星期 只关注0,6,7，绘制日期底色 0左 侧半圆 6 右侧半圆  7 左右侧都半圆
      for (let i = 0; i < this.MonthInfo()[1]; i++) {
        let day = [lastY, lastM + 1, 0, 0, lastMDs + i - this.MonthInfo()[1] + 1,0,0,0];
        this.setSelected(day);
        arr.push(day); // (year, month, month(0:pre,1:current month / date with data,2:next, 3: date no data), selected(0:no, 1:yes), day) day的结构定义
      }
      // 当前月份数据 从1号开始
      for (let i = 1; i < this.MonthInfo()[2] + 1; i++) {
        let day = [this.props.y, this.props.m + 1, 0, 0, i,1,0,0];
        day = this.setSelected(day);
        arr.push(day);
      }
      // 下一个月
      if ((this.MonthInfo()[1] + this.MonthInfo()[2]) % 7 !== 0) {
        for (let i = 0; i < 7 - ((this.MonthInfo()[1] + this.MonthInfo()[2]) % 7); i++) {
          let day = [nextY, nextM + 1, 2, 0, i + 1,0,0,0];
          day = this.setSelected(day);
          arr.push(day);
        }
      }

      this.setDayInInterval(arr);
      this.setDayWithData(arr);
      return arr;
    }

    setDayWithData(arr) {
      if (this.props.dates == null) return;
      arr.forEach((day) => {
        this.isInDates(day);
      });
    }

  isInDates(day) {
    let date = new Date(Date.parse(`${ day[0] }/${ day[1] }/${ day[4] }`));
    let find = false;
    let length = this.props.dates.length;
    for (let i in this.props.dates) {
      let timestamp = this.props.dates[i];
      let day1 = dayjs(timestamp);
      let day2 = dayjs(date);
      let same = day1.isSame(day2, 'day');
      if (same) {

        day[2] = 1;
        find = true;
        let hasPre = false;
        let hasNext = false;
        day1 = day1.hour(0).minute(0).second(0);
        if (i == 0) {
          hasPre = false;
        } else {
          //1719689079000,1719763201000, 74122000 86400000 172773000    86387000     86386000
          //  LOG  =======isInDates [1719689079000, 1719763201000, 1719849634000, 1719936021000, 1720022407000]
          let timestampPre = this.props.dates[Number(i)-1];
          let day3 = dayjs(timestampPre);
          day3 = day3.hour(0).minute(0).second(0);
          let diffPre = day1.diff(day3, 'day');
          if (diffPre > 0 && diffPre <= 1) {
            hasPre = true;
          }
        }

        if (i == length -1) {
          hasNext = false;
        } else {
          let timestampNext = this.props.dates[Number(i) + 1];
          let day3 = dayjs(timestampNext);
          day3 = day3.hour(0).minute(0).second(0);
          let diffNext = day3.diff(day1, 'day');
          if (diffNext > 0 && diffNext <= 1) {
            hasNext = true;
          }
        }
        if (!hasPre && !hasNext) {
          day[6] = 7;
        } else if (hasPre && !hasNext) {
          day[6] = 6;
        } else if (hasNext && !hasPre) {
          day[6] = 0;
        } else if (hasNext && hasPre) {
          day[6] = date.getDay();
        } else {
          day[6] = date.getDay();
        }

        break;
      }
    }


    if(!find) {
      day[2] = 3;
      day[6] = date.getDay();
    }
  }

  
    setDayInInterval(arr) {
      if (this.props.interval == -1) return;
      let now = new Date();
      arr.forEach((day) => {
        let date = new Date(Date.parse(`${ day[0] }/${ day[1] }/${ day[4] }`));
        let diff = now - date;
        if (diff > 0 && diff < this.props.interval) {
          day[2] = 1;
        } else {
          day[2] = 3;
        }
        // 691200000 604800
        if (diff > 0 && diff <= 3600 * 24 * 1000) {
          day[6] = 6;
        } else if (this.props.interval < 3600 * 24 * 9 * 1000 && diff > 3600 * 24 * 7 * 1000 && diff < this.props.interval){
          // 云存为七天的情况 第一天
          day[6] = 0;
        } else if (diff > 3600 * 24 * 30 * 1000 && diff < this.props.interval) {
          // 云存为一个月
          day[6] = 0;
        } else {
          day[6] = date.getDay();
        }

      });
    }

    setSelected(day) {
      // if (this.props.y == day[0] && this.props.m + 1 == day[1] && this.props.d == day[4]) {
      if (this.props.y == this.props.cy && this.props.y == day[0] && this.props.m + 1 == this.props.cm && this.props.m + 1 == day[1] && this.props.d == this.props.cd && this.props.d == day[4]) {
        day[3] = 1;
      } else {
        day[3] = 0;
      }
      let date = new Date(Date.parse(`${ day[0] }/${ day[1] }/${ day[4] }`));
      let day1 = dayjs(nowDate);
      let day2 = dayjs(date);
      let same = day1.isSame(day2, 'day');
      if (same) {
        day[7] = 1;
      }
      return day;
    }

    update(newDate, from) {
      console.log(from, newDate);
      this.initState(newDate.getFullYear(), newDate.getMonth(), newDate.getDate());
    }

    render() {
      console.log('panel', this.MonthInfo()[0]);
      return (
        < View style= { [styles.container, { width: this.props.width }] } >
          {/* < View style= {{ justifyContent: 'center', flexDirection: 'row', height: 19, alignItems: 'center', marginTop: 7 } } >
            < Text style= {{ fontSize: 16, color: '#666666', fontWeight: '500' } } > {this.MonthInfo()[0] } </ Text >
          </ View > */}
          <View style={{flexDirection: 'row', marginHorizontal: 28}}>
            <View style={{ height: 0.5, width: '100%', backgroundColor: 'rgba(0, 0, 0, 0.1)', marginVertical: 19 }}></View>

          </View>
          < View style= {{ width: '100%', justifyContent: 'space-around', alignItems: 'center', flexDirection: 'row', paddingHorizontal: 12 } } >
            {
              [LocalizedStrings['sun'], LocalizedStrings['mon'], LocalizedStrings['tue'], LocalizedStrings['wed'], LocalizedStrings['thu'], LocalizedStrings['fri'], LocalizedStrings['sat']].map((item, index) => {
                return (
                  < Text style= {{ fontSize: 13, color: "#999999", fontWeight: '500' } } key = {index} > { item } </ Text >
                );
              })
            }
          </ View > 

          < View style= {{ width: '100%', justifyContent: 'space-around', alignItems: 'center', flexDirection: 'row', flexWrap: 'wrap', marginTop: 20, paddingHorizontal: 12 } } >
            {
              this.dateArr().map((items, nums) => {
                // let bColor = items[3] == 0 ? colors.normal : colors.selected;
                let bColor = colors.selected;
                let borderStyle = items[6] == 0 ? {
                  borderTopLeftRadius: 20,
                  borderBottomLeftRadius: 20
                } : items[6] == 6 ? {
                  borderTopRightRadius: 20,
                  borderBottomRightRadius : 20
                } : items[6] == 7 ? {
                  borderRadius: 20
                } : {}
                return (
                  <TouchableOpacity
                    activeOpacity={1}
                    onPress= {() => {
                      console.log(`selected: ${ items[0] } ${ items[1] } ${ items[2]} ${ items[3]} ${ items[4] }`);// items is the day index in the month, nums is the index of
                      // this.props.navigation.state.params.onDateChanged(items);
                      if (this.props.interval != -1 && items[2] == 3) {
                        let mStr = CameraConfig.isVip ? (this.isEuropeServer ? "eu_date_out_rolling_interval" : "date_out_rolling_interval") : "kanjia_date_out_rolling_interval";
                        let mToastValue = LocalizedStrings[mStr];
                        if (this.props.toastStr) {
                          mToastValue = this.props.toastStr;
                        }
                        Toast.show(mToastValue, {
                          duration: Toast.durations.SHORT,
                          position: Toast.positions.CENTER
                        });
                      } else {
                        this.props.onDateChanged(items);
                      }
                    } }
                    style= {[{ width: '14.28%', marginBottom: 4, justifyContent: 'center', alignItems: 'center', paddingVertical: 2, backgroundColor: items[2] == 1 ? "rgba(50, 186, 192, 0.1)" : "white" }, borderStyle] }
                    key = {nums}>
                    {/*< View style= {{ width: 40, height: 40, borderRadius: 20, justifyContent: 'center', alignItems: 'center', backgroundColor: items[3] == 1 ? '#EEEEEE' : 'white' } } >*/}
                      <View style= {{ width: 40, height: 40, borderRadius: 20, justifyContent: 'center', alignItems: 'center', backgroundColor: items[3] == 1 ? '#32BAC0' : items[2] == 1 && items[3] != 1 ? "transparent" : 'white' } } >

                      <Text style= {{ fontSize: Util.isLanguageCN() ? 18 : 12, color: items[5] === 0? '#CCCCCC' : items[3] == 1 ? '#FFFFFF' : '#000000' } } > { items[7] == 1 ? LocalizedStrings['calendar_today'] : items[4] } </ Text >
                        {
                          items[7] == 1?
                            <View style={{width: 4, height: 4, borderRadius: 2, backgroundColor: '#32BAC0', marginTop: 2}}/> : null
                        }

                    </View>
                  </ TouchableOpacity >
                );
              })
            }
          </ View >

        </ View >
      );
    }

}

export default CalendarPanel;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF00',
    alignItems: 'center'
  }
});