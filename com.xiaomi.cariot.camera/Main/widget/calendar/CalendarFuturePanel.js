import ImageButton from 'miot/ui/ImageButton';
import React from 'react';
import {
  View,
  Text,
  Image,
  Modal,
  Easing,
  ViewPropTypes,
  StyleSheet,
  ScrollView,
  TouchableOpacity
} from 'react-native';
import CameraConfig from '../../util/CameraConfig';


import LinearGradient from 'react-native-linear-gradient';
import Toast from 'react-native-root-toast';
import { PropTypes } from 'victory-core/es';
import { localStrings as LocalizedStrings } from '../../MHLocalizableString';
import { Host } from "miot";
import dayjs from 'dayjs';
import Util from "../../util2/Util";

let signArr = [];

const nowDate = new Date(new Date().setHours(0, 0, 0, 0));
const colors = {
  themecolor: "gray",
  selected: "gray",
  normal: "light"
};
const Metrics = {
  CH: 100,
  CW: 100
};

/**
 * @Author: byh
 * @Date: 2024/6/3
 * @explanation:
 * 可选今天及未来日期
 * 2038年1月19号之后的日期不可选
 *********************************************************/
class CalendarFuturePanel extends React.Component {
  static propTypes = {
    y: PropTypes.number,
    m: PropTypes.number,
    d: PropTypes.number,
    cy: PropTypes.number,
    cm: PropTypes.number,
    cd: PropTypes.number,
    visible: PropTypes.bool,
    onDateChanged: PropTypes.func,
    ...ViewPropTypes
  };


  constructor(props) {
    super(props);
    this.isEuropeServer = CameraConfig.getIsEuropeServer();
    this.initState(this.props.y, this.props.m, this.props.d, this.props.selDay);
  }

  initState(y, m, d) {
    this.setState({
      y: y,
      m: m,
      d: d,
      clickNum: undefined
    });
    // console.log('calendar panel input: ', this.props.y, this.props.m + 1, this.props.d);
  }

  // 获得上一个月的第一天
  DateStr() {
    let rr = this.props.m + 1;
    return `${ this.props.y }-${ rr }-${ 1 }`;
  }

  MonthInfo() {
    // console.log('转化成字符串后的时间', this.DateStr())
    let Nowdate = (this.DateStr()).split("-"), // 获取某一日期
      NowYear = parseInt(Nowdate[0]), // 某年份
      NowMonth = parseInt(Nowdate[1]), // 某月份
      Nowday = parseInt(Nowdate[2]), // 某天
      date = new Date();
    // console. log( '改变的月份', NowMonth)
    // 顶部标题样式
    let title = `${ NowYear } - ${ NowMonth }`;
    // 获取当前月份第一天为周几
    date.setFullYear(NowYear); // 把当前年份设为某年
    date.setMonth(NowMonth - 1); // 把当前月份设为某月
    date.setDate(Nowday);
    let weeks = [0, 1, 2, 3, 4, 5, 6];
    // getDay获取当前月份为星期几
    let date1 = weeks[date.getDay()];

    // 获取当前月份天数
    let days = this.getMonthDays(NowYear, NowMonth);
    return [title, date1, days];
  }

  getMonthDays(year, month) {
    let thisDate = new Date(year, month, 0); // 当天数为0 js自动处理为上一月的最后一天
    return thisDate.getDate();
  }

  componentWillMount() {

  }

  // 获取当前日期
  getD = () => {
    let date = new Date();
    let d = date.getDate();
    return d;
  };

  dateArr = () => {
    console.log('get dateArr', this.props.y, this.props.m, this.props.d);
    let arr = [];
    let arr2 = [];
    let lastM = this.props.m == 0 ? 11 : this.props.m - 1;
    let nextM = this.props.m == 11 ? 0 : this.props.m + 1;
    let lastY = this.props.m === 0 ? this.props.y - 1 : this.props.y;
    let nextY = this.props.m === 11 ? this.props.y + 1 : this.props.y;
    let lastMDs = this.getMonthDays(lastY, lastM + 1);
    let nextMDs = this.getMonthDays(nextY, nextM + 1);
    // console.log("this "+ this.props.y + '-' + (this.props.m+1) + ' monthinfo: ' + this.MonthInfo()[1]);
    // console.log("landing last"+ lastY + '-' + (lastM + 1) + ' : '+lastMDs );
    // console.log("landing next "+ nextY + '-' + (nextM + 1) + ' : '+nextMDs );
    for (let i = 0; i < this.MonthInfo()[1]; i++) {
      let day = [lastY, lastM + 1, 0, 0, lastMDs + i - this.MonthInfo()[1] + 1, 0, 0];
      this.setSelected(day);
      arr.push(day); // (year, month, month(0:pre,1:current month / date with data,2:next, 3: date no data), selected(0:no, 1:yes), day) day的结构定义
    }
    console.log('get arr1', arr);
    for (let i = 1; i < this.MonthInfo()[2] + 1; i++) {
      let day = [this.props.y, this.props.m + 1, 0, 0, i, 1, 0];
      day = this.setSelected(day);
      arr.push(day);
    }

    if ((this.MonthInfo()[1] + this.MonthInfo()[2]) % 7 !== 0) {
      for (let i = 0; i < 7 - ((this.MonthInfo()[1] + this.MonthInfo()[2]) % 7); i++) {
        let day = [nextY, nextM + 1, 2, 0, i + 1, 0, 0];
        day = this.setSelected(day);
        arr.push(day);
      }
    }

    console.log('get arr3', arr);
    this.setDayInInterval(arr);
    console.log('get arr4', arr);
    this.setDayWithData(arr);
    console.log('get arr5', arr);
    return arr;
  };

  setDayWithData(arr) {
    // if (this.props.dates == null) return;
    arr.forEach((day) => {
      let date = new Date(Date.parse(`${ day[0] }/${ day[1] }/${ day[4] }`));
      let dateEnd = new Date(Date.parse(`2038/1/19`));
      if (date >= nowDate && date <= dateEnd) {
        day[2] = 1;
        day[5] = 1;
      } else {
        day[2] = 3;
        day[5] = 0;
      }
      let day1 = dayjs(nowDate);
      let day2 = dayjs(date);
      let same = day1.isSame(day2, 'day');
      if (same) {
        day[6] = 1;
      }
    });
  }

  isInDates(day) {

    let date = new Date(Date.parse(`${ day[0] }/${ day[1] }/${ day[4] }`));
    // nowDate
    console.log("==============", date, nowDate, date >= nowDate);
    if (date >= nowDate) {
      day[2] = 1;
    } else {
      day[2] = 3;
    }
    // let find = false;
    // for (let i in this.props.dates) {
    //   let timestamp = this.props.dates[i];
    //   let day1 = dayjs(timestamp);
    //   let day2 = dayjs(date);
    //   let same = day1.isSame(day2, 'day');
    //   if (same) {
    //     day[2] = 1;
    //     find = true;
    //     break;
    //   }
    // }
    // if (!find) {
    //   day[2] = 3;
    // }
  }


  setDayInInterval(arr) {
    if (this.props.interval == -1) return;
    let now = new Date();
    arr.forEach((day) => {
      let date = new Date(Date.parse(`${ day[0] }/${ day[1] }/${ day[4] }`));
      let diff = now - date;
      if (diff > 0 && diff < this.props.interval) {
        day[2] = 1;
      } else {
        day[2] = 3;
      }
    });
  }

  setSelected(day) {
    // if (this.props.y == day[0] && this.props.m + 1 == day[1] && this.props.d == day[4]) {
    if (this.props.y == this.props.y && this.props.y == day[0] && this.props.m + 1 == this.props.cm && this.props.m + 1 == day[1] && this.props.d == this.props.cd && this.props.d == day[4]) {
      day[3] = 1;
    } else {
      day[3] = 0;
    }
    return day;
  }

  update(newDate, from) {
    console.log(from, newDate);
    this.initState(newDate.getFullYear(), newDate.getMonth(), newDate.getDate());
  }

  render() {
    console.log('panel', this.MonthInfo()[0]);
    return (
      < View style={ [styles.container, { width: this.props.width }] }>
        {/* < View style= {{ justifyContent: 'center', flexDirection: 'row', height: 19, alignItems: 'center', marginTop: 7 } } >
            < Text style= {{ fontSize: 16, color: '#666666', fontWeight: '500' } } > {this.MonthInfo()[0] } </ Text >
          </ View > */ }

        <View style={ {
          width: '100%',
          justifyContent: 'space-around',
          alignItems: 'center',
          flexDirection: 'row',
          marginTop: 29
        } }>
          {
            [LocalizedStrings['sun'], LocalizedStrings['mon'], LocalizedStrings['tue'], LocalizedStrings['wed'], LocalizedStrings['thu'], LocalizedStrings['fri'], LocalizedStrings['sat']].map((item, index) => {
              return (
                <Text style={ { fontSize: 13, color: "#999999", fontWeight: '500' } } key={ index }> { item } </Text>
              );
            })
          }
        </View>

        < View style={ {
          width: '100%',
          justifyContent: 'space-around',
          alignItems: 'center',
          flexDirection: 'row',
          flexWrap: 'wrap',
          marginTop: 22
        } }>
          {
            this.dateArr().map((items, nums) => {
              // let bColor = items[3] == 0 ? colors.normal : colors.selected;
              let bColor = colors.selected;
              return (
                <TouchableOpacity
                  onPress={ () => {
                    console.log(`selected: ${ items[0] } ${ items[1] } ${ items[2] } ${ items[3] } ${ items[4] }`);// items is the day index in the month, nums is the index of
                    // this.props.navigation.state.params.onDateChanged(items);
                    if (items[2] == 3) {

                    } else {
                      this.props.onDateChanged(items);
                    }
                  } }
                  style={ { width: '14.28%', marginBottom: 4, justifyContent: 'center', alignItems: 'center' } }
                  key={ nums }>
                  {/*< View style= {{ width: 40, height: 40, borderRadius: 20, justifyContent: 'center', alignItems: 'center', backgroundColor: items[3] == 1 ? '#EEEEEE' : 'white' } } >*/ }
                  <View style={ {
                    width: 40,
                    height: 40,
                    borderRadius: 20,
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: items[3] == 1 ? '#32BAC0' : 'white'
                  } }>

                    <Text style={ {
                      fontSize: Util.isLanguageCN() ? 18 : 12,
                      color: items[3] == 1 ? '#FFFFFF' : items[5] === 0 ? '#CCCCCC' : '#000000'
                    } }> { items[6] == 1 ? LocalizedStrings['calendar_today'] : items[4] } </Text>
                    {
                      items[6] == 1 && items[3] != 1 ?
                        <View style={ {
                          width: 4,
                          height: 4,
                          borderRadius: 2,
                          backgroundColor: '#32BAC0',
                          marginTop: 2
                        } }/> : null
                    }

                  </View>
                </TouchableOpacity>
              );
            })
          }
        </View>

      </View>
    );
  }

}

export default CalendarFuturePanel;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF00',
    alignItems: 'center'
  }
});