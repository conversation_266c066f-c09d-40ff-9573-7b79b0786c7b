import React from 'react';
import EventGrid, { FooterH } from "./EventGrid";
import { View } from "react-native";
import EventCard, { CardHeight, CardMB } from "./EventCard";
import { BaseStyles } from "../BasePage";
import { LoadStatus } from "./EventList";
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import { Order } from "../framework/EventLoaderInf";
import Separator from 'miot/ui/Separator';
import Util from "../util2/Util";
import dayjs from 'dayjs';

/*
UI data form
[
section0 = {
  title: XXX,
  data:[itm1, itm2, itm3]
}
.....
sectionX = {
  title: XXX,
  data:[itm1, itm2, itm3]
}
]
*/
const TAG = "EventSectionList";
export const EvSectionFilter = (aEv) => {
  return false;
};

export function evList2SectionList(aEvL) {
  let sectionLst = [];
  let dic = {};
  for (let i = 0; i < aEvL.length; i++) {
    let item = aEvL[i];
    buildSection(dic, item);
  }
  for (let key in dic) {
    let section = {
      title: key,
      data: dic[key],
      selected: false
    };
    sectionLst.push(section);
  }
  return sectionLst;
}

function buildSection(aSecDict, aItm, aOrder = Order.Desc) {

  let dStr = dayjs.unix(aItm.createTime / 1000).format(LocalizedStrings["yyyymmdd"]);
  if (Util.isToday(dayjs.unix(aItm.createTime / 1000))) {
    dStr = `${ dStr } | ${ LocalizedStrings['today'] }`;
  } else if (Util.isYestoday(dayjs.unix(aItm.createTime / 1000))) {
    dStr = `${ dStr } | ${ LocalizedStrings['yestoday'] }`;
  }

  let sec = aSecDict[dStr];
  if (!sec) {
    console.log(TAG, "buildSection create", dStr);
    aSecDict[dStr] = [aItm];
    return dStr;
  } else {
    if (Order.Asc == aOrder) {
      sec.unshift(aItm);
    } else {
      sec.push(aItm);
    }

    return null;
  }
}

export default class EventSectionListTurbo extends EventGrid {
  constructor(aProps) {
    super(aProps);
    this.mCardH = CardHeight + CardMB;
    this.mSecHeaderBg = BaseStyles.mainBg.backgroundColor;
    // console.log(TAG, "constructor !!!!!!!!!", super.mLayoutGetter);

    let order = this.props.loaderArgs.order ? this.props.loaderArgs.order : Order.Desc;
    let nextDateS = null;
    let startDateS = null;
    let loadingStatusS = null;
    if (Order.Desc == order) {
      let next = this.state.nextDate;
      nextDateS = { [Order.Desc]: this.state.nextDate, [Order.Asc]: new Date(next.getFullYear(), next.getMonth(), next.getDate(), 23, 59, 59) };
      // reset  loading status for both direction
      loadingStatusS = { [Order.Desc]: LoadStatus.Idle, [Order.Asc]: LoadStatus.Idle };
    } else {
      throw "not  support";
    }
    this.state = Object.assign({}, { nextDateS, loadingStatusS }, this.state);
    console.log(TAG, "constructor !!!!!!!!!", this.state);
  }

  getInitScroll() {
    let ev = this.state.events;
    if (this.props.playingId) {
      for (let sectionIndex = 0; sectionIndex < ev.length; ++sectionIndex) {
        let section = ev[sectionIndex].data;
        for (let itemIndex = 0; itemIndex < section.length; ++itemIndex) {
          let itm = section[itemIndex];
          if (itm.fileId === this.props.playingId) {
            return { animated: false, itemIndex, sectionIndex, viewOffset: 0, viewPosition: 0 };
          }
        }
      }
    }
    return null;
  }

  // reverse loading not work
  getEventList(date, event, isMore = false, aOrder = Order.Desc) {
    let now = Date.now();
    let reNonce = `${ now }${ Math.random(now) }`;
    this.mReNonce = reNonce;
    this.mLoader.getAllEvent(date, event, isMore, aOrder)
      .then((data) => {
        console.log(TAG, date, "nonce", "isMore", isMore,
          "length", data.items.length, "hasMore", data.hasMore, "nextDate", data.nextTime);
        let nextDateS = Object.assign(this.state.nextDateS, { [aOrder]: data.nextTime });
        if (this.mReNonce === reNonce
          || (!isMore && date.getTime() == this.props.loaderArgs.startDate.getTime()) /* for fastloading */) {
          let oldItems = isMore ? this.state.events : [];
          data.items = this.props.dataFilter ? data.items.filter(this.props.dataFilter) : data.items;
          this.downloadFileThump(data.items);

          if (data.items && data.items.length > 0) {
            // let items = this.mergeEvents(oldItems, groupedItems);

            let items = this.appendEvents(oldItems, data.items, aOrder);
            let clds = (data.hasMore == true) ? LoadStatus.Idle : LoadStatus.Finish;
            let loadingStatus = Order.Desc == aOrder ? clds : LoadStatus.Idle;

            this.setState({
              loadingStatusS: Object.assign(this.state.loadingStatusS, { [aOrder]: clds }),
              events: items,
              nextDateS,
              loadingStatus
            });
            console.log(TAG, "loadComplete", loadingStatus);
          } else {
            // no more data
            let clds = LoadStatus.Finish;
            let loadingStatus = Order.Desc == aOrder ? clds : this.state.loadingStatusS[Order.Desc];
            this.setState({ loadingStatusS: Object.assign(this.state.loadingStatusS, { [aOrder]: clds }),
              nextDateS,
              loadingStatus
            });
          }
        } else {
          console.log(TAG, "drop obselete nonce", reNonce, "vs", this.mReNonce);
        }
      })
      .catch((error) => {
        console.log(TAG, "getEventList got error", error);
      });
  }

  appendEvents(aGroups, aItms, aOrder) {
    if (Order.Desc == aOrder) {
      return super.appendEvents(aGroups, aItms);
    } else {
      let order = [];
      let dic = {};
      let dicSel = {};
      if (aGroups.length > 0) {
        // use last for fill
        let grp = aGroups.shift();
        order.push(grp.title);
        console.log(TAG, "add exist grp", grp.title);
        dic[grp.title] = grp.data;
        dicSel[grp.title] = grp.selected;
      }
      for (let i = 0; i < aItms.length; i++) {
        let item = aItms[i];
        let newSec = this.buildSection(dic, item, aOrder);
        if (newSec != null) {
          order.unshift(newSec);
        }
      }

      for (let ol = order.length - 1; ol > -1; --ol) {
        let key = order[ol];
        let groupItem = {
          title: key,
          data: dic[key],
          selected: dicSel[key] ? dicSel[key] : false
        };
        console.log(TAG, "add grp", key);
        aGroups.unshift(groupItem);
      }
      return aGroups;
    }
  }



  applyFilter(aEvs, aFilter) {
    let curEv = aEvs;
    for (let sec of curEv) {
      sec.data = sec.data.filter(aFilter);
    }
    return curEv;
  }

  getAdjacentEvent(aEv) {
    let curEv = this.state.events;
    for (let i = 0; i < curEv.length; ++i) {
      let evGrp = curEv[i].data;
      let idx = evGrp.findIndex((aItm) => {
        return aItm.fileId == aEv.fileId;
      });
      if (idx != -1) {
        if (idx + 1 < evGrp.length) {
          return evGrp[idx + 1];
        } else {
          // check net grp
          if (i + 1 < curEv.length) {
            return curEv[i + 1].data[0];
          }
          // look back
          else if (idx - 1 >= 0) {
            return evGrp[idx - 1];
          }
          // pre section
          else if (i > 0) {
            return curEv[i - 1].data[0];
          } else {
            return null;
          }
        }
      }

    }
  }


  buildSection(aSecDict, aItm, aOrder) {
    return buildSection(aSecDict, aItm, aOrder);
  }


  mRefresh = () => {
    // current do nothing
    console.log(TAG, "mRefresh", this.state);
    let lds = this.state.loadingStatusS[Order.Asc];
    if (lds != LoadStatus.Finish) {
      this.getEventList(this.state.nextDateS[Order.Asc], this.getEventFilter(), true, Order.Asc);
    } else {
      // fallback to refresh from head
      this.getEventList(this.state.startDate, this.getEventFilter(), false);
    }

  }

  mOnEnd = () => {
    console.log(TAG, "mOnEnd", this.state);
    if (LoadStatus.Finish === this.state.loadingStatusS[Order.Desc] || LoadStatus.Loading === this.state.loadingStatus) {
      console.log(TAG, "onEndReached skip loading for status", this.state.loadingStatus);
    } else {
      let date = this.state.nextDateS[Order.Desc];
      this.getEventList(date, this.props.loaderArgs.filter, true, Order.Desc);
      this.setState({ loadingStatus: LoadStatus.Loading });
    }
  }


  mRItem = ({ section, index }) => {
    // console.log(TAG, "mRItem aaa", section, "idx", index);
    let item = section.data[index];
    return (
      <EventCard item={item}
        isPlaying = {this.props.playingId === item.fileId}
        cardPressed={(aItm) => {

          // this.naviTo('AlarmDetail', { item: item, event: this.state.selectedEventKey });
          item.isRead = true;
          let events = this.state.events;
          let nextDate = this.state.nextDate;
          this.props.onEventPress(item, { events, nextDate });
        }}/>);
  }

  mRSecF = () => {
    return (<View style={{ height: FooterH, paddingHorizontal: 25, paddingBottom: 4, justifyContent: "center" }}>
      <Separator/>
    </View>
    );
  }
}
