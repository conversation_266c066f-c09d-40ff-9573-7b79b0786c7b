import React from 'react';
import { Text, Image, StyleSheet, View, TouchableOpacity, TouchableWithoutFeedback } from 'react-native';
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import { SCREEN_WIDTH } from '../util2/Const';
import Util from '../util2/Util';
import { BaseStyles } from "../BasePage";
import LinearGradient from 'react-native-linear-gradient';
import { DescriptionConstants } from '../Constants';
import { Radius, styles } from "micariot-ui-sdk";
import { CarFont } from "../car/common/Styles";
import { DarkMode } from "miot";
import DateFormater from "../util2/DateFormater";

const horizontalPadding = 20;
const interSpace = 10;
const columnNum = 5;
const USEFUL_WIDTH = 1152;
// export const CardWidth = Math.floor((USEFUL_WIDTH - horizontalPadding * 2 - interSpace * (columnNum - 1)) / columnNum);
export const CardWidth = 208;
// export const CardHeight = 174 + 25;// Math.floor(CardWidth * 64 / 99) + 34;
const ITEM_PADDING_TOP = 18;
const ITEM_PADDING_BOTTOM = 24;
// export const CardHeight = 154 + ITEM_PADDING_TOP + ITEM_PADDING_BOTTOM;// Math.floor(CardWidth * 64 / 99) + 34;
export const CardHeight = 147 + ITEM_PADDING_TOP+ ITEM_PADDING_BOTTOM;// Math.floor(CardWidth * 64 / 99) + 34;
const TAG = "EventGridCardCar";
export default class EventGridCardCar extends React.Component {

  constructor(props) {
    super(props);
  }

  render() {
    const item = this.props.item;
    const imgStoreUrl = item.imgStoreUrl;
    let imgSource = imgStoreUrl;
    if (imgStoreUrl) {
      if (Util.isLocalThumb(imgStoreUrl, item.mediaType)) {
        imgSource = { uri: imgStoreUrl };
      } else {
        imgSource = { uri: `file://${ imgStoreUrl }` };
      }
    }
    let evTypeIc = Util.getSmallIconFromType(item.type);
    // console.log(TAG, "selected", item.selected);
    // let imgSource = imgStoreUrl == null ? null : { uri: `file://${ imgStoreUrl }` };
    let selectIconSource = require('../../Resources/Images/car/car_video_unselect.png');
    if (item.selected) {
      selectIconSource = require('../../Resources/Images/car/car_video_select.png');
    }
    let durStr = null;
    if (item.duration) {
      // durStr = `${ Util.zeroPad(Math.floor(item.duration / 60), 10) }:${ Util.zeroPad(Math.floor(item.duration % 60), 10) }`;
      durStr = DateFormater.instance().timeToString(item.duration);
    }
    let typeIcon = (item.mediaType === "image") ? require("../../resources2/images/icon_type_image.png")
      : (item.mediaType === "sdcard" ? require("../../Resources/Images/icon_type_folder.png") : require("../../resources2/images/icon_type_video.png"));
    // console.log("Card", "duration", item.duration);
    typeIcon = require('../../Resources/Images/car/car_type_video.png');
    return (<TouchableOpacity
      style={ [itemStyles.container, this.props.style] }
      onPress={ () => {
        this.props.cardPressed && this.props.cardPressed(item);
      } }
      onLongPress={ () => {
        console.log("=============long press");
        this.props.cardLongPressed && this.props.cardLongPressed(item);
      } }
      activeOpacity={ this.props.isEditing ? 1 : 0.2 }
    >

      <Image
        style={ [itemStyles.img, { backgroundColor: DarkMode.getColorScheme() === 'dark' ? "xm#C1CCE21F" : "#EEEEEE" }] }
        source={ imgSource }
        accessibilityLabel={ DescriptionConstants.lc_35 }
      />
      {
        durStr ?
          <LinearGradient colors={ ['#00000000', '#00000000'] } style={ [itemStyles.img, {
            position: 'absolute',
            bottom: 54,
            left: 0,
            height: '70%',
            borderRadius: 9
          }] }>
            <View style={ [BaseStyles.row, {
              justifyContent: "flex-start",
              paddingLeft: 4,
              position: 'absolute',
              alignItems: 'center',
              bottom: 10,
              left: 15
            }] }>

              <Image style={ { height: 16, width: 23 } } source={ typeIcon }/>
              <Text style={ [CarFont.Size._22, { marginLeft: 6, color: "white" }] }
                    accessibilityLabel={ DescriptionConstants.yc_8 + "00:" + durStr }>{ durStr }</Text>
            </View>
          </LinearGradient>
          :
          <Image style={ [{ height: 16, width: 23 } , { position: "absolute", bottom: 68, left: 15 }] } source={ typeIcon }/>
      }


      { this.props.isEditing ?
        <Image
          style={ [itemStyles.icon, { top: 10 + 25, right: 10 }] }
          accessibilityLabel={ this.props.item.selected ? DescriptionConstants.selected : DescriptionConstants.unSelected }
          source={ selectIconSource }/> : null }

      <View
        style={ [BaseStyles.row, { justifyContent: "flex-start", alignItems: "center", marginTop: 8, height: 30 }] }>
        {
          evTypeIc ? <Image style={ { marginRight: 2, width: 12, height: 12 } } source={ evTypeIc }/> :
            <View style={ { paddingLeft: 7 } }/>
        }
        <Text
          style={ [styles.itemTextStyle, { fontSize: 22, marginTop: 8 }] }
          accessibilityLabel={ DescriptionConstants.rp_65 + item.eventTime }>{ item.eventTime }</Text>
      </View>
    </TouchableOpacity>);
  }

}

const itemStyles = StyleSheet.create({
  container: {
    width: CardWidth,
    height: CardHeight,
    paddingTop: ITEM_PADDING_TOP
  },

  img: {
    width: CardWidth,
    height: 117,
    borderRadius: Radius.WidgetLevel
  },
  icon: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    width: 40,
    height: 40
  },

  typeIcon: {
    position: 'absolute',
    bottom: 4,
    left: 4,
    height: 14,
    width: 14
  }
});
