import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  FlatList,
  Image,
  Platform,
  ScrollView,
  TouchableOpacity,
  DeviceEventEmitter, Dimensions
} from "react-native";
import {  Device, Service } from 'miot';
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import EventCard, { CardHeight, CardMB } from "./EventCard";
import { BaseStyles } from "../BasePage";
import { DldStatus, Order } from "../framework/EventLoaderInf";
import { CldDldTypes } from "../framework/CloudEventLoader";
import Util from "../util2/Util"
import { DescriptionConstants } from '../Constants';
import AlarmUtilV2 from "../util/AlarmUtilV2";
import StackNavigationInstance, { SD_CLOUD_FORCE_LOAD_TAB_DATA } from "../StackNavigationInstance";
import CameraConfig from "../util/CameraConfig";
import StorageKeys from "../StorageKeys";
import Toast from "../components/Toast";
import TrackUtil from "../util/TrackUtil";
export const LoadStatus = {
  Idle: "Idel",
  Finish: "Finish",
  Failed: "Failed",
  Loading: "Loading"
};
const TAG = "EventList";
export const DefFilter = "Default";
const ListFooterH = Platform.OS == "ios" ? 20 : 10;
const kWindowHeight = Math.max(Dimensions.get("window").height, Dimensions.get("window").width);
const kPxScale = Math.min(kWindowHeight / 896, 1); // 以iphone11的高度为设计标准高度, 目前设计也以之为尺寸上限

export default class BuyCloudIntroView extends React.Component {

  static defaultProps = {
    eventHeaderView: () => { return null; },
    eventHeaderHeight: 0,

  }
  constructor(aProps) {
    super(aProps);

    this.state = {

    };
  }

  componentDidMount() {

  }


  componentWillUnmount() {

  }


  render() {
    let item1 = {
      source: Util.isDark() ? require("../../Resources/Images/icon_cloud_buy_save_light.png") : require("../../Resources/Images/icon_cloud_buy_save.png"),
      title: LocalizedStrings["cloud_buy_benefit_storage"],
      text: LocalizedStrings["cloud_buy_benefit_storage_desc"]
    };
    let item2 = {
      source: Util.isDark() ? require("../../Resources/Images/icon_cloud_buy_notice_light.png") : require("../../Resources/Images/icon_cloud_buy_notice.png"),
      title: LocalizedStrings["cloud_buy_benefit_notice"],
      text: LocalizedStrings["cloud_buy_benefit_notice_desc"]
    };
    let item3 = {
      source: Util.isDark() ? require("../../Resources/Images/icon_cloud_buy_encryt_light.png") : require("../../Resources/Images/icon_cloud_buy_encryt.png"),
      title: LocalizedStrings["cloud_buy_benefit_data"],
      text: LocalizedStrings["cloud_buy_benefit_data_desc"]
    };
    let item4 = {
      source: Util.isDark() ? require("../../Resources/Images/icon_cloud_buy_space_light.png") : require("../../Resources/Images/icon_cloud_buy_space.png"),
      title: LocalizedStrings["cloud_buy_benefit_space"],
      text: LocalizedStrings["cloud_buy_benefit_space_desc"]
    };
    let stateCover = {
      position: 'absolute',
      width: '100%',
      height: '100%',
      display: "flex",
      // alignItems: "flex-start",
      // justifyContent: 'space-evenly',
      marginTop: (this.isEuropeServer ? 30 : 55) * kPxScale,
      marginLeft: 10
    };
    let stateCoverTitle = {
      textAlign: "left",
      textAlignVertical: "center",
      marginLeft: 20
    };
    let benefits = [LocalizedStrings['cloud_buy_benefit_name'], LocalizedStrings['cloud_buy_benefit_time'], LocalizedStrings['cloud_buy_benefit_interval'], LocalizedStrings['cloud_buy_benefit_deadline'], LocalizedStrings['cloud_buy_benefit_detect'], LocalizedStrings['ss_daily_story']];
    let free = [LocalizedStrings['cloud_buy_benefit_name_free'], LocalizedStrings['cloud_buy_benefit_time_9'], LocalizedStrings['cloud_buy_benefit_interval_3'], LocalizedStrings['cloud_buy_benefit_deadline_7'], "-", "-"];
    let join = [LocalizedStrings['cloud_buy_benefit_name_join'], LocalizedStrings['cloud_buy_benefit_time_no_limit'], LocalizedStrings['cloud_buy_benefit_interval_no'], LocalizedStrings['cloud_buy_benefit_deadline_30'], LocalizedStrings['camera_face'], "√"];
    let isIOS = Platform.OS === 'ios';
    let isLanCN = Util.isLanguageCN();
    let textWidth = isLanCN ? 160 : 250;
    let titleFontsize = this.isEuropeServer ? 31 * kPxScale : isLanCN ? 33 : 31; // 海外都是英文xiaomi home secure, 所以都随尺寸变化
    let titleLineHeight = this.isEuropeServer ? 37 * kPxScale : 37;
    return (
      <View style={{ flex: 1, flexDirection: 'column', width: '100%', height: '100%', alignItems: "center"}}>
        <ScrollView style={{ display: "flex", width: "100%", height: "100%", flexGrow: 1 }}>
          <View style={{ height: 178, marginHorizontal: 24, marginTop: 24 }}>
            <Image source={require("../../Resources/Images/guide_cloud_buy_head.png")} style={{ height: 176, width: "100%" }} />
          </View>
          <Text style={{ marginHorizontal: 28, marginTop: 24, color: 'rgba(0, 0, 0, 0.60)', fontSize: 14, fontWeight: "400" }}>{LocalizedStrings['cloud_buy_desc']}</Text>
          <Text style={{ marginHorizontal: 28, marginTop: 40, color: '#000000', fontSize: 18, fontWeight: "bold" }}>{LocalizedStrings['cloud_buy_benefit']}</Text>
          <FlatList
            data={[item1, item2, item3, item4]}
            renderItem={({ item, index }) => this._renderBuyViewItem2(item, index)}
            numColumns={2} // 设置为2列
            keyExtractor={(item,index) => `buy_bnf_${index}`}
            contentContainerStyle={{ flexGrow: 1, marginLeft: 28, marginTop: 10 }}
          />
          <Text style={{ marginHorizontal: 28, marginTop: 30, color: '#000000', fontSize: 18, fontWeight: "bold" }}>{LocalizedStrings['cloud_buy_benefit_compare']}</Text>
          <View style={{ borderRadius: 20, borderWidth: 1, borderColor: Util.isDark() ? 'xm#333333' : '#0000000F', marginHorizontal: 28, marginTop: 20, flexDirection: "row", marginBottom: 119 }}>
            <View style={{ flex: 1 }}>
              <FlatList
                data={benefits}
                renderItem={({ item, index }) => this._renderBuyViewItemBenefit(item, index)}
                keyExtractor={(item,index) => `benefits_${index}`}
                contentContainerStyle={{ flexGrow: 1, flex: 1 }}
              />
            </View>
            <View style={{ flex: 1 }}>
              <FlatList
                data={free}
                renderItem={({ item, index }) => this._renderBuyViewItemFree(item, index)}
                keyExtractor={(item,index) => `free_${index}`}
                contentContainerStyle={{ flexGrow: 1, backgroundColor: "rgba(0, 0, 0, 0.04)", flex: 1 }}
              />
            </View>
            <View style={{ flex: 1 }}>
              <FlatList
                key={3}
                data={join}
                renderItem={({ item, index }) => this._renderBuyViewItemJoin(item, index, join)}
                keyExtractor={(item,index) => `join_${index}`}
                contentContainerStyle={{ flexGrow: 1, flex: 1 }}
              />
            </View>
          </View>
        </ScrollView>
        <View style={{ position: 'absolute', bottom: 0, width: "100%" }}>
          <View style={{ display: "flex", flexDirection: "row", paddingHorizontal: 28, backgroundColor: '#FFFFFF', paddingBottom: 30, paddingTop: 12 }}>
            <TouchableOpacity
              style={{ flex: 1, minHeight: 46, justifyContent: 'center', alignItems: 'center', borderRadius: 23, backgroundColor: '#F5F5F5', marginRight: 12 }}
              onPress={() => { // cancel
                if (Device.isReadonlyShared) {
                  Toast.success("share_user_permission_hint");
                  return;
                }
                if (!Device.isOnline) {
                  Toast.success('device_offline');
                  return;
                }
                console.log('click_buy_cloud clicked, Storage_CloudStorage_Purchase_ClickNum');
                // 打开看家开关
                AlarmUtilV2.openMotionDetectionSwitch().then((res) => {
                  // 开启了免费看家
                  this.props.onFreeCloudOpen();
                }).catch((error) => {
                  Toast.fail("action_failed");
                });
                // 埋点--免费试用点击
                TrackUtil.reportClickEvent("Monitoring_CloudStorageIntroduce_FreeTrial_ClickNum")
              }}>

              <Text style={[BaseStyles.text16, { color: Util.isDark() ? 'xm#ffffffe7' : '#000000', fontWeight: '500', textAlign: 'center' }]} > {LocalizedStrings['cloud_buy_benefit_name_free']} </ Text >
            </TouchableOpacity>
            <TouchableOpacity
              style={{ flex: 1, minHeight: 46, justifyContent: 'center', alignItems: 'center', borderRadius: 23, backgroundColor: '#32BAC0' }}
              onPress={() => { // cancel
                if (!Device.isOwner) {
                  Toast.success("share_user_permission_hint");
                  return;
                }
                console.log('click_buy_cloud clicked, Storage_CloudStorage_Purchase_ClickNum');
                TrackUtil.reportClickEvent("Storage_CloudStorage_Purchase_ClickNum"); // Storage_CloudStorage_Purchase_ClickNum
                Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: "stroagemgt_button_v1" });
                this.props.onBuyCloudPress();
                // 完整服务点击
                TrackUtil.reportClickEvent("Monitoring_CloudStorageIntroduce_CompleteService_ClickNum")
              }}>
              <Text style={[BaseStyles.text16, { color: Util.isDark() ? 'xm#ffffffe7' : '#ffffff', fontWeight: '500', textAlign: 'center' }]} > {LocalizedStrings['bug_all_server']} </ Text >
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  _renderBuyViewItem2(item, idx) {
    let panelOptionItemLayout = {
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
      height: Util.isLanguageCN() ? 68 : 108,
    };
    return (
      <View key={`bnf_${idx}`} style={{ flex: 1, marginRight: 28 }}>
        <View
          style={panelOptionItemLayout}
        >
          <Image
            style={{ width: 38, height: 38, position: "relative" }}
            source={item.source}
          />

          <View style={{ display: "flex", height: "100%", flex: 1, position: "relative", flexDirection: "column", justifyContent: "center" }}>
            <Text numberOfLines={3}
                  style={[BaseStyles.text14, { marginLeft: 13, marginTop: 0, color: "#000000", fontWeight: "bold", lineHeight: 20 }]}
            >
              {item.title}
            </Text>

            <Text
              style={[BaseStyles.text12, { marginLeft: 13, marginTop: 4, color: "#999999", fontWeight: "400", lineHeight: 16 }]}
              numberOfLines={5}
              ellipsizeMode={"tail"}
            >
              {item.text}
            </Text>
          </View>
        </View>
      </View>
    );
  }
  _renderBuyViewItemBenefit(item, idx) {
    let firstStyle = null;
    let titleTextStyle = null;
    if (idx === 0) {
      firstStyle = { backgroundColor: "rgba(0, 0, 0, 0.04)", borderTopLeftRadius: 20 };
      titleTextStyle = { fontSize: 14, color: "rgba(0, 0, 0, 0.50)", fontWeight: "bold" };
    }
    return (
      <View style={[{ alignItems: 'center', height: 50, justifyContent: 'center' }, firstStyle]} key={`one_${idx}`}>
        <Text style={[{ fontSize: 12, color: "rgba(0, 0, 0, 0.50)", fontWeight: "400" }, titleTextStyle]}>{item}</Text>
      </View>
    );
  }

  _renderBuyViewItemFree(item, idx) {
    let firstStyle = null
    let titleTextStyle = null;

    if (idx === 0) {
      firstStyle = { backgroundColor: "rgba(0, 0, 0, 0.1)" };
      titleTextStyle = { fontSize: 14, color: "#000000", fontWeight: "bold" };

    }
    return (
      <View style={[{ alignItems: 'center', height: 50, justifyContent: 'center' }, firstStyle]} key={`two_${idx}`}>
        <Text style={[{ fontSize: 12, color: "#000000", fontWeight: "400" }, titleTextStyle]}>{item}</Text>
      </View>
    );
  }

  _renderBuyViewItemJoin(item, idx, join) {
    let firstStyle = null
    let titleTextStyle = null;
    if (idx === 0) {
      firstStyle = { backgroundColor: "rgba(50, 186, 192, 0.20)", borderTopRightRadius: 20 };
      titleTextStyle = { fontSize: 14, color: "#000000", fontWeight: "bold" };
    }
    let lastStyle = null
    if (idx === join.length - 1) {
      lastStyle = { borderBottomRightRadius: 20 }
    }
    return (
      <View style={[{ alignItems: 'center', height: 50, justifyContent: 'center', backgroundColor: "rgba(50, 186, 192, 0.10)" }, firstStyle, lastStyle]} key={`three_${idx}`}>
        <Text style={[{ fontSize: 12, color: "#32BAC0", fontWeight: "400" }, titleTextStyle]}>{item}</Text>
      </View>
    );
  }


  componentDidUpdate(aPrevProps) {

  }
}


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BaseStyles.mainBg.backgroundColor
  },
  listContainer: {
    borderRadius: 10,
    backgroundColor: 'white',
    marginTop: 10,
    marginHorizontal: 10
  },
  item: {
    height: 44
  },
  emptyView: {
    height: "50%",
    backgroundColor: "blue",
    justifyContent: 'center',
    alignItems: 'center'
  }
});
