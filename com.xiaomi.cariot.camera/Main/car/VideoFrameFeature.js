import LogUtil from "../util/LogUtil";
import { PixelRatio } from "react-native";
const VIDEO_WIDTH_PX = PixelRatio.getPixelSizeForLayoutSize(1152);
const VIDEO_HEIGHT_PX = PixelRatio.getPixelSizeForLayoutSize(648);
export function calculateScaleParamByHumanFeature(humanFeature) {
  let coordination = humanFeature.box;
  let positions = null;
  try {
    positions = JSON.parse(coordination);
  } catch (err) {
    console.log("========calculateScaleParamByHumanFeature",err);
    return {
      scale: 1,
      offsetX: 0.0,
      offsetY: 0.0,
      chan: 0
    };
  }
  return HumanFeatureParams(positions);
}

export function HumanFeatureParams(positions) {

  let humanHeight = positions[3] - positions[1];
  let humanWidth = positions[2] - positions[0];

  // 头像锁定，放大100/55倍,然后把这个大小放大到整个屏幕大小，放这么大需要的放大倍数
  let imageHeight = humanHeight / 55 * 100; // 放大后图形高度
  if (imageHeight >= 1000 || imageHeight < 0) {
    return {
      scale: 1,
      offsetX: 0.0,
      offsetY: 0.0,
      chan: 0
    };
  }
  // let imageWidth = imageHeight; // 放大后图形的宽度。这里是百分比，纵向百分比处理后，横向百分比也是一样。
  let imageWidth = humanWidth / 55 * 100;
  // let imageWidth = imageHeight / humanHeight * humanWidth;
  let scaleFactor = 1000 / imageHeight; //缩放比例。

  // console.log("+++++++++++HumanFeatureParams",imageWidth,imageHeight,humanHeight,humanWidth);
  let newTopLeftX = positions[0] + humanWidth / 2 - imageWidth / 2; // 放大后图形左上角的x坐标。
  // let newTopLeftY = positions[1] - imageWidth * 0.3; // 放大后图形左上角的y坐标。
  let newTopLeftY = positions[1] - (imageHeight / 2 - humanHeight / 2); // 放大后图形左上角的y坐标。

  let newBottomRightX = newTopLeftX + imageWidth; // 放大后图形右下角的x坐标。
  let newBottomRightY = newTopLeftY + imageHeight; // 放大后图形右下角的y坐标。

  let needY = false;
  let oldTopLeftY = 0;
  if (newTopLeftX < 0) {
    newTopLeftX = 0;
    newBottomRightX = imageWidth;
  }

  if (newTopLeftY < 0) {
    oldTopLeftY = newTopLeftY;
    newTopLeftY = 0;
    // newBottomRightY = imageHeight - Math.abs(oldTopLeftY);
    newBottomRightY = imageHeight;
  }

  if (newBottomRightX > 1000) {
    newBottomRightX = 1000;
    newTopLeftX = 1000 - imageWidth;
    newTopLeftX = newTopLeftX > 0 ? newTopLeftX : 0;
  }
  if (newBottomRightY > 1000) {
    newTopLeftY = 1000 - imageHeight;
    newBottomRightY = 1000;
    newTopLeftY = newTopLeftY > 0 ? newTopLeftY : 0;
  }

  imageHeight = newBottomRightY - newTopLeftY; // 重新计算放大后图形的高度。
  // imageWidth = newBottomRightX - newTopLeftX; // 重新计算放大后图形的高度。
  scaleFactor = 1000 / imageHeight; // 重新计算缩放比例。
  // scaleFactor = imageHeight / humanHeight; // 重新计算缩放比例。
  if (scaleFactor > 6) { // 最大放大6倍
    scaleFactor = 6;
  }

  let centerX = newTopLeftX + (newBottomRightX - newTopLeftX) / 2; // 计算放大后图形的中心点，放大的中心点
  // let centerY = newTopLeftY + (newBottomRightY - newTopLeftY) / 2;
  let centerY = newTopLeftY + (newBottomRightY - newTopLeftY) / 2;

  // scaleFactor = needHeight / VIDEO_HEIGHT_PX * 1000 / humanHeight;
  // alert("centerX: " + centerX + " centerY: " + centerY + " scale:" + scaleFactor);

  let offsetX = (centerX - 500) / 500 * scaleFactor;
  let offsetY = (centerY - 500) / 500 * scaleFactor;
  let scale = {
    scale: scaleFactor,
    offsetX: offsetX,
    offsetY: offsetY,
    chan: 0
  };
  // console.log('hfresult:', JSON.stringify(scale) + " " + centerX + " " + centerY +  " " + newTopLeftX + " " + newTopLeftY + " " + newBottomRightX + " " + newBottomRightY);
  // LogUtil.logOnAll(`HF::::position: ${ JSON.stringify(positions) }, result:${ JSON.stringify(scale) }`);
  return scale;
}

export function calculateIoU(currentCoordination, lastCoordination) {
  let currentHeight = currentCoordination[3] - currentCoordination[1];
  let currentWidth = currentCoordination[2] - currentCoordination[0];

  let lastHeight = lastCoordination[3] - lastCoordination[1];
  let lastWidth = lastCoordination[2] - lastCoordination[0];
  
  const xA = Math.max(currentCoordination[0], lastCoordination[0]);
  const yA = Math.max(currentCoordination[1], lastCoordination[1]);
  const xB = Math.min(currentCoordination[2], lastCoordination[2]);
  const yB = Math.min(currentCoordination[3], lastCoordination[3]);

  // Calculate the area of intersection rectangle
  const intersectionArea = Math.max(0, xB - xA + 1) * Math.max(0, yB - yA + 1);

  // Calculate the area of both rectangles
  const boxAArea = currentWidth * currentHeight;
  const boxBArea = lastWidth * lastHeight;

  // Calculate the IoU
  const iou = intersectionArea / (boxAArea + boxBArea - intersectionArea);

  return iou;
}