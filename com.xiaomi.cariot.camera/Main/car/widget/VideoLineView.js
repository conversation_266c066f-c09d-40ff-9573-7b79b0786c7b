import React from 'react';
import { Image, PanResponder, SectionList, Text, TouchableWithoutFeedback, View } from "react-native";
import { CarFont, CarRadius, carStyles } from "../common/Styles";
import PropTypes from "prop-types";
import { Constants, Radius, styles } from "micariot-ui-sdk";
import { DarkMode, Host } from "miot";
import Util from "../../util2/Util";
import DateFormater from "../../util2/DateFormater";
import LogUtil from "../../util/LogUtil";
import SdFileManager from "../../sdcard/util/SdFileManager";
import dayjs from "dayjs";
import eventSource from "../../../Resources/Images/car/event_pet.png";
import { LoadStatus } from "../../widget/EventList";
import { DescriptionConstants } from "../../Constants";
import { localStrings as LocalizedStrings } from "../../MHLocalizableString";
import TrackUtil from "../../util/TrackUtil";
import CameraConfig from "../../util/CameraConfig";
// @20250416由368 -> 369适配新时间轴样式
const HeaderH = 369;
const HeaderHShort = 20;
const SectionHeaderH = 78 + 24;
const SectionFooterH = 78 + 24;
const ItemOneImageW = 112;
const ItemImageContainerHeight = 60;
const ItemOneImageHeight = 60;
const ItemEventImageHalfW = 0;
// const ItemEventImageHalfW = 16;
const ItemImageDividerW = 2;
const ItemDividerW = 24;
const TAG = "VideoLineView";
const LINE_TYPE = {
  SD: 0,
  CLOUD: 1
};

export class VideoLineView extends React.Component {
  constructor(props, context) {
    super(props, context);
    this.itemInfo = new Map();
    this.itemExpandInfo = new Map();
    this.scrollEndItem = null;
    this.clickCount = 0;

    this.isScrolling = false;
    this.isScrollingCount = 0;
    this.lastScrollDate = new Date().getTime();
    this.ignoreScroll = false;
    this.layoutList = [];
    this.isClick = false;
    this.clickNum = 0;
    this.playItem = null;
    this.maxOffset = 0;
  }

  state = {
    expand: false,
    maxOffsetLength: 0
  };
  static propTypes = {
    data: PropTypes.any,
    isPlaying: PropTypes.bool,
    isLoading: PropTypes.bool,
    highWindowFront: PropTypes.bool,
    footerWidth: PropTypes.number
  };

  static defaultProps = {
    data: [],
    isPlaying: false,
    isLoading: true,
    highWindowFront: false,
    footerWidth: HeaderH,
  };

  componentDidUpdate(aPrevProps) {
    if (this.props.data !== aPrevProps.data) {
      console.log(TAG, "componentDidUpdate");
      // 随着数据的更新，会被调用
      this.getItemPosition();
    }

    if (this.props.type !== aPrevProps.type) {
      console.log("componentDidUpdate type is change");
      // 需要清空下
      this.itemInfo = new Map();
      this.itemExpandInfo = new Map();
      this.getItemPosition();
    }

    if (this.props.isPlaying !== aPrevProps.isPlaying) {
      // 播放模式变了
      this.getItemPosition();
    }
  }

  componentDidMount() {
    console.log(TAG, "componentDidMount");
    this.getItemPosition();
  }

  componentWillUnmount() {
    this.scrollEndDelay && clearTimeout(this.scrollEndDelay);
    this.delayToResetScrollState && clearTimeout(this.delayToResetScrollState);
  }

  render() {
    let scrollEnabled = true;
    if (this.props.highWindowFront) {
      scrollEnabled = false;
    }
    return (
      <SectionList
        ref={ (ref) => {
          this.mSectionList = ref;
        } }
        horizontal={ true }
        inverted={ true }
        scrollEventThrottle={5}
        scrollEnabled={scrollEnabled}
        overScrollMode={"never"}
        showsHorizontalScrollIndicator={ false }
        sections={ this.props.data }
        keyExtractor={ (item, index) => `${ item.startTime }_${ index }` }
        renderItem={ this.renderItem }
        renderSectionFooter={ this.renderSectionFooter }
        ListFooterComponent={ this.renderFoot }
        ListHeaderComponent={ this.renderHeader }
        ListEmptyComponent={this.renderEmptyView()}
        onScrollToIndexFailed={(info) => {
          console.log("onScrollToIndexFailed", info);
        }}
        onScrollBeginDrag={() => {
          console.log("++++++我开始滚动了+++++++++");
          this.scrollEndDelay && clearTimeout(this.scrollEndDelay);
          this.delayToResetScrollState && clearTimeout(this.delayToResetScrollState);
          this.isScrolling = true;
        }}
        onScrollEndDrag={() => {
          console.log("++++++我滚动结束了+++++++++");
          // 标记位需要延迟一点点重置，否则云存播放中会存在播放进度导致滚动，冲掉用户滚动后位置问题
          this.delayToResetScrollState && clearTimeout(this.delayToResetScrollState);
          this.delayToResetScrollState = setTimeout(() => {
            // 一般滚动结束后会会执行onMomentumScrollEnd方法，这是个兜底，延迟把状态重置掉
            this.isScrolling = false;
          }, 3000);

        }}
        onScroll={ this.onScroll }
        onMomentumScrollEnd={ this.onScrollEnd }
        // getItemLayout={ this.mLayoutGetterV22 }
        // getItemLayout={ this.mLayoutGetterT }
        getItemLayout={ this.mLayoutGetterZ }
      />
    );
  }

  viewableChange = (event) => {
    console.log("===+viewableChange====+=",event)
    // console.log("===+viewableChange====+=item",event.changed[0].item)
    // console.log("===+====+=key",event.changed[0].key)
    // console.log("===+====+=section",event.changed[0].section)
    console.log("===+====+=",event.viewableItems)
  }
  renderEmptyView = () => {
    if (this.props.isLoading) {
      return null;
    }
    return (
      <View>
        <View
          key={`empty_key`}
          style={ {
            backgroundColor: carStyles.lineDayBg.backgroundColor,
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: 29,
            width: 78,
            height: 30,
            borderRadius: 39,
            marginRight: 24
          } }>
          <Text style={ { fontSize: CarFont.Size._22 } }>{ DateFormater.instance().formatTimestampToMMDD() }</Text>
        </View>
      </View>
    );
  }

  /**
   *
   * @param data
   * @param index item项所在的index，占用index
   *              renderSectionFooter renderSectionHeader 都占用index
   *              ListFooterComponent ListHeaderComponent 不在getItemLayout的计算范围内
   *              sectionHeader即使没有，也要添加个length为0的，及时没有也会暂用index
   * @return {{offset, length: number, index: *}}
   */
  mLayoutGetterZ = (data, index) => {
    if (!data || !data.length) {
      // console.log("mLayoutGetterZ1");
      return { length: 0, offset: 0, index };
    }

    // Start with the offset set to the height of the list header.
    let offset = HeaderH;
    let offsetTwo = HeaderH;
    // Initialize the global index counter to track overall position within the list.
    let globalIndex = 0;

    // Iterate through each section in the data.
    for (let sectionIndex = 0; sectionIndex < data.length; sectionIndex++) {
      // Resolve the heights at the beginning to avoid duplication
      const currentSectionHeaderHeight = 0;
      const currentSectionFooterHeight = SectionFooterH;
      const currentSectionSeparatorHeight = 0;

      // Handle the section header.
      if (index === globalIndex) {
        // 添加，可能展开可能缩起
        this.setItemInfo(index, currentSectionHeaderHeight, offset,-1, sectionIndex,true);
        // 与上面反之
        this.setItemInfo(index, currentSectionHeaderHeight, offsetTwo,-1, sectionIndex,true, true);

        return { length: currentSectionHeaderHeight, offset, index };
      }
      offset += currentSectionHeaderHeight;
      offsetTwo += currentSectionHeaderHeight;
      globalIndex++;

      // Add the height of the top section separator after the section header.
      offset += currentSectionSeparatorHeight;
      offsetTwo += currentSectionSeparatorHeight;

      // Iterate through each item within the current section.
      for (
        let itemIndex = 0;
        itemIndex < data[sectionIndex].data.length;
        itemIndex++
      ) {
        const currentItemHeight = this.getItemWidthByDuration(data[sectionIndex].data[itemIndex].duration) + ItemEventImageHalfW + ItemDividerW;
        const currentItemHeightReverse = this.getItemWidthByDurationReverse(data[sectionIndex].data[itemIndex].duration) + ItemEventImageHalfW + ItemDividerW;

        if (index === globalIndex) {
          this.setItemInfo(index,currentItemHeight,offset,itemIndex,sectionIndex,false);
          this.setItemInfo(index,currentItemHeightReverse,offsetTwo,itemIndex,sectionIndex,false, true);
          return { length: currentItemHeight, offset, index };
        }

        // Add the item's height to the offset and check for item separators.
        offset += currentItemHeight;
        offsetTwo += currentItemHeightReverse;
        globalIndex++;

        // If not the last item in the section, add the item separator's height.
        if (itemIndex < data[sectionIndex].data.length - 1) {
          offset += 0
          offsetTwo += 0
        }
      }

      // Add the height of the bottom section separator before the section footer.
      offset += currentSectionSeparatorHeight;
      offsetTwo += currentSectionSeparatorHeight;

      // Handle the section footer.
      if (index === globalIndex) {
        this.setItemInfo(index,currentSectionFooterHeight,offset,-2,sectionIndex,true);
        this.setItemInfo(index,currentSectionFooterHeight,offsetTwo,-2,sectionIndex,true, true);
        return { length: currentSectionFooterHeight, offset, index };
      }
      offset += currentSectionFooterHeight;
      offsetTwo += currentSectionFooterHeight;
      globalIndex++;
    }

    // Return a default layout object if the specified index is out of bounds.
    return { length: 0, offset: 0, index };
  };

  setItemInfo(aIdx, length, offset, itemIndex, sectionIndex, isHeaderFooter, save = false) {
    if (save) {
      // 对另一个反过来添加
      if (this.state.expand) {
        this.itemInfo.set(aIdx, { index: aIdx, length, offset, itemIndex, sectionIndex, isHeaderFooter });
      } else {
        this.itemExpandInfo.set(aIdx, { index: aIdx, length, offset, itemIndex, sectionIndex, isHeaderFooter });
      }
    } else {
      if (this.state.expand) {
        this.itemExpandInfo.set(aIdx, { index: aIdx, length, offset, itemIndex, sectionIndex, isHeaderFooter });
      } else {
        this.itemInfo.set(aIdx, { index: aIdx, length, offset, itemIndex, sectionIndex, isHeaderFooter });
      }
    }

  }

  getItemPosition() {
    let list = this.props.data;
    let layoutList = [];
    let layoutIndex = 0;
    let layoutOffset = HeaderH;
    let sIndex = 0;
    let cIndex = 0;
    list.forEach((section, sectionIndex) => {
      sIndex = sectionIndex;
      // console.log("++++++++sectionIndex", sectionIndex, section.data.length);
      // sectionHeader
      layoutList.push({
        index: layoutIndex,
        length: 0,
        offset: layoutOffset,
        sectionIndex: sIndex,
        itemIndex: -1,
        isHeaderFooter: true
      });
      // 添加，可能展开可能缩起
      this.setItemInfo(layoutIndex, 0, layoutOffset,-1, sectionIndex,true);
      // 与上面反之
      // this.setItemInfo(layoutIndex, 0, layoutOffset,-1, sectionIndex,true, true);

      layoutIndex = layoutIndex + 1;
      // 这里应该还有个section header，这里加上后面之后的地方都需要处理下,sectionHeader length为0
      // 结构为listHeader - sectionHeader - item... - sectionFooter - sectionHeader - item... - sectionFooter - ...
      section.data.forEach((item, itemIndex) => {
        cIndex = itemIndex;

        const itemHeight = this.getItemWidthByDuration(item.duration) + ItemEventImageHalfW + ItemDividerW;
        // const itemHeightReverse = this.getItemWidthByDurationReverse(item.duration) + ItemEventImageHalfW + ItemDividerW;

        layoutList.push({
          index: layoutIndex,
          length: itemHeight,
          offset: layoutOffset,
          sectionIndex: sIndex,
          itemIndex: itemIndex,
          isHeaderFooter: false
        });
        this.setItemInfo(layoutIndex, itemHeight, layoutOffset, itemIndex, sectionIndex,false);
        // this.setItemInfo(layoutIndex, itemHeightReverse,offsetTwo,itemIndex,sectionIndex,false, true);
        layoutIndex += 1;
        layoutOffset += itemHeight;
      });
      // 添加一个section footer
      layoutList.push({
        index: layoutIndex,
        length: SectionFooterH,
        offset: layoutOffset,
        sectionIndex: sIndex,
        itemIndex: cIndex + 1,
        isHeaderFooter: true
      });

      this.setItemInfo(layoutIndex, SectionFooterH, layoutOffset, -2, sectionIndex,true);

      layoutIndex += 1;
      layoutOffset += SectionFooterH;
    });
    console.log("==============getItemPosition:", layoutIndex, layoutOffset);
    this.layoutList = layoutList;
    // 默认情况下最大的offset
    this.maxOffset = layoutOffset;
    this.setState({ maxOffsetLength: layoutOffset });
    if (this.maxOffset < 760 && this.maxOffset >= 20 && !this.props.isPlaying) {
      // 更新下 需要屏蔽掉播放中执行此处导致的更新
      // this.scrollToLocation({ animated: true, sectionIndex: 0, itemIndex: 0, viewPosition: 0, viewOffset: 20 });
      console.log("++++++++++++++++++++++++");
      this.scrollToOffset({ animated: true, offset: 0 });
    }
  }

  getItemPositionInfo(sectionIndex, itemIndex) {
    if (!this.layoutList || this.layoutList.length ==0) {
      this.getItemPosition();
    }
    return this.layoutList.find((item) => itemIndex === item.itemIndex && sectionIndex === item.sectionIndex);
  }

  getItemWidthByDuration(duration) {
    if (this.props.type === LINE_TYPE.SD) {
      duration = duration / 1000;
    }
    if (duration < 300) {
      // 小于5分钟 默认展示1张
      return this.state.expand ? ItemImageDividerW + ItemImageDividerW * 2 + ItemOneImageW * 2 : ItemOneImageW + ItemImageDividerW * 2;
    } else if (duration < 3600) {
      // 小于1小时 默认展示3张
      return this.state.expand ? ItemImageDividerW * 5 + ItemImageDividerW * 2 + ItemOneImageW * 6 : ItemImageDividerW * 2 + ItemImageDividerW * 2 + ItemOneImageW * 3;
    } else {
      // 大于等于1小时 默认展示6张图片
      return this.state.expand ? ItemImageDividerW * 11 + ItemImageDividerW * 2 + ItemOneImageW * 12 : ItemImageDividerW * 5 + ItemImageDividerW * 2 + ItemOneImageW * 6;
    }
  }

  getItemWidthByDurationReverse(duration) {
    if (this.props.type === LINE_TYPE.SD) {
      duration = duration / 1000;
    }
    if (duration < 300) {
      // 小于5分钟 默认展示1张
      return this.state.expand ? ItemOneImageW + ItemImageDividerW * 2 : ItemImageDividerW + ItemImageDividerW * 2 + ItemOneImageW * 2 ;
    } else if (duration < 3600) {
      // 小于1小时 默认展示3张
      return this.state.expand ?  ItemImageDividerW * 2 + ItemImageDividerW * 2 + ItemOneImageW * 3 : ItemImageDividerW * 5 + ItemImageDividerW * 2 + ItemOneImageW * 6;
    } else {
      // 大于等于1小时 默认展示6张图片
      return this.state.expand ? ItemImageDividerW * 5 + ItemImageDividerW * 2 + ItemOneImageW * 6 : ItemImageDividerW * 11 + ItemImageDividerW * 2 + ItemOneImageW * 12;
    }
  }

  onScroll = (e) => {
    this.scrollY = e.nativeEvent.contentOffset.y;
    this.scrollX = e.nativeEvent.contentOffset.x;
    console.log(`onScroll scrollY:${ this.scrollY } scrollX: ${ this.scrollX }`);
    if (this.scrollX < 0) {
      // 负值，不应该存在
      this.scrollToLocation({ animated: false, sectionIndex: 0, itemIndex: 0, viewPosition: 0, viewOffset: 20 });
    }
    if (this.ignoreScroll) {
      console.log("+++++++++++scroll is ignore");
      return;
    }
  };

  onScrollEnd = (e) => {
    console.log("onScrollEnd", e.nativeEvent.contentOffset.x);
    let scrollX = e.nativeEvent.contentOffset.x;

    this.scrollEndDelay && clearTimeout(this.scrollEndDelay);
    this.scrollEndDelay = setTimeout(() => {
      // 一般滚动结束后会会执行onMomentumScrollEnd方法，这是个兜底，延迟把状态重置掉
      this.isScrolling = false;
    }, 300);
    // if (!this.props.isPlaying) {
    //   console.log("+++++++++no need to scroll");
    //   return;
    // }
    let itemsInfo;
    if (this.state.expand) {
      itemsInfo = this.itemExpandInfo;
    } else {
      itemsInfo = this.itemInfo;
    }

    if (itemsInfo == null || itemsInfo.size <= 0) {
      LogUtil.logOnAll(TAG, "onScrollEnd itemsInfo is null");
      return;
    }
    let infoKeys = [...itemsInfo.keys()];
    if (infoKeys == null || infoKeys.length == 0) {
      LogUtil.logOnAll(TAG, "onScrollEnd itemsInfo key is null");
      return;
    }
    infoKeys.sort((a1, a2) => {
      return a1 - a2;
    });
    // console.log("++++++++++", infoKeys,this.layoutList);
    // infoKeys 长度至少有5个
    if (infoKeys.length < 3) {
    // if (infoKeys.length < 4) {
      LogUtil.logOnAll("list length is abnormal");
      return;
    }
    let middleItemInfo = null;
    let middleScroll = false;
    let playStart = false;
    // 查找滚动后，处于中间位置的item
    console.log("++++++++++++++++++++++++++=",itemsInfo.get(infoKeys[infoKeys.length - 1]),itemsInfo.get(infoKeys[0]));
    if (scrollX == 0) {
      // 说明拖动到了最右侧
      // 需要退出回看进入实时查看
      this.props.onScrollEndToLive && this.props.onScrollEndToLive();
      return;
    }
    if (scrollX > 0 && scrollX < 23) {
      scrollX = 28;
    }
    const listHeaderH = HeaderH;
    let maxItemInfo = itemsInfo.get(infoKeys[infoKeys.length - 1]);
    let maxOffset = maxItemInfo.offset + maxItemInfo.length - listHeaderH;
    for (let i = 0; i < infoKeys.length; i++) {
      let itemInfo = itemsInfo.get(infoKeys[i]);
      // let leftOffset = itemInfo.offset + itemInfo.length - listHeaderH;
      // let rightOffset = itemInfo.offset - listHeaderH;
      // @20250416 需求变更，UI变更导致计算方式变更,需要修正左右边界，
      let leftOffset = itemInfo.offset + itemInfo.length - listHeaderH + ItemDividerW;
      // let rightOffset = itemInfo.offset - listHeaderH - ItemDividerW;
      // rightOffset右侧应该包含右侧的margin，因为是算到这个item中的
      let rightOffset = itemInfo.offset - listHeaderH;
      // console.log("====:",leftOffset,rightOffset,scrollX);
      if (scrollX >= rightOffset && scrollX <= leftOffset) {
        middleScroll = true;
        // 处于此item中
        middleItemInfo = itemInfo;

        if (itemInfo.isHeaderFooter) {
          playStart = true;
          console.log("++++++++++++++++++++++++++=",itemInfo,infoKeys.length,scrollX);
          if (itemInfo.itemIndex == -1) {
            // section header
            middleItemInfo = itemsInfo.get(infoKeys[i + 1]);
          } else {
            // section footer
            middleItemInfo = itemsInfo.get(infoKeys[i - 1]);
          }

        }

        break;
      }

      if (scrollX > maxOffset) {
        middleItemInfo = itemsInfo.get(infoKeys[infoKeys.length - 2]);
        break;
      }
    }
    LogUtil.logOnAll(TAG, "onScrollEnd msg", scrollX, maxOffset, middleItemInfo, playStart);

    if (middleItemInfo == null) {
      LogUtil.logOnAll(TAG, "onScrollEnd middleItemInfo is not find");
      return;
    }
    if (!this.props.data
      || this.props.data.length <= middleItemInfo.sectionIndex
      || !this.props.data[middleItemInfo.sectionIndex].data
      || this.props.data[middleItemInfo.sectionIndex].data.length <= middleItemInfo.itemIndex) {
      LogUtil.logOnAll(TAG, "onScrollEnd some data is wrong");
      return;
    }

    this.scrollEndItem = this.props.data[middleItemInfo.sectionIndex].data[middleItemInfo.itemIndex];
    // console.log("+++++++++++++++",this.scrollEndItem,middleItemInfo);

    let offset = 0;
    // 计算视频播放偏移量
    if (!playStart) {
      // let usefulWidth = middleItemInfo.length - ItemEventImageHalfW - ItemDividerW;
      let usefulWidth = middleItemInfo.length - ItemEventImageHalfW - ItemDividerW;
      if (scrollX >= middleItemInfo.offset + usefulWidth + ItemDividerW - listHeaderH) {
        offset = 0;
      } else {
        let unitWidth = this.scrollEndItem.duration / usefulWidth;
        // 380+299=679
        // let leftWidth = usefulWidth - (scrollX - (middleItemInfo.offset - listHeaderH));
        let leftWidth = usefulWidth - (scrollX - (middleItemInfo.offset - listHeaderH) - ItemDividerW);
        offset = Number.parseInt(leftWidth * unitWidth);
        console.log("}}}}}}}}}}}=>", usefulWidth, unitWidth, leftWidth);
      }
    }
    // console.log("=================",this.scrollEndItem, middleItemInfo,offset);
    // 需要通知出去，播放或者标记位置信息
    this.props.onScrollEnd && this.props.onScrollEnd(this.scrollEndItem, middleItemInfo, offset);

  };

  renderItem = ({ item, index, section }) => {
    let images = item.images;
    if (images) {
      if (!this.state.expand) {
        images = images.filter((_, index) => index % 2 === 0);
      } else {
        if (images.length == 1) {
          images.push(images[0]);
        }
      }
    }
    let eventSource;
    let showPlayBorder = false;
    if (this.props.type === LINE_TYPE.CLOUD) {
      switch (item.eventType) {
        case "Pet":
          eventSource = require('../../../Resources/Images/car/event_pet_new.png');
          break;
        case "ChildDetected":
          eventSource = require('../../../Resources/Images/car/event_child_new.png');
          break;
        default:
          eventSource = null;
          break;
      }
      showPlayBorder = this.props.isPlaying && this.scrollEndItem && item.fileId == this.scrollEndItem.fileId;
    } else {
      eventSource = item.eventType == "42" ? require('../../../Resources/Images/car/event_child_new.png') : item.eventType == "2"
        ? require('../../../Resources/Images/car/event_pet_new.png') : null;
      showPlayBorder = this.props.isPlaying && this.scrollEndItem && item.startTime == this.scrollEndItem.startTime;
    }
    return (
      <TouchableWithoutFeedback
        onPress={ () => this.handlingTap(item, index, section) }>
        <View
          style={ { flexDirection: 'row', alignItems: 'center', marginRight: Constants.DEFAULT_TEXT_MARGIN_BOTTOM } }>

          <View style={ {
            borderRadius: CarRadius.Radius12,
            overflow: 'hidden',
            // marginLeft: 16,
            height: ItemImageContainerHeight,
            borderColor: showPlayBorder ? carStyles.itemBorderSelectStyle.color : carStyles.itemBorderStyle.color,
            borderWidth: 2,
            flexDirection: 'row',
            opacity: this.props.highWindowFront ? 0.35 : 1,
            backgroundColor: DarkMode.getColorScheme() === 'dark' ? "xm#C1CCE21F" : "#EEEEEE"
          } }>
            {
              images.map((imagesItem, cIndex) => {
                let imgSource;
                if (this.props.type === LINE_TYPE.SD) {
                  let path = SdFileManager.getInstance().getImageFilePath(imagesItem.startTime);
                  imgSource = (path == null) ? null : ({ uri: `file://${ Host.file.storageBasePath }${ path }` });

                } else {
                  // const imgStoreUrl = item.imgStoreUrl;
                  const imgStoreUrl = imagesItem.imgStoreUrl;
                  imgSource = imgStoreUrl;
                  if (imgStoreUrl) {
                    if (Util.isLocalThumb(imgStoreUrl, item.mediaType)) {
                      imgSource = { uri: imgStoreUrl };
                    } else {
                      imgSource = { uri: `file://${ imgStoreUrl }` };
                    }
                  }
                }
                // console.log("0000000000000=>", imgSource);
                return (
                  <View
                    key={ `image_${index}_${cIndex}` }
                    style={ { flexDirection: 'row' } }>
                    {/*{*/}
                    {/*  cIndex == 0 ? <View style={ {*/}
                    {/*    backgroundColor: 'xm#0000FF',*/}
                    {/*    position: 'absolute'*/}
                    {/*  } }>*/}
                    {/*    <View style={{width: 2}}></View>*/}
                    {/*  </View> : null*/}
                    {/*}*/}
                    <View style={{
                      borderLeftWidth:  cIndex != 0 ? 2 : 0,
                      borderLeftColor: "xm#000000"
                    }}>
                      <View style={{
                        width: ItemOneImageW,
                        height: ItemOneImageHeight
                      }}>
                        <Image
                          style={ [{
                            width: ItemOneImageW,
                            height: ItemOneImageHeight
                          }, { backgroundColor: DarkMode.getColorScheme() === 'dark' ? "xm#4C4D51" : "#EEEEEE" }] }
                          resizeMode="cover"
                          source={ imgSource }/>
                      </View>

                    </View>

                  </View>

                );
              })
            }

          </View>
          <Image
            style={ { height: 30, width: 30, position: 'absolute', left: 4, top: (88 - ItemImageContainerHeight) / 2 + 2 } }
            resizeMode="contain"
            source={ eventSource }/>
          {/*<Text style={ { height: 34, width: 32, position: 'absolute', left: 0 } }>{index}</Text>*/}
        </View>
      </TouchableWithoutFeedback>
    );
  };

  handlingTap(item, index, section) {
    let sectionIndex = this.props.data.findIndex((item) => section.title === item.title);
    console.log("{{{{{{{{{{{{", index, sectionIndex);
    if (this.props.highWindowFront) {
      LogUtil.logOnAll(TAG, "some high window front");
      return;
    }
    this.clickNum++;
    // 毫秒内点击过后阻止执行定时器
    if (this.isClick) {
      return;
    }
    // 毫秒内第一次点击
    this.isClick = true;
    setTimeout(() => {
      // 超过1次都属于双击
      if (this.clickNum > 1) {
        console.log("=========doubleClick");
        this.doubleClick(item, index, sectionIndex);
      } else {
        console.log("=========oneClick");
        this.oneClick(item, index, sectionIndex);
      }
      this.clickNum = 0;
      this.isClick = false;
    }, 300);

  }

  handleClickAction(item, index, sectionIndex) {
    this.clickCount = this.clickCount + 1;
    setTimeout(() => {
      if (this.clickCount !== 2) {
        this.oneClick(item, index, sectionIndex); // this catches one click
        this.isScrolling = false;
      }
      this.clickCount = 0;
    }, 300);
  }

  doubleClick(item, index, sectionIndex) {
    console.log("++++++++double click");
    let itemsInfo = this.itemInfo;
    if (this.state.expand) {
      itemsInfo = this.itemExpandInfo;
    }
    // 找到数据item位置信息，后面播放中滚动会用到
    if (itemsInfo == null || itemsInfo.size <= 0) {
      LogUtil.logOnAll(TAG, "oneClick itemsInfo error");
      return;
    }
    let infoValues = [...itemsInfo.values()];
    if (infoValues == null || infoValues.length == 0) {
      LogUtil.logOnAll(TAG, "itemsInfo error null");
      return;
    }
    // console.log("infoValues:",infoValues);
    let positionInfo = infoValues.find((item) => item.itemIndex === index && item.sectionIndex === sectionIndex && !item.isHeaderFooter);
    if (positionInfo == null) {
      LogUtil.logOnAll(TAG, "find not find positionInfo", item, infoValues, index, sectionIndex);
      return;
    }
    // this.scrollEndItem = item;
    this.setState({ expand: !this.state.expand }, () => {
      this.getItemPosition(this.props.data);
      this.props.scaleChange && this.props.scaleChange(item, positionInfo);
    });
  }

  oneClick(item, index, sectionIndex) {
    console.log("++++++++", item, index);
    // let sectionIndex = this.props.data.findIndex(item => item.title === section.title);
    console.log("========sectionIndex:", sectionIndex);
    let itemsInfo = this.itemInfo;
    if (this.state.expand) {
      itemsInfo = this.itemExpandInfo;
    }
    // 找到数据item位置信息，后面播放中滚动会用到
    if (itemsInfo == null || itemsInfo.size <= 0) {
      LogUtil.logOnAll(TAG, "oneClick itemsInfo error");
      return;
    }
    let infoValues = [...itemsInfo.values()];
    if (infoValues == null || infoValues.length == 0) {
      LogUtil.logOnAll(TAG, "itemsInfo error null");
      return;
    }
    // console.log("infoValues:",infoValues);
    let positionInfo = infoValues.find((item) => item.itemIndex === index && item.sectionIndex === sectionIndex && !item.isHeaderFooter);
    if (positionInfo == null) {
      LogUtil.logOnAll(TAG, "find not find positionInfo", item, infoValues, index, sectionIndex);
      return;
    }
    this.scrollEndItem = item;
    this.props.onItemPress && this.props.onItemPress(item, positionInfo);
    const listHeaderH = HeaderH;
    let viewOffset = listHeaderH - (positionInfo.length - ItemDividerW - ItemEventImageHalfW);
    // let viewOffset = - (positionInfo.length - ItemDividerW - ItemEventImageHalfW);

    // let scrollParam = { animated: false, sectionIndex, itemIndex: positionInfo.index, viewPosition: 0, viewOffset: viewOffset };
    // 为什么会是前一个item滚动到顶部？？？？ 云存和SD卡回看还不一致？？？？
    // let itemIndex = this.props.type === LINE_TYPE.SD ? positionInfo.itemIndex + 1 : positionInfo.itemIndex;
    let scrollParam = {
      animated: false,
      sectionIndex,
      itemIndex: positionInfo.itemIndex,
      viewPosition: 0,
      viewOffset: 0
    };
    let offset;
    if (listHeaderH > positionInfo.length) {
      offset = positionInfo.offset - (listHeaderH - positionInfo.length) - ItemEventImageHalfW;
    } else {
      offset = positionInfo.offset + (positionInfo.length - listHeaderH) - ItemEventImageHalfW;
    }
    console.log("++++++++++++oneClick", offset, positionInfo, viewOffset, sectionIndex, scrollParam);
    if (this.props.isPlaying) {
      this.scrollToOffset({ animated: false, offset: offset });
    } else {
      // 未播放时，list左右需要展开半个屏幕宽度，上面的位置信息其实都是不对的
    }
  }

  // 取时间轴上最新的那条数据
  getLastVideoInfo() {
    let itemInfo = this.state.expand ? this.itemExpandInfo : this.itemInfo;
    console.log(TAG, "getLastVideoInfo", this.props.data.length);
    if (this.props.data.length === 0 || this.props.data[0].data.length === 0 || itemInfo.size <= 0) {
      return { item: null, itemInfo: null };
    }

    let item = this.props.data[0].data[0];
    let positionInfo = itemInfo.get(1);
    return { item, itemInfo: positionInfo };
  }

  /**
   * @Author: byh
   * @Date: 2024/12/18
   * @explanation:
   * find the timestamp in video
   *********************************************************/
  findItemInfoByTimestamp(timestamp) {
    // 所在分组
    let sectionIndex = this.props.data.findIndex((item) => {
      let dStr1 = dayjs.unix(item.title / 1000).format('MM/DD/YYYY');
      let dStr2 = dayjs.unix(timestamp / 1000).format('MM/DD/YYYY');
      return dStr1 === dStr2;
    });

    if (sectionIndex == -1) {
      return { item: null, itemInfo: null };
    }

    let sections = this.props.data[sectionIndex].data;
    if (sections.length <= 0) {
      return { item: null, itemInfo: null };
    }

    let itemIndex = -1;
    let item = null;
    
    // 降序
    for (let i = 0; i < sections.length; i++) {
      if (sections[i].startTime <= timestamp && timestamp <= sections[i].endTime) {
        itemIndex = i;
        item = sections[i];
        break;
      }
    }

    LogUtil.logOnAll(TAG, "findItemInfoByTimestamp",sectionIndex,itemIndex);
    if (itemIndex == -1) {
      return { item: null, itemInfo: null };
    }
    if (!this.layoutList || this.layoutList.length == 0) {
      // 某些情况下有数据但layoutList有问题
      this.getItemPosition();
    }
    let positionInfo = this.layoutList.find((item) => item.sectionIndex === sectionIndex && item.itemIndex === itemIndex);
    LogUtil.logOnAll(TAG, "findItemInfoByTimestamp positionInfo:", positionInfo);
    if (positionInfo == null) {
      return { item: null, itemInfo: null };
    }
    let oldItem = this.scrollEndItem;
    this.scrollEndItem = item;
    if (!oldItem || oldItem.startTime != this.scrollEndItem.startTime) {
      // 刷新下view
      console.log(TAG, "=============refresh view================");
      this.setState({});
    }
    return { item: item, itemInfo: positionInfo };
  }

  /**
   * @Author: byh
   * @Date: 2024/12/17
   * @explanation:
   * find last item info by timestamp
   *********************************************************/
  findLastItemInfoByTimestamp(timestamp) {

    let sectionIndex = this.props.data.findIndex((item) => {
      let dStr1 = dayjs.unix(item.title / 1000).format('MM/DD/YYYY');
      let dStr2 = dayjs.unix(timestamp / 1000).format('MM/DD/YYYY');
      console.log("++++++++++++sectionIndex -1", dStr1, dStr2);

      return dStr1 === dStr2;
    });

    if (sectionIndex == -1) {
      console.log("++++++++++++sectionIndex -1");
      return { item: null, itemInfo: null };
    }
    let itemIndex = this.props.data[sectionIndex].data.length - 1;
    let item = this.props.data[sectionIndex].data[itemIndex];
    let itemsInfo = this.itemInfo;
    if (this.state.expand) {
      itemsInfo = this.itemExpandInfo;
    }
    // let infoValues = [...itemsInfo.values()];
    let infoValues = this.layoutList;
    // console.log("+++++++++++itemIndex+", itemIndex, sectionIndex, infoValues);
    if (!this.layoutList || this.layoutList.length ==0) {
      this.getItemPosition();
    }
    let positionInfo = this.layoutList.find((item) => item.sectionIndex === sectionIndex && item.itemIndex === itemIndex);
    console.log("++++++++++++positionInfo", positionInfo);
    this.scrollEndItem = item;
    return { item: item, itemInfo: positionInfo };

  }

  setCurrentPlayItem(item) {
    this.scrollEndItem = item;
    this.setState({});
  }

  getCurrentPlayItem() {
    return this.scrollEndItem;
  }

  scrollToItemByItemInfo(positionInfo) {
    if (positionInfo == null) {
      LogUtil.logOnAll(TAG, "scrollToItemByItemInfo", "positionInfo is null");
      return;
    }
    const listHeaderH = HeaderH;
    let offset;
    if (listHeaderH > positionInfo.length) {
      offset = positionInfo.offset - (listHeaderH - positionInfo.length) - ItemEventImageHalfW;
    } else {
      offset = positionInfo.offset + (positionInfo.length - listHeaderH) - ItemEventImageHalfW;
    }
    console.log("++++++++++++scroll2", offset);

    this.scrollToOffset({ animated: false, offset: offset });
  }

  scrollToDayStartByTimestamp(timestamp, notify = false) {
    // 1、找到这个时间戳所在的section
    // 2、这个section中，最后的那个视频
    // 3、取这个item的位置信息
    // 4、滚动到这个item
    // 5、通知出去

    // let sectionIndex = this.props.data.findIndex((item) => section.title === item.title);
    // console.log("scrollToDayStartByTimestamp", this.props.data);
    // let sectionIndex = this.props.data.findIndex((item) => {
    //   let dStr1 = dayjs.unix(item.title / 1000).format('MM/DD/YYYY');
    //   let dStr2 = dayjs.unix(timestamp / 1000).format('MM/DD/YYYY');
    //   console.log("++++++++++++sectionIndex -1", dStr1, dStr2);
    //
    //   return dStr1 === dStr2;
    // });
    //
    // if (sectionIndex == -1) {
    //   console.log("++++++++++++sectionIndex -1");
    //   return;
    // }
    // let itemIndex = this.props.data[sectionIndex].data.length - 1;
    // let item = this.props.data[sectionIndex].data[itemIndex];
    // let itemsInfo = this.itemInfo;
    // if (this.state.expand) {
    //   itemsInfo = this.itemExpandInfo;
    // }
    // let infoValues = [...itemsInfo.values()];
    // console.log("+++++++++++itemIndex+", itemIndex, sectionIndex, infoValues);
    // let positionInfo2 = infoValues.find((item) => item.sectionIndex === sectionIndex && item.itemIndex === itemIndex);
    // let positionInfo = this.layoutList.find((item) => item.sectionIndex === sectionIndex && item.itemIndex === itemIndex);
    // console.log("++++++++++++positionInfo", positionInfo, positionInfo2);
    let { item, itemInfo } = this.findLastItemInfoByTimestamp(timestamp);
    if (item == null || itemInfo == null) {
      return;
    }
    const listHeaderH = HeaderH;
    let viewOffset = listHeaderH - (itemInfo.length - ItemDividerW - ItemEventImageHalfW);
    console.log("++++++++++++scrollToTimestampWithNotify", itemInfo, viewOffset);
    let itemScrollIndex = itemInfo.itemIndex;

    let scrollParam = {
      animated: false,
      sectionIndex: itemInfo.sectionIndex,
      itemIndex: itemScrollIndex,
      viewPosition: 0,
      viewOffset: viewOffset
    };
    console.log("++++++++++++scrollParam", scrollParam);
    let offset;
    if (listHeaderH > itemInfo.length) {
      offset = itemInfo.offset - (listHeaderH - itemInfo.length) - ItemEventImageHalfW;
    } else {
      offset = itemInfo.offset + (itemInfo.length - listHeaderH) - ItemEventImageHalfW;
    }
    this.scrollToOffset({ animated: false, offset: offset });
    // 通知外部
    this.scrollEndItem = item;
    this.props.onScrollEnd && this.props.onScrollEnd(item, itemInfo, 0);

  }

  scrollToPositionByTimestamp(item, itemInfo, timestamps) {
    if (item == null || itemInfo == null || !timestamps || this.isScrolling) {
      LogUtil.logOnAll(TAG, "scroll not do", this.isScrolling, !timestamps, itemInfo);
      return;
    }
    const listHeaderH = HeaderH;
    // let viewOffset = listHeaderH - (itemInfo.length - ItemDividerW - ItemEventImageHalfW);
    let viewOffset = listHeaderH - (itemInfo.length - ItemEventImageHalfW);
    // 云存的duration单位是s，SD卡的是ms
    let duration = item.duration;
    let unitWidth = (itemInfo.length - ItemDividerW - ItemEventImageHalfW) / duration;
    // let unitWidth = (itemInfo.length - ItemEventImageHalfW) / duration;
    let playTime = timestamps - item.startTime;
    playTime = this.props.type === LINE_TYPE.SD ? playTime : playTime / 1000;
    let playOffset = playTime * unitWidth;
    let playEndOffset = itemInfo.length - ItemDividerW - ItemEventImageHalfW;
    let offset;
    if (listHeaderH > itemInfo.length) {
      offset = itemInfo.offset - (listHeaderH - itemInfo.length) - ItemEventImageHalfW;
    } else {
      offset = itemInfo.offset + (itemInfo.length - listHeaderH) - ItemEventImageHalfW;
    }
    // 某个item边界右侧
    let rightOffset = offset - playEndOffset;
    offset = offset - playOffset;
    if (offset < rightOffset) {
      // 修正右侧的偏移，防止超出item边界
      LogUtil.logOnAll(TAG, "outOfBound", offset, rightOffset);
      offset = rightOffset;
    }
    this.scrollToOffset({ animated: false, offset: offset });
    // let scrollParam = { animated: false, sectionIndex: itemInfo.sectionIndex, itemIndex: itemInfo.index, viewPosition: 0, viewOffset: viewOffset };
    // this.scrollToLocation(scrollParam);
  }

  // 滚动到时间轴的最右侧
  scrollToEnd(anim = false) {
    LogUtil.logOnAll(TAG, "scrollToEnd", this.props.isPlaying);
    const listHeaderH = HeaderH;
    let scrollParam = { animated: anim, sectionIndex: 0, itemIndex: 0, viewPosition: 0, viewOffset: 0 };
    this.scrollToLocation(scrollParam);
  }


  scrollToOffset(scrollParam = { animated: false, offset: 0 }) {
    if (this.mSectionList != null) {
      this.mSectionList && this.mSectionList._wrapperListRef._listRef.scrollToOffset(scrollParam);
      // this.ignoreScrollDelay && clearTimeout(this.ignoreScrollDelay);
      // this.ignoreScrollDelay = setTimeout(() => this.ignoreScroll = false, 100); // 延迟重置标志位
    }
  }

  scrollToLocation(scrollParam = { animated: false, sectionIndex: 0, itemIndex: 0, viewPosition: 0, viewOffset: 0 }) {
    if (this.mSectionList != null) {
      // this.ignoreScroll = true;
      if (this.props.data && this.props.data.length > 0) {
        this.mSectionList.scrollToLocation(scrollParam);
      }
    }
  }

  onMovePrev(oldItem, itemPosInfo) {
    // 查找上一个视频
    if (itemPosInfo == null) {
      LogUtil.logOnAll(TAG, "onMovePrev itemPosInfo is null");
      return;
    }
    let itemsInfo;
    if (this.state.expand) {
      itemsInfo = this.itemExpandInfo;
    } else {
      itemsInfo = this.itemInfo;
    }
    if (itemsInfo == null || itemsInfo.size <= 0) {
      return;
    }
    let infoKeys = [...itemsInfo.keys()];
    if (infoKeys == null || infoKeys.length == 0) {
      return;
    }

    infoKeys.sort((a1, a2) => {
      return a1 - a2;
    });

    console.log("+++=+=+=+==+=+==+==",itemPosInfo, infoKeys,itemsInfo);
    let findItemInfo = null;
    // 从后往前找
    for (let i = 0; i < infoKeys.length; i++) {
      let itemInfo = itemsInfo.get(infoKeys[i]);

      if (itemInfo.index > itemPosInfo.index) {
        // 找的比这个大
        // 判断这个小的是否是正常的item
        if (itemInfo.isHeaderFooter) {
          continue;
        }
        findItemInfo = itemInfo;
        break;
      }

    }

    // 找到了
    if (findItemInfo != null) {
      let item = this.props.data[findItemInfo.sectionIndex].data[findItemInfo.itemIndex];
      this.oneClick(item, findItemInfo.itemIndex, findItemInfo.sectionIndex);
    }

  }

  onMoveNext(oldItem, itemPosInfo) {
    // 查找下一个视频
    let itemsInfo;
    if (this.state.expand) {
      itemsInfo = this.itemExpandInfo;
    } else {
      itemsInfo = this.itemInfo;
    }
    console.log("=================", itemPosInfo);
    if (itemsInfo == null || itemsInfo.size <= 0) {
      return;
    }
    console.log("=================0");

    let infoKeys = [...itemsInfo.keys()];
    if (infoKeys == null || infoKeys.length == 0) {
      return;
    }
    console.log("=================1");

    infoKeys.sort((a1, a2) => {
      return a1 - a2;
    });

    let findItemInfo = null;
    // 从后往前找
    for (let i = infoKeys.length - 1; i >= 0; i--) {
      let itemInfo = itemsInfo.get(infoKeys[i]);

      if (itemInfo.index < itemPosInfo.index) {
        // 找的比这个小的
        // 判断这个小的是否是正常的item
        if (itemInfo.isHeaderFooter) {
          continue;
        }
        findItemInfo = itemInfo;
        break;
      }
    }

    // 找到了
    if (findItemInfo != null) {
      console.log("=================xxxxx", findItemInfo);

      let item = this.props.data[findItemInfo.sectionIndex].data[findItemInfo.itemIndex];
      this.oneClick(item, findItemInfo.itemIndex, findItemInfo.sectionIndex);
    }
  }

  renderSectionFooter = ({ section: { title } }) => {
    return (
      <View
        key={`${title}`}
        style={ {
          backgroundColor: carStyles.lineDayBg.backgroundColor,
          alignItems: 'center',
          justifyContent: 'center',
          marginTop: 29,
          width: 78,
          height: 30,
          borderRadius: 39,
          marginRight: Constants.DEFAULT_TEXT_MARGIN_BOTTOM }}>
        <Text style={ { fontSize: CarFont.Size._22 } }>{ DateFormater.instance().formatTimestampToMMDD(title) }</Text>
      </View>
    );
  };

  renderHeader = (info) => {
    return (
      <View style={ {
        // alignItems: 'center',
        justifyContent: 'center',
        // flexDirection: "row",
        width: HeaderH,
        height: 88,
        // borderRadius: 39,
        // opacity: this.props.highWindowFront ? 0.35 : 1,

        backgroundColor: carStyles.itemBgStyle.backgroundColor
      } }>
        {
          this.props.highWindowFront && !this.props.isPlaying ? null :
            <Image style={{ height: 63, width: 3, radius: 3 }} source={DarkMode.getColorScheme() === "dark" ? require('../../../Resources/Images/car/car_time_line_divider.png') : require('../../../Resources/Images/car/car_time_line_divider_light.png')}/>
        }
      </View>
    );
  };

  renderFoot = () => {
    return (
      <View
        style={ {
          padding: 10,
          marginTop: 29,
          width: this.props.footerWidth,
          height: 30,
          borderRadius: 39
      } }>
      </View>
    );
  };
}