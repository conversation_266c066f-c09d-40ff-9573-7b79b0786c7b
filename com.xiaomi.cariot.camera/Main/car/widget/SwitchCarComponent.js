import React from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import PropTypes from 'prop-types';
import { styles, Opacity, Switch } from "micariot-ui-sdk";
import { DarkMode } from "miot";

const DEFAULT_MARGIN_LEFT_RIGHT = 32;
const DEFAULT_TEXT_MARGIN_TOP = 8;
const DEFAULT_TITLE_MAX_LINES = 1;
const DEFAULT_SUBTITLE_MAX_LINES = 2;
const DEFAULT_HEIGHT = 120;

/**
 * @export public
 * @module SwitchComponent
 * @description SwitchComponent for CarIoT
 * @property {bool} value - 开关状态，默认值 false
 * @property {function} onValueChange - 切换开关的回调函数
 * @property {string} title - 主标题
 * @property {string} subTitle - 副标题
 * @property {bool} disabled - 是否禁用，默认值 false
 * @property {object} style - 自定义style
 * @property {bool} disableAnim - 禁用动画，可选参数，true为禁用动画，false或者未定义为有动画
 */
class SwitchCarComponent extends React.PureComponent {
    static propTypes = {
      value: PropTypes.bool,
      onValueChange: PropTypes.func.isRequired,
      onInfoPress: PropTypes.func,
      title: PropTypes.string,
      subTitle: PropTypes.string,
      disabled: PropTypes.bool,
      style: PropTypes.object,
      disableAnim: PropTypes.bool
    };

    constructor(props) {
      super(props);
    }

    componentDidMount() {
    
    }

    render() {
      const opacity = this.props.disabled ? Opacity.Disabled : Opacity.Normal;
      const {
        colorScheme
      } = this.context;
      return (
        <View
          style={[{
            flexDirection: "row",
            alignItems: "center",
            height: DEFAULT_HEIGHT
          }, this.props.style]}
        >
          <Switch
            ref={(ref) => {
              this.switchRef = ref;
            }}
            value={this.props.value}
            onValueChange={this.props.onValueChange}
            disabled={this.props.disabled}
            disableAnim={this.props.disableAnim}
          />
          <View
            style={{
              marginLeft: DEFAULT_MARGIN_LEFT_RIGHT,
              flex: 1,
              opacity
            }}
          >
            <View style={{flexDirection: 'row'}}>
              <Text style={[styles.titleTextStyle]} numberOfLines={DEFAULT_TITLE_MAX_LINES}>{this.props.title}</Text>
              <TouchableOpacity
                style={{ marginLeft: 20 }}
                onPress={this.props.onInfoPress}>
                <Image style={{ width: 40, height: 40 }} source={ DarkMode.getColorScheme() === 'dark' ? require('../../../Resources/Images/car/car_info.png') : require('../../../Resources/Images/car/car_info_light.png')}/>
              </TouchableOpacity>
            </View>

            {this.props.subTitle ?
              <Text style={[styles.subTitleTextStyle, { marginTop: DEFAULT_TEXT_MARGIN_TOP }]} numberOfLines={DEFAULT_SUBTITLE_MAX_LINES}>{this.props.subTitle}</Text> : null}
          </View>
        </View> 
      );
    }
}

export default SwitchCarComponent;
export { SwitchCarComponent };