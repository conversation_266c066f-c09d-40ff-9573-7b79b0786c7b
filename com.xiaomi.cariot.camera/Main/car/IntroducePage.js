import React from 'react';
import {
  View,
  ScrollView,
  Text,
  TouchableOpacity,
  Image, TouchableWithoutFeedback, StyleSheet
} from 'react-native';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import {
  styles,
  Constants,
  Font
} from 'micariot-ui-sdk';
import { CarPluginWindow, carStyles } from './common/Styles';
import { DarkMode, PackageEvent } from "miot";
import Toast from "../components/ToastCar";
import ProjectInfo from "../../project.json";

const TAG = "IntroducePage";
export default class IntroducePage extends React.Component {
  static navigationOptions = (navigation) => {
    return {
      headerTransparent: true,
      header: null
    };
  };

  constructor(props, context) {
    super(props, context);
    this.state = {
    };
  }

  componentDidMount() {

  }

  render() {

    return (
      <View style={ [carStyles.mediumLargeContainerStyle, { paddingLeft: 0, paddingRight: 0 }] }>
        { this.renderTitle() }
        <ScrollView
          contentContainerStyle={ {
            paddingBottom: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
            paddingLeft: CarPluginWindow.MediumLarge.MarginLeft_Right,
            paddingRight: CarPluginWindow.MediumLarge.MarginLeft_Right,
            marginTop: 68
          } }
          showsVerticalScrollIndicator={ true }
          scrollEventThrottle={ 16 }>
          <View style={stylesIntro.content}>
            <View style={stylesIntro.contentImg}>
              <Image style={stylesIntro.img} source={DarkMode.getColorScheme() == 'dark' ? require("../../Resources/Images/car/car_sd_intro_dark.png") : require("../../Resources/Images/car/car_sd_intro.png")}/>
            </View>
            <View style={stylesIntro.contentMsg}>
              <Text style={[styles.buttonTextStyle, stylesIntro.msgTx]}>{LocalizedStrings['how_view_msg1']}</Text>
              <Text style={[styles.buttonTextStyle, stylesIntro.msgTx]}>{LocalizedStrings['how_view_msg2']}</Text>
              <Text style={[styles.buttonTextStyle, stylesIntro.msgTx]}>{LocalizedStrings['how_view_msg3']}</Text>
              <Text style={[carStyles.attentionStyle]}>{LocalizedStrings['how_view_msg_attention']}</Text>
            </View>
          </View>
        </ScrollView>
      </View>
    );
  }

  renderTitle() {

    return (
      <View style={ [{
        flexDirection: 'row',
        alignItems: 'center',
        height: 148,
        paddingLeft: CarPluginWindow.MediumLarge.MarginLeft_Right,
        paddingRight: CarPluginWindow.MediumLarge.MarginLeft_Right
      }, this.props.style] }>
        <TouchableOpacity
          onPress={ () => {
            this.props.navigation.goBack();
          } }
        >
          <Image
            style={ { width: 48, height: 48 } }
            source={ DarkMode.getColorScheme() == 'dark' ? require("../../Resources/Images/car/ic_nav_close.png") : require("../../Resources/Images/car/ic_nav_close_light.png") }/>
        </TouchableOpacity>
        <View
          style={ {
            flexDirection: 'row',
            alignItems: 'center',
            flex: 1,
            justifyContent: 'center',
            width: '100%',
            marginLeft: CarPluginWindow.MediumLarge.MarginLeft_Right,
            // height: '100%',
            position: 'absolute'
          } }
        >
          <TouchableWithoutFeedback
            onLongPress={ () => {
              let version_info = `plugin version_code:${ ProjectInfo.version_code }`;

              Toast._showToast(version_info);
            } }
          >
            <Text numberOfLines={ 1 } style={ [styles.titleTextStyle, {
              fontSize: Font.Size._36
            }] }>{ LocalizedStrings['how_export_video_title'] }</Text>
          </TouchableWithoutFeedback>
        </View>
      </View>
    );
  }

  componentWillUnmount() {
    
  }
}

const stylesIntro = StyleSheet.create({
  content: {
    display: "flex",
    flexDirection: "row"
  },
  contentImg: {
    width: 327,
    height: 709,
    marginLeft: 86
  },
  img: {
    width: 327,
    height: 709
  },
  contentMsg: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    marginLeft: 138
  },
  msgTx: {
    marginBottom: 56
  },
  msgTxAttention: {

  }
});
