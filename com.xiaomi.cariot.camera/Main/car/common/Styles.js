// import DynamicColor from './DynamicColor';
// import { dynamicStyleSheet } from './DynamicStyleSheet';

import { DynamicColor, dynamicStyleSheet } from "miot/ui";
import { Radius, PluginWindow, Color, Font, Constants } from 'micariot-ui-sdk';

export const CarConstants = {
  VIDEO_CONTENT_WIDTH: 1152,
  VIDEO_CONTENT_HEIGHT: 648,
  MARGIN_18: 18
};
// 插件窗口参数
export const CarPluginWindow = {
  MediumLarge: {
    Width: 1296,
    Height: 1053,
    MarginLeft_Right: 72
  },
};
export const CarRadius = {
  Radius12: 12
};
// 抽取字体style
export const CarFont = {
  Size: {
    _22: 22
  },
  Color: {
    Light: {
      TextWhite: '#FFFFFF',
      TextCancel: '#121A2E',
      blue: '#1F8FFF',

    },
    Dark: {
      TextWhite: '#FFFFFFE5',
      TextCancel: '#FFFFFFE5',
      blue: '#1F8FFF'
    }
  }
};

export const CarColor = {
  Border: '#FFFFFFCC',
  ShadeCover: '#00000033',
  Light: {
    innerPrimary: '#EFF3FB',
    innerVideoBg: '#EFF3FB',
    selectBg: '#FFFFFF',
    nearHandBg: '#D6DDEB',
    nearHandPress: '#2A3A5C14',
    lineContent: '#FDFDFF',
    progress: '#0A6CFF',
    progressBg: '#121B2E1F',
    black_30: '#60697A4C',
    line: '#313742',
    // homeAbnormal: '#B9C0CE',
    // homeAbnormal: '#C4CCDD72',
    homeAbnormal: '#CED5E4',
    homeAbnormalBg: '#C4CCDD72',
    loadingBg: '#CED5E5',
    itemSecondaryBg: '#EBF0FB',
    txRed: '#E44338',
    homeBar: '#EBF0FB',
    itemBorder: "#5A677FA6",
    // itemBorder: "#828997",
    // itemBorderSelect: "#156EF4",
    // itemBorderSelect: "#121B2E99",
    itemBorderSelect: "#121A2E",
    group: "#06173A80",
    calDayItem: "#121B2E99",
    calDayUnableItem: '#C7CCD6',
    shadowColor: '#00000014',
    livePressColor: '#0D68F2',
    liveNormalPressColor: '#E5EAF6',
    selectedItem: '#0073E514',
    selectedItemTx: '#156EF4',
    AccentSecondary: '#EBF0FB',
    blurMask: '#00000066',

  },
  Dark: {
    innerPrimary: '#484C56',
    innerDayPrimary: '#383B43',
    innerVideoBg: '#383B43',
    selectBg: '#FFFFFF33',
    nearHandBg: '#25272D',
    nearHandPress: '#FFFFFF14',
    lineContent: '#484C56',
    progress: '#0060F0',
    progressBg: '#C1CCE21F',
    white_30: '#FFFFFF4C',
    homeAbnormal: '#2E3138',
    loadingBg: '#2E3138',
    itemSecondaryBg: '#373A43',
    txRed: '#FB4C3C',
    homeBar: '#383B43',
    itemBorder: "#FFFFFFA6",
    // itemBorderSelect: "#0060F0",
    // itemBorderSelect: "#FFFFFFE5",
    itemBorderSelect: "#FFFFFF",
    group: "#D9E1F273",
    // calDayItem: '#FFFFFF',
    calDayItem: '#FFFFFFE5',
    calDayUnableItem: '#FFFFFF7A',
    shadowColor: '#00000066',
    livePressColor: '#146DF1',
    liveNormalPressColor: '#383A43',
    selectedItem: '#0080FF14',
    selectedItemTx: '#1F8FFF',
    AccentSecondary: '#383B43',
    blurMask: '#000000B2',
    attention: "#FFFFFF7A"
  }
};

export const carStyles = dynamicStyleSheet({
  dialogStyle: {
    width: 640,
    alignItems: 'center',
    backgroundColor: new DynamicColor(Color.Light.SurfaceDialog, Color.Dark.SurfaceDialog),
    paddingLeft: 56,
    paddingRight: 56,
    paddingBottom: 56,
    borderRadius: Radius.PanelLevel,
    marginTop: 84
  },
  complexDialogStyle: {
    width: 864,
    alignItems: 'center',
    backgroundColor: new DynamicColor(Color.Light.SurfaceDialog, Color.Dark.SurfaceDialog),
    paddingLeft: 56,
    paddingRight: 56,
    paddingBottom: 56,
    borderRadius: Radius.PanelLevel,
    marginTop: 84
  },
  modalStyle: {
    backgroundColor: new DynamicColor(Color.Light.Shade, Color.Dark.Shade)
  },

  mediumLargeContainerStyle: {
    width: CarPluginWindow.MediumLarge.Width,
    height: CarPluginWindow.MediumLarge.Height,
    paddingLeft: CarPluginWindow.MediumLarge.MarginLeft_Right,
    paddingRight: CarPluginWindow.MediumLarge.MarginLeft_Right,
    backgroundColor: new DynamicColor(Color.Light.SurfaceDialog, Color.Dark.SurfaceDialog)
  },

  videoContainerStyle : {
    width: 1152,
    height: 648
  },
  buttonUnselected: {
    backgroundColor: new DynamicColor(CarColor.Light.innerPrimary, CarColor.Dark.innerPrimary)
  },

  innerVideoContainerStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.innerPrimary, CarColor.Dark.innerDayPrimary),
    borderRadius: Radius.WidgetLevel
  },
  daySelectStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.selectBg, CarColor.Dark.selectBg),
    borderRadius: 72,
    width: 72,
    height: 72,
    justifyContent: 'center',
    alignItems: 'center'
  },

  dayUnselectStyle: {
    width: 72,
    height: 72,
    justifyContent: 'center',
    alignItems: 'center'
  },

  nearHandStyle : {
    backgroundColor: new DynamicColor(CarColor.Light.nearHandBg, CarColor.Dark.nearHandBg),
  },
  nearHandItemStyle : {
    backgroundColor: new DynamicColor(CarColor.Light.nearHandPress, CarColor.Dark.nearHandPress),
  },
  lineDayBg: {
    backgroundColor: new DynamicColor(CarColor.Light.lineContent, CarColor.Dark.lineContent)
  },
  progressStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.progress, CarColor.Dark.progress)
  },
  progressBgStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.progressBg, CarColor.Dark.progressBg)
  },

  dialogButtonSureStyle: {
    color: new DynamicColor(CarFont.Color.Light.TextWhite, CarFont.Color.Dark.TextWhite),
    fontSize: Font.Size._26
  },

  dialogButtonCancelStyle: {
    color: new DynamicColor(CarFont.Color.Light.TextCancel, CarFont.Color.Dark.TextCancel),
    fontSize: Font.Size._26
  },

  videoStorageNotOpenImage: {
    width: 200,
    height: 128,
    marginTop: 200,
    marginBottom: Constants.DEFAULT_TEXT_MARGIN_BOTTOM
  },

  storageTitle: {
    color: new DynamicColor(Font.Color.Light.TextPrimary, Font.Color.Dark.TextPrimary),
    fontSize: Font.Size._28
  },
  homeAbnormalStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.homeAbnormal, CarColor.Dark.homeAbnormal),
  },
  selectAllStyle: {
    color: new DynamicColor(CarFont.Color.Light.blue, CarFont.Color.Dark.blue),
    fontSize: Font.Size._28,
    paddingHorizontal: 15
  },

  videoErrorImageStyle : {
    width: 200,
    height: 128,
  },

  centerLineContentStyle: {
    position: "absolute",
    justifyContent: "center",
    alignItems: 'center',
    // backgroundColor: new DynamicColor(CarColor.Light.black_30, CarColor.Dark.white_30),
    width: 24,
    height: 88
  },

  centerLineStyle: {
    position: "absolute",
    backgroundColor: new DynamicColor(CarColor.Light.line, CarColor.Light.selectBg),
    width: 4,
    height: 88,
  },

  videoLoadingStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.loadingBg, CarColor.Dark.loadingBg)
  },
  itemSecondaryBgStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.itemSecondaryBg, CarColor.Dark.itemSecondaryBg)
  },

  redTxColorStyle: {
    color: new DynamicColor(CarColor.Light.txRed, CarColor.Dark.txRed)
  },

  homeBarStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.homeBar, CarColor.Dark.homeBar)
  },
  itemBorderStyle: {
    color: new DynamicColor(CarColor.Light.itemBorder, CarColor.Dark.itemBorder)
  },
  itemBorderSelectStyle: {
    color: new DynamicColor(CarColor.Light.itemBorderSelect, CarColor.Dark.itemBorderSelect)
  },

  groupStyle: {
    color: new DynamicColor(CarColor.Light.group, CarColor.Dark.group)
  },

  dayItemStyle: {
    color: new DynamicColor(CarColor.Light.calDayItem, CarColor.Dark.calDayItem),
    fontSize: 24
  },
  dayItemUnableStyle: {
    color: new DynamicColor(CarColor.Light.calDayUnableItem, CarColor.Dark.calDayUnableItem),
    fontSize: 24
  },
  shadowStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.shadowColor, CarColor.Dark.shadowColor)
  },
  livePressStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.livePressColor, CarColor.Dark.livePressColor)
  },
  liveNormalPressStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.liveNormalPressColor, CarColor.Dark.liveNormalPressColor)
  },
  nearHandSelectedItemStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.selectedItem, CarColor.Dark.selectedItem),
    color: new DynamicColor(CarColor.Light.selectedItemTx, CarColor.Dark.selectedItemTx),
  },
  itemBgStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.AccentSecondary, CarColor.Dark.AccentSecondary)
  },
  blurMaskStyle: {
    backgroundColor: new DynamicColor(CarColor.Light.blurMask, CarColor.Dark.blurMask)
  },
  attentionStyle: {
    color: new DynamicColor(CarColor.Light.calDayItem, CarColor.Dark.attention),
    fontSize: Font.Size._26
  }

});

export const Opacity = {
  // 正常
  Normal: 1,
  // 置灰
  Disabled: 0.35
};
