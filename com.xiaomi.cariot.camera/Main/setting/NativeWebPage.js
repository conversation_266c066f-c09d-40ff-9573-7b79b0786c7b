/* eslint-disable key-spacing */
// react-native 代码
/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 * @flow
 */
import { Service } from 'miot';
import { Device } from 'miot/device';
import Host from 'miot/Host';
import React, { Component } from 'react';
import { BackHandler, Dimensions, NativeModules, Platform, StyleSheet, Text, TouchableHighlight, View, Image, TouchableOpacity, ActivityIndicator, TouchableWithoutFeedback } from 'react-native';
import StorageKeys from '../StorageKeys';
import Toast from '../components/Toast';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { WebView } from "react-native-webview";
import { NavigationBar } from 'mhui-rn';
import CameraConfig from '../util/CameraConfig';
import VersionUtil from '../util/VersionUtil';
import { CAMERA_CONTROL_SEPC_PARAMS } from '../Constants';
import ParsedText from 'react-native-parsed-text';

const { height, width } = Dimensions.get('window');

// 用来展示web页面，点web跳转到不同的rn页面。
export default class NativeWebPage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isVip: false,
      _loading: false
    };
    this.data = 0;
    //  this.sendMessage = this.sendMessage.bind(this);
    //  this.handleMessage = this.handleMessage.bind(this);

    this.title = this.props.navigation.getParam("title", "");
    this.showFace = this.props.navigation.getParam("showFace", false);
    this.sdcardCode = this.props.navigation.getParam("sdcardCode", false);
    this.sdcardStatus = this.props.navigation.getParam("sdcardStatus", 0);
    this.isVip = this.props.navigation.getParam("isVip", 0);
    this.isShowSdcardPage = this.props.navigation.getParam("isShowSdcardPage", 0);
    // this.props.navigation.navigate("SdcardCloudTimelinePage", { sdcardCode: this.sdcardCode, isVip: this.isVip, isShowSdcardPage: showSdcard });

    this.url = this.props.navigation.getParam("url");

    StorageKeys.IS_VIP_STATUS
      .then((result) => {
        this.setState({ isVip: result });
      });

    StorageKeys.IS_SHOW_SDCARD_PAGE.then((res) => {
      this.showSdcardPage = res;
    });
    this.isSupportCountry = CameraConfig.isSupportCloud();
    this.onLinkPressed = this.onLinkPressed.bind(this);
    this.canGoback = false;

    this.userAgent = `MiHome/${ Host.version }${ Platform.OS === "ios" ? (` APPV/${ Host.version }`) : (` Android-${ Host.systemInfo.sysVersion }-${ Host.version }-${ Host.systemInfo.miuiVersion != null ? (`miui-${ Host.systemInfo.miuiVersion }-SmartHome`) : "SmartHome" }`) }`;
  }

  componentDidMount() {
    if (Platform.OS == "android") {
      BackHandler.addEventListener('hardwareBackPress', this.goBack);
    }

    // show:true

    this.props.navigation.setParams({

      title: this.title,
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
            if (this.canGoback) {
              this.webview && this.webview.goBack();
              return;
            }
            this.props.navigation.goBack(); 
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }

    });
  }

  componentWillUnmount() {
    if (Platform.OS == "android") {
      BackHandler.removeEventListener('hardwareBackPress', this.goBack);
    }
  }

  goBack = () => {
    if (this.canGoback) {
      this.webview && this.webview.goBack();
      return true;
    }
    return false;
  }

  render() {
    // 可测试跳转
    // const uri = { uri: "https://www.baidu.com" };
    //  const uri = require('../../Resources/index.html')
    let { _loading } = this.state;
    return (
      <View style={styles.container}>
        {
          Platform.OS == "android" ?
            <WebView
              style={styles.webview}
              source={{ uri: this.url }}
              ref={(webview) => this.webview = webview}
              onLoadStart = { () => { this.setState({ _loading:true }); } }
              onLoadEnd={(syntheticEvent) => {
                // update component to be aware of loading status
                const { nativeEvent } = syntheticEvent;
                this.isLoading = nativeEvent.loading;
                
                // since this issue arise on android only. delay only on android platform
                if (Platform.OS === 'android') {
                  // delay loader to hide default android webpage not available screen
                  setTimeout(() => {
                    this.setState({ _loading:false, showError: true });
                  }, 300);
                } else {
                  this.setState({ _loading:false, showError: true });
                }
              }}
              renderError={this.renderError}
              onNavigationStateChange={(event) => {
                console.log(event);
                this.canGoback = event.canGoBack;
                if (event.title == event.url) { // 避免显示url作为标题
                  return;
                }
                this.title = event.title;
                this.props.navigation.setParams({
                  title: event.title
                });
              }}
              injectedJavaScript={`
            window.currentTitle = document.title;
            setInterval(function() {
              if (window.currentTitle != document.title) {
                  window.currentTitle = document.title;
                  window.postMessage('$#doctitle-' + document.title);
              }else{
                console.log('hechufang res:' + document.title);
              }
            }, 500);
            window.addEventListener('popstate', function() {
              window.postMessage(document.title)
            })
          `}
              onMessage={(e) => {
                const message = e.nativeEvent;
                if (message.data && message.data.indexOf("$#doctitle-") == 0) {
                  this.title = message.data.split('-')[1];
                  this.props.navigation.setParams({
                    title: this.title
                  });
                }
                this.canGoback = message.canGoBack;
              }}
              onShouldStartLoadWithRequest={this.onLinkPressed}
              showsHorizontalScrollIndicator={false}
              showsVerticalScrollIndicator={false}
              // userAgent={(Platform.OS === "android") ? "MiHome Android" : "MiHome"}
              userAgent={this.userAgent}
              sharedCookiesEnabled={true}
            />
            :
            <WebView
              style={styles.webview}
              source={{ uri: this.url }}
              ref={(webview) => this.webview = webview}
              onLoadStart = { () => { this.setState({ _loading:true }); } }
              onLoadEnd={(syntheticEvent) => { // ios internal link click will not trigger the callback
                // update component to be aware of loading status
                const { nativeEvent } = syntheticEvent;
                this.isLoading = nativeEvent.loading;
                
                // since this issue arise on android only. delay only on android platform
                if (Platform.OS === 'android') {
                  // delay loader to hide default android webpage not available screen
                  setTimeout(() => {
                    this.setState({ _loading:false, showError: true });
                  }, 300);
                } else {
                  this.setState({ _loading:false, showError: true });
                }
              }}
              renderError={this.renderError}
              onNavigationStateChange={(event) => {
                console.log(event);
                if (this.jump2url && event?.url?.toLowerCase() == this.jump2url && !event.loading) {
                  this.setState({ _loading:false});
                }
                this.canGoback = event.canGoBack;
                this.title = event.title;
                this.props.navigation.setParams({
                  title: event.title
                });
              }}
              injectedJavaScript={`
              window.currentTitle = document.title;
              // alert(location.href)
              setInterval(function() {
                // alert(document.title)
                  if (window.currentTitle != document.title) {
                      window.currentTitle = document.title;
                      window.postMessage('$#doctitle-' + document.title);
                  }
              }, 500);
            window.addEventListener('popstate', function() {
              window.postMessage('$#doctitle-' + document.title)
            })
            window.addEventListener('pagehide', function() {
              window.currentTitle = '';
            })
          `}
              onMessage={(e) => {
              
                const message = e.nativeEvent;
                // alert(message.data)
                if (message.data && message.data.indexOf("$#doctitle-") == 0) {
                  this.title = message.data.split('-')[1];
                  this.props.navigation.setParams({
                    title: this.title
                  });
                }
                this.canGoback = message.canGoBack;
              }}
              onShouldStartLoadWithRequest={this.onLinkPressed}
              showsHorizontalScrollIndicator={false}
              showsVerticalScrollIndicator={false}
              // userAgent={(Platform.OS === "android") ? "MiHome Android" : "MiHome"}
              applicationNameForUserAgent={`MiHome/${ Host.version }${ Platform.OS === "ios" ? (` APPV/${ Host.version }`) : (` Android-${ Host.systemInfo.sysVersion }-${ Host.version }`) }`}
              sharedCookiesEnabled={true}
              incognito={false}
              cacheEnabled={false}
            />

        }
        {
          // this.renderLoading()
        }

      </View>
    );
  }
  renderLoading() {
    if (this.state._loading) {
      return (
        <View style={[StyleSheet.absoluteFill, {
          flex: 1,
          backgroundColor: 'white',
          alignItems: 'center',
          justifyContent: 'center',
          padding: 24
        }]}>
          <ActivityIndicator size="large" color="#8ec641" />
        </View>
      );
    }
  }
  renderError=() => {
    return (<View style={{ width: "100%", height: "100%", position: "absolute", display: "flex", justifyContent: "center", alignItems: "center", backgroundColor: "white" }}
    >
      <Image
        source={require("../../Resources/Images/webview_error.png")}
        style={{ width: 138, height: 138 }}
      >
      </Image>
      <ParsedText
        style={{ marginTop: 21, fontSize: 15 }}
        parse={
          [
            { pattern: /\%(.+?)\%/g, style: { color: "#32BAC0" }, onPress: this.handleRetryConnect, renderText: this.renderText }

          ]
        }
        childrenProps={{ allowFontScaling: false }}
      >
        {LocalizedStrings["webview_reconnect_network_tip"]}
      </ParsedText>
      <TouchableOpacity
        style={{ textAlign: "center", borderRadius: 20, margin: 30, marginStart: 30, marginEnd: 30, padding: 20, backgroundColor: "#f5f5f5" }}
        onPress={() => {
          this.webview.reload();
        }}>
        <Text style={{ fontSize: 15, color: "#1f2329" }}>
          {LocalizedStrings["webview_reconnect_refresh"]}
        </Text>
      </TouchableOpacity>
    </View>);
  }

  onError(syntheticEvent) {
    this.setState({ showError: true });
  }

  renderText(matchingString, matches) {
    
    let find = '\\%';
    let re = new RegExp(find, 'g');
    return matchingString.replace(re, '');
  }

  handleRetryConnect = () => {
    Host.ui.openTerminalDeviceSettingPage(2);
  }

  onLinkPressed(event) {
    if (event.url && event.url.includes(this.url)) {
      this.canGoback = false;
    }
    console.log(event.url); 
    let url = event.url.toLowerCase();
    this.jump2url = url;
    let shouldHandle = false;
    if (url.includes("camera_PresetPosition".toLowerCase())) {
      Toast.fail("action_failed");
      // ignore  currently
      shouldHandle = true;
    } else if (url.includes("camera_Sleep".toLowerCase())) {
      // this.props.navigation.navigate("SleepSetting");
      let params = {
        onMethod: VersionUtil.isUsingSpec(Device.model) ? "set_properties" : "set_power",
        onParam: VersionUtil.isUsingSpec(Device.model) ? [{ ...CAMERA_CONTROL_SEPC_PARAMS[6], value: false }] : "off",
        offMethod: VersionUtil.isUsingSpec(Device.model) ? "set_properties" : "set_power",
        offParam: VersionUtil.isUsingSpec(Device.model) ? [{ ...CAMERA_CONTROL_SEPC_PARAMS[6], value: true }] : "on",
        // displayName: "自定义场景名称",
        // identify: Device.deviceID,
        // onTimerTips: '',
        // offTimerTips: '定时列表页面、设置时间页面 关闭时间副标题（默认：关闭时间）',
        // listTimerTips: '定时列表页面 定时时间段副标题（默认：开启时段）',
        bothTimerMustBeSet: true,
        showOnTimerType: true,
        showOffTimerType: true,
        showPeriodTimerType: true
      };
      if (CameraConfig.isSupportPhysicalCover(Device.model)) {
        params = {
          onMethod: VersionUtil.isUsingSpec(Device.model) ? "set_properties" : "set_power",
          onParam: VersionUtil.isUsingSpec(Device.model) ? [{ ...CAMERA_CONTROL_SEPC_PARAMS[6], value: false }] : "off",
          offMethod: VersionUtil.isUsingSpec(Device.model) ? "set_properties" : "set_power",
          offParam: VersionUtil.isUsingSpec(Device.model) ? [{ ...CAMERA_CONTROL_SEPC_PARAMS[6], value: true }] : "on",
          displayName: LocalizedStrings["physical_cover_auto"],
          identify: Device.deviceID,
          onTimerTips: LocalizedStrings["physical_cover_start_time"],
          offTimerTips: LocalizedStrings["physical_cover_end_time"],
          listTimerTips: LocalizedStrings["physical_cover_fixtime"],
          bothTimerMustBeSet: true,
          showOnTimerType: true,
          showOffTimerType: true,
          showPeriodTimerType: true
        };
      }
      Service.scene.openTimerSettingPageWithOptions(params);

      shouldHandle = true;
    } else if (url.includes("camera_PlayBack".toLowerCase())) {
      let showSdcard = this.showSdcardPage;
      if (this.sdcardCode == 3) {
        showSdcard = false;
      }
      // 能够打开小贴士的  都默认有cloud吧

      this.props.navigation.navigate("SdcardCloudTimelinePage", { sdcardCode: this.sdcardCode, isVip: this.isVip, isShowSdcardPage: showSdcard, isSupportCloud: this.isSupportCountry });// todo 待实现回看的代码
      shouldHandle = true;
    } else if (url.includes("camera_NightVision".toLowerCase())) {

      this.props.navigation.navigate(CameraConfig.isNewNightVision(Device.model) ? "NightVisionSettingV2" : "NightVisionSetting");
      shouldHandle = true;
    } else if (url.includes("camera_Face".toLowerCase())) {
      if (VersionUtil.isAiCameraModel(Device.model)) {
        this.props.navigation.navigate('FaceManager2')
      } else {
        this.state.isVip ? this.props.navigation.navigate('FaceManager', { isVip: this.isVip }) : this.props.navigation.navigate('NoVipFaceManager');
      }
      shouldHandle = true;
    } else if (url.includes("camera_RotateImage".toLowerCase())) {
      if (CameraConfig.isSupportImageRotate(Device.model)) {
        this.props.navigation.navigate("ImageRotateSetting");
      } else {
        Toast.fail("not_support_function");
      }
      shouldHandle = true;
    } else if (url.includes("camera_wdr".toLowerCase())) {
      this.props.navigation.navigate("WDRSetting");
      shouldHandle = true;
    } else if (url.includes("camera_sdcardstorage".toLowerCase())) {

      this.props.navigation.navigate("SDCardSetting");
      shouldHandle = true;
    } else if (url.includes("camera_Share".toLowerCase())) {
      shouldHandle = true;
      // here open share device activity
      if (!Device.isOwner) {
        Toast.fail("share_user_permission_hint");
        return;
      }
      Host.ui.openShareDevicePage();
    } else if (url.includes("mihome://device/goOut".toLowerCase())) {
      console.log("goout url", url);
      shouldHandle = true;
      let quoteIndex = url.indexOf("?");
      if (quoteIndex != -1) {
        let paramStr = url.substr(quoteIndex + 1);
        let strs = paramStr.split("&");
        let map = {};
        if (strs != null) {
          for (let i = 0; i < strs.length; i++) {
            let str = strs[i];
            let paramKeyValue = str.split("=");
            if (paramKeyValue.length == 2) {
              let key = paramKeyValue[0];
              let value = paramKeyValue[1];
              if (key.toLowerCase == "rectemplateid") {
                key = "rectemplateid";
              }
              map[key] = value;
            }
          }
        }
        map.did = Device.deviceID;
        try {
          NativeModules.MIOTService.goEditRecommendScene(map);
        } catch (error) {
          Toast.fail("action_failed", error);
        }
      } else {
        Toast.fail("action_failed");
      }

    }
    return !shouldHandle;
  }
}
const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  webview: {
    width: "100%",
    height: "100%"
  }
});