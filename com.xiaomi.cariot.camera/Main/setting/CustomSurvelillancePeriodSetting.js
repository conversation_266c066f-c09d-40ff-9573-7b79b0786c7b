'use strict';

import { Device } from "miot";
import { Styles } from 'miot/resources';
import MHDatePicker from 'miot/ui/MHDatePicker';
import { ListItem } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { View } from 'react-native';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import API from '../API';
import Toast from '../components/Toast';
import AlarmUtil from '../util/AlarmUtil';
import VersionUtil from '../util/VersionUtil';
import { NavigationBar } from "mhui-rn";

export default class CustomSurvelillancePeriodSetting extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      startTime: '08:00',
      endTime: '20:00',
      // not use
      detectionSwitch: false,
      interval: '',

      index: 0,

      isPickerVisiable: false,
      pickerTitle: '',

      pickerCurrentTime: ''
    };
    if (this.props.navigation.state.params) {
      this.startTime = this.props.navigation.state.params.startTime;
      this.endTime = this.props.navigation.state.params.endTime;
      this.commitCallback = this.props.navigation.state.params.commitCallback;
      this.igNetworkCommit = this.commitCallback != null;
    }
    
  }

  render() {
    return (
      <View style={styles.container}>
        <Separator />
        {/* {this.renderTitleView()} */}
        {
          [
            { title: 'csps_start', value: this.state.startTime },
            { title: 'csps_end', value: this.state.endTime }
          ].map((item, i) => {
            return (
              <ListItem
                key={1111 + i}
                title={LocalizedStrings[item.title]}
                subtitle={item.value}
                accessibilityLabel={LocalizedStrings[item.title] + item.value}
                onPress={() =>
                  this._onSelectedItem(i, item.title, item.value)
                } />
            );
          })
        }
        <MHDatePicker
          visible={this.state.isPickerVisiable}
          title={this.state.pickerTitle}
          type={MHDatePicker.TYPE.TIME24}
          current={this.state.pickerCurrentTime}
          onSelect={(res) =>
            this._onSelectTime(res)
          }
        />
      </View>
    );
  }

  renderTitleView() {
    const titleProps = {
      title: LocalizedStrings['csps_title'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.CLOSE,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      right: [
        {
          key: NavigationBar.ICON.COMPLETE,
          onPress: () => {
            Toast.loading('c_setting');
            this._putMotionDetection();
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        // color: '#333333',
        fontWeight: 500
      }
    };
    return (
      <NavigationBar {...titleProps} />
    );
  }

  componentDidMount() {
    this.props.navigation.setParams({
      title: LocalizedStrings['csps_title'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.CLOSE,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      right: [
        {
          key: NavigationBar.ICON.COMPLETE,
          onPress: () => {
            Toast.loading('c_setting');
            this._putMotionDetection();
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        // color: '#333333',
        fontWeight: 500
      }
    });

    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {

        if (this.igNetworkCommit) {
          this.setState({
            startTime: this.startTime,
            endTime: this.endTime
          });
          
          return;
        }
        this._getSetting();
      }
    );
  }

  componentWillUnmount() {
    this.willFocusSubscription.remove();
  }

  _getSetting() {
    if (VersionUtil.isUsingSpec(Device.model)) {
      AlarmUtil.getSpecAlarmConfig(2).then((result) => {
        if (result instanceof Array) {
          let detectionSwitch = result[0].value;
          let alarmInterval = result[1].value;
          let sensitivity = result[2].value;
          let startTime = result[3].value;
          let endTime = result[4].value;
          startTime = AlarmUtil.formatTimeString(startTime);
          endTime = AlarmUtil.formatTimeString(endTime);
          if (startTime && endTime && alarmInterval && detectionSwitch) {
            this.setState({
              detectionSwitch: detectionSwitch,
              interval: alarmInterval,
              startTime: startTime,
              endTime: endTime
            });
          } else {
            Toast.fail('c_get_fail');
          }
          
        }
      }).catch((err) => {
        console.log(`getSpecAlarmConfig err=${ JSON.stringify(err) }`);
        Toast.fail('c_get_fail', err);

      });
    } else {
      AlarmUtil.getAlarmConfig().then((res) => {
        if (res.code == 0) {
        } else {
          Toast.fail('c_get_fail');
          console.log('request fail');
          return;
        }
        let startTime = AlarmUtil.formatTimeString(res.data.motionDetectionSwitch.startTime);
        let endTime = AlarmUtil.formatTimeString(res.data.motionDetectionSwitch.endTime);
        if (res && res.data && res.data.motionDetectionSwitch && res.data.motionDetectionSwitch.detectionSwitch && res.data.motionDetectionSwitch.interval && res.data.motionDetectionSwitch.startTime && res.data.motionDetectionSwitch.endTime) {
          this.setState({
            detectionSwitch: res.data.motionDetectionSwitch.detectionSwitch,
            interval: res.data.motionDetectionSwitch.interval,
            startTime: startTime,
            endTime: endTime
          });
        } else {
          Toast.fail('c_get_fail');
        }
        
      }).catch((err) => {
        Toast.fail('c_get_fail', err);
      });
    }
  }

  _onSelectedItem(index, title, value) {
    this.setState({
      index: index,
      isPickerVisiable: true,
      pickerTitle: LocalizedStrings[title],
      pickerCurrentTime: value.split(':')
    });
  }

  _onSelectTime(time) {
    let timeStr = `${ time.rawString }`;
    if (this.state.index == 0) {
      this.setState({
        startTime: timeStr
      });
    } else {
      this.setState({
        endTime: timeStr
      });
    }
  }

  _putMotionDetection(type) {
    Toast.loading('c_setting');
    if (this.igNetworkCommit) {
      this.commitCallback(this.state.startTime + ":00", this.state.endTime + ":00")
        .then((res) => {
          this.props.navigation.goBack();
          Toast.success('c_set_success');
        })
        .catch((err) => {
          Toast.fail('c_set_fail', err);
        });
      return;
    }

    if (VersionUtil.isUsingSpec(Device.model)) {
      let param = { startTime: this.state.startTime + ":00", endTime: this.state.endTime + ":00" };
      AlarmUtil.putSpecMotionDetectionPeriod(param).then((res) => {
        this.props.navigation.goBack();
        Toast.success('c_set_success');
      }).catch((err) => {
        Toast.fail('c_set_fail', err);
      });
    } else {
      AlarmUtil.putMotionDetection({
        open: this.state.detectionSwitch,
        interval: this.state.interval,
        startTime: this.state.startTime + ":00",
        endTime: this.state.endTime + ":00"
      }).then((res) => {
        if (res.code == 0) {
          this.props.navigation.goBack();
          Toast.success('c_set_success');
        } else {
          Toast.fail('c_set_fail');
        }
      }).catch((err) => {
        Toast.fail('c_set_fail', err);
      });
    }
  }

}
