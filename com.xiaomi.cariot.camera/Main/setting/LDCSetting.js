'use strict';

import { Device } from "miot";
import { Styles } from 'miot/resources';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { StyleSheet, ScrollView, View, Image, Text, SafeAreaView, TouchableOpacity, Dimensions } from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import Toast from '../components/Toast';
import Host from "miot/Host";
import { styles } from './SettingStyles';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import StorageKeys from '../StorageKeys';
import VersionUtil from "../util/VersionUtil";
import Service from "miot/Service";
import TrackUtil from '../util/TrackUtil';
import { DescriptionConstants } from "../Constants";

// PropertyParam{did='1020163363', siid=2, piid=5, value=null, timestamp=0, resultCode=-1}
import NavigationBar from "miot/ui/NavigationBar";
import RPC from "../util/RPC";
import AlarmUtilV2, { PIID_CAMERA_CORRECTION, SIID_CAMERA_CONTROL } from "../util/AlarmUtilV2";
import CameraConfig from '../util/CameraConfig';
import BaseSettingPage from "../BaseSettingPage";

const WDR_SPEC = {
  Key: { did: Device.deviceID, siid: 2, piid: 5 },
  ON: [{ did: Device.deviceID, siid: 2, piid: 5, value: true }],
  OFF: [{ did: Device.deviceID, siid: 2, piid: 5, value: false }]
};

const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width);// use this.winPortraitWidth instead
const imageWidth = (kWindowWidth - 60) / 2;
const imageHeight = imageWidth / 1.5;

export default class LDCSetting extends BaseSettingPage {



  constructor(props, context) {
    super(props, context);

    this.state = {
      isLDCEnable: false
    };
    this.isLDCEnable = false;
  }
  getTitle() {
    return LocalizedStrings['is_lens_distortion_correction'];
  }

  renderSettingContent() {
    return (
      <View style={styles.container}>

          <View style={{ marginHorizontal: 24, marginTop: 20, marginBottom: 20, flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' }}>
            {
              [
                { text: LocalizedStrings['ldc_before_activation'], uri: require('../../Resources/Images/ldc_close_icon.webp') },
                { text: LocalizedStrings['ldc_after_activation'], uri: require('../../Resources/Images/ldc_open_icon.webp') }
              ].map((item, i) => {
                return (
                  <View
                    style={{ alignItems: 'center' }}
                    key={i}
                  >
                    <Image
                      key={i}
                      style={{ width: imageWidth, borderRadius: 12, height: imageWidth }}
                      source={item.uri}
                      accessibilityLabel={DescriptionConstants.example_image}
                    />
                    <Text
                      numberOfLines={2}
                      style={{ paddingHorizontal: 10, position: "absolute", bottom: 4, fontSize: 12, color: 'xm#ffffff' }}
                    >
                      {item.text}
                    </Text>
                  </View>
                );
              })
            }
          </View>

          <View style={styles.featureSetting}>
            <ListItemWithSwitch
              accessibilityLabel={DescriptionConstants.sz_4_59_1}
              title={LocalizedStrings['is_lens_distortion_correction']}
              value={this.state.isLDCEnable}
              onValueChange={(value) => this.onLDCValueChange(value)}
              showSeparator={false}
              titleStyle={{ fontWeight: 'bold' }}
              unlimitedHeightEnable={true}
              titleNumberOfLines={3}
              key={3}
              onPress={() => {
              }}
              accessibilitySwitch={{
                accessibilityLabel: DescriptionConstants.sz_4_59_1
              }}
            />
          </View>

          <View style={{ marginHorizontal: 27, marginTop: 12 }}
            key={6}
          >

            <Text
              accessibilityLabel={DescriptionConstants.sz_4_61}
              style={{ fontSize: 13, color: 'rgba(0,0,0,0.5)' }}
            >
              { `${LocalizedStrings['function_desc']  }\n${  LocalizedStrings['ldc_tip']}` }
            </Text>

          </View>
      </View>
    );
  }

  componentDidMount() {
    super.componentDidMount();
    if (this.props.navigation.state?.params?.isLDCEnable != null) {
      //reuse state;
      this.setState({ isLDCEnable: this.props.navigation.state.params.isLDCEnable });
    } else {
      this.get_ldc().then((res) => {
        this.setState({
          isLDCEnable: res
        });
      }).catch((err) => {
        Toast.fail('c_get_fail', err);
      });
    }
    

  }

  onLDCValueChange(value) {
    TrackUtil.reportClickEvent("LensDistortionCorrectionOnOff_ClickNum");
    if (CameraConfig.isDeviceCorrect(Device.model)) {
      const params = [{ sname: SIID_CAMERA_CONTROL, pname: PIID_CAMERA_CORRECTION, value: value }];
      AlarmUtilV2.setSpecPValue(params)
        .then((res) => {
          if (res[0].code == 0) {
            this.setState({
              isLDCEnable: value
            });
            if (this.props.navigation.state.params.selectLDCCallBack) {
              this.props.navigation.state.params.selectLDCCallBack(value);
            }
          } else {
            this.setState({
              isLDCEnable: !value
            });
            Toast.fail('c_set_fail');
          }
        })
        .catch((err) => {
          this.setState({
            isLDCEnable: !value
          });
          Toast.fail('c_set_fail', err);
        });
    } else {
      StorageKeys.IS_LENS_DISTORTION_CORREECTION = value;
      this.setState({
        isLDCEnable: value
      });
      Toast.success('c_set_success');
      if (this.props.navigation.state.params.selectLDCCallBack) {
        this.props.navigation.state.params.selectLDCCallBack(value);
      }
    }
  }

  static get_ldc() {
    
    this.isRequsting = true;
    return new Promise((resolve, reject) => {

      if (CameraConfig.isDeviceCorrect(Device.model)) {
        const params = [{ sname: SIID_CAMERA_CONTROL, pname: PIID_CAMERA_CORRECTION }];
        AlarmUtilV2.getSpecPValue(params,2)
          .then((res) => {
            if (res[0].code == 0) {
              resolve(res[0].value);
            } else {
              reject();
            }
          })
          .catch((err) => {
            reject();
          });
      }else {
        StorageKeys.IS_LENS_DISTORTION_CORREECTION.then((res) => {
          resolve(res);
        }).catch(() => {
          resolve(false);
        });
      }
    });

  }

}
