'use strict';

import { Device } from "miot";
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { ScrollView, View, Text, Platform, FlatList, Animated, Easing } from 'react-native';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import StorageKeys from '../StorageKeys';
import Toast from '../components/Toast';
import { Image } from "react-native";
import { LoadingDialog } from "miot/ui";
import CameraPlayer from "../util/CameraPlayer";
import NavigationBar from "miot/ui/NavigationBar";
import RPC from "../util/RPC";


export default class BandNearbyClosePage extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      loadingVisible: false,
      isRefreshing: false,
      isScaning: false,
      autoClose: false,
      bindedItems: [],
      unbindedItems: [],
      sectionData: [{ key: "first", data: [] }, { key: "second", data: [] }]
    };
    this.loadingImage = null;
    this.autoCloseSwitcher = null;
    this.queryCount = 5;
    this.twoSeconds = 2000;
    this.oneSeconds = 1000;
    this.scanResultTimeout = null;
    this.rotationAnim = new Animated.Value(0);
    this.animation = null;
    this.isComponentUnmounted = false;

  }

  render() {
    return (
      <View style={styles.container}>    
        <ScrollView>
          {this._renderHeader()}
          {this._renderSection1()}
          {this._renderBindedItems()}
          {this._renderSection2()}
          {this._renderUnbindedItems()}

        </ScrollView>

        <LoadingDialog
          visible={this.state.loadingVisible}
          message={LocalizedStrings["updating"]}
          timeout={0}
          cancelable={true}
        >

        </LoadingDialog>

      </View>


    );
  }



  _renderHeader() {
    return (
      <View style={{ width: "100%", display: "flex", flexDirection: "column" }}>
        <Text style={{ marginTop: 26, marginBottom: 16, paddingLeft: 13, paddingRight: 13, color: "black", fontSize: 13 }}>
          {LocalizedStrings["band_nearby_auto_close_title_1"]}
        </Text>

        <Text style={{ marginBottom: 12, paddingLeft: 13, paddingRight: 13, color: "black", fontSize: 13 }}>
          {LocalizedStrings["band_nearby_auto_close_title_2"]}
        </Text>

        <Image
          style={{ alignContent: "center", width: 334, height: 175, marginLeft: 13, marginRight: 13, resizeMode: "stretch" }}
          // style={{width: 100, height: 100, backgroundColor: "red"}}
          source={require("../../Resources/Images/set_bg_auto2_nor.png")}
        />

        <View
          style={{ marginTop: 26 }}
        />


        <ListItemWithSwitch
          titleStyle={{ fontWeight: 'bold' }}
          title={LocalizedStrings['band_nearby_auto_close_status_title']}
          value={this.state.autoClose}
          ref={(ref) => {
            this.autoCloseSwitcher = ref;
          }}
          onValueChange={(value) => this._onValueChanged(value)}
          containerStyle={{ backgroundColor: "#ffffff" }}
          showSeparator={false}

        />

      </View>

    );
  }


  _onValueChanged(value) {
    this.setState({ loadingVisible: true });
    console.log("是否打开开关？", value);
    RPC.callMethod("set_band_nearby", value ? ["on"] : ["off"])// 手环靠近就关闭  这个功能开启
      .then((result) => {
        console.log("toggle swtich result", result);
        this.setState({ autoClose: value, loadingVisible: false });
      })
      .catch((error) => {
        console.log("toggle swtich error", error);
        this.setState({ autoClose: !value, loadingVisible: false });
      });

  }

  _renderSection1() {
    return (
      <View style={{ width: "100%", height: 54, display: "flex" }}>

        <View
          style={{
            width: "100%",
            flexGrow: 1,
            // paddingBottom: 14,
            paddingLeft: 13,
            display: "flex",
            justifyContent: "center"
          }}
        >
          <Text style={{ color: "#00000066", fontSize: 12 }}>
            {LocalizedStrings["band_nearby_auto_close_device_band"]}
          </Text>

        </View>
        {/* <Separator /> */}


      </View >

    );
  }

  _renderSection2() {
    return (
      <View style={{ width: "100%", height: 54, display: "flex", flexDirection: "column" }}>

        <View
          style={{ width: "100%", flexGrow: 1, paddingLeft: 13, display: "flex", flexDirection: "row", alignItems: "center" }}
        >
          <Text
            style={{ color: "#808080", fontSize: 12 }}
          >
            {LocalizedStrings["band_nearby_auto_close_new_band_title"]}
          </Text>
          {
            this.state.isScaning ?
              <Animated.Image
                ref={(ref) => {
                  this.loadingImage = ref;
                }}
                style={{ width: 13, height: 13, marginLeft: 10, resizeMode: "stretch", transform: [{ rotate: this.rotationAnim.interpolate({ inputRange: [0, 1], outputRange: ["0deg", "360deg"] }) }] }}
                source={require("../../Resources/Images/aircon_band_scan.png")}
              />
              : null
          }
        </View>

        {/* <Separator /> */}
      </View >
    );
  }


  _renderBindedItems() {
    return (this.state.bindedItems.map((item, i) => {
      return (
        <ListItem
          titleStyle={{ fontWeight: 'bold' }}
          key={i}
          title={this._formatMac(item)}
          subtitle={item["isNearBy"] ? LocalizedStrings["band_nearby_auto_close_device_band_hint"] : ""}
          onPress={() => {
            this.props.navigation.navigate("BandInfoPage", {
              item: item, unbindCallback: () => {
                console.log("done removed");
                // 要从nearby中删除这个设备
                let bindItems = this.state.bindedItems;
                bindItems.splice(i, 1);// 删除这个设备
                this.setState({ bindedItems: bindItems });
              }
            });
          }}
          showSeparator={false}

        ></ListItem>

      );
    }));
  }

  _renderUnbindedItems() {
    return (this.state.unbindedItems.map((item, j) => {
      return (
        <ListItem
          titleStyle={{ fontWeight: 'bold' }}
          showSeparator={false}
          key={j}
          title={this._formatMac(item)}
          subtitle={Device.mac == item["mac"] ? LocalizedStrings["band_nearby_auto_close_device_band_hint"] : ""}
          onPress={() => { // 点了附近的设备，就要先删除附近的设备，再添加到已绑定设备里
            this.setState({ loadingVisible: true });
            let obj = {};
            obj["mac"] = item["mac"];
            obj["pid"] = item["pid"];
            obj["beaconkey"] = "FFFFFFFFFFFFFFFFFFFFFFFF";
            obj["eid"] = 8193;
            RPC.callMethod("evtRuleAdd", [obj])
              .then((result) => {
                this.setState({ loadingVisible: false });
                // Toast.success("c_set_success");
                let index = -1;
                for (let i = 0; i < this.state.unbindedItems.length; i++) {
                  let band = this.state.unbindedItems[i];
                  if (band.mac === item["mac"]) {
                    index = i;
                    break;
                  }
                }
                if (index >= 0) {
                  let unbindedItems = this.state.unbindedItems;
                  let removedItem = unbindedItems.splice(index, 1)[0];//
                  let bindedItems = this.state.bindedItems;
                  removedItem.isNearBy = true;
                  bindedItems.push(removedItem);
                  console.log("binded:", bindedItems, "unbinded:", unbindedItems);
                  this.setState({ loadingVisible: false, bindedItems: bindedItems, unbindedItems: unbindedItems });
                }
              })
              .catch((error) => {
                this.setState({ loadingVisible: false });
                Toast.fail("c_set_fail", error);
              });
          }}
        ></ListItem>

      );
    }));
  }

  _formatMac(item) {
    let bandName = "";
    if (item["name"] != null && item["name"] !== "") {
      bandName = bandName + item["name"];
    } else {
      bandName = `${ bandName }MI`;
    }
    let macName = item["mac"];
    bandName = `${ bandName }(${ macName.substring(macName.length - 5, macName.length - 3) }${ macName.substring(macName.length - 2, macName.length) })`;
    return bandName;
  }
 
  componentDidMount() {
    this.props.navigation.setParams({
    //  show:true
      title: LocalizedStrings['settings_auto_scence_bletooth'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    this.refreshData();
    CameraPlayer.getInstance().bindBandNearbyCallback(this.bandNearbyCallback);

  }

  bandNearbyCallback = (value) => {
    
  }

  refreshData() {
    this.setState({ loadingVisible: true });
    RPC.callMethod("get_band_nearby", [])// 获取开关
      .then((result) => {
        console.log("get nearby close switch:", result);
        let open = result.result == "on";
        this.setState({ autoClose: open, loadingVisible: false });
      })
      .catch((error) => {
        this.setState({ loadingVisible: false });
        console.log("get nearby close switch error:", error);
      });

    RPC.callMethod("evtRuleDump", [])// 获取绑定的设备
      .then((result) => {
        console.log("fetch binded bands:", result);
        let bindedBands = [];
        let json = result.result;
        if (json == null) {
          this.setState({ loadingVisible: false });
          return;
        }
        for (let i = 0; i < json.length; i++) {
          let item = json[i];
          let evtid = item.evtid;
          if (evtid != null && evtid != 0x2001) {
            continue;
          }
          item.isNearBy = false;
          for (let j = 0; j < this.state.unbindedItems.length; j++) { // 如果绑定的设备在附近，就显示不同的文案
            let unbindItem = this.state.unbindedItems[j];
            if (unbindItem.mac == item.mac) {
              item.isNearBy = true;
              break;
            }
          }
          bindedBands.push(item);
        }

        this.setState({ loadingVisible: false, bindedItems: bindedBands });
        this._startScan();// 获取到绑定设备后，再扫描

      })
      .catch((result) => {
        console.log("fetch binded bands error:", result);
        this.setState({ loadingVisible: false });
      });
  }

  _startScan() {
    if (this.state.isScaning) { // 查找过程中就不再加载了
      return;
    }
    this._startAnim();
    RPC.callMethod("start_search_band", [10000])
      .then((result) => {
        console.log("start search bands", result);
        if (this.isComponentUnmounted) {
          return;
        }
        this.scanResultTimeout = setTimeout(() => {
          this._scanResult();
        }, this.twoSeconds);

      })
      .catch((error) => {
        console.log("start search bands error", error);
        this.setState({ isRefreshing: false });
        this._stopAnim();
      });
  }

  _startAnim() {
    this.setState({ isScaning: true });
    this.rotationAnim.setValue(0);
    this.animation = Animated.timing(this.rotationAnim, {
      toValue: 1,
      duration: 1000,
      easing: Easing.linear,
      useNativeDriver: true,
      perspective: 1000
    });
    this.animation.start(() => {
      if (!this.state.isScaning) {
        return;
      }
      this._startAnim();
    });

  }

  _stopAnim() {
    this.rotationAnim.stopAnimation();
    this.setState({ isScaning: false });
    if (this.animation != null) {
      this.animation.stop();
    }
  }

  _scanResult() {
    if (this.isComponentUnmounted) {
      return;
    }
    if (this.queryCount <= 0) {
      console.log("anim should stop ");
      this._stopAnim();
      return;
    }
    this.queryCount = this.queryCount - 1;
    console.log("begin get search band result");

    RPC.callMethod("get_search_band_result", [])
      .then((result) => {
        console.log("get search band result:", result);
        let array = result.result;
        console.log("get search band result:", array);
        if (array == null) {

          return;
        }
        let bandList = [];
        for (let i = 0; i < array.length; i++) {
          let item = array[i];
          bandList.push(item);
        }
        this._updateCameraBands(bandList);

        clearTimeout(this.scanResultTimeout);
        this.scanResultTimeout = setTimeout(() => {
          this._scanResult();
        }, this.twoSeconds);

      })
      .catch((error) => {
        console.log("get search band result error:", error);
        clearTimeout(this.scanResultTimeout);
        this.scanResultTimeout = setTimeout(() => {
          this._scanResult();
        }, this.twoSeconds);
      });

  }

  _updateCameraBands(cameraBands) {
    if (cameraBands == null || cameraBands.length <= 0) {
      return;
    }
    let sectionData = this.state.bindedItems;
    let notBindedItems = [];
    for (let i = 0; i < cameraBands.length; i++) {
      let band = cameraBands[i];
      let mac = band.mac;
      let hasBinded = false;
      for (let i = 0; i < sectionData.length; i++) {
        let binded = sectionData[i];
        if (binded.mac === mac) {
          binded.isNearBy = true;
          hasBinded = true;
          break;
        }
      }
      if (hasBinded) { // 如果扫描到附近设备的列表里，有已经绑定的，就通知已经绑定列表里刷新
        // ignore
      } else { // 没有绑定，显示到非绑定列表里
        notBindedItems.push(band);
      }
    }

    this.setState({ unbindedItems: notBindedItems, bindedItems: sectionData });
  }

  componentWillUnmount() {
    clearTimeout(this.scanResultTimeout);
    this.isComponentUnmounted = true;
    this._stopAnim();
    
    CameraPlayer.getInstance().bindBandNearbyCallback(null);
  }


}
