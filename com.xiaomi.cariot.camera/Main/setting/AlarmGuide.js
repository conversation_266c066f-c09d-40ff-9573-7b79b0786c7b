'use strict';

import Separator from 'miot/ui/Separator';
import React from 'react';
import { View, Image, Dimensions, Text, TouchableOpacity, ScrollView } from 'react-native';
import { Device, Service } from 'miot';
import CameraConfig from '../util/CameraConfig';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import StorageKeys from '../StorageKeys';
import Toast from '../components/Toast';
import AlarmUtil from '../util/AlarmUtil';
import { NavigationBar } from 'mhui-rn';
import VersionUtil from '../util/VersionUtil';
import { log } from 'miot/utils/fns';

export default class AlarmGuide extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      btnPressing: false
    };
    StorageKeys.IS_VIP_STATUS.then((res) => {
      this.isVip = res;
    }).catch((err) => {
      this.isVip = false;
      Toast.fail('c_get_fail', err);
    });
  }

  render() {

    let btnBgColor = '#32BAC0';
    let bodyContainerStyle = {
      display: "flex",
      flexDirection: "column",
      width: "100%",
      height: "100%",
      alignItems: "center",
    };

    let bodyImageStyle = {
      // width: this.winWidth,
      width: "88%",
      height: 220,
      borderRadius: 15,
      marginTop: 20
    };

    let hitStyle = {
      fontSize: 18,
      fontWeight: "bold",
      marginTop: 44,
      // marginBottom: 10,
      alignSelf: "flex-start",
      marginLeft: 25,
      marginRight: 25
    };

    let msgScrollStyle = {
      display: "flex",
      flexDirection: "column",
      flex: 1,
      width: "100%",
      marginBottom: 70
    };

    let msgContainerStyle = {
      display: "flex",
      flexDirection: "row",
      marginTop: 15,
      marginLeft: 25, 
      marginRight: 27
    };

    let dotImgStyle = {
      marginTop: 6
    };

    let msgStyle = {
      fontSize: 15,
      marginLeft: 3,
      color: "#7F7F7F",
      lineHeight: 22,
      fontWeight: '200'
    };

    let btnContainerStyle = {
      position: "absolute",
      bottom: 20,
      width: "85%",
      height: 46,
      backgroundColor: btnBgColor,
      justifyContent: "center",
      borderRadius: 23
    };

    let btnTextStyle = {
      fontSize: 16,
      color: "#ffffff",
      fontWeight: "bold",
      textAlign: "center"
    };

    // let hint = LocalizedStrings['set_house_keeping_hint'];
    // let msg1 = LocalizedStrings['set_house_keeping_msg1'];

    let hint = LocalizedStrings['set_house_keeping_hint1_nofreesvl'];
    let msg1 = LocalizedStrings['set_house_keeping_msg1_nofreesvl'];

    let msg2 = LocalizedStrings['set_house_keeping_msg2'];
    let btnText = LocalizedStrings['set_house_keeping'];

    return (
      <View style={styles.container}>
        {/* <Separator/> */}
        <View style={bodyContainerStyle}>
          <Image style={bodyImageStyle}
            source={this.imgSource}
          />

          <Text style={hitStyle}>
            {hint}
          </Text>

          {
           this.props.navigation.state.params.freeHomSurStatus ? //判断是否有海外云存需求
          <ScrollView style={msgScrollStyle}>
            <View style = {msgContainerStyle}>
              <Text style = {msgStyle}>
                {LocalizedStrings['set_house_keeping_Cloud_mag1']}
              </Text>
           </View>
         </ScrollView> : 
         <ScrollView style={msgScrollStyle}>
            <View style = {msgContainerStyle}>
              <Text style = {msgStyle}>
                {msg1}
              </Text>
            </View>

            {/*<View style={msgContainerStyle}>*/}
            {/*  <Text style={msgStyle}>*/}
            {/*    {msg2}*/}
            {/*  </Text>*/}
            {/*</View>*/}
          </ScrollView>
          }


          <TouchableOpacity style={btnContainerStyle}
            onPressIn={() => {
              this.setState({ btnPressing: true });
            }}
            onPressOut={() => {
              this.setState({ btnPressing: false });
            }}
            onPress={() => {
              StorageKeys.IS_ALARM_GUIDE_SHOWN = true;
              if (!VersionUtil.isFirmwareSupportCloud(Device.model)) {
                this.props.navigation.navigate('SurvelillanceSettingOld', {
                  vip: this.props.navigation.state.params.vip,
                  onGoBack: () => {
                    // this.props.navigation.goBack();
                    Service.miotcamera.showAlarmVideos(CameraConfig.getAlarmTypes(Device.model, this.isVip), Device.did, true);
                  }
                });
              } else {
                this.props.navigation.navigate('SurvelillanceSetting', {
                  vip: this.props.navigation.state.params.vip,
                  onGoBack: () => {
                    this.props.navigation.goBack();
                    let shouldDisplayNewStorageManage = CameraConfig.shouldDisplayNewStorageManage(Device.model);
                    if (shouldDisplayNewStorageManage) {
                      this.props.navigation.navigate("AlarmPage", { vip: this.isVip });
                    } else {
                      setTimeout(() => {
                        Service.miotcamera.showAlarmVideos(CameraConfig.getAlarmTypes(Device.model, this.isVip), Device.did, true);
                      }, 800);
                    }
                  }
                });
              }

              // this.props.navigation.navigate("SurvelillanceSetting", { vip: this.props.navigation.state.params.vip,
              //   onGoBack: () => {
              //     // this.props.navigation.goBack();
              //     Service.miotcamera.showAlarmVideos(CameraConfig.getAlarmTypes(Device.model, this.isVip), Device.did, true);
              //   }
              // });
            }}
          >
            <Text style={btnTextStyle}>
              {btnText}
            </Text>
          </TouchableOpacity>
            <TouchableOpacity style={{ position: "absolute", width: "100%", height: "100%" }}
              onPressIn = {() => {
                this.setState({ btnPressing: true });
              }}
              onPressOut = {() => {
                this.setState({ btnPressing: false });
              }}
              onPress={() => {                
                StorageKeys.IS_ALARM_GUIDE_SHOWN = true;
                if (!VersionUtil.isFirmwareSupportCloud(Device.model)) {
                  this.props.navigation.navigate('SurvelillanceSettingOld', { vip: this.props.navigation.state.params.vip,freeHomeSurExpireTime: this.props.navigation.state.params.freeHomeSurExpireTime,
                    freeHomSurStatus: this.props.navigation.state.params.freeHomSurStatus,
                    onGoBack: () => {
                      // this.props.navigation.goBack();
                      Service.miotcamera.showAlarmVideos(CameraConfig.getAlarmTypes(Device.model, this.isVip), Device.did, true);
                    }
                  });
                } else {
                  this.props.navigation.navigate('SurvelillanceSetting', { vip: this.props.navigation.state.params.vip,freeHomeSurExpireTime: this.props.navigation.state.params.freeHomeSurExpireTime,
                    freeHomSurStatus: this.props.navigation.state.params.freeHomSurStatus,
                    onGoBack: () => {
                      this.props.navigation.goBack();
                      let shouldDisplayNewStorageManage = CameraConfig.shouldDisplayNewStorageManage(Device.model);
                      if (shouldDisplayNewStorageManage) {
                        this.props.navigation.navigate("AlarmPage", { vip: this.isVip, freeHomeSurExpireTime: this.props.navigation.state.params.freeHomeSurExpireTime, freeHomSurStatus: this.props.navigation.state.params.freeHomSurStatus });
                      } else {
                        setTimeout(() => {
                          Service.miotcamera.showAlarmVideos(CameraConfig.getAlarmTypes(Device.model, this.isVip), Device.did, true);
                        }, 800);
                      }
                    }
                  });
                }

                // this.props.navigation.navigate("SurvelillanceSetting", { vip: this.props.navigation.state.params.vip,
                //   onGoBack: () => {
                //     // this.props.navigation.goBack();
                //     Service.miotcamera.showAlarmVideos(CameraConfig.getAlarmTypes(Device.model, this.isVip), Device.did, true);
                //   }
                // });
              }}
            />
          </View>
      </View>
    );
  }

  componentDidMount() {
    this.props.navigation.setParams({

      title: LocalizedStrings['house_keeping'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: 'black',
        fontWeight: 'bold'
      }
    });

    let winWidth = Dimensions.get('window').width;
    let winHeight = Dimensions.get('window').height;
    this.winWidth = Math.min(winWidth, winHeight);
    this.winHeight = Math.max(winWidth, winHeight);

    // this.imgSource = require("../../Resources/Images/guide_housekeeping_img_guide_head_021.jpg");
    this.imgSource = this.getGuideImage();
    const result = Image.resolveAssetSource(this.imgSource);
    let srcHeight = result.height;
    let srcWidth = result.width;
    this.imgHeight = this.winWidth * srcHeight / srcWidth;

    this.isVip = this.props.navigation.state.params.isVip;
    // this.isVip = true;
    this.forceBabyCry = CameraConfig.isSupportNonVipBabyCry(Device.model);
  }

  getGuideImage() {
    let model = Device.model;
    let ret = require("../../Resources/Images/guide_housekeeping_img_guide_head.jpg");
    return ret;
  }

}
