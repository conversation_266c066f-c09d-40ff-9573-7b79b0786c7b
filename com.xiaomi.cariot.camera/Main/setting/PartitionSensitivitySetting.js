'use strict';

import { Styles } from 'miot/resources';
import Separator from 'miot/ui/Separator';
import ImageButton from "miot/ui/ImageButton";
import React from 'react';
import { StyleSheet, Image, Text, View, Platform } from 'react-native';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import API from "../API";
import Toast from '../components/Toast';
import AlbumHelper, { SNAPSHOT_FLIP_IMG_PATH, SNAPSHOT_SETTING_IMG_PATH } from "../util/AlbumHelper";
import Host from 'miot/Host';
import MessageDialog from "miot/ui/Dialog/MessageDialog";
import StorageKeys from "../StorageKeys";
import AlarmUtil from '../util/AlarmUtil';
import { NavigationBar } from 'mhui-rn';

/**
 * 分区灵敏度设置页面
 */
export default class PartitionSensitivitySetting extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      source: require('../../Resources/Images/mjv3_wdr_wdr_mode.jpg'),
      sensitive: [
        0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0
      ],
      originSensitive: [],
      isDialogVisiable: false
    };
  }

  render() {
    return (
      <View style={styles.container}>
        <Separator />

        <Text style={styles.bodyTitle}>{LocalizedStrings['pss_body_title']}</Text>
        <View style={styles.bodyImage}>
          <Image style={{ position: "absolute", width: '100%', height: '100%' }}
            source={this.state.source}
          />
          <View style={styles.bodyCover}>
            {
              this.state.sensitive.map((weight, i) => {
                return (
                  <ImageButton
                    key={i}
                    style={
                      [
                        styles.grid,
                        { backgroundColor: this._color(weight) }
                      ]
                    }
                    onPress={() => { this._onSelectedItem(i); }}
                  />
                );
              })
            }
          </View>
        </View>
        <View style={styles.detail}>
          <Text style={styles.detailText}>{LocalizedStrings['pss_detail']}</Text>
          {
            [
              { weight: 3, name: 'h' },
              { weight: 2, name: 'm' },
              { weight: 1, name: 'l' },
              { weight: 0, name: 'n' }
            ].map((item, i) => {
              return (
                <View style={styles.detailUnit}
                  key={i + 80}
                >
                  <View style={this._detailIconStyle(item.weight)}></View>
                  <Text style={styles.detailText}>
                    {LocalizedStrings[`pss_${ item.name }`]}
                  </Text>
                </View>
              );
            })
          }
        </View>

        <MessageDialog
          key={5}
          visible={this.state.isDialogVisiable}
          title={LocalizedStrings['pss_dialog_title']}
          message={LocalizedStrings['pss_dialog_content']}
          buttons={this.dialogButtons}
        />

      </View>
    );
  }

  dialogButtons = [{
    text: LocalizedStrings["action_cancle"],
    callback: () => {
      this.setState({
        isDialogVisiable: false
      });
    }
  }, {
    text: LocalizedStrings['pss_dialog_title'],
    callback: () => {
      this.props.navigation.goBack();
    }
  }];

  /**
   * 判断两个数组是否相同
   * @param array1
   * @param array2
   * @returns {boolean}
   */
  compareArray(a1, a2) {
    if (a1 === a2) return true;
    if ((!a1 && a2) || (a1 && !a2)) return false;
    if (a1.length !== a2.length) return false;
    for (let i = 0, n = a1.length; i < n; i++) {
      if (a1[i] !== a2[i]) return false;
    }
    return true;
  }


  componentDidMount() {
    const textStyle = {
      color: Styles.common.MHGreen,
      width: 60,
      fontSize: 14
    };
    this.props.navigation.setParams({
      title: LocalizedStrings['ss_partition_sensitivity_settings'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      style: { backgroundColor: '#FFF' },
      left: [
        {
          key: NavigationBar.ICON.CLOSE,
          onPress: () => {
            if (this.compareArray(this.state.originSensitive, this.state.sensitive)) {
              this.props.navigation.goBack();
            } else {
              this.setState({
                isDialogVisiable: true
              });
            }
          }
        }
      ],
      right: [
        {
          key: NavigationBar.ICON.COMPLETE,
          onPress: () => {
            Toast.loading('c_setting');
            API.post('/miot/camera/app/v1/put/sensitive', 'business.smartcamera', {
              'sensitive': JSON.stringify({ sensitive: this.state.sensitive })
            }).then((res) => {
              if (res.code == 0) {
                this.props.navigation.goBack();
                Toast.success('c_set_success');
              } else {
                Toast.fail('c_set_fail');
              }
            }).catch((err) => {
              Toast.fail('c_set_fail', err);
            });
          }
        }
      ]


    });

    // API.get('/miot/camera/app/v1/get/alarmSwitch', 'business.smartcamera').then((res) => {
    AlarmUtil.getAlarmConfig().then((res) => {
      if (res.code == 0) {
      } else {
        console.log('request fail');
        return;
      }
      this.setState(() => {
        const sensitive = res.data.sensitive;
        return {
          sensitive: sensitive,
          originSensitive: sensitive.concat()
        };
      });
    }).catch((err) => {
      Toast.fail('c_get_fail', err);
    });

    StorageKeys.IS_IMAGE_FLIP.
      then((isFlipOn) => {
        let imgPath = isFlipOn ? SNAPSHOT_FLIP_IMG_PATH : SNAPSHOT_SETTING_IMG_PATH;
        Host.file.isFileExists(imgPath)
          .then((result) => {
            let timestamp = new Date().getTime();
            let path = `${ imgPath }?timestamp=${ timestamp }`;
            if (Platform.OS === "ios") {
              path = imgPath;
            }
            console.log("final path:", `${ Host.file.storageBasePath }/${ path }`);
            this.setState({ source: { uri: `${ Host.file.storageBasePath }/${ path }` } });
          })
          .catch((result) => {

          });

      });

  }

  _onSelectedItem(index) {
    this.setState((state) => {
      const sensitive = state.sensitive;
      sensitive[index] = (sensitive[index] + 1) % 4;
      return {
        sensitive: sensitive
      };
    });
  }

  _color(weight) {
    switch (weight) {
      case 3: return '#CF2C1E';
      case 2: return '#F6C142';
      case 1: return '#94DF45';
      default: return '#949494';
    }
  }

  _detailIconStyle(weight) {
    return {
      width: 12,
      aspectRatio: 1,
      marginRight: 6,
      backgroundColor: this._color(weight)
    };
  }
}

export const styles = StyleSheet.create({
  container: {
    backgroundColor: Styles.common.backgroundColor,
    height: '100%',
    width: "100%",
    display: "flex",
    flexDirection: 'column',
    flexWrap: 'nowrap',
    alignItems: "center"
  },

  bodyTitle: {
    marginTop: 40,
    marginBottom: 20,
    fontSize: 30,
    color: 'black',
    textAlign: 'center'
  },
  bodyImage: {
    marginTop: 20,
    position: "relative",
    backgroundColor: 'grey',
    width: '100%',
    aspectRatio: 1920.0 / 1080.0
  },
  bodyCover: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    flexDirection: 'row',
    flexWrap: 'wrap'
  },
  grid: {
    width: '12.5%',
    height: '25%',
    borderWidth: 1,
    borderColor: 'white',
    opacity: 0.5
  },
  detail: {
    marginTop: 15,
    flexDirection: 'row',
    alignItems: 'center'
  },
  detailUnit: {
    flexDirection: 'row',
    marginRight: 6
  },
  detailText: {
    fontSize: 12,
    color: 'black'
  }
});
