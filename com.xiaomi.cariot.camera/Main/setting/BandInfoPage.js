'use strict';

import { Device } from "miot";
import Separator from 'miot/ui/Separator';
import React from 'react';
import { ScrollView, View, Text, Platform, FlatList, TouchableOpacity } from 'react-native';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import Toast from '../components/Toast';
import { MessageDialog } from 'miot/ui';
import { LoadingDialog } from 'miot/ui/Dialog';
import RPC from "../util/RPC";
import { NavigationBar } from "mhui-rn";


export default class BandInfoPage extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      loadingVisible: false,
      deleteVisible: false
    };
    this.cameraBand = this.props.navigation.getParam("item");
    if (this.cameraBand == null) {
      this.props.navigation.goBack();
    }
    this.callback = this.props.navigation.getParam("unbindCallback");

  }

  render() {
    if (this.cameraBand == null) {
      return (<View></View>);
    } else {
      return (
        <View style={styles.container}>
          <Separator />
          <View style={{ width: "100%", flexGrow: 1, display: "flex", flexDirection: "column", alignItems: "center" }}>
            <View style={{ width: "100%", height: 50, display: "flex", justifyContent: "center" }}>
              <Text style={{ paddingLeft: 15, paddingTop: 14, color: "#00000066", fontSize: 11 }}>
                {LocalizedStrings["mac_address"]}
              </Text>
            </View>
            <View style={{ width: "100%", height: 0.5, backgroundColor: "#00000022" }}/>
            <View style={{ width: "100%", height: 50, display: "flex", justifyContent: "center" }}>
              <Text style={{ paddingLeft: 15, paddingTop: 14, color: "#000000cc", fontSize: 12 }}>
                {this.cameraBand["mac"]}
              </Text>
              
            </View>
            <View style={{ width: "100%", height: 0.5, backgroundColor: "#00000022" }}/>
            
          </View>
          <View style={{ width: "100%", display: "flex", flexDirection: "row" }}>
            <TouchableOpacity style={{ flexGrow: 1, height: 40, marginHorizontal: 22, marginBottom: 22, marginTop: 11, backgroundColor: "#ffffff", borderRadius: 20, display: "flex", alignItems: "center", justifyContent: "center" }}
              onPress={() => {
                this._ignoreBand();
              }}
            >
              <Text style={{ color: "#000000", fontSize: 14 }}>
                {LocalizedStrings["ignore"]}

              </Text>
            </TouchableOpacity>

          </View>

          <MessageDialog
            visible={this.state.deleteVisible}
            title={LocalizedStrings["delete_mac_confirm_title"]}
            cancel={LocalizedStrings["action_cancle"]}
            confirm={LocalizedStrings["delete_confirm"]}
            onCancel={(e) => {
            }}
            cancelable={true}
            onConfirm={(e) => {
              this._confirmIgnoreBand();
            }}
            onDismiss={() => {
              this.setState({ deleteVisible: false });
            }}
          >
          </MessageDialog>

          <LoadingDialog
            visible={this.state.loadingVisible}
            message={LocalizedStrings["updating"]}
            timeout={0}
          >

          </LoadingDialog>

        </View>
      );
    }

  }

  _ignoreBand() {
    this.setState({ deleteVisible: true });
  }

  _confirmIgnoreBand() {
    this.setState({ loadingVisible: true });
    let obj = {};
    obj["mac"] = this.cameraBand.mac;
    obj["pid"] = this.cameraBand.pid;
    // obj["rssi"] = this.cameraBand.rssi;
    obj["eid"] = 8193;
    RPC.callMethod("evtRuleDel", [obj])
      .then(() => {
        this.setState({ loadingVisible: false });
        this.props.navigation.goBack();
        if (this.callback != null) {
          this.callback();
        }
      })
      .catch((error) => {
        this.setState({ loadingVisible: false });
        Toast.fail("c_set_fail", error);
      });
  }


  componentDidMount() {
    this.props.navigation.setParams({

      title: this.cameraBand["name"] == null ? LocalizedStrings['settings_auto_scence_bletooth'] : this.cameraBand["name"],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });


  }
}
