'use strict';

import { DarkMode, Host, Device } from "miot";
import { ListItem } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { ScrollView, Text, View } from 'react-native';
import { MessageDialog } from 'miot/ui/Dialog';
import NavigationBar from "miot/ui/NavigationBar";

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import Toast from '../components/Toast';
import Service from "miot/Service";
import VersionUtil from "../util/VersionUtil";
import { CAMERA_CONTROL_SEPC_PARAMS, CAMERA_SDCARD_SPEC_PARAMS } from "../Constants";
import CameraPlayer from "../util/CameraPlayer";
import CameraConfig from "../util/CameraConfig";
import RPC from "../util/RPC";
import TrackUtil from '../util/TrackUtil';
import LogUtil from "../util/LogUtil";
import BaseSettingPage from "../BaseSettingPage";
export default class StorageSetting extends BaseSettingPage {

  constructor(props, context) {
    super(props, context);

    this.state = {
      sdCatdStatusValue: "",
      enableSms: false,
      sdcardStatusRight: false,
      showNasUpgradeTipsDLG: true
    };
  }

  getTitle() {
    return LocalizedStrings['manage_storage'];
  }

  renderSettingContent() {
    console.log("enableSms:" + this.state.enableSms);
    let disabled = !this.state.enableSms;
    let grey = { opacity: 0.3 };
    let mArr = VersionUtil.isFirmwareSupportNas(Device.model) ?
      [
        { title: 'sts_1_t', name: 'sts_1_n', value: this.state.sdCatdStatusValue, targetPage: 'SDCardSetting' },
        { title: 'sts_2_t', name: 'sts_2_n', value: '', targetPage: 'NASNetworkLocation', subtitle: LocalizedStrings['sts_2_n_sub'] }
      ] :
      [
        { title: 'sts_1_t', name: 'sts_1_n', value: this.state.sdCatdStatusValue, targetPage: 'SDCardSetting' }
      ];
    return (
      <View style={styles.container}>
        {/* <Separator /> */}
        <ScrollView showsVerticalScrollIndicator={false}>
          {
            mArr.map((item, i) => {
              return (
                <View
                  key={i}
                >
                  <View style={{
                    marginHorizontal: 24,
                    backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff',
                    marginBottom: 20,
                    marginTop: 20,
                    justifyContent: 'flex-end'
                  }}>
                    <Text style={{ marginLeft: 2, fontSize: 12, color: '#8c93b0' }}>{LocalizedStrings[item.title]}</Text>
                  </View>
                  <View style={{ marginHorizontal: -1 }}>
                    <ListItem
                      showSeparator={false}
                      unlimitedHeightEnable={true}
                      titleNumberOfLines={2}
                      valueNumberOfLines={3}
                      subtitleNumberOfLines={3}
                      titleStyle={{ fontWeight: 'bold' }}
                      title={LocalizedStrings[item.name]}
                      value={item.value}
                      subtitle={item.subtitle}
                      subtitleStyle ={{ color: '#000' }}
                      // disabled={i == 1 ? disabled : false}
                      onPress={(_) => {
                        if (i == 0) {
                          TrackUtil.reportClickEvent("Setting_SDCardStatus_ClickNum");
                        }

                        if ('NASNetworkLocation' == item.targetPage) {
                          // RPC.callMethod("nas_get_config", {})
                          //   .then((res) => {
                          //     LogUtil.logOnAll("nas_get_config4-=-=-=-=", JSON.stringify(res));
                          //     if (res) {
                          //       this.props.navigation.navigate(item.targetPage);
                          //       CameraConfig.checkNasVersion = true;
                          //       CameraConfig.nasUpgradeDlgBtnChecked = true;
                          //     }
                          //   }).catch((err) => {
                          //     Toast.fail('c_get_fail');
                          //     LogUtil.logOnAll("StorageSetting", "nas_get_config failed" + JSON.stringify(err));
                          //   });

                          RPC.callMethod("nas_get_config", {})
                            .then((res) => {
                              this.nasData = res;
                              LogUtil.logOnAll("nas_get_config4-=-=-=-=", JSON.stringify(res));
                              if (res && res.result && res.result.state != 0) {
                                // nas已经配置
                                this.props.navigation.navigate("NASNetworkLocation");
                                CameraConfig.checkNasVersion = true;
                                CameraConfig.nasUpgradeDlgBtnChecked = true;
                              } else {
                                CameraConfig.checkNasVersion = true;
                                CameraConfig.nasUpgradeDlgBtnChecked = true;
                                this.props.navigation.navigate("NasIntro");
                              }
                            }).catch((err) => {
                            // Toast.fail('c_get_fail');
                            LogUtil.logOnAll("StorageSetting", "nas_get_config failed" + JSON.stringify(err));
                            if (err && err.message && err.message.indexOf('two same rpc requests can not be send in 1 second!') != -1) {
                              if (this.nasData && this.nasData.result && this.nasData.result.state != 0) {
                                // nas已经配置
                                this.props.navigation.navigate("NASNetworkLocation");
                                CameraConfig.checkNasVersion = true;
                                CameraConfig.nasUpgradeDlgBtnChecked = true;
                              } else {
                                CameraConfig.checkNasVersion = true;
                                CameraConfig.nasUpgradeDlgBtnChecked = true;
                                this.props.navigation.navigate("NasIntro");
                              }
                            } else {
                              this.props.navigation.navigate("NasIntro");
                            }
                          });

                        }
                        else {
                          if (item.targetPage === "SDCardSetting" && Device.isReadonlyShared) {
                            Toast.fail('share_permission_cannot_control');
                          } else {
                            this.props.navigation.navigate(item.targetPage);
                          }
                        }


                      }}
                    />
                    {i == 1 ? null : <View style={styles.whiteblank} />}


                  </View>
                </View>
              );
            })
          }
        </ScrollView>
        {this.renderNasUpgradeTipsDlg()}
      </View>
    );
  }

  componentDidMount() {
    super.componentDidMount();
    // this.props.navigation.setParams({
    //   title: LocalizedStrings['manage_storage'],
    //   type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
    //   left: [
    //     {
    //       key: NavigationBar.ICON.BACK,
    //       onPress: () => { this.props.navigation.goBack(); }
    //     }
    //   ],
    //   titleStyle: {
    //     fontSize: 18,
    //     color: '#333333',
    //     fontWeight: 500
    //   }
    // });

    this.willFocusSubscription = this.props.navigation.addListener(
      'willFocus', () => {
        this._getSetting();
      }
    );
  }

  componentWillUnmount() {
    this.willFocusSubscription.remove();
  }

  _getSetting() {
    CameraPlayer.getInstance().getSdcardStatus(true)
      .then(({ sdcardCode, recordMode }) => {
        let motionRecordPaused = recordMode === 2;
        if (sdcardCode == 0 || sdcardCode == 2 || sdcardCode == 4) { // 格式化中。正常。满了
          this.setState({
            enableSms: true
          });
        } else {
          this.setState({ enableSms: false });
        }
        if (sdcardCode <= 5 && sdcardCode >= 0) {
          this.setState({
            sdcardStatusRight: true
          });
        }
        if (motionRecordPaused && (sdcardCode == 0 || sdcardCode == 2)) {
          this.setState({
            sdCatdStatusValue: LocalizedStrings['camera_storage_pause']
          });
        } else {
          this.setState({
            sdCatdStatusValue: LocalizedStrings[`sds_status_${ sdcardCode }`]
          });
        }
      })
      .catch((error) => {
        Toast.fail('c_get_fail', error.error);
      });

  }

  renderNasUpgradeTipsDlg() {
    if (!this.state.showNasUpgradeTipsDLG || CameraConfig.nasUpgradeTips == -1 || (CameraConfig.nasUpgradeTipsShown != -1 && CameraConfig.nasUpgradeTipsShown == CameraConfig.nasUpgradeTips)) {
      return null;
    }
    let title = "nas_upgrade_dlg_title";
    let msg = CameraConfig.nasUpgradeTips == 1 ? "nas_upgrade_dlg_msg1" : CameraConfig.nasUpgradeTips == 2 ? "nas_upgrade_dlg_msg2" : "nas_upgrade_dlg_msg3";
    let btns = [
      {
        text: LocalizedStrings['btn_cancel'],
        callback: (obj) => {
          console.log("点击了取消键");
          CameraConfig.nasUpgradeTipsShown = CameraConfig.nasUpgradeTips;
          this.setState({ showNasUpgradeTipsDLG: false });
        }
      },
      {
        text: LocalizedStrings['btn_confirm'],
        callback: (obj) => {
          console.log("点击了确定键");
          // 调整到固件升级页面去
          Host.ui.openDeviceUpgradePage();
          CameraConfig.checkNasVersion = true;
          CameraConfig.nasUpgradeDlgBtnChecked = true;
          CameraConfig.nasUpgradeTipsShown = CameraConfig.nasUpgradeTips;
          this.setState({ showNasUpgradeTipsDLG: false });
        }
      }

    ];
    if (CameraConfig.nasUpgradeTips == 3) {
      btns = [
        {
          text: LocalizedStrings['nas_upgrade_dlg_cfm_btn'],
          callback: (obj) => {
            console.log("点击了我知道了键");
            CameraConfig.checkNasVersion = true;
            CameraConfig.nasUpgradeDlgBtnChecked = true;
            CameraConfig.nasUpgradeTipsShown = CameraConfig.nasUpgradeTips;
            this.setState({ showNasUpgradeTipsDLG: false });
          }
        }
      ];
    }
    return (
      <MessageDialog
        title={LocalizedStrings[title]}
        message={LocalizedStrings[msg]}
        visible={this.state.showNasUpgradeTipsDLG}
        type={MessageDialog.TYPE.UNDERLINE}
        cancelable={false}
        buttons={btns}
      />
    );
  }
}
