import React from 'react';
import {
  ScrollView,
  View,
  BackHandler,
  FlatList,
  Platform,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions
} from 'react-native';
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import AlarmUtil from '../util/AlarmUtil';
import Toast from '../components/Toast';
import { DarkMode } from 'miot/Device';
import { InputDialog, NavigationBar, Separator } from 'mhui-rn';
import { Device } from 'miot/device';
import ItemLongTimeAlarm from '../ui/ItemLongTimeAlarm';
import {
  AbstractDialog, ActionSheet, ChoiceDialog,
  LoadingDialog, MessageDialog, PinCodeDialog, ProgressDialog, ShareDialog
} from 'miot/ui/Dialog';
import MHDatePicker from 'miot/ui/MHDatePicker';
import BaseSettingPage from "../BaseSettingPage";
import Util from '../util2/Util';
import { strings as I18n } from "miot/resources";
import CoverLayer from "../widget/CoverLayer";
import Calendar from "../widget/Calendar";
import CalendarFuture from "../widget/CalendarFuture";
import { AccessibilityRoles, getAccessibilityConfig } from "mhui-rn/dist/utils/accessibility-helper";
import { StringSpinner } from 'mhui-rn';
import { Locale } from "mhui-rn/dist/locale";
import dayjs from "dayjs";
import { Host } from "miot";
const dayOfYear = require('dayjs/plugin/dayOfYear');

const { width: screenWidth, height: screenHeight } = Dimensions.get("screen");

const hours24 = constructArray(24, true, true);
const minutes = constructArray(60, true, true);

function constructArray(length, zeroPrefix = true, fromZero = false) {
  const maxLength = (length - (fromZero ? 1 : 0)).toString().length;
  return Array.from({
    length
  }, (v, i) => ((zeroPrefix ? '0000000000000' : '') + (i + (fromZero ? 0 : 1))).slice(-maxLength));
}

export default class ClockAlarmSet extends BaseSettingPage {

  getTitle(): string {
    return LocalizedStrings['clock_alarm_add'];
  }

  hasRightButton(): boolean {
    return true;
  }

  leftPress() {
    if (this.coverLayerState) {
      this.coverLayer.hide();
      return;
    }
    if (this.state.canSave) {
      this.setState({ showSaveDialog: true });
      return;
    }
    this.props.navigation.goBack();
  }

  rightPress() {
    if (this.coverLayerState) {
      this.coverLayer.hide();
      return;
    }
    this.checkAndSubmit();
  }

  componentDidMount() {
    super.componentDidMount();
    this.setEditData();
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  componentWillUnmount() {
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  constructor(props, context) {
    super(props, context);
    dayjs.extend(dayOfYear);
    const nowDate = new Date();
    nowDate.setMinutes(nowDate.getMinutes() + 5);
    const hours = nowDate.getHours().toString().padStart(2, '0');
    const minutes = nowDate.getMinutes().toString().padStart(2, '0');
    this.state = {
      showNoteNameDialog: false,
      showRepeatWeekDialog: false,
      showTimeDialog: false,
      showSaveDialog: false,
      canSave: false,
      setTime: 0, // 0 开始时间，1 结束时间
      // mDate: new Date('2024/06/06'),
      mDate: new Date(),
      hour: hours,
      minute: minutes,
      alarmRepeatDialog: false,
      alarmBackupDialog: false,
      repeatType: 0,
      tempRepeatType: 0,
      repeat: 0,
      notes: "",
      noteType: 0,
      tempNoteType: 0,
    };
    this.tempHour = this.state.hour;
    this.tempMinute = this.state.minute;

    this.data = {
      selectedIndexArray: [-1],
      multiIndexArray: []
    };
    this.repeatData = [
      { title: LocalizedStrings['clock_alarm_repeat_once'], value: 0 },
      { title: LocalizedStrings['plug_timer_everyday'], value: 1 },
      { title: LocalizedStrings['clock_alarm_repeat_work'], value: 2 },
      { title: LocalizedStrings['clock_alarm_repeat_rest'], value: 3 },
      { title: LocalizedStrings['plug_timer_sef_define'], value: 4 }
    ];
    this.backupData = [
      { title: LocalizedStrings['backup_none'], value: 0 },
      { title: LocalizedStrings['take_medicine'], value: 1 },
      { title: LocalizedStrings['eat_food'], value: 2 },
      { title: LocalizedStrings['do_homework'], value: 3 },
      { title: LocalizedStrings['wakeup'], value: 4 },
      { title: LocalizedStrings['plug_timer_sef_define'], value: 5 }
    ];
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.onResume();
      }
    );


    // {\"start\":\"07:00\",\"end\":\"09:00\",\"repeat\":127,\"enable\":false,\"clock_idx\":0,\"name\":\"早上无人出现\"}
    this.pageCallback = this.props.navigation.getParam('callback');
    this.coverLayerState = false;
  }

  onBackHandler = () => {
    if (this.state.canSave) {
      this.setState({ showSaveDialog: true });
      return true;
    }
    return false;
  };

  setWeekByRepeat() {
    let flag = 0b00000001;
    let i = 0;
    this.data.multiIndexArray = [];
    for (i = 0; i < 7; i++) {
      if ((this.item.repeat & flag) != 0) {
        this.data.multiIndexArray.push((i + 6) % 7);
      }
      flag = flag << 1;
    }

    if (0b00000000 == this.item.repeat) {
      this.data.selectedIndexArray = [0];
    } else if (0b01111111 == this.item.repeat) {
      this.data.selectedIndexArray = [1];
    } else {
      this.data.selectedIndexArray = [2];
    }
  }

  setEditData() {
    this.item = this.props.navigation.getParam('item');
    let repeatType = 0, noteType = 0, repeat = 0;
    let notes = "";
    let tempRepeatCopy = 0;
    let calDate = new Date();
    if (this.item) {
      let timeArr = this.item.time.split(":");
      this.tempHour = timeArr[0];
      this.tempMinute = timeArr[1];
      repeatType = this.item.type;
      noteType = this.item.noteType;
      repeat = this.item.repeat;
      if (this.item.noteType == 5) {
        notes = this.item.notes;
      }

      if (this.item.type == 0) {
        // 执行一次
        calDate = new Date(this.item.repeat * 1000);
      }

      if (this.item.type == 1) {
        tempRepeatCopy = 0b01111111;
      }

      if (this.item.type == 4) {
        tempRepeatCopy = this.item.repeat;
      }

      if (this.item.type != 0 && this.item.type != 2 && this.item.type != 3) {
        let flag = 0b00000001;
        let i = 0;
        this.data.multiIndexArray = [];
        for (i = 0; i < 7; i++) {
          if ((this.item.repeat & flag) != 0) {
            this.data.multiIndexArray.push((i + 6) % 7);
          }
          flag = flag << 1;
        }
      }

    } else {
      // 重复类型为执行一次
      this.item = {};
      repeatType = 0;
      noteType = 0;
      repeat = Math.round(calDate.getTime() / 1000);
      let nowDate = new Date();
      nowDate.setMinutes(nowDate.getMinutes() + 5);
      let hours = nowDate.getHours().toString().padStart(2, '0');
      let minutes = nowDate.getMinutes().toString().padStart(2, '0');
      this.tempHour = hours;
      this.tempMinute = minutes;

    }

    console.log("{{{{{{{{{{{{{{{{{{{{",notes,repeat,noteType)
    this.tempDate = calDate;
    this.setState({
      mDate: calDate,
      repeatType: repeatType,
      tempRepeatType: repeatType,
      noteType: noteType,
      tempNoteType: noteType,
      notes: notes,
      repeat: repeat,
      tempRepeatCopy: tempRepeatCopy
    });
  }

  onResume() {

  }

  checkAndSubmit() {
    // if (!this.item.name || this.item.name.trim() == "") {
    //   Toast.fail("add_feature_empty_tips");
    //   return;
    // }
    // if (!this.item.start || this.item.start.trim() == "" || !this.item.end || this.item.end.trim() == "") {
    //   Toast.fail("plug_timer_unset");
    //   return;
    // }
    // if (this.item.start && this.item.end && this.item.start == this.item.end) {
    //   Toast.fail("plug_timer_offtime_illegal");
    //   return;
    // }

    this.item.time = `${this.tempHour}:${this.tempMinute}`;
    this.item.type = this.state.repeatType;
    this.item.repeat = this.state.repeat;
    // if (this.state.repeatType == 0) {
    //   this.item.repeat = Math.round(this.state.mDate.getTime() / 1000);
    // }

    this.item.noteType = this.state.noteType;
    this.item.notes = this.state.notes;
    this.item.enable = true;
    console.log("=====================",this.item);
    this.pageCallback(this.item);
    this.props.navigation.goBack();
  }

  renderTitleBar() {
    let titleBarContent = {
      title: LocalizedStrings['long_time_type_period'],
      left: [
        {
          key: NavigationBar.ICON.CLOSE,
          onPress: () => {
            if (this.state.canSave) {
              this.setState({ showSaveDialog: true });
              return;
            }
            this.props.navigation.goBack();
          }
        }
      ],
      right: [
        {
          key: NavigationBar.ICON.COMPLETE,
          onPress: () => {
            this.checkAndSubmit();
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    };
    // this.props.navigation.setParams(titleBarContent);
    return (
      <NavigationBar { ...titleBarContent } />
    );
  }

  renderNameDialog() {
    return (
      <InputDialog
        visible={ this.state.showNoteNameDialog }
        title={ LocalizedStrings['backup_custom'] }
        subTitle="qwe"
        onDismiss={ (_) => {
          this.setState({ showNoteNameDialog: false, noteNameError: false });
        } }
        buttons={ [
          {
            callback: (_) => this.setState({ showNoteNameDialog: false, noteNameError: false })
          },
          {
            callback: (result) => {
              // {"checked": false, "hasPressUnderlineText": false, "textInputArray": ["value"]}
              if (this.state.noteNameError) {
                return;
              }
              let textInputArray = result.textInputArray;
              console.log(`textInputArray`, textInputArray[0]);
              this.tempNotes = textInputArray[0].trim();
              this.setState({ showNoteNameDialog: false, tempNoteType: this.tempNoteType, noteNameError: false });
            }
          }
        ] }
        inputs={ [
          {
            placeholder: LocalizedStrings['backup'],
            defaultValue: this.state.notes ? this.state.notes : '',
            textInputProps: {
              autoFocus: true
            },
            onChangeText: (result) => {
              let isEmoji = Util.containsEmoji(result);
              let length = result.length;
              // let isCommon = this.isTextcommon(result);
              if (isEmoji) {
                this.setState({
                  noteNameError: true,
                  commentErr: LocalizedStrings["Special_symbol_input_not_supported_temporarily"]
                });
              } else if (length > 10) {
                this.setState({ noteNameError: true, commentErr: LocalizedStrings["input_name_too_long2"] });
              } else if (length <= 0 || result.trim().length == 0) {
                this.setState({ noteNameError: true, commentErr: LocalizedStrings["add_feature_empty_tips"] });
              } else {
                this.setState({ noteNameError: false, commentErr: "error" });
              }
            },
            type: 'DELETE',
            isCorrect: !this.state.noteNameError
          }
        ] }
        inputWarnText={ this.state.commentErr }
        noInputDisButton={ true }
      />
    );
  }


  repeatWeekDialog() {
    return (
      <ChoiceDialog
        type={ ChoiceDialog.TYPE.MULTIPLE }
        visible={ this.state.showRepeatWeekDialog }
        useNewType={ true }
        title={ LocalizedStrings['plug_timer_sef_define'] }
        options={ [
          {
            title: LocalizedStrings['monday1']
          },
          {
            title: LocalizedStrings['tuesday1']
          },
          {
            title: LocalizedStrings['wednesday1']
          },
          {
            title: LocalizedStrings['thursday1']
          },
          {
            title: LocalizedStrings['friday1']
          },
          {
            title: LocalizedStrings['saturday1']
          },
          {
            title: LocalizedStrings['sunday1']
          }
        ] }
        selectedIndexArray={ this.data.multiIndexArray }
        color="#32BAC0"
        buttons={ [
          {
            // style: { color: 'lightblue' },
            callback: () => {
              // this.item.repeat = this.repeatArrayCopy;
              this.setState({ showRepeatWeekDialog: false, periodRepeat: this.repeatArrayCopy });
            }
          },
          {
            // style: { color: 'lightblue' },
            callback: (result) => {
              console.log(`selected:`, result);
              if (result && result.length <= 0) {
                this.setState({showRepeatWeekDialog: false});
                return;
              }
              this.data.multiIndexArray = result;
              this.setState({
                showRepeatWeekDialog: false, canSave: true

              });
              let repeat = 0b00000000;
              this.data.multiIndexArray.forEach((item) => {
                console.log("==============", (item + 1) % 7);
               repeat = repeat | (0b00000001 << (item + 1) % 7);
              });
              this.setState({ tempRepeatCopy: repeat, showRepeatWeekDialog: false, tempRepeatType: this.tempCustomRepeatType });
            }
          }
        ] }
        onDismiss={ () => this.setState({ showRepeatWeekDialog: false }) }
      />);
  }

  renderTimeDialog() {
    return (
      <MHDatePicker
        visible={ this.state.showTimeDialog }
        title={ this.state.setTime == 0 ? LocalizedStrings['csps_start'] : LocalizedStrings['csps_end'] }
        type={ MHDatePicker.TYPE.TIME24 }
        datePickerStyle={ {
          rightButtonStyle: { color: "#FFFFFF" },
          rightButtonBgStyle: { bgColorNormal: "#32BAC0", bgColorPressed: "#32BAC099" }
        } }
        onDismiss={ () => this.setState({ showTimeDialog: false }) }
        onSelect={ (res) => {
          console.log(JSON.stringify(res));
          let str = `${ res.rawArray[0] }:${ res.rawArray[1] }`;
          this.state.setTime == 0 ? this.item.start = str : this.item.end = str;
          this.setState({ canSave: true });
        } }
        current={ this.state.setTime == 0 ? (this.item.start ? this.item.start.split(':') : new Date()) :
          (this.item.end ? this.item.end.split(':') : new Date()) }
      />
    );
  }

  _renderBackDialog() {
    return (
      <MessageDialog
        visible={ this.state.showSaveDialog }
        message={ LocalizedStrings['exit_change_disappear'] }
        messageStyle={ { textAlign: "center" } }
        canDismiss={ false }
        buttons={ [
          {
            text: LocalizedStrings["action_cancle"],
            callback: (_) => {
              this.setState({ showSaveDialog: false });
            }
          },
          {
            text: LocalizedStrings["exit"],
            callback: (_) => {
              this.setState({ showSaveDialog: false });
              this.props.navigation.goBack();
            }
          }
        ] }
      />
    );
  }

  mShowPopupView = () => {
    this.coverLayer.showWithContent(
      () => {
        // let nowDate = new Date();
        console.log('calendar input: ', this.state.mDate);
        let mWidth = Math.min(Dimensions.get("window").height, Dimensions.get("window").width);
        return (
          <View style={ { height: 510, width: mWidth } }>
            <CalendarFuture
              ref={ (hdl) => this.mCalendar = hdl }
              visible={ this.showCalendar }
              y={ this.state.mDate.getFullYear() }
              m={ this.state.mDate.getMonth() + 1 }
              d={ this.state.mDate.getDate() }
              interval={ -1 } // ???? 需要通过接口获取有效日期范围，并填充到dates里面去
              width={ mWidth }
              onDateChanged={ this.mSwitchOneDay }
              onCancel={ () => {
                this.coverLayer.hide();
              } }
              onAllVideo={ this.mSwitchAllVideo }
              dates={ this.mDatesWithData ?? null }  // 有内容的日期
            >
            </CalendarFuture>
          </View>
        );
      },
      () => this.coverLayer.hide(),
      CoverLayer.popupMode.bottom
    );
  };

  mSwitchAllVideo = () => {
    this.mSwitchOneDay(null, true);
  }
  mSwitchOneDay = (items, isToday = false) => {
    console.log(`get selected: ${ items }, isToday? ${ isToday }`);
    let sltDate = new Date();
    if (!isToday) {
      sltDate = new Date(Date.parse(`${ items[0] }/${ items[1] }/${ items[4] }`));
    }
    this.tempDate = sltDate;
    // this.setState({ mDate: sltDate });
    this.coverLayer.hide();
  }

  _renderAlarmRepeatDialog() {
    return (
      <AbstractDialog
        style={ { width: screenWidth,
          bottom: 0,
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          borderBottomLeftRadius: Host.isPad ? 20 : 0,
          borderBottomRightRadius: Host.isPad ? 20 : 0,
          marginHorizontal: 0 }}
        visible={this.state.alarmRepeatDialog}
        showSubtitle={false}
        onDismiss={() => {
          this.setState({ alarmRepeatDialog: false });
        }}
        showTitle={false}
        showButton={false}
        // canDismiss={false}
      >
        {this._renderRepeatView()}
      </AbstractDialog>
    );
  }
  _renderRepeatView() {
    return (
      <View style={{ alignItems: "center", marginBottom: 16 }}>
        <Text
          style={{
            fontSize: 16, color: "#000000", marginTop: 25, fontWeight: "700"
          }}
        >
          {LocalizedStrings["plug_timer_repeat"]}
        </Text>
        <View style={{ marginTop: 15 }}>
          {this.repeatData.map((item, index) => {
            return (
              <TouchableOpacity
                key={index}
                onPress={() => {
                  if (index == 0) {
                    // 执行一次
                    this.mShowPopupView();
                    this.setState({alarmRepeatDialog: false, tempRepeatType: 0, tempRepeatCopy: 0b00000000});
                  } else if (index == 1) {
                    // 每天
                    this.setState({tempRepeatType: 1, repeat: 0b01111111, tempRepeatCopy: 0b01111111});
                  } else if (index == 2) {
                    this.setState({tempRepeatType: 2, tempRepeatCopy: 0b00000000});
                  } else if (index == 3) {
                    this.setState({tempRepeatType: 3, tempRepeatCopy: 0b00000000});
                  } else {
                    // 自定义
                    let flag = 0b00000001;
                    let i = 0;
                    this.data.multiIndexArray = [];
                    for (i = 0; i < 7; i++) {
                      if ((this.state.tempRepeatCopy & flag) != 0) {
                        this.data.multiIndexArray.push((i+6) % 7);
                      }
                      flag = flag << 1;
                    }
                    this.tempCustomRepeatType = 4;
                    this.setState({ showRepeatWeekDialog: true });
                  }
                }}
              >
                <View
                  style={{ maxWidth: "100%",
                    width: screenWidth, height: 54,
                    backgroundColor:
                      this.state.tempRepeatType == item.value ? "rgba(50,186,192,0.1)" : "#ffffff", flexDirection: "row", alignItems: "center", justifyContent: "space-between"
                  }}
                  key={index}
                >
                  <Text
                    style={{
                      marginLeft: 30, fontSize: 16, color: this.state.tempRepeatType == item.value ? "#32BAC0" : "#000000", fontWeight: "500"
                    }}
                  >
                    {item.title}
                  </Text>
                  {this.state.tempRepeatType == item.value && (
                    <Image
                      style={{ width: 22, height: 22, marginRight: 22 }}
                      source={require("../../Resources/Images/plugin_icon_customVoice_selectItem.png")}
                    ></Image>
                  )}
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
        <View style={{ flexDirection: "row", justifyContent: "space-between", marginTop: 20, marginLeft: 0, marginBottom: 10 }}>
          <TouchableOpacity
            onPress={() => {
              this.setState({ alarmRepeatDialog: false });
            }}
          >
            <View style={{ width: 147, height: 46, backgroundColor: "#F5F5F5", borderRadius: 23, justifyContent: "center", alignItems: "center" }}>
              <Text style={{ fontSize: 16, color: "#4C4C4C" }}>{LocalizedStrings.btn_cancel}</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              if (this.state.tempRepeatType == 0) {
                this.setState({ alarmRepeatDialog: false, repeatType: this.state.tempRepeatType, repeat: Math.round(this.tempDate.getTime() / 1000), canSave: true, mDate: this.tempDate });
              } else {
                this.setState({ alarmRepeatDialog: false, repeatType: this.state.tempRepeatType, repeat: this.state.tempRepeatCopy, canSave: true });
              }
            }}
          >
            <View style={{ width: 147, height: 46, backgroundColor: "#32BAC0", borderRadius: 23, justifyContent: "center", alignItems: "center", marginLeft: 20 }}>
              <Text style={{ fontSize: 16, color: "#ffffff" }}>{LocalizedStrings.btn_confirm}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  _renderAlarmBackupDialog() {
    return (
      <AbstractDialog
        style={ { width: screenWidth,
          bottom: 0,
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          borderBottomLeftRadius: Host.isPad ? 20 : 0,
          borderBottomRightRadius: Host.isPad ? 20 : 0,
          marginHorizontal: 0 }}
        visible={this.state.alarmBackupDialog}
        showSubtitle={false}
        onDismiss={() => {
          this.setState({ alarmBackupDialog: false });
        }}
        showTitle={false}
        showButton={false}
        // canDismiss={false}
      >
        {this._renderNoteView()}
      </AbstractDialog>
    );
  }

  _renderNoteView() {
    return (
      <View style={{ alignItems: "center", marginBottom: 16 }}>
        <Text
          style={{
            fontSize: 16, color: "#000000", marginTop: 25, fontWeight: "700"
          }}
        >
          {LocalizedStrings["backup"]}
        </Text>
        <View style={{ marginTop: 15 }}>
          {this.backupData.map((item, index) => {
            return (
              <TouchableOpacity
                key={index}
                onPress={() => {
                  if (index == 5) {
                    // 自定义备注
                    this.tempNoteType = index;
                    this.setState({ showNoteNameDialog: true });
                  } else {
                    this.setState({ tempNoteType: index });
                  }
                }}
              >
                <View
                  style={{ maxWidth: "100%",
                    width: screenWidth, height: 54,
                    backgroundColor:
                      this.state.tempNoteType == item.value ? "rgba(50,186,192,0.1)" : "#ffffff", flexDirection: "row", alignItems: "center", justifyContent: "space-between"
                  }}
                  key={index}
                >
                  <Text
                    style={{
                      marginLeft: 30, fontSize: 16, color: this.state.tempNoteType == item.value ? "#32BAC0" : "#000000", fontWeight: "500"
                    }}
                  >
                    {item.title}
                  </Text>
                  {this.state.tempNoteType == item.value && (
                    <Image
                      style={{ width: 22, height: 22, marginRight: 22 }}
                      source={require("../../Resources/Images/plugin_icon_customVoice_selectItem.png")}
                    ></Image>
                  )}
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
        <View style={{ flexDirection: "row", justifyContent: "space-between", marginTop: 20, marginLeft: 0, marginBottom: 10 }}>
          <TouchableOpacity
            onPress={() => {
              this.setState({ alarmBackupDialog: false });
            }}
          >
            <View style={{ width: 147, height: 46, backgroundColor: "#F5F5F5", borderRadius: 23, justifyContent: "center", alignItems: "center" }}>
              <Text style={{ fontSize: 16, color: "#4C4C4C" }}>{LocalizedStrings.btn_cancel}</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              this.setState({ alarmBackupDialog: false, noteType: this.state.tempNoteType, notes: this.tempNotes, canSave: true });
            }}
          >
            <View style={{ width: 147, height: 46, backgroundColor: "#32BAC0", borderRadius: 23, justifyContent: "center", alignItems: "center", marginLeft: 20 }}>
              <Text style={{ fontSize: 16, color: "#ffffff" }}>{LocalizedStrings.btn_confirm}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  }


  getRepeatValue() {
    console.log("++++++++++++getRepeatValue",this.state.repeatType )

    if (this.state.repeatType == 0) {
      return dayjs(this.state.mDate).format(LocalizedStrings["yyyymmdd"]);
    } else if (this.state.repeatType == 4) {
      return Util.getRepeatString(this.state.repeat);
    }
    return this.repeatData[this.state.repeatType].title;
  }

  timeChange() {
    // this.item.time = `${this.tempHour}:${this.tempMinute}`;
    // this.item.type = this.state.repeatType;

    if (this.state.repeatType != 0) {
      // 非一次性
      return;
    }

    let nowDate = new Date();
    let nowHour = nowDate.getHours();
    let nowMinute = nowDate.getMinutes();

    let chooseHour = parseInt(this.tempHour);
    let chooseMinute = parseInt(this.tempMinute);
    console.log("time:",nowHour,nowMinute,chooseHour,chooseMinute);
    let isToday = dayjs().dayOfYear() === dayjs(this.state.mDate).dayOfYear();
    if ((chooseHour * 60 + chooseMinute) <= (nowHour * 60 + nowMinute) && isToday) {
      // 更新日期时间到明天
      let nextDate = this.state.mDate;
      nextDate.setDate(nextDate.getDate() + 1);
      let ml = Math.round(this.state.mDate.getTime() / 1000);
      this.setState({mDate: this.state.mDate, repeat: ml});
    }
  }

  getBackupValue() {
    if (this.state.noteType == 5) {
      return  this.state.notes;
    }

    return this.backupData[this.state.noteType].title;
  }
  coverLayerStateChange(value) {
    console.log("==============", value);
    this.coverLayerState = value;
    if (!value) {
      this.setState({alarmRepeatDialog: true});
    }
  }

  renderSettingContent() {
    return (<View style={ {
      display: "flex",
      height: "100%",
      width: '100%',
      flex: 1,
      flexDirection: "column",
      backgroundColor: Util.isDark() ? "#xm000000" : "#FFFFFF"
    } }>
      <View style={ { flexDirection: 'row', justifyContent: 'center', marginHorizontal: 28, marginVertical: 24 } }>
        <StringSpinner
          style={ { width: 100, height: 250, alignSelf: "center" } }
          dataSource={ hours24 }
          defaultValue={ `${ this.tempHour }` }
          valueFormat={ "%.0f" }
          pickerInnerStyle={ {
            lineColor: "#00000000",
            // textColor: "#ff0000",
            // selectTextColor: "#0000FF",
            fontSize: 28,
            selectFontSize: 32,
            rowHeight: 35,
            selectTextColor: "#32BAC0",
            unitTextColor: "#32BAC0"
          } }
          unit={ Locale.of(LocalizedStrings.language).hourUnit }
          onValueChanged={ (data) => {
            console.log(`hour newValue:${ data.newValue },oldValue:${ data.oldValue }`);
            this.tempHour = data.newValue;
            this.timeChange();
            this.setState({ canSave: true });
            console.log("====================hour1",this.tempHour);

            // let str = `${ Number.parseInt(this.tempHour) }${ Locale.of(LocalizedStrings.language).hourUnit }${ Number.parseInt(this.tempMinute) }${ Locale.of(LocalizedStrings.language).minuteUnit }`;
            // let disable = Number.parseInt(this.tempHour) === 0 && Number.parseInt(this.tempMinute) === 0;
            // this.setState({ subtitleTempStr: str, confirmDisable: disable });
          } }
          onValueFastChanged={ (data) => {
            // console.log(`mhpicker:${JSON.stringify(data)}`);
            console.log("hour ValueFastChanged", data.nativeEvent);
            this.tempHour = data.nativeEvent.newValue;
            console.log("====================hour2",this.tempHour);
          } }
        />
        <StringSpinner
          style={ { width: 100, height: 250, alignSelf: "center" } }
          dataSource={ minutes }
          defaultValue={ `${ this.tempMinute }` }
          valueFormat={ "%.0f" }
          pickerInnerStyle={ {
            lineColor: "#00000000",
            // textColor: "#ff0000",
            // selectTextColor: "#0000FF",
            fontSize: 28,
            selectFontSize: 32,
            rowHeight: 35,
            selectTextColor: "#32BAC0",
            unitTextColor: "#32BAC0"
            // selectBgColor: "#f5f5f5"
          } }
          unit={ Locale.of(LocalizedStrings.language).minuteUnit }
          onValueChanged={ (data) => {
            console.log(`minute newValue:${ data.newValue },oldValue:${ data.oldValue }`);
            this.tempMinute = data.newValue;
            this.timeChange();
            this.setState({ canSave: true });
            console.log("====================minute1",this.tempMinute);
            // let str = `${ Number.parseInt(this.tempHour) }${ Locale.of(LocalizedStrings.language).hourUnit }${ Number.parseInt(this.tempMinute) }${ Locale.of(LocalizedStrings.language).minuteUnit }`;
            // let disable = Number.parseInt(this.tempHour) === 0 && Number.parseInt(this.tempMinute) === 0;
            // this.setState({ subtitleTempStr: str, confirmDisable: disable });
          } }
          onValueFastChanged={ (data) => {
            // console.log(`mhpicker:${JSON.stringify(data)}`);
            console.log("minute ValueFastChanged", data.nativeEvent);
            this.tempMinute = data.nativeEvent.newValue;

            console.log("====================minute2",this.tempMinute);
          } }
        />
      </View>

      <View style={ {
        backgroundColor: '#e5e5e5',
        height: StyleSheet.hairlineWidth,
        marginHorizontal: 28,
        marginVertical: 20
      } }/>
      <Text style={ {
        color: '#8C93B0',
        fontSize: 12,
        lineHeight: 16,
        marginHorizontal: 28
      } }>{ LocalizedStrings['other'] }</Text>
      <ListItem
        title={ LocalizedStrings['plug_timer_repeat'] }
        containerStyle={ { paddingHorizontal: 28, fontWeight: 'bold' } }
        value={this.getRepeatValue()}
        valueMaxWidth={"40%"}
        showSeparator={ false }
        onPress={ () => {
          // this.mShowPopupView();
          this.setState({ alarmRepeatDialog: true, tempRepeatType: this.state.repeatType });
        } }/>
      <ListItem
        title={ LocalizedStrings['backup'] }
        containerStyle={ { paddingHorizontal: 28, fontWeight: 'bold' } }
        value={ this.getBackupValue() }
        showSeparator={ false }
        onPress={ () => {
          this.setState({ alarmBackupDialog: true, tempNoteType: this.state.noteType });
        } }/>

      { this.renderNameDialog() }
      { this.repeatWeekDialog() }
      {/*{ this.renderTimeDialog() }*/}
      { this._renderBackDialog() }
      { this._renderAlarmRepeatDialog() }
      { this._renderAlarmBackupDialog() }
    </View>);
  }
}