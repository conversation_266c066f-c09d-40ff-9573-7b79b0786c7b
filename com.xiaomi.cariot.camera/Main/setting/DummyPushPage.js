'use strict';

import { Device } from "miot";
import { Service } from "miot";
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { ScrollView, View } from 'react-native';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import Toast from '../components/Toast';
import VersionUtil from "../util/VersionUtil";
import Package from "miot/Package";

// 空白首页，啥也不干。
export default class DummyMainPage extends React.Component {

  constructor(props, context) {
    super(props, context);

    this.state = {
      isPower: true,
      openBandNearby: false
    };
  }

  render() {
    return (
      <View style={{ width: "100%", height: "100%", backgroundColor: "#ffffff" }}>
        
      </View>
    );
  }

  componentDidMount() {
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.isPageForeGround = true;
        this._onResume();
      }
    );
    
  }

  _onResume() {
    console.log("why!, _onResume");
    Package.exit();
  }


  componentWillUnmount() {
    this.didFocusListener.remove();
  }
}
