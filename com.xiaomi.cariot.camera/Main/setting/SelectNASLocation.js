'use strict';
import { Device, DarkMode } from "miot";
import React from 'react';
import { Image, ActivityIndicator, StyleSheet, ScrollView, View, Text,TouchableOpacity, TextInput, Button } from 'react-native';
import RPC from "../util/RPC";
import Toast from '../components/Toast';
import { ListItemWithSwitch, ListItem } from 'miot/ui/ListItem';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import NavigationBar from "miot/ui/NavigationBar";
import LogUtil from "../util/LogUtil";
import { MessageDialog } from "mhui-rn";
export default class SelectNASLocation extends React.Component {
  static navigationOptions = ({navigation}) => {
    return {
      header: (
          <NavigationBar
              type={"dark" == DarkMode.getColorScheme() ? NavigationBar.TYPE.DARK : NavigationBar.TYPE.LIGHT}
              backgroundColor={"dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff'}
              left={navigation.getParam('left')}
              right={navigation.getParam('right')}
              title={navigation.getParam("navTitle")}
          />
      )
    };
  };

  constructor(props, context) {
    super(props, context);
    this.state = {
      selectOldVideo: 0,
      isPickerOldVideo: false,
      storageLocation: '1',
      showLoading: true,
      showFailDialog: false,
      darkMode: false,
      currentDir:null,
      canSave: false,
      showSaveDialog:false
    };

  }

  refreshNavigationBar() {

    this.props.navigation.setParams({
      backgroundColor: "#F6F6F6",
      title: LocalizedStrings['sel_nas_location'],
      type: "dark" == DarkMode.getColorScheme() ? NavigationBar.TYPE.DARK : NavigationBar.TYPE.LIGHT,
      left: [
        {
          key: NavigationBar.ICON.CLOSE,
          onPress: () => {
            if (this.state.canSave) {
              this.setState({showSaveDialog: true});
              return;
            }
            this.props.navigation.goBack();
          }
        }
      ],
      right: [
        {
          key:  NavigationBar.ICON.COMPLETE,
          disable: !this.state.canSave,
          onPress: () => {
            this._onSelectedItem(this.state.currentDir);
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });

  }

  // 监听列表滚动事件
  scrollViewScroll = (event) => {
    const y = event.nativeEvent.contentOffset.y;
    let flag = y > 28;
    if (this.showTitle == flag) {
      return;
    }
    if (flag) {
      this.showTitle = true;
      this.props.navigation.setParams({ navTitle: LocalizedStrings['sel_nas_location'] });
    } else {
      this.showTitle = false;
      this.props.navigation.setParams({ navTitle: "" });
    }
  };

  render() {
    return (
      <View style={styles.container}>
        <ScrollView showsVerticalScrollIndicator={false}
                    scrollEventThrottle={1}
                    onScroll={this.scrollViewScroll}>

          <View style={{ flexDirection: "row", flexWrap: "wrap" }} key={'0'}>
            <Text style={{ fontSize: 30, color: "rgba(0, 0, 0, 0.80)", fontWeight: "300", position: "relative", marginLeft: 25, marginTop: 3, marginBottom: 23,fontFamily:'MI-LANTING--GBK1-Light' }}>
              {this.props.navigation.state.params.title}</Text>
          </View>
        {this._renderSelectView()}
        </ScrollView>

        {this._renderLoadingView()}
        {this._renderSetFailDialog()}
        {this._renderBackDialog()}
      </View>
    );
  }

  _renderSetFailDialog() {
    return (
      <MessageDialog
        visible={this.state.showFailDialog}
        title={LocalizedStrings['c_set_fail']}
        message={LocalizedStrings["nas_set_fail_tips"]}
        messageStyle={{textAlign:"center"}}
        canDismiss={true}
        buttons={[
          {
            text: LocalizedStrings["offline_divice_ok"],
            callback: (_) => {
              this.setState({ showFailDialog: false });
            }
          }
        ]}
      />
    );
  }

  componentDidMount() {
    let { locationList } = this.props.navigation.state.params
    console.log(' locationList', locationList)
    if (locationList == null || locationList.length == 0) {
      this.setState({
        noLocationView: true,
        showLoading: false,
        selectView: false
      })
    }
    else {
      this.setState({
        showLoading: false,
        locationList: locationList,
        selectView: true
      });
    }

    this.refreshNavigationBar();
    //刷新的时候 
    //这里可以不写在这里
    this.willFocusSubscription = this.props.navigation.addListener(
      'didFocus', () => {
        this._getStorage();
      }
    );


  }
  _getStorage() {
    let { deviceMessage } = this.props.navigation.state.params
    this.setState({
      deviceMessage: deviceMessage
    })

  }
  componentWillUnmount() {
    if (this.getInfoIntervalID > 0) {
      clearInterval(this.getInfoIntervalID);
      this.getInfoIntervalID = 0;
    }

    this.willFocusSubscription.remove();
  }
  _renderNoLocationView() {
    if (this.state.noLocationView) {
      return (
        <View
          style={{  marginTop:70, justifyContent: "center", alignItems: "center" }}
        >
          <View style={{
            justifyContent: 'center',
            alignItems: 'center',
          }}>
            <Image
              style={{ width: 54, height: 46,marginBottom:25 }}
              source={require('../../Resources/Images/icon_nas_folder_gray.png')}
            />
            <Text style={{ color: 'rgba(0, 0, 0, 0.4)',textAlign:'center'}}>{LocalizedStrings['nas_folder_empty_hint']}</Text>
          </View>
        </View>

      )
    }
  }
  _renderLoadingView() {
    if (!this.state.showLoading) {
      return;
    }
    let isDark = DarkMode.getColorScheme() == "dark";
    return (
      <View
        // style={styles.container}
        style={{ backgroundColor: 'white', width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center" }}
      >
        <ActivityIndicator
          style={{ width: 54, height: 54 }}
          color={isDark ? "xm#ffffff" : "xm#000000"}
          size={"large"}

        />
        <Text
          style={{ marginTop: 10, fontSize: 12, color: "#000000" }}>
          {LocalizedStrings["camera_loading"]}
        </Text>
      </View>
    );

  }
  _renderSelectView() {
    if (this.state.selectView) {
      return (
        <ScrollView style={styles.container}>
          <View style={{height: 72, paddingHorizontal: 28, display: 'flex', flexDirection: 'row',
            justifyContent: "flex-start", alignItems: "center"}} key={'0'}>
            <View>
              <Image style={{width: 50, height: 50}}
                     source={require('../../Resources/Images/icon_nas_disk.png')}></Image>
            </View>

            <View style={{flex: 1}}>
              <Text style={{fontSize: 16, color: "black", marginLeft: 14}}>
                {this.state.deviceMessage.share.name}</Text>
            </View>
          </View>

          <View style={styles.whiteBlank} />

           {
            this.state.locationList.map((item, i) =>
                (
                    <TouchableOpacity key={i} onPress={() => {
                      this.setState({currentDir:item,canSave:true},()=>this.refreshNavigationBar());
                    }}>

                      <View style={{height: 72, paddingHorizontal: 28, display: 'flex', flexDirection: 'row',
                        justifyContent: "flex-start", alignItems: "center"}} key={i}>
                        <View>
                          <Image style={{width: 50, height: 50}}
                                 source={require('../../Resources/Images/icon_nas_file.png')}></Image>
                        </View>

                        <View style={{flex: 1}}>
                          <Text style={{fontSize: 16, color: "black", marginLeft: 14}}>
                            {item}</Text>
                        </View>
                        <Image style={{width:22,height:22}} source={this.state.currentDir == item?require("../../Resources/Images/icon_single_checked.png"):require("../../Resources/Images/icon_single_unchecked.png")}/>
                      </View>
                    </TouchableOpacity>

                )
            )
          }
          {this._renderNoLocationView()}
        </ScrollView>
      )
    }


  }
  _onSelectedItem(value) {
    let { deviceMessage } = this.props.navigation.state.params;
    Toast.loading('c_setting');
    let params = {
      sync_interval: 300,
      video_retention_time: 7776000,
      last_sync_time: 520000,
      state: 1,
      share: {
        type: deviceMessage.share.type,
        group: deviceMessage.share.group,
        addr: deviceMessage.share.addr,
        name: deviceMessage.share.name,
        dir: value,
        user: deviceMessage.share.user,
        pass: deviceMessage.share.pass,
        // dirName: this.state.locationListName,
      }

    }
    RPC.callMethod("nas_set_config", params).then((res) => {
      if (res.result[0] == 'OK') {
        this.props.navigation.navigate('NASNetworkLocation', { deviceMessage: params, NASLocation: value })

        // setTimeout(() => {
        // this._getNASStatus();
        // }, 300);
        // clearInterval(this.getInfoIntervalID);

        // this.getInfoIntervalID = setInterval(() => {
        //     this._getNASStatus();
        // }, 2000);


      }
      // this.setState({
      //     storageLocation: res.result[0] == 'OK' ? value : this.state.storageLocation
      // });
      Toast.success('c_set_success');
    }).catch((err) => {
      LogUtil.logOnAll("SelectNASLocation", "nas_set_config failed" + JSON.stringify(err));
      this.setState({ showFailDialog: true });
      // Toast.fail('c_set_fail', err);
    });
  }

  _renderBackDialog() {
    return (
        <MessageDialog
            visible={this.state.showSaveDialog}
            message={LocalizedStrings['exit_change_disappear']}
            messageStyle={{textAlign: "center"}}
            canDismiss={false}
            buttons={[
              {
                text: LocalizedStrings["action_cancle"],
                callback: (_) => {
                  this.setState({showSaveDialog: false});
                }
              },
              {
                text: LocalizedStrings["btn_confirm"],
                callback: (_) => {
                  this.setState({showSaveDialog: false});
                  this.props.navigation.goBack();
                }
              }
            ]}
        />
    )
  }

  _getNASStatus() {
    RPC.callMethod("nas_get_config", {}).then((res) => {
      console.log("nas_get_config", res)
      LogUtil.logOnAll("nas_get_config3-=-=-=-=", JSON.stringify(res));
      if (res.result.dir == this.state.storageLocationName) {
        this.props.navigation.navigate('NASNetworkLocation', { deviceMessage: res.result, NASLocation: this.state.storageLocationName })
      }

    }).catch((err) => {
      LogUtil.logOnAll("SelectNASLocation", "nas_get_config failed" + JSON.stringify(err));
      console.log('err', err)
      Toast.fail('c_get_fail', err);

    })
  }



}
export const styles = StyleSheet.create({
  container: {
    backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff',
    flexDirection: 'column',
    height: '100%',
    width: '100%',
  },
  whiteBlank: {
    height: 0.5,
    marginHorizontal: 28,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    marginTop: 20,
    marginBottom: 20
  },
});
