import Host from 'miot/Host';
import React, { Component } from 'react';
import { Image, Dimensions, PermissionsAndroid, StyleSheet, Text, TouchableOpacity, View, Button, ScrollView } from 'react-native';
import Camera, { RNCamera } from 'react-native-camera';
import Canvas from 'react-native-canvas';
import NavigationBar from "miot/ui/NavigationBar";
import { Platform } from 'react-native';
import { Package, Device, Service, PackageEvent, System, DarkMode } from 'miot';
import { RkButton } from 'react-native-ui-kitten';
import Util from "../util2/Util";
import Toast from '../components/Toast';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import LogUtil from '../util/LogUtil';
import Orientation from 'react-native-orientation';
import FDSUtil from "../util/FDSUtil";

let containerWidth = Dimensions.get("window").width
let containerHeight = Dimensions.get("window").height

export default class AvatarDisplay extends Component {
  constructor(props, context) {
    super(props, context);
    this.inType = this.props.navigation.getParam("type");
    this.selectData = this.props.navigation.getParam("data");
    this.state = {
      uploadUri: this.inType == "camera" ? '': Host.isAndroid?{uri: `${ Host.file.storageBasePath }/${ this.selectData?.originFilename }?v=${Math.random(100000)}`}:{uri: `${ Host.file.storageBasePath }/${ this.selectData?.originFilename }`},
      canvasFinish: false
    };

    this.cameraData = null;

  }

  state = {
    displayVideoView: false,
    cameraType: RNCamera.Constants.Type.front
  }

  componentDidMount() {

    this.props.navigation.setParams({
      // show:true
      title: '',
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.CLOSE,
          onPress: () => {
            this.props.navigation.goBack();
            // if (this.inType == "photo") {
            //   this.props.navigation.goBack();
            // } else {
            //   if (this.state.uploadUri) {
            //     // this.camera.pausePreview();
            //     this.setState({
            //       canvasFinish:false,
            //       uploadUri: ''
            //     })
            //   } else {
            //     this.setState({
            //       displayVideoView:false
            //     })
            //     this.props.navigation.goBack()
            //   }
            // }
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      if (!this.isPageForeGround) { // app进入前台，ios/android都会调用。对android，从native页面返回也会调用这个页面
        return;
      }
      this.isPluginForeGround = true;// rnactivity调用了onresume
      this.checkPreConditions();
    });
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.checkPreConditions();
      }
    );
    this.didBlurListener = this.props.navigation.addListener(
      'didBlur',
      () => {
        this.setState({
          displayVideoView: false
        });
      }
    );

    Orientation.lockToPortrait();
  }
  componentWillUnmount() {
    this.didFocusListener.remove()
    this.didBlurListener.remove()
    this.didResumeListener.remove()
    if (Host.isPad && Host.isAndroid) {
      Orientation.unlockAllOrientations();
    }
  }

  checkPreConditions() {
    this.checkCameraPermission()
      .then(() => {
        if (this.props.navigation.isFocused()) {
          this.setState({ displayVideoView: true })
          // setTimeout(() => {
          //   this.setState({
          //     cameraType: RNCamera.Constants.Type.front
          //   })
          // }, 10);
        } else {

        }

      })
      .catch((err) => {
        console.log('err', err)
        Toast.fail("auth_fail", err)
      });

  }

  checkCameraPermission() {
    return new Promise((resolve, reject) => {
      if (Platform.OS == "android") {

        PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.CAMERA)
          .then((result) => {
            if (result === PermissionsAndroid.RESULTS.GRANTED) {
              resolve();
            }
            else if (result === PermissionsAndroid.RESULTS.REJECTED) {
              Toast.fail('please_open_camera')
              reject();////
            }
          })
          .catch((err) => {
            reject();
          })
      } else {
        System.permission.request("camera").then((res) => {
          // alert(`requestPermission,result:${ res }`);
          if (res) {
            resolve();//xxxx
          }
          else {
            Toast.fail('auth_fail')
          }

        }).catch((error) => {
          // alert(`requestPermission,error:${ JSON.parse(error) }`);
          reject();
        });
      }
    });
  }



  handleCanvas = (canvas) => {
    console.log('containerWidth', containerWidth, Host.isPad)

    if (canvas == null) {
      return;
    }
    const ctx = canvas.getContext('2d');
    canvas.width = containerWidth;
    canvas.height = containerWidth //这里的高度要和下面的那个对应
    ctx.fillStyle = "dark" == DarkMode.getColorScheme() ? "xm#000" : "#fff";
    ctx.fillRect(0, 0, containerWidth, containerWidth);
    ctx.fillStyle = "dark" == DarkMode.getColorScheme() ? "xm#000" : "#fff";
    ctx.arc(containerWidth / 2, containerWidth / 2 + 12, (containerWidth / 2) - 26, 0, 2 * Math.PI);//x是中间，y是200
    // ctx.arc(containerWidth / 2, containerWidth / 2, (containerWidth / 2) -26, 0, 2 * Math.PI);//x是中间，y是200
    ctx.globalCompositeOperation = 'xor';
    ctx.fill();
    setTimeout(() => {
      this.setState({
        canvasFinish: true
      })
    }, 500)


  }
  takePicture = async function () {
    //这里等待
    // if (Host.isIOS) {
    //   if (!await Camera.checkDeviceAuthorizationStatus()) {
    //     alert('相机权限未开启')
    //     return
    //   }
    // }
    if (Host.isAndroid) {
      if (!await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.CAMERA)) {
        let str = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_camera"])
        console.log(str, 'str')
        Toast._showToast(str)
        return
      } else {
        console.log('相机权限已开启');
      }
    }
    if (this.camera) {
      const options = {
        quality: 1,
        mirrorImage: true,
        doNotSave: false,
        pauseAfterCapture: true,
        fixOrientation: true,
        width: 1000,
        height: 1000,
        base64: true
        // writeExif:true,
        // path: Host.file.storageBasePath
      }
      Toast.loading('c_setting');//请稍后
      this.cameraData = await this.camera.takePictureAsync(options);
      console.log(this.cameraData, 'data', Host.file.storageBasePath)
      let source = { uri: this.cameraData.uri };
      //这里是开始预览
      this.camera.resumePreview();
      this.setState({
        uploadUri: source
      });
      // console.log(this.state.uploadUri);
    }
  }
  _cancelChoose() {
    if (this.inType == "camera") {
      this.setState({ canvasFinish:false, uploadUri: '' });
    } else {
      this.props.navigation.goBack();
      if (this.props.navigation.state.params ) {
        this.props.navigation.state.params.callback({
          action: "cancel"
        })
      }
    }

  }

  unloadAvatarImg() {
    let path = this.state.uploadUri.uri.slice(7)
    console.log('path', path, this.state.uploadUri.uri)
    if (this.inType == "camera") {
      this.cropImage(this.cameraData);
      let targetName = `avatar/crop_avatar.jpg`;

      // Host.file.writeFileThroughBase64(targetName, this.cameraData.base64).then(result => {
      //   console.log("==========write success");
      //   this.uploadAvatarImg(targetName);
      // }).catch((error) => {
      //   Toast.fail("c_set_fail");
      // });
    } else {
      this.uploadAvatarImg(this.selectData.filename);
    }

  }

  async cropImage(data) {
    try {
      let width = data.width;
      let height = data.height;
      let targetName = `avatar/crop_avatar.jpg`;
      let tempTargetName = `avatar/crop_avatar_temp.jpg`;
      let imgMin = Math.min(width,height);
      let cropSize = imgMin;
      let imgMax = Math.max(width,height);
      let x = 0, y = 0;
      if (width >= height) {
        x = (imgMax - imgMin) / 2;
      } else {
        y = (imgMax - imgMin) / 2;
      }
      Host.file.deleteFile(targetName).then((res) => {
        console.log("================",res)
        this.doCropImage(data,targetName,tempTargetName,cropSize,x,y);
      }).catch((error) => {
        console.log("================error",error);
        this.doCropImage(data,targetName,tempTargetName,cropSize,x,y);
      });

    } catch (e) {
      console.log("crop avatar error2",e)
      Toast.fail("c_set_fail");
    }
  }

  doCropImage(data,targetName,tempTargetName,cropSize,x,y) {
    Host.file.writeFileThroughBase64(tempTargetName, data.base64).then(async result => {
      console.log("write file success", result);
      let params = {
        offset: { x: x, y: y },
        size: { width: cropSize, height: cropSize },
        displaySize: { width: 238, height: 238 }
      };
      console.log("crop params", params);
      Host.file.cropImage(targetName,tempTargetName, params).then((res) => {
        console.log("is success", res);
        this.uploadAvatarImg(targetName);
      }).catch((error) => {
        console.log("crop avatar error1", error);
      });

    }).catch(err => {
      console.log("write file error", err);
      Toast.fail("c_set_fail");
    })
  }

  uploadAvatarImg(filename) {
    FDSUtil.uploadAvatarToServer(filename).then((res) => {
      // 上传成功
      this.props.navigation.goBack();
      if (this.props.navigation.state.params ) {
        this.props.navigation.state.params.callback({
          action: "success",
          objName: res.objName,
          downloadUrl: res.downloadUrl
        })
      }
    }).catch((error) => {
      Toast.fail("c_set_fail");
    });
  }

  render() {
    let isFocused = this.props.navigation.isFocused();//是否聚焦显示了
    let imgWidth = (Math.sqrt(Math.pow(containerWidth - 52, 2))) / Math.sqrt(2)
    if (!this.state.uploadUri) {
      return (
        <View style={[styles.container]}>
          {/* 上面照相部分 */}
          <View style={{ width: containerWidth - 52, aspectRatio: 3 / 4, }}>
          {/*<View style={{ width: containerWidth - 52, aspectRatio: 1, marginTop: 38 }}>*/}
            {
              this.state.displayVideoView && this.state.canvasFinish ?
                <RNCamera
                  ref={(ref) => {
                    this.camera = ref;
                  }}
                  style={styles.preview}
                  type={RNCamera.Constants.Type.front}
                  ratio="4:3"
                  // ratio="1:1"
                  flashMode={RNCamera.Constants.FlashMode.off}
                  cameraViewDimensions={{
                    width: containerWidth - 52,
                    height: (containerWidth - 52) * 4 / 3
                    // height: containerWidth - 52
                  }}
                  onFacesDetected={null}
                  onGoogleVisionBarcodesDetected={null}
                  onTextRecognized={null}
                  onBarCodeRead={null}
                  captureAudio={false}
                  onCameraReady={async (result) => {//also will fired by changing type or cameraId
                    let supportRatio = await this.camera.getSupportedRatiosAsync()
                    let supportPictureSize = await this.camera.getAvailablePictureSizes();
                    console.log(JSON.stringify(supportRatio), JSON.stringify(supportPictureSize));
                    // ["2:1","3:2","4:3","11:9","16:9"] ["320x240","640x480","800x600","1280x960","1920x1440","2048x1536"]
                    //2592-1940
                  }}
                  onMountError={(error) => {//打开摄像头失败了
                    Toast.fail('camera_connect_error', error)
                    console.log("mount camera error" + JSON.stringify(error));
                  }}
                  onStatusChange={({ cameraStatus }) => {
                    console.log("camera status changed:" + cameraStatus);
                  }}
                  onPictureTaken={() => {
                  }}
                /> :
                null
            }

          </View>

          <View style={{ position: 'absolute', width: containerWidth, height:'100%' }}>
            {/* 这里是扣得圆 */}
            <ScrollView scrollEnabled={Platform.OS == 'ios' && !Host.isPad ? false : true}>
              <Canvas ref={this.handleCanvas} />

              <View style={{ backgroundColor: "dark" == DarkMode.getColorScheme() ? "xm#000" : "#fff", height: containerHeight / 2, alignItems: "center", marginTop: -5 }}>
                <View style={{ flexGrow: 1, marginTop: 20 }}>
                  <Text>{LocalizedStrings["camera_take_photo_tips"]}</Text>
                </View>
                <View style={{ flexGrow: 1 }}>
                  <TouchableOpacity
                    onPress={this.takePicture.bind(this)}
                    style={{ borderColor: '#32BAC0', borderWidth: 2, width: 80, height: 80, borderRadius: 40, justifyContent: 'center', alignItems: "center", marginTop: containerHeight <= 736 ? -60 : 0 }}>
                    <View style={{ width: 70, height: 70, borderRadius: 35, backgroundColor: '#32BAC0' }}>
                    </View>
                  </TouchableOpacity>
                  <Text style={{ textAlign: "center", marginTop: 5, color: "dark" == DarkMode.getColorScheme() ? "xm#fff" : "#000" }}>
                    {LocalizedStrings["takePhoto"]}
                  </Text>
                </View>

              </View>
            </ScrollView>

          </View>
          {/* 四角边框 */}
          <View
            style={{
              position: 'absolute', marginLeft: 0,
              marginTop: containerWidth / 2 - imgWidth / 2 + 12 //这里的margintop要根据圆的位置计算一下
            }}>
            <Image
              style={{ width: imgWidth, height: imgWidth }}
              source={require('../../Resources/Images/four-corner.png')}>

            </Image>
          </View>
        </View >
      );
    }
    else {
      return (
        <View style={styles.container}>
          <View style={{ flexGrow: 1, marginTop: 38 }}>
            {/* 预览图片 */}
            <Image
              style={{ width: containerWidth - 52, height: containerWidth - 52, borderRadius: (containerWidth - 52) / 2 }}
              source={this.state.uploadUri}
            />
          </View>


          <View style={{ flexDirection: 'row' }}
          >
            <RkButton
              style={{ marginHorizontal: 27, flexGrow: 1, height: 46, borderRadius: 23, backgroundColor: '#0000000F', display: 'flex' }}
              onPress={() => { this._cancelChoose() }}
              activeOpacity={0.8}
            >
              <Text style={{ color: '#000000CC', fontSize: 16, fontWeight: 'bold', textAlign: 'center' }}>
                {this.inType == "camera" ? LocalizedStrings["upload_avatar_take_again"] : LocalizedStrings["upload_avatar_choose_again"]}
              </Text>
            </RkButton>

          </View>
          <View style={{ flexDirection: 'row', marginTop: 12, marginBottom: 27 }}
          >
            <RkButton
              style={{ marginHorizontal: 27, flexGrow: 1, height: 46, borderRadius: 23, backgroundColor: '#32BAC0', display: 'flex' }}
              onPress={() => {
                // this._uploadFaceimg()
                this.unloadAvatarImg();
              }}
              activeOpacity={0.8}
            >
              <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold', textAlign: 'center' }}>
                {LocalizedStrings["csps_right"]}
              </Text>
            </RkButton>
          </View>
        </View>
      )
    }

  }

}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    // justifyContent:"center"
    // flexDirection: 'column',
    backgroundColor: "dark" == DarkMode.getColorScheme() ? "xm#000" : "#fff",

  },
  preview: {

    flex: 1,
    // justifyContent: 'center',
    // alignItems: 'center',
  },
  capture: {
    flex: 0,
    // backgroundColor:"white",
    borderRadius: 5,
    padding: 15,
    paddingHorizontal: 20,
    alignSelf: 'center',
    margin: 20,
  },
});