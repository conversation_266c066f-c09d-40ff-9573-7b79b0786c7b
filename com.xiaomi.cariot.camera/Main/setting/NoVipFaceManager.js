'use strict';
import React from 'react';
import { Image, ScrollView, View, Text, TouchableOpacity, Dimensions } from 'react-native';
import { Device, Service, DarkMode, API_LEVEL } from 'miot';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import NavigationBar from "miot/ui/NavigationBar";
import StorageKeys from '../StorageKeys';
const kWindowHeight = Math.max(Dimensions.get("screen").height, Dimensions.get("screen").width);
const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width);

export default class NoVipFaceManager extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
    };

  }


  render() {

    return (
      <View style={styles.container}>
        <ScrollView style={{ flex: 1 }}>
          <View style={{ flexDirection: 'column', alignItems: "center", }}>
            <Image style={{ width: 174, height: 174, marginTop: 20 }} source={require('../../Resources/Images/icon_face_manager_first_larg.jpg')}>

            </Image>
            <View>
              <Text style={{ fontSize: 12, textAlign: 'center', marginHorizontal: 40, marginTop: 20 }}>
                {LocalizedStrings["face_manager_first_tips"]}
              </Text>
            </View>

          </View>
          <View style={{
            height: 0.5,
            marginHorizontal: 24,
            backgroundColor: "#e5e5e5",
            marginBottom: 10,
            marginTop: 20,
          }}>

          </View>

          <View >
            <View style={{ flexDirection: 'row', alignItems: "center", marginLeft: 20, marginVertical: 10 }} >
              <Image
                source={require('../../Resources/Images/icon_face_manager_first.png')}
                style={{ width: 25, height: 25, tintColor: "dark" == DarkMode.getColorScheme() ?'#DDDDDDFF':""}}
              >

              </Image>
              <View style={{ width: kWindowWidth - 65 }}>
                <Text style={{ fontSize: 15, textAlign: 'left', marginLeft: 10 }}>
                  {LocalizedStrings["face_manager_first_tips1"]}
                </Text>
              </View>
            </View>
            <View style={{ flexDirection: 'row', alignItems: "center", marginLeft: 20, marginVertical: 10 }} >
              <Image
                source={require('../../Resources/Images/icon_face_manager_first_mark.png')}
                style={{ width: 25, height: 25, tintColor: "dark" == DarkMode.getColorScheme() ? '#DDDDDDFF' : "" }}
              >

              </Image>
              <View style={{ width: kWindowWidth - 65 }}>
                <Text style={{ fontSize: 15, textAlign: 'left', marginLeft: 10 }}>
                  {LocalizedStrings["face_manager_first_tips2"]}
                </Text>
              </View>
            </View>
            <View style={{ flexDirection: 'row', alignItems: "center", marginLeft: 20, marginVertical: 10 }} >
              <Image
                source={require('../../Resources/Images/icon_face_manager_first_share.png')}
                style={{ width: 25, height: 25, tintColor: "dark" == DarkMode.getColorScheme() ? '#DDDDDDFF' : "" }}
              >

              </Image>
              <View style={{ width: kWindowWidth - 65 }}>
                <Text style={{ fontSize: 15, textAlign: 'left', marginLeft: 10 }}>
                  {LocalizedStrings["face_manager_first_tips3"]}
                </Text>
              </View>
            </View>
            <View style={{
              height: 0.5,
              marginHorizontal: 24,
              backgroundColor: "#e5e5e5",
              marginBottom: 10,
              marginTop: 20,
            }}>

            </View>
            <View style={{ marginHorizontal: 24 }}>
              <Text style={{ fontSize: 11, color: '#999999', lineHeight: 18 }}>
                {LocalizedStrings["low_power_agreement"]}
              </Text>
            </View>




          </View>
        </ScrollView>

        <View style={{  height: 46, width: '100%', flexDirection: 'row', marginBottom: 20, marginTop: 20, justifyContent: 'center', alignItems: 'flex-end' }}
        >

          <Text style={{ fontSize: 13, fontWeight: 'bold', textAlign: 'center', color: '#999999' }}>
            {LocalizedStrings["face_need_open_cloud"]}
            <Text style={{ fontSize: 13, fontWeight: 'bold', textAlign: 'center', color: '#32BAC0', textDecorationLine: 'underline', textDecorationColor: '#32BAC0' }}
              onPress={() => {
                API_LEVEL > 10068 ? Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true) : (
                  StorageKeys.IN_CLOSE_WINDOW.then((result) => {
                    if (result) {
                      Service.miotcamera.showCloudStorageSetting();
                    } else {
                      Service.miotcamera.showCloudStorage(true, true);
                    }
                  }));
              }}
            >
              {LocalizedStrings["c_cloudvip_buy"]}
            </Text>
          </Text>
        </View>
      </View>
    );
  }

  componentDidMount() {
    this.props.navigation.setParams({
      // show:true
      title: LocalizedStrings['camera_face'],
      type: DarkMode.getColorScheme() == 'dark' ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack() }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
  }



}
