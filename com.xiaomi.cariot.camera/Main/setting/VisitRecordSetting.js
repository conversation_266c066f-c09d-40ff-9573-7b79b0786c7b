'use strict';

import { ListItemWithSwitch } from 'miot/ui/ListItem';

import React from 'react';
import { Dimensions, ScrollView, View, Text, SectionList, Image, ActivityIndicator, Platform } from 'react-native';
import { Device, Service } from 'miot';


import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import Toast from '../components/Toast';
import NavigationBar from "miot/ui/NavigationBar";

import { DarkMode } from 'miot'
import Host from "miot/Host";
import dayjs from 'dayjs';

const WindowHeight = Math.max(Dimensions.get("window").height, Dimensions.get("window").width);
const WindowWidth = Math.min(Dimensions.get("window").height, Dimensions.get("window").width);
import LogUtil from '../util/LogUtil';
import NetInfo from "@react-native-community/netinfo";
class HeaderDay extends React.PureComponent {

  render() {
    let appearDay = this.props.section.title.slice(1)
    let appearWeek = [LocalizedStrings["sunday1"], LocalizedStrings["monday1"], LocalizedStrings["tuesday1"], LocalizedStrings["wednesday1"], LocalizedStrings["thursday1"], LocalizedStrings["friday1"], LocalizedStrings["saturday1"]];

    return (
      <View style={{ backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff', }}>
        {/* <View style={styles.whiteblank}>
          </View> */}
        <Text style={{ fontSize: 12, fontWeight: 'bold', color: "#8C93B0", marginLeft: 27, marginBottom: 10 }}>
          {appearDay}  {appearWeek[Number(this.props.section.title.slice(0, 1))]}
        </Text>
      </View>
    )

  }
}
class VisitList extends React.PureComponent {

  render() {
    return (
      <View style={{ display: 'flex', flexDirection: 'column', marginLeft: 27, backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : 'xm#fff', height: 72, }}>


        <View style={{ flexDirection: "row", flexWrap: "wrap" }}>
          <Text style={{ color: '#000000', fontSize: 16, fontWeight: '500' }}>
            {this.props.item.userName}
          </Text>
          <Text style={{ color: '#000000', fontSize: 16, fontWeight: '500' }}>
            ({this.props.item.uid})
          </Text>
          {
            this.props.item.permitLevel == 3 ? null :
              <Image
                style={{ width: 36, height: 21, marginLeft: 10 }}
                source={require('../../Resources/Images/shareicon.png')} />
          }
        </View>

        <View>
          <Text style={{ color: '#999999', fontSize: 13 }}>
            {this.props.item.appearHour} | {this.props.item.address} |  {this.props.item.clientUA}
          </Text>
        </View>

      </View>
    )
  }
}
export default class VisitRecordSetting extends React.PureComponent {

  constructor(props, context) {
    super(props, context);
    this.state = {
      showVisitRecordList: true,
      isRefreshing: false,
      loading: false,
      moreEndtime: 0,
      starttime: 0,
      showMoreLoading: false,
      UngroupedList: [],
      hasMore: true
    }
  }
  componentDidMount() {

    this.props.navigation.setParams({
      // show:true
      title: "",
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack() }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    // console.log(Device.deviceID)
    // console.log(Device.permitLevel, 'permitLevel')


    this.getVisitInfo()
  }
  getVisitInfo() {
    console.log("refresh visit info...");
    let endtime = new Date().getTime();
    let starttime = endtime - *********;//获取七天之内的数据,starttime 已经确定
    this.setState({
      starttime: starttime,
      endTime: endtime
    });
    console.log(endtime, starttime, Device.deviceID, '起止时间')
    Service.callSmartHomeAPI('/v2/device/batchGetRecord', {
      "did": Device.deviceID,
      // "uid":Service.account.ID
      "starttime": starttime,
      "endtime": endtime,
      "limit": 100
    })
      .then((res) => {
        console.log('resss', res.records)

        this.setState({
          isRefreshing: false
        });
        console.log("refresh visit info success");
        if (res.records) {
          console.log("total new data length:" + res.records.length);
          let records = res.records;//
          this.setState({
            UngroupedList: records,
            moreEndtime: records[records.length - 1].timestamp
          });

          let visitArr = this._onGroupedData(this._onPickedData(records))
          this.setState({
            visitData: visitArr
          });
        } else {
          Toast.fail('visit_record_null');
        }

      }).catch((err) => {
        LogUtil.logOnAll("VisitRecordSetting", "batchGetRecord failed" + err);
        NetInfo.fetch().done((status) => {
          console.log('status', JSON.stringify(status))
          let isWifi = status.toString().toLowerCase() === "wifi" || (status.type && status.type.toLowerCase() === "wifi");
          let isCellular = status.toString().toLowerCase() === "cellular" || (status.type && status.type.toLowerCase() === "cellular");
          let isConnected = status.isConnected;
          if (Platform.OS == 'android' && (isWifi || isCellular)) {
            isConnected = true;
          }
          if (!isConnected) {
            Toast.fail('no_network')
          }
          else {
            Toast.fail("c_get_fail", err);
          }
        })
        if (this.state.isRefreshing) {
          this.setState({
            isRefreshing: false
          });
        }
        // this.props.navigation.goBack(); //网络错误不应该推出去
      });


  }
  //这里是一个对象，key是日期
  _onPickedData(arr) {
    let temp = [];
    temp = arr.reduce((total, currentValue, currentIndex, arr2) => {
      let appearTime = dayjs.unix(currentValue['timestamp'] / 1000).format("d" + LocalizedStrings["yyyymmdd"] + ",HH:mm")
      let curDay = appearTime.split(',')[0]
      let curHour = appearTime.split(',')[1]
      currentValue['appearDay'] = curDay
      currentValue['appearHour'] = curHour
      let currentVal = currentValue['appearDay'];
      total[currentVal] || (total[currentVal] = []);//当前数组有吗没有是空，有的话就有
      total[currentVal].push(currentValue);
      return total;
    }, {});
    console.log("日期格式修改",temp);
    return temp
  }
  //这个是sectionliast要求的格式
  _onGroupedData(temp) {
    let groupArr = []
    for (var days in temp) {
      // console.log(days, arr[days])
      let obj = {}
      obj['title'] = days
      obj['data'] = temp[days]
      groupArr.push(obj)
    }
    console.log("格式变",groupArr);
    return groupArr
  }

  _onGetMoreData() {
    if (this.state.showMoreLoading) {
      return;//已经在加载更多了；
    }
    this.setState({
      showMoreLoading: true
    });

    let starttime = this.state.starttime;
    let moreEndtime = this.state.moreEndtime;// state里没有声明
    //这里的时间还有待商量
    Service.callSmartHomeAPI('/v2/device/batchGetRecord', {
      "did": Device.deviceID,
      // "uid":Service.account.ID
      "starttime": starttime,
      "endtime": moreEndtime,
      "limit": 100
    }).then((res) => {
      this.setState({
        showMoreLoading: false
      });
      if (res.records.length == 0 || res.records[res.records.length - 1].timestamp == this.state.moreEndtime) {
        this.setState({
          hasMore: false
        })
        return;
      } else {
        let moreEndTime = res.records[res.records.length - 1].timestamp;
        console.log("update MoreEndTime:" + moreEndTime);
        this.setState({
          moreEndtime: moreEndTime
        });
        let lastData = this.state.UngroupedList;
        let newData = lastData.concat(res.records);
        console.log("load more data:", "original dataLength:" + lastData.length + " new DataLength:" + newData.length);
        let visitArr = this._onGroupedData(this._onPickedData(newData));//重新group一遍。
        this.setState({
          visitData: visitArr,
          UngroupedList: newData
        });
      }

    }).catch((err) => {
      console.log('获取更多失败', err);
      NetInfo.fetch().done((status) => {
        console.log('status', JSON.stringify(status))
        let isWifi = status.toString().toLowerCase() === "wifi" || (status.type && status.type.toLowerCase() === "wifi");
        let isCellular = status.toString().toLowerCase() === "cellular" || (status.type && status.type.toLowerCase() === "cellular");
        let isConnected = status.isConnected;
        if (Platform.OS == 'android' && (isWifi || isCellular)) {
          isConnected = true;
        }
        if (!isConnected) {
          Toast.fail('no_network')
        }
        else {
          Toast.fail("c_get_fail", err);
        }
      })
      this.setState({ showMoreLoading: false });
    });

  }
  render() {
    return (
      <View style={styles.container}>
        {this._renderVisitRecordListView()}
        {this._renderLoadMoreView()}
      </View>
    );
  }

  //这里应该以bottom view放到sectionList的footer里，然后如果数据拉取完毕，应该给用户一个提示；
  _renderLoadMoreView() {
    if (!this.state.isRefreshing) {
      return null;
    }
    return (
      <View style={{
        alignItems: "center",
        zIndex: -1,
        marginBottom: Host.isAndroid ? 30 : 20
      }}>
        <ActivityIndicator
          style={{ color: "red", }}
          size={"small"}
          color={"#000000"}
          animating={true}
        />
        <Text>{LocalizedStrings["camera_loading"]}</Text>
      </View>
    )


  }

  _renderVisitRecordListView() {
    if (this.state.showVisitRecordList) {
      return (
        <View style={{ height: '100%' }}>

          <SectionList
            onScroll={this.scrollViewScroll}
            sections={this.state.visitData}
            keyExtractor={(item, index) => item + index}
            renderItem={({ item, index }) =>
              <VisitList item={item} />
            }
            renderSectionHeader={({ section }) =>
              // this._renderHeaderDay(section)
              <HeaderDay section={section} />
            }
            ListHeaderComponent={this._renderHeaderTitle()}
            ListFooterComponent={
              this.state.hasMore ? this._renderLoadMoreView() : null
            }
            stickySectionHeadersEnabled={true}
            refreshing={this.state.isRefreshing}

            onRefresh={() => {
              this.setState({
                isRefreshing: true
              })
              this.getVisitInfo(); //下拉刷新加载数据
            }}
            onEndReachedThreshold={0.1}

            onEndReached={(item, index) => {
              this._onGetMoreData();
            }}
            getItemLayout={(data, index) => (//这里需要根据group数据做限制；
              { length: 72, offset: 72 * index + 20, index }
            )}
          />
        </View>
      );
    }
  }

  _renderHeaderTitle() {
    return(
      <View style={{ flexDirection: "row", flexWrap: "wrap" }} key={0}>
        <Text style={{ fontSize: 30, color: "rgba(0, 0, 0, 0.80)", fontWeight:"300",position: "relative", marginLeft: 25, marginTop: 3, marginBottom:23,fontFamily:'MI-LANTING--GBK1-Light'  }}>
          {LocalizedStrings['visit_record']}
        </Text>
      </View>
    )
  }
  // 监听列表滚动事件
  scrollViewScroll = (event) => {
    const y = event.nativeEvent.contentOffset.y;
    let flag = y > 28;
    if (this.showTitle == flag) {
      return;
    }
    if (flag) {
      this.showTitle = true;
      this.props.navigation.setParams({ title: LocalizedStrings['visit_record'] });
    } else {
      this.showTitle = false;
      this.props.navigation.setParams({ title: "" });
    }
  };
  _renderHeaderDay(item) {
    // let appearWeek;
    let appearDay = item.title.slice(1)
    let appearWeek = [LocalizedStrings["sunday1"], LocalizedStrings["monday1"], LocalizedStrings["tuesday1"], LocalizedStrings["wednesday1"], LocalizedStrings["thursday1"], LocalizedStrings["friday1"], LocalizedStrings["saturday1"]];
    return (
      <View style={{ backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff', }}>
        {/* <View style={styles.whiteblank}>
        </View> */}
        <Text style={{ fontSize: 12, fontWeight: 'bold', color: "#8C93B0", marginLeft: 27, marginBottom: 10 }}>
          {appearDay}  {appearWeek[Number(item.title.slice(0, 1))]}
        </Text>
      </View>
    );
  }

  _renderDayRecord(item, index) {
    // console.log(item, index, 'item')
    return (
      <View style={{ backgroundColor: "dark" == DarkMode.getColorScheme() ? 'xm#000' : '#fff', height: 72 }}>
        <View style={{ marginLeft: 27, marginTop: 16, display: 'flex', flexDirection: 'column', }}>

          <View style={{ flexDirection: "row", alignItems: 'center' }}>
            <Text style={{ color: '#000000', fontSize: 16, fontWeight: '500' }}>
              {item.userName} ({item.uid})
            </Text>
            {
              item.permitLevel == 3 ? null :
                <Image
                  style={{ width: 36, height: 21, marginLeft: 10 }}
                  source={require('../../Resources/Images/shareicon.png')} />
            }
          </View>


          <Text style={{ color: '#999999', fontSize: 13 }}>
            {item.appearHour} | {item.address} |  {item.clientUA}
          </Text>


        </View>

      </View>
    );
  }


  _onRecordSwitchValueChange(value) {
    console.log(value, 'value');
    Toast.loading('c_setting');
    this.setState({
      showVisitRecordList: value
    });

  }


}
