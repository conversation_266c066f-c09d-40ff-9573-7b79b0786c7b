'use strict';

import { Device, Service } from "miot";
import { Styles } from 'miot/resources';
import { ListItem, ListItemWithSlider, ListItemWithSwitch } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { ScrollView, Image, Text, View, TouchableOpacity, Dimensions } from 'react-native';
import CameraConfig from '../util/CameraConfig';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import VersionUtil from "../util/VersionUtil";
import { CAMERA_CONTROL_SEPC_PARAMS } from "../Constants";
import Toast from '../components/Toast';
import NavigationBar from "miot/ui/NavigationBar";
import ImageButton from "miot/ui/ImageButton";
import RPC from "../util/RPC";
import TrackUtil from '../util/TrackUtil';
import { DescriptionConstants } from "../Constants";
import ChoiceItemCustom from "../ui/ChoiceItemCustom";
const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width);// use this.winPortraitWidth instead
const imageWidth = (kWindowWidth - 60) / 2;
export default class NightVisionSetting extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      glimmerColor: false,
      nightMode: null
    };
  }


  render() {

    let nightVisionArray = [
      { name: LocalizedStrings['nvs_auto'], value: 0 },
      { name: this.supportGlimmer ? LocalizedStrings['nvs_open2'] : LocalizedStrings['nvs_open'], value: 2 },
      { name: LocalizedStrings['nvs_close'], value: 1 }];
    return (
      <View style={styles.container}>
      
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.featureSetting}
            key={5}
          >

            {this.supportGlimmer ?
              <ListItemWithSwitch
                title={LocalizedStrings['glimmer_colorful_pic']}
                showSeparator={false}
                titleNumberOfLines={2}
                unlimitedHeightEnable={true}
                value={this.state.glimmerColor}
                onValueChange={(value) => this._setGlimmerColor(value)}
                titleStyle={{ fontWeight: 'bold' }}
                onPress={() => {
                }}
                accessibilitySwitch={{
                  accessibilityLabel: DescriptionConstants.sz_4_76
                }}
              />
              :
              null
            }

            {this._renderGlimmerPictures()}

            {this.supportGlimmer ?
              <View style={{ 
                height: 0.5,
                marginHorizontal: 24,
                backgroundColor: "#e5e5e5",
                marginBottom: 25,
                marginTop: 0 }}
              key={8}
              />
              :
              null
            }
            {/* 上面是微光全彩 下面开始夜视 */}
            <View style={{ marginHorizontal: 24, marginTop: 20, marginBottom: 15, flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' }}>
              {
                [
                  { text: LocalizedStrings['ldc_before_activation'], uri: require('../../Resources/Images/mjcamera_infrared_normal_mode.webp') },
                  { text: LocalizedStrings['ldc_after_activation'], uri: require('../../Resources/Images/mjcamera_infrared_night_mode.webp') }
                ].map((item, i) => {
                  return (
                    <View
                      style={{ alignItems: 'center' }}
                      key={i}
                    >
                      <Image
                        key={i}
                        style={{ width: imageWidth, borderRadius: 12, height: imageWidth }}
                        source={item.uri}
                        accessibilityLabel={DescriptionConstants.example_image}
                      />
                      <Text
                        numberOfLines={2}
                        style={{ paddingHorizontal: 10, position: "absolute", bottom: 4, fontSize: 12, color: 'xm#ffffff' }}
                      >
                        {item.text}
                      </Text>
                    </View>
                  );
                })
              }
            </View>
            {/*{*/}
            {/*  nightVisionArray.map((item, i) => {*/}
            {/*    return (*/}
            {/*      <ListItem*/}
            {/*        key={i}*/}
            {/*        showSeparator={false}*/}
            {/*        unlimitedHeightEnable={i == 1?true: false}*/}
            {/*        */}
            {/*        title={*/}
            {/*          (this.state.nightMode == item.value ? '> ' : '  ') + item.name*/}
            {/*        }*/}
            {/*        titleNumberOfLines={4}*/}
            {/*        titleStyle={*/}
            {/*          { fontSize: 15, fontWeight: 'bold', color: this.state.nightMode == item.value ? Styles.common.MHGreen : 'black' }*/}
            {/*        }*/}
            {/*        hideArrow={true}*/}
            {/*        onPress={() =>*/}
            {/*          this._onSelectedItem(item.value)*/}
            {/*        } */}
            {/*        />*/}
            {/*    );*/}
            {/*  })*/}
            {/*}*/}
            <View style={{ marginHorizontal: 27, marginBottom: 20 }}
                  key={6}
            >
              <Text
                style={{ fontSize: 13, color: 'rgba(0,0,0,0.5)' }}>
                {LocalizedStrings['nvs_detail']}
              </Text>
            </View>
            <View style={{
              height: 0.5,
              marginHorizontal: 27,
              backgroundColor: "#e5e5e5",
              marginBottom: 28,
              marginTop: 0 }}
                  key={8}
            />
            <Text
              style={{ fontSize: 12, color: '#8C93B0', marginBottom: 8, marginHorizontal: 27 }}>
              {LocalizedStrings['mode_tx']}
            </Text>
            {
              nightVisionArray.map((item, i) => {
                return (
                  <ChoiceItemCustom
                    key={i}
                    checked={this.state.nightMode == item.value}
                    containerStyle={{ display: "flex",height: 70, marginHorizontal: 22, marginVertical: 6, justifyContent: 'center' }}
                    selectedIconLeft={require("../../Resources/Images/icon_smart_monitor_tick.png")}
                    title={item.name}
                    onValueChange={(value) => {value && this._onSelectedItem(item.value)}}
                  />
                );
              })
            }

          </View>

        </ScrollView>
      </View>
    );
  }

  _renderGlimmerPictures() {
    if (!this.supportGlimmer) {
      return null;
    }

    return (
      <View style={{ margin: 27 }}
        key={7}
      >
        <Text 
          style={{ fontSize: 13, color: 'rgba(0,0,0,0.5)' }}>
          {LocalizedStrings['glimmer_colorful_pic_desc']}
        </Text>
        <View style={{ top: 10, flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' }}>
          {
            [
              { text: LocalizedStrings['glimmer_colorful_switch_off'], uri: require('../../Resources/Images/glimmer_switch_off.jpg') },
              { text: LocalizedStrings['glimmer_colorful_switch_on'], uri: require('../../Resources/Images/glimmer_switch_on.jpg') }
            ].map((item, i) => {
              return (
                <TouchableOpacity style={{ width: '46%', minHeight: 160 }}
                  key={i}
                  activeOpacity={1}
                >
                  <Image
                    key={i}
                    style={{ width: '100%', height: 100, borderRadius: 12 }}
                    source={item.uri}
                    accessibilityLabel={DescriptionConstants.example_image}
                  />
                  <Text 
                    style={{ fontSize: 13, color: 'rgba(0,0,0,0.5)', top: 10 }}>
                    {item.text}
                  </Text>
                </TouchableOpacity>
              );
            })
          }
        </View>
      </View>
    );
  }

  componentDidMount() {
    this.props.navigation.setParams({
      title: LocalizedStrings['night_vision_setting'],
      titleNumberOfLines: 2,
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });
    
    if (VersionUtil.isUsingSpec(Device.model)) {
      Service.spec.getPropertiesValue([CAMERA_CONTROL_SEPC_PARAMS[0]], 2)
        .then((res) => {
          console.log(res);
          let value = res[0].value;
          this.setState({
            nightMode: 2 - value
          });
        })
        .catch((err) => {
          Toast.fail('c_get_fail', err);
        });
    } else {
      RPC.callMethod("get_prop", [
        'night_mode'
      ]).then((res) => {
        this.setState({
          nightMode: res.result[0]
        });
      }).catch((err) => {
        Toast.fail('c_get_fail', err);
      });
    }

    

    // this.supportGlimmer = CameraConfig.isSupportGlimmerColor(Device.model);
    this.supportGlimmer = false;
    if (this.supportGlimmer) {
      this._getGlimmerColor();
    }
  }

  _onSelectedItem(value) {
    TrackUtil.reportResultEvent("NightVisionNumber", "type", value + 1);
    Toast.loading('c_setting');
    if (VersionUtil.isUsingSpec(Device.model)) {
      value = 2 - value;
      Service.spec.setPropertiesValue([{ ...CAMERA_CONTROL_SEPC_PARAMS[0], value }])
        .then((res) => {
          if (res[0].code == 0) {
            this.setState({
              nightMode: (2 - value)
            });          
            Toast.success('c_set_success'); 
          } else {
            throw "response not right";
          }
 
        })
        .catch((err) => {
          Toast.fail('c_set_fail', err);
        });
    } else {
      RPC.callMethod("set_night_mode", [
        value
      ]).then((res) => {
        this.setState({
          nightMode: res.result[0] == 'OK' ? value : this.state.nightMode
        });
        Toast.success('c_set_success');
      }).catch((err) => {
        Toast.fail('c_set_fail', err);
      });
    }
  }

  _setGlimmerColor(value) {
    TrackUtil.reportClickEvent("FullColorOnOff_ClickNum");
    Toast.loading('c_setting');
    let strValue = value ? "on" : "off";
    if (VersionUtil.isUsingSpec(Device.model)) {
      Service.spec.setPropertiesValue([{ ...CAMERA_CONTROL_SEPC_PARAMS[1], value }])
        .then((res) => {

          if (res[0].code == 0) {
            this.setState({
              glimmerColor: value
            });          
            Toast.success('c_set_success'); 
          } else {
            throw "response not right";
          }
        })
        .catch((err) => {
          Toast.fail('c_set_fail', err);
        });
    } else {
      RPC.callMethod("set_full_color", [
        strValue
      ]).then((res) => {
        this.setState({
          glimmerColor: res.result[0] == 'OK' ? value : this.state.glimmerColor
        });
        Toast.success('c_set_success');
      }).catch((err) => {
        Toast.fail('c_set_fail', err);
      });
    }
    
  }

  _getGlimmerColor() {
    if (VersionUtil.isUsingSpec(Device.model)) {
      Service.spec.getPropertiesValue([CAMERA_CONTROL_SEPC_PARAMS[1]], 2)
        .then((res) => {
          console.log(res);
          let isOn = res[0].value;
          this.setState({ glimmerColor: isOn });
        })
        .catch((err) => {
          Toast.fail('c_get_fail', err);
        });
    } else {
      RPC.callMethod("get_prop", [
        'full_color'
      ]).then((res) => {
        let isOn = res.result[0] == 'on' ? true : false;
        this.setState({ glimmerColor: isOn });
      }).catch((err) => {
        console.log(`get full color error: ${ err }`);
        Toast.fail('c_get_fail', err);
      });
    }
  }
}