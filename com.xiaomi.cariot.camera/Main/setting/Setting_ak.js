'use strict';

import { Device, Service, System, Host, PackageEvent, API_LEVEL, DarkMode } from "miot";
import { strings } from 'miot/resources';
import { CommonSetting, SETTING_KEYS } from "miot/ui/CommonSetting";
import { firstAllOptions, secondAllOptions } from "miot/ui/CommonSetting/CommonSetting";
import { MessageDialog, NavigationBar } from "mhui-rn";
import { ListItem } from 'miot/ui/ListItem';
import React from 'react';
import { ScrollView, Text, View, Platform, PermissionsAndroid, StatusBar } from 'react-native';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import Toast from '../components/Toast';
import VersionUtil from "../util/VersionUtil";
import CameraConfig from '../util/CameraConfig';
import TrackUtil from '../util/TrackUtil';
import { DescriptionConstants } from "../Constants";
import VipUtil from "../util/VipUtil";
import LogUtil from "../util/LogUtil";
import BaseSettingPage from "../BaseSettingPage";
import { Settings } from "miot/ui";

export default class Setting extends BaseSettingPage {



  constructor(props, context) {
    super(props, context);
    this.state = {
      showDot: [],
      extraOptions: {
        excludeRequiredOptions: [
          SETTING_KEYS.first_options.PRODUCT_BAIKE
        ]
      },
      permissionRequestState: 0,
      showPermissionDialog: false
    };
    this.isCloudServer=CameraConfig.getIsCloudServer();
    console.log("🚀 ~ file: Setting.js ~ line 49 ~ Setting ~ constructor ~ this.isCloudServer", this.isCloudServer)
    
    this.firmwareUpdate = this.props.navigation.getParam("hasFirmwareUpdate");
    this.nasTips = this.props.navigation.getParam("hasNasTips");
    this.backFromItemTime = 0;
    this.mSupportCloud = VersionUtil.isFirmwareSupportCloud(Device.model);
    this.mSupportCloudCountry = CameraConfig.isSupportCloud(); // 云存储设置需要单独伶出来
    this.isPageForeGround = true;// 默认当前page在前台
    this.isPluginForeGround = true;// 默认当前插件在前台
    this.isAppForeground = true;
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        this.isPageForeGround = true;
        this.isPluginForeGround = true;
        this.isAppForeground = true;
        this._onResume();
      }
    );
    this.didBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'didBlur',
      () => {
        this.isPageForeGround = false;
        this._onPause();
      }
    );

    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.isAppForeground = true;// rnactivity调用了onresume
      this._onResume();
    });

    this.willPauseListener = PackageEvent.packageWillPause.addListener(() => {
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this.isAppForeground = false;// rnactivity调用了onresume
      this._onPause();
    });

    if (Platform.OS == "ios") {
      this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.isPluginForeGround = true;// rnactivity调用了onresume
        this._onResume();
      });

      this.willDisappearListener = PackageEvent.packageViewWillDisappearIOS.addListener(() => {
        if (!this.isPageForeGround) { // 如果当前插件不在前台
          return;
        }
        this.isPluginForeGround = false;// rnactivity调用了onpause
        this._onPause();
      });
    }

    this.isVip = CameraConfig.isVip;
    this.isInExpireWindow = this.props.navigation?.state?.params?.inCloseWindow;
    this._showAI();
  }

  _onResume() {
    if (!this.isPageForeGround) {
      return;
    }

    if (!this.isPluginForeGround) {
      return;
    }

    if (!this.isAppForeground) {
      return;
    }
    // 之前在render里设置的颜色，从设置里退回，如果摄像头处于关闭状态 render不会掉用。

    DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');

    if (CameraConfig.isToUpdateVipStatue) {
      VipUtil.getVipStatus().then(({ isVip }) => {
        this.isVip = isVip;
        this._showAI();
        CameraConfig.isToUpdateVipStatue = false;
      }).catch((err) => {
        LogUtil.logOnAll("Settings VipUtil.getVipStatus() err = ", JSON.stringify(err));
      });
    }
    this.setState({});// 强制刷新一下
  }

  _onPause() {
    this.backFromItemTime = new Date().getTime();
  }

  getTitle() {
    return LocalizedStrings["setting"];
  }

  renderSettingContent() {
    let item1 = (
      <ListItem
        title={LocalizedStrings['s_camera_setting']}
        key={1}
        titleNumberOfLines={2}

        showSeparator={false}
        onPress={() => {
          console.log('ssss')
          TrackUtil.reportClickEvent("Setting_Camera_ClickNum")
          this._navigate('CameraSetting')
        }
        }
        titleStyle={{ fontWeight: 'bold' }}
        accessibilityLabel={DescriptionConstants.sz_4}
      />
    );
    let item2 = (
      <ListItem
        title={LocalizedStrings['s_survelillance_setting']}
        key={2}
        titleNumberOfLines={2}
        showSeparator={false}
        onPress={() => {
          let time = new Date().getTime();
          let dTime = time - this.backFromItemTime;
          console.log(`time: ${dTime}`);
          TrackUtil.reportClickEvent("Setting_Monitoring_ClickNum");
          if (dTime > 1500 || Platform.OS == "android") {
            if (!this.mSupportCloud) {
              this._navigate('SurvelillanceSettingOld', { vip: this.isVip });
            } else {
              this._navigate('SurvelillanceSetting', { vip: this.isVip });
            }
          }
        }
        }
        titleStyle={{ fontWeight: 'bold' }}
        accessibilityLabel={DescriptionConstants.sz_5}
      />
    );

    let showAI = this.showFace || this.showBabyCry || this.showPet || this.showFaceManager;
    if (CameraConfig.Model_chuangmi_051a01 == Device.model || CameraConfig.Model_chuangmi_086ac1 == Device.model) {
      showAI = false;
    }
    // showAI = true;
    let item3 = (showAI ?
      <ListItem title={LocalizedStrings['s_ai_setting']}
        key={3}
        titleNumberOfLines={2}
        showSeparator={false}
        onPress={() => {
          TrackUtil.reportClickEvent("Setting_Automation_ClickNum");
          this._navigateAISetting();
        }
        }
        titleStyle={{ fontWeight: 'bold' }}
        accessibilityLabel={DescriptionConstants.sz_6}
      /> : null
    );
    let item4 = (
      <ListItem title={LocalizedStrings['manage_storage']}
        key={4}
        titleNumberOfLines={2}
        showSeparator={false}
        showDot={this.nasTips && !CameraConfig.nasUpgradeDlgBtnChecked}
        onPress={() => {
          TrackUtil.reportClickEvent("Setting_Storage_ClickNum")
          this._navigate('StorageSetting')

        }
        }
        titleStyle={{ fontWeight: 'bold' }}
        accessibilityLabel={DescriptionConstants.sz_7}
      />
    );
    let item5 = (
      <ListItem
        titleStyle={{ fontWeight: 'bold' }}
        title={LocalizedStrings['s_photo_album']}
        key={5}
        titleNumberOfLines={2}
        showSeparator={false}
        onPress={() => {
          TrackUtil.reportClickEvent("Setting_Album_ClickNum");
          if (Platform.OS == "android") {
            PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
              .then((granted) => {
                console.log("granted", granted);
                if (granted === PermissionsAndroid.RESULTS.GRANTED) {
                  this._showAlbum();
                } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
                  this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
                } else {
                  Toast.success("camera_no_write_permission");
                }
              }).catch((error) => {
                Toast.fail("action_failed", error);
              });
          } else {
            // no ios's photos const use hardcode
            System.permission.request("photos").then((res) => {
              this._showAlbum();
            }).catch((error) => {
              // Toast.success("camera_no_write_permission");
              this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
            });
          }

        }
        }
        accessibilityLabel={DescriptionConstants.sz_8}
      />
    );
    let item6 = (
      <ListItem
        title={this.isCloudServer?LocalizedStrings['eu_cloud_seting']:LocalizedStrings['cloud_seting']}
        key={6}
        titleNumberOfLines={2}
        showSeparator={false}
        onPress={() => {
          TrackUtil.reportClickEvent("Setting_CloudStorage_ClickNum");
          if (!CameraConfig.isVip) {
            CameraConfig.isToUpdateVipStatue = true;
            API_LEVEL > 10068 ? Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: "setting_list" }) : Service.miotcamera.showCloudStorage(true, true);
          } else {
            Service.miotcamera.showCloudStorageSetting();
          }
        }
        }
        
        titleStyle={{ fontWeight: 'bold' }}
        accessibilityLabel={DescriptionConstants.sz_9}
      />
    );
    let item7 = (
      <ListItem
        key={7}
        showSeparator={false}
        titleNumberOfLines={2}
        title={LocalizedStrings['cs_wx_call']}
        onPress={() =>
          this.props.navigation.navigate('WXCallSetting')
        }
        titleStyle={{ fontWeight: 'bold' }}

      />
    );

    let item8 = (
      <ListItem title={LocalizedStrings['cs_call_record']}
        key={8}
        titleNumberOfLines={2}
        showSeparator={false}
        onPress={() => {
          this.props.navigation.navigate('CallRecordSetting');
        }
        }
        titleStyle={{ fontWeight: 'bold' }}
        accessibilityLabel={DescriptionConstants.sz_7}
      />
    );
    let item9 = (
      <ListItem title={LocalizedStrings['visit_record']}
        key={9}
        titleNumberOfLines={2}
        showSeparator={false}
        onPress={() => {
          TrackUtil.reportClickEvent("Setting_Storage_ClickNum")
          this.props.navigation.navigate('VisitRecordSetting')
        }
        }
        titleStyle={{ fontWeight: 'bold' }}
        accessibilityLabel={DescriptionConstants.sz_7}
      />
    );
    let item10 = (
      <ListItem title={LocalizedStrings['ss_event_type_idm_cast_screen']}
                key={10}
                titleNumberOfLines={2}
                disabled={this.isLocalMode}
                showSeparator={false}
                onPress={() => {
                  TrackUtil.reportClickEvent("Setting_Storage_ClickNum")
                  // Service.miotcamera.showScreenLinkagePage(true, 3, Device.deviceID, ['push_enable_visit_linkage','push_enable_visit_staying']);
                  Service.miotcamera.showScreenLinkagePage(true, 3, Device.deviceID, [
                    // {eventName: 'event.8.7', pushName: "push_enable_event.8.7", pushDescription: LocalizedStrings['baby_cry_desc']},
                    {eventName: 'event.8.12', pushName: "push_enable_event.8.12", pushDescription: LocalizedStrings['people_move_desc']},
                    {eventName: 'event.8.14', pushName: "push_enable_event.8.14", pushDescription: LocalizedStrings['object_move_desc']},
                    {eventName: 'event.8.13', pushName: "push_enable_event.8.13", pushDescription: LocalizedStrings['loud_desc']},
                    // {eventName: 'event.8.12', pushName: "push_enable_event.8.12", pushDescription: LocalizedStrings['cough_desc']},
                    // {eventName: 'event.16.1', pushName: "push_enable_event.16.1", pushDescription: LocalizedStrings['pass_in_desc']},
                    // {eventName: 'event.16.2', pushName: "push_enable_event.16.2", pushDescription: LocalizedStrings['pass_out_desc']},
                    // {eventName: 'event.18.5', pushName: "push_enable_event.18.5", pushDescription: LocalizedStrings['expression_desc']},
                    // {eventName: 'event.18.6', pushName: "push_enable_event.18.6", pushDescription: LocalizedStrings['detect_mouth_norse']},
                  ]);
                }
                }
                titleStyle={{ fontWeight: 'bold' }}
                accessibilityLabel={DescriptionConstants.sz_7}
      />
    );
    // 显示部分一级菜单项
    let firstOptions = [
      firstAllOptions.NAME,
      firstAllOptions.LOCATION,
      firstAllOptions.SHARE,
      firstAllOptions.IFTTT,
      firstAllOptions.FIRMWARE_UPGRADE,
      firstAllOptions.MORE,
      firstAllOptions.HELP,
      firstAllOptions.LEGAL_INFO
    ];
    // 显示部分二级菜单项
    let secondOptions = [
      secondAllOptions.SECURITY,
      secondAllOptions.FEEDBACK,
      secondAllOptions.TIMEZONE,
      secondAllOptions.ADD_TO_DESKTOP
    ];
    // 一级title显示不全问题
    let commonSettingStyle = {
      itemStyle: {
        titleNumberOfLines: 2,
        unlimitedHeightEnable: true
      }
    };

    if (CameraConfig.isSupportBtGateWay(Device.model)) {
      secondOptions = [
        secondAllOptions.SECURITY,
        secondAllOptions.FEEDBACK,
        secondAllOptions.TIMEZONE,
        secondAllOptions.BTGATEWAY,
        secondAllOptions.TIMEZONE,
        secondAllOptions.ADD_TO_DESKTOP
      ];
    }
    let customOptions = this.mSupportCloud ?
      ((!Device.isOwner || !this.mSupportCloudCountry) // 不是设备主人or支持云存的国家 不让进云存储设置页面

          ? [item1, Device.isReadonlyShared ? null : item2, item3, item4, CameraConfig.shouldDisplayNewStorageManage(Device.model) ? null : item5,Device.isOwner ? item10 : null]
          : [item1, Device.isReadonlyShared ? null : item2, item3, item4, CameraConfig.shouldDisplayNewStorageManage(Device.model) ? null : item5, item6, item8, CameraConfig.isSupportVisitInfo(Device.model) ? item9 : null,Device.isOwner ? item10 : null]
      )
      :
      ([item1, item2, item4, CameraConfig.shouldDisplayNewStorageManage(Device.model) ? null : item5, Device.isOwner ? item10 : null]);
    return (
      <View style={styles.container}>

        {Settings({
          navigation: this.props.navigation,
          firstOptions,
          secondOptions,
          showDot: this.state.showDot,
          extraOptions: this.state.extraOptions,
          children: customOptions })}
          {/*<View style={styles.whiteblank}*/}

          {/*/>*/}

          {/*<CommonSetting*/}
          {/*  key={10}*/}
          {/*  navigation={this.props.navigation}*/}
          {/*  firstOptions={firstOptions}*/}
          {/*  secondOptions={secondOptions}*/}
          {/*  extraOptions={this.state.extraOptions}*/}
          {/*  showDot={this.state.showDot}*/}
          {/*  useNewType={true}*/}

          {/*/>*/}
          <View style={{ height: 20 }} />

          <MessageDialog
            visible={this.state.showAIBuyCloudVip}
            message={this.isCloudServer?LocalizedStrings['eu_c_cloudvip_need']:LocalizedStrings['c_cloudvip_need']}
            buttons={[
              { text: strings.cancel, callback: () => this.setState({ showAIBuyCloudVip: false }) },
              {
                text: LocalizedStrings['c_cloudvip_buy'],
                callback: () => this._buyCloudVip()
              }
            ]}
          />
        {this._renderPermissionDialog()}
      </View>
    );
  }

  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 麦克风
    // 
    let message = "";
    if (this.state.permissionRequestState == 0) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_microphone"]);
    }
    return (
      <MessageDialog
        title={LocalizedStrings["tips"]}
        message={message}
        messageStyle={{
          fontSize: 14,
      }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );
  }

  // onBack = () => {
  //   this.props.navigation.goBack();
  //   return true;
  // }
  componentDidMount() {
    super.componentDidMount();
    CameraConfig.lockToPortrait();// 切换回竖屏
    let title = LocalizedStrings["setting"];
    LogUtil.logOnAll("setting page title:" + title);
    // this.props.navigation.setParams({
    //   title: title,
    //   type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
    //   left: [
    //     {
    //       key: NavigationBar.ICON.BACK,
    //       onPress: () => { this.props.navigation.goBack(); }
    //     }
    //   ],
    //   titleStyle: {
    //     fontSize: 18,
    //     color: '#333333',
    //     fontWeight: 500
    //   }
    // });

    let showReddot = this.firmwareUpdate ? [firstAllOptions.FIRMWARE_UPGRADE] : [];
    // TODO: 拉数据
    this.setState({
      showDot: showReddot
    });

    
    this.setState({
      extraOptions: {
        showUpgrade: true,
        option: null,
        syncDevice: false,
        networkInfoConfig: 1,
        excludeRequiredOptions: [
          // SETTING_KEYS.first_options.PRODUCT_BAIKE,
          Device.model == VersionUtil.Model_chuangmi_051a01 ? SETTING_KEYS.first_options.BTGATEWAY : null
        ]
      }
    });
  }
  componentWillUnmount() {
    this.didFocusListener.remove();
    this.didBlurListener.remove();
    this.didResumeListener.remove();
    this.willPauseListener.remove();
    if (this.willAppearListener) {
      this.willAppearListener.remove();
    }
    if (this.willDisappearListener) {
      this.willDisappearListener.remove();
    }
    // if (Platform.OS === "android") {
    //   BackHandler.removeEventListener("hardwareBackPress", this.onBack);
    // }
  }
  _navigate(name, params = null) {
    this.props.navigation.navigate(name, params);
  }

  _navigateAISetting() {
    // this._navigate("AISetting", { showFace: this.showFace, showBabyCry: this.showBabyCry, showPet: this.showPet });
    this._navigate("AICameraSettingsV2", { showFace: this.showFace, showBabyCry: this.showBabyCry, showPet: this.showPet });
  }

  _showAlbum() {
    console.log("why!, _showAlbum");

    console.log("why!, use rn album");
    if (CameraConfig.shouldDisplayNewStorageManage(Device.model)) {
      this.props.navigation.navigate("AllStorage", { initPageIndex: 1, vip: this.isVip });
    } else {
      this._navigate('Album');
    }
    TrackUtil.reportClickEvent('Camera_Album_ClickNum');
  }

  _showAI() {
    let isInternational = CameraConfig.getInternationalServerStatus();
    // AI设置中定义了，而且不需要vip状态或者已经是vip状态了的才展示
    this.showFace = this.isVip || VersionUtil.isAiCameraModel(Device.model);
    this.showBabyCry = false;
    this.showPet = false;
    this.showFaceManager = !isInternational;

    if (VersionUtil.isAiCameraModel(Device.model)) {
      this.showPet = true;
    }
    if (isInternational) { // 海外不支持人脸
      this.showFace = false;
    }
    if (this.isVip || CameraConfig.isSupportNonVipBabyCry(Device.model)) {
      this.showBabyCry = true;
    }
    // 026c02有特定的逻辑：如果开启了云存，就认为具有本地宝宝哭声识别能力    如果之前打开了宝宝哭声开关，也认为有本地宝宝哭声识别能力，其他情况一律认为不支持宝宝哭声能力
    let sBabyCry = CameraConfig.babyCrySpecialModel(Device.model);
    if (sBabyCry != -1) {
      this.showBabyCry = sBabyCry;
    }
  }
}
