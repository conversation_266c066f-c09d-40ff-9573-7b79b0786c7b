'use strict';

import { DarkMode } from 'miot/Device';
import { Styles } from 'miot/resources';
import { StyleSheet ,Dimensions,StatusBar, Platform} from 'react-native';
import { Host } from 'miot';
const kWindowHeight = Math.max(Dimensions.get("screen").height, Dimensions.get("screen").width);
const navigationBarHeightFat = kWindowHeight > 500 ? 65 : 50; // 导航栏高度
let statusBarHeight = 20;// ipone 没有这个需要自定义
if (Platform.OS == 'android') {
  statusBarHeight = StatusBar.currentHeight || 0;
}
import Util from "../util2/Util";
let isDark = Util.isDark();

let titleBarHeight = navigationBarHeightFat + statusBarHeight;

let listener = (value) => {
  let darkmode = value.colorScheme;//不管了 重新创建一遍styleObj 重新assign;
  // 暗黑模式发生了改变
  
  Object.assign(styles, getSettingCommonStyle()); // 暗黑模式发生改变，强制刷新一次
};

DarkMode.addChangeListener(listener);

export function removeDarkListener() {
  DarkMode.removeChangeListener(listener);
}


let styleObj = getSettingCommonStyle();// 调用一次就会重复刷新一次。

function getSettingCommonStyle() {//调用一次就会重新刷新一次
  return {
    container: {
      backgroundColor: Util.isDark()? "#xm000000" : 'white',
      flex: 1
    },
    featureSetting: {
      // marginTop: 8,
      backgroundColor: Util.isDark()? "#xm000000" : 'white',
      // marginHorizontal: 24
    },
    blank: {
      height: 8,
      backgroundColor: Styles.common.backgroundColor
    },
    whiteblank: {
      height: 0.5,
      marginHorizontal: 24,
      backgroundColor: "#e5e5e5",
      marginBottom: 30,
      marginTop: 20
  
    },
    commonPaddingLeft: {
      // backgroundColor: '#fff',
      paddingLeft: Styles.common.padding,
      paddingBottom: 18,
      color:'#999',
      fontSize:12,
      marginRight:24
    },
    blankWidthLine: {
      height: 8,
      backgroundColor: Styles.common.backgroundColor,
      borderTopColor: Styles.common.hairlineColor,
      borderTopWidth: StyleSheet.hairlineWidth,
      borderBottomColor: Styles.common.hairlineColor,
      borderBottomWidth: StyleSheet.hairlineWidth
    },
    titleContainer: {
      height: 32,
      backgroundColor: Util.isDark()? "#xm000000" : 'white',
      justifyContent: 'center',
      paddingLeft: Styles.common.padding
    },
    title: {
      fontSize: 12,
      color: '#8c93b0',
      lineHeight: 14
    },
    // 这里新加标题栏样式
    titleBarStyle: {
      top: 0,
      width: "100%",
      backgroundColor: Util.isDark()? "#xm000000" : 'white',
      zIndex: 1,
      display: 'flex',
      justifyContent: 'space-between',
      flexDirection: "row",
      marginTop: statusBarHeight,
      height: navigationBarHeightFat
    },
    imageBtnStyle: {
      width: 40,
      height: 40,
      position: "absolute",
      marginLeft: 9,
      marginTop: statusBarHeight / 2
    },
    textContainerStyle: {
      justifyContent: 'center',
      textAlign: 'center'
    },
    titleTextStyle: {
      fontSize: 18,
      textAlignVertical: 'center',
      textAlign: 'center',
      color: 'black',
      fontWeight: '500'
    },
    desc_title:{
      color: "#000000",
      fontSize: 18,
      fontWeight: "bold",
      paddingHorizontal: 24,
      fontFamily: "MI Lan Pro"
    },
    desc_subtitle:{
      color: "rgba(0, 0, 0, 0.6)",
      fontSize: 14,
      marginTop: 10,
      lineHeight: 21,
      fontFamily: "MI Lan Pro",
      paddingHorizontal: 24,
      fontWeight: "300"
    },
  };
}

export const styles = StyleSheet.create(styleObj);


// 刚刚进来的时候强制刷新一次darkmode:

listener(DarkMode.getColorScheme());// 进来的时候就强制刷新一遍
