import { StyleSheet, ScrollView, Image, Text, View, Dimensions, StatusBar, TouchableOpacity } from 'react-native';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles as settingStyles } from './SettingStyles';
import { Device, Host } from "miot";
import StorageKeys from '../StorageKeys';
import StackNavigationInstance from '../StackNavigationInstance';
import CameraConfig from "../util/CameraConfig";
import VersionUtil from '../util/VersionUtil';
import Service from 'miot/Service';


export default class NoSdcardPage extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      showBuyButton: false
    };
    this.isCloudServer = CameraConfig.getIsCloudServer();
    this.supportLargeSdcard = CameraConfig.supportLargeSdcard(Device.model);
    this.disableCloudSetting = !VersionUtil.isFirmwareSupportCloud(Device.model);
  }

  componentDidMount() {
    this.getLocalStatus();

  }

  async getLocalStatus() {
    if (this.disableCloudSetting) { // 跳过去吧 009 不支持云存的设备
      return;
    }
    try {
      let isVip = await StorageKeys.IS_VIP_STATUS;
      let isSupportCloud = CameraConfig.isSupportCloud();
      let deviceSupportCloud = CameraConfig.isDeviceSupportCloud();
      if (!deviceSupportCloud || isVip || !Device.isOwner) {
        this.setState({ showBuyButton: false });
      } else {
        this.setState({ showBuyButton: true });
      }
    } catch (exception) {

    }
  }

  render() {
    console.log('Device.model ', Device.model)
    let showBuyText = this.state.showBuyButton;
    let width = Dimensions.get("window").width - 34;
    let videoTimeList;
    let videoDescribe = ['sds_buy_tips', 'sds_buy_tips1', 'sds_buy_tips2', 'sds_buy_tips3']
    if (this.supportLargeSdcard) { // 真是乱   c01a01 c01a02支持大卡，但是分辨率还是保持2k   039 049支持大分辨率
      videoTimeList = [
        ['sds_grid00', CameraConfig.isSupport2K(Device.model) ? "camera_quality_fhd2k" : (CameraConfig.isSupport25K(Device.model) ? 'sds_grid03' : 'sds_grid02')],
        ['sds_grid10', 'sds_grid12'],
        ['sds_grid20', 'sds_grid22'],
        ['sds_grid30', 'sds_grid32'],
        ['sds_grid40', 'sds_grid42'],
        ['sds_grid50', 'sds_grid52']
      ];
      if (CameraConfig.isSupportSuperDefinition(Device.model)) {
        videoTimeList = [
          ['sds_grid00', 'sds_grid04'],
          ['sds_grid10', 'sds_grid23'],
          ['sds_grid20', 'sds_grid22'],
          ['sds_grid30', 'sds_grid21'],
          ['sds_grid40', 'sds_grid24'],
          ['sds_grid50', 'sds_grid25']
        ];
      }
      videoDescribe = ['sds_buy_tips', 'sds_buy_tips1', 'sds_buy_tips2', CameraConfig.supportSDCardV2(Device.model) ? null : 'sds_buy_tips21', 'sds_buy_tips3'];
    } else {
      videoTimeList = [
        ['sds_grid00', CameraConfig.isSupport2K(Device.model) ? "camera_quality_fhd2k" : (CameraConfig.isSupport25K(Device.model) ? 'sds_grid03' : 'sds_grid02')],
        ['sds_grid10', 'sds_grid12'],
        ['sds_grid20', 'sds_grid22']
      ];
    }
    videoTimeList = [
      ['sds_grid00', 'super_resolution_4k'],
      ['sds_grid100', 'sds_grid101'],
      ['sds_grid10', 'sds_grid13'],
      ['sds_grid20', 'sds_grid23'],
      ['sds_grid30', 'sds_grid22'],
      ['sds_grid40', 'sds_grid43'],
      ['sds_grid50', 'sds_grid53']
    ];

    let kanjia_jieshao = [
      LocalizedStrings['sds_buy_tips_v2_1'], 
      LocalizedStrings['sds_buy_tips4']
    ];
    let goumai_jianyi = [
      LocalizedStrings['sds_buy_tips_v2_2'].replace("%1s", "8").replace("%2s", "256"), 
      LocalizedStrings['sds_buy_tips1'], 
      LocalizedStrings['sds_buy_tips_v2_3']
    ];
    let tdWidth = Number(100 / videoTimeList[0].length) + "%"
    console.log('tdwidth', typeof tdWidth, tdWidth)
    return (
      <View style={{ width: "100%", flex: 1, position: "relative", backgroundColor: '#ffffff' }}>
        <ScrollView 
          style={{ width: "100%", flex: 1, position: "relative", backgroundColor: '#ffffff' }}
          contentContainerStyle={{ alignItems: "center", justifyContent: "space-around" }}
          showsVerticalScrollIndicator={false}
          key={10000} >
          {/* <Separator /> */}
          {/*<View style={styles.noTopBack}>*/}
          {/*  <Image style={styles.noTopImage}*/}
          {/*    source={require('../../Resources/Images/car/pic_no_sdcard_tip.webp')}*/}
          {/*  />*/}
          {/*  /!* <Text style={styles.noText}>{LocalizedStrings['sds_nosd']}</Text> *!/*/}
          {/*  <View style={{ height: 20 }}></View>*/}
          {/*</View>*/}
          <View style={{ height: 20 }}></View>
          <View style={styles.noDetailBack}>
            <Text style={{ fontSize: 19, fontWeight: "bold" }}>{LocalizedStrings['sds_buy_tips_v2_title1']}</Text>
            {
              kanjia_jieshao.map((item, i) => {
                if (!item) {
                  return null;
                }
                return (
                  <Text style={styles.noTextLeft}
                    key={11000 + i}
                  >{ item }</Text>

                );
              })
            }
            <Text style={{ fontSize: 19, fontWeight: "bold", marginTop: 20 }}>{LocalizedStrings['sds_buy_tips_v2_title2']}</Text>
            {
              goumai_jianyi.map((item, i) => {
                if (!item) {
                  return null;
                }
                return (
                  <Text style={styles.noTextLeft}
                    key={11000 + i}
                  >{ item }</Text>

                );
              })
            }
            <View style={{ height: 8 }}></View>
            <View style={styles.noGridContainer}>
              {
                videoTimeList.map((items, j) => {
                  let radius = 20;
                  return (
                    <View style={{ width: '100%', flexDirection: 'row' }}
                      key={j + 1000}
                    >
                      {
                        items.map((item, i) => {
                          return (
                            <View 
                              style={{
                                width: tdWidth,
                                height: 40,
                                borderWidth: 0.3,
                                borderColor: 'rgba(0,0,0,0.1)',
                                alignItems: 'center',
                                borderTopLeftRadius: i == 0 && j == 0 ? radius : 0,
                                borderTopRightRadius: i == 1 && j == 0 ? radius : 0,
                                borderBottomLeftRadius: i == 0 && j == videoTimeList.length - 1 ? radius : 0,
                                borderBottomRightRadius: i == 1 && j == videoTimeList.length - 1 ? radius : 0,
                                justifyContent: 'center'
                              }}
                              key={i + 100}
                            >
                              <Text style={styles.noText}> {LocalizedStrings[item]} </Text>
                            </View>
                          );
                        })
                      }
                    </View>
                  );
                })
              }
            </View>
          </View>
        </ScrollView>
        {
          showBuyText ?
            <View style={{ flexDirection: "column" }}>
              <Text style={{ alignSelf: "center", paddingBottom: 5, paddingTop: 10, color: "rgba(0, 0, 0, 0.4)" }}> {LocalizedStrings["buy_cloud_video_tips"]} </Text>
              <TouchableOpacity style={{ marginBottom: 10, marginTop: 20, minHeight: 46, position: "relative", bottom: 10, backgroundColor: "#32BAC0", borderRadius: 30, width: width, marginHorizontal: 20, paddingVertical: 10, display: "flex", alignItems: "center", justifyContent: "center" }}
                onPress={() => {
                  if (this.props.onButtonClick != null) {
                    this.props.onButtonClick();
                    return;
                  }

                  // let mSupportCloudCountry = CameraConfig.isSupportCloud();
                  // let isInternationalServer = CameraConfig.getInternationalServerStatus();
                  // if (isInternationalServer && mSupportCloudCountry) {// 海外云存 跳购买页
                  Service.miotcamera.showCloudStorage(true, true, Device.deviceID, "", true, { channel: "sdmgt_button" });// 直接跳过去了
                  // } else {
                  //   StackNavigationInstance.jumpToStackNavigationPage("CloudIntroPage");
                  // }
                }}
              >
                <Text style={{ color: "#ffffff", fontSize: 15, textAlign: "center" }}>
                  {this.isCloudServer ? LocalizedStrings["eu_buy_cloud_video_tips"] : LocalizedStrings["bug_cloud"]}
                </Text>
              </TouchableOpacity>
            </View> : null
        }
      </View>
    );
  }
}

const storageStyles = StyleSheet.create({
  stateContainer: {
    width: '100%',
    backgroundColor: 'white',
    alignItems: 'center'
  },
  stageBack: {
    width: 217,
    height: 217,
    display: "flex",
    alignItems: "center"
  },
  stateImage: {
    width: '100%',
    height: '100%'
  },
  stateCover: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    display: "flex",
    alignItems: 'center'
  },
  stateCoverTitle: {
    marginTop: 66,
    fontSize: 22,
    color: 'white',
    textAlign: "center"
  },
  stateCoverSeprate: {
    position: "absolute",
    top: 124,
    backgroundColor: 'rgba(255,255,255,0.5)',
    height: 1,
    width: '80%'
  },
  stateCoverDetail: {
    position: "absolute",
    bottom: 56,
    fontSize: 15,
    color: 'white'
  },
  totalText: {
    marginTop: 20,
    marginBottom: 30,
    fontSize: 16,
    color: 'rgba(0,0,0,0.6)'
  },
  tipsBack: {
    paddingTop: 3,
    paddingLeft: 24,
    paddingRight: 24
  },
  tips: {
    marginTop: 5,
    fontSize: 14,
    color: 'rgba(0,0,0,0.5)'
  },
  noTopBack: {
    backgroundColor: 'white',
    width: '100%',
    alignItems: 'center'
  },
  noTopImage: {
    resizeMode: 'stretch',
    marginTop: 15,
    width: "90%",
    height: 198
  },
  noDetailBack: {
    paddingTop: 3,
    paddingLeft: 24,
    paddingRight: 24,
    paddingBottom: 13
  },
  noGridContainer: {
    marginTop: 15,
    borderWidth: 0.3,
    borderColor: 'rgba(0,0,0,0.1)',
    borderRadius: 20
  },
  noText: {
    marginTop: 5,
    fontSize: 14,
    color: 'rgba(0,0,0,0.5)',
    textAlign: "center",
    paddingHorizontal: 10
  },
  noTextLeft: {
    marginTop: 5,
    fontSize: 14,
    color: 'rgba(0,0,0,0.5)'
  }
});

const styles = { ...settingStyles, ...storageStyles };