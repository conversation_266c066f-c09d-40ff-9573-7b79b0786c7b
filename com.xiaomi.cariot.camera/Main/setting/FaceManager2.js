'use strict';

import React from 'react';
import { ScrollView, Button, BackHandler, View, Text, Image, StyleSheet, FlatList, Dimensions, TouchableOpacity, ActivityIndicator, Platform ,PermissionsAndroid} from 'react-native';
import { Device, Service, DarkMode, Host, System } from 'miot';
import { MessageDialog } from "mhui-rn";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import MultiSelectableNavigationBar from '../ui/MultiSelectableNavigationBar';
import AlbumHelper from '../util/AlbumHelper';
import Toast from '../components/Toast';
import API from '../API';
import { getOrientation } from 'react-native-orientation';
import { ChoiceDialog,AbstractDialog} from 'miot/ui/Dialog';
import Util from "../util2/Util";
import InputDlgEx from '../widget/InputDlgEx';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import LogUtil from '../util/LogUtil';
import NavigationBar from "miot/ui/NavigationBar";
import ScrollableTabView, { DefaultTabBar } from 'react-native-scrollable-tab-view';
import { ListItem, ListItemWithSlider, ListItemWithSwitch } from 'miot/ui/ListItem';
import TrackUtil from '../util/TrackUtil';
import StorageKeys from '../StorageKeys';
import CameraConfig from '../util/CameraConfig';

const TAG = 'FaceManager2';
const kIsCN = Util.isLanguageCN();

const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width);
export default class FaceManager2 extends React.Component {
  state = {
    isSelectMode: false,
    index: 0,
    isEmpty: true,
    isCurrentDayEmpty: false,
    calendarDays: [],
    dialogVisible: false,
    albumFiles: [],
    showLoading: true,
    coverFaceInfosList: [],
    unmarkFacesImgList: [],
    selectedFiguresList: [],
    isRefreshing: false,
    commentDlg: false,
    faceListLength: 0,
    faceFigureInfo: undefined
  };
  constructor(props) {
    super(props);
    this.isDelete = false;
    this.dateTime = new Date();
    this.figureInfos = [];
    this.coverFaceInfosList = [];
    this.figureIdMap = new Map();
    this.isDark = DarkMode.getColorScheme() == "dark";
    this.coverFaceImags = {};
    this.faceImags = {};
    this.is051 = CameraConfig.Model_chuangmi_051a01 == Device.model;
  }




  dimensionListener = (args) => {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    if (Platform.OS === "ios") {
      console.log('why!, setDimensionsIos000: ', args);
      console.log('why!, Dimensions', Dimensions.get('window'));
      if (args && args.screen && args.window) {
        if (args.screen.width !== args.window.width || args.screen.height !== args.window.height) {
          setTimeout(() => Dimensions.set({ 'window': args.screen }), 10);
          console.log('纠正========');
        }
      }
    }
  }
  componentDidMount() {
    Dimensions.addEventListener('change', this.dimensionListener);
    this.props.navigation.setParams({
      title: LocalizedStrings["as_facial_recognized"],//"人脸识别"
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack() }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#000000',
        fontWeight: 500,
        fontFamily: "MILanPro_MEDIUM--GB1-4"
      }
    });
    self.windowWidth = Dimensions.get("window").width;
    self.windowHeight = Dimensions.get("window").height;
    if (self.windowHeight < self.windowWidth) {
      let sw = self.windowWidth;
      self.windowWidth = self.windowHeight;
      self.windowHeight = sw;
    }
    this.setState({ index: 1 });

    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        // this.isPageForeGround = true;
        // this._onGetData();
        this.isPageForeGround = true;
        //通过判断是否为第一次进入来控制引导图是否显示
        StorageKeys.IS_AI_FACE_OPEN.
          then((result) => {
            this.setState({
              isAIFrame: result
            });
            console.log('rettt', result)
            if (result||this.is051) {
              this.setState({ showNoOpenFaceManager: false });
              this._onGetData()
            }
            else {
              this.setState({
                showNoOpenFaceManager: true,
                isSelectMode: false
              });
            }
          })
          .catch((err) => {
            console.log('errr', err)
            this.setState({ showNoOpenFaceManager: true})
          });
      }
    );
    this.didBlurListener = this.props.navigation.addListener(
      'didBlur',
      () => {
        this.isPageForeGround = false;
      }
    );
  }

  // android返回键处理
  onBackHandler = () => {
    if (!this.isPageForeGround) {
      return false;
    }
    if (this.state.isSelectMode) {
      this.onSelectAllChanged(false);
      this.setNavigation(false, false, false);
      this.setState({ isSelectMode: false });
      return true;
    } else {
      return false;
    }
  }

  //获取后端数据
  _onGetData() {
    this.setState({
      index: 1,
    });
    //获取已备注的人脸数据
    Util.getAllFigure(false).then((res) => {
      console.log(res, '已备注的人脸数据')
      this.setState({
        mAllFigureInf: res,
        figureInfos: [...res, { faceUrl: false, figureName: LocalizedStrings["add"] }],
        showMarkView: true,
        figureInfosLength: res.length,
        name: res.name
      });
      this.state.figureInfos.forEach((item) => {
        // console.log("====================", this.coverFaceImags[item.coverFaceId]);
        if (!this.coverFaceImags[item.coverFaceId]) {
          Util.getFaceImgUrl(item.coverFaceId).then((res) => {
            this.coverFaceImags[item.coverFaceId] = res;
            this.forceUpdate();
          }).catch((err) => {
            console.log("Util.getFaceImgUrl err=", JSON.stringify(err));
          });
        }
      });
      console.log(this.state.figureInfos, 'this.figureInfos')
    })
      .catch((err) => {
        console.log('获取已备注的人脸err', err);
        LogUtil.logOnAll("FaceManager2", "getAllFigure" + err);
      });

    //获取最近出现的人脸数据
    Util.getFacesCluster().then((res) => {
      //模仿原生代码的写法，看不懂为啥这么写
      this.setState({
        showLoading: false,
      })
      let faceInfos = res;
      let faceLabelMap = new Map();
      this.figureIdMap.clear();
      for (let i = 0; i < faceInfos?.length; i++) {
        let faceInfo = faceInfos[i];
        if (faceLabelMap.has(faceInfo.faceLable)) {
          let faceLable = faceLabelMap.get(faceInfo.faceLable);
          if (faceLable.figureName == null && faceInfo.figureName != null) {
            faceLable.figureName = faceInfo.figureName;
            faceLable.figureId = faceInfo.figureId;
          }
          if (faceInfo.figureId != null && faceInfo.figureId !== faceLable.figureId) {
            if (this.figureIdMap.has(faceInfo.figureId)) {
              let figure = this.figureIdMap.get(faceInfo.figureId);
              figure.faceIds.push(faceInfo.faceId);
            } else {
              faceInfo.faceIds.push(faceInfo.faceId);
              this.figureIdMap.set(faceInfo.figureId, faceInfo);
            }
          } else {
            faceLable.faceIds.push(faceInfo.faceId);
          }
        } else {
          faceInfo.faceIds.push(faceInfo.faceId);
          faceLabelMap.set(faceInfo.faceLable, faceInfo);
        }
      }

      console.log("faceLabelMap", JSON.stringify(faceLabelMap));

      let dupList = [];
      faceLabelMap.forEach((value, key) => {
        let faceLable = key;
        let faceInfo = value;
        let figureId = faceInfo.figureId;
        if (figureId != null) {
          if (this.figureIdMap.has(figureId)) {
            let faceInfo = this.figureIdMap.get(figureId);
            faceInfo.faceIds.push(...faceInfo.faceIds);
          } else {
            this.figureIdMap.set(figureId, faceInfo);
          }
        } else {
          dupList.push(faceInfo);
          this._downloadFaceImages(faceInfo);
        }
      });
      this.figureIdMap.forEach((value, key) => {
        dupList.push(value);
        this._downloadFaceImages(value);
      });
      dupList.sort((left, right) => { //逆序
        if (left.updateTime < right.updateTime) {
          return 1;
        } else if (left.updateTime > right.updateTime) {
          return -1;
        }
        return 0;
      });
      // console.log(JSON.stringify(dupList));
      this.setState({
        recentFacesImgList: dupList,
        showRecentFacesListView: true,
        isRefreshing: false,
        recentFaces:res.length
      });

      // console.log('获取最近出现的人脸', res)
    })
      .catch((err) => {
        console.log('获取最近出现的人脸err', err)
        LogUtil.logOnAll("FaceManager2", "getFacesCluster" + err);
      })

  }

  _downloadFaceImages(faceInfo) {
    // console.log("faceInfo=====", JSON.stringify(faceInfo));
    if (!this.faceImags[faceInfo.faceId]) {
      // console.log("faceInfo=====getFaceImgUrl");
      Util.getFaceImgUrl(faceInfo.faceId.toString()).then((res) => {
        // console.log("getFaceImgUrl res =====", JSON.stringify(res));
        this.faceImags[faceInfo.faceId] = res;
        this.forceUpdate();
      }).catch((err) => {
        console.log("_downloadFaceImages err=", JSON.stringify(err));
      });
    }
  }

  //在组件卸载及销毁之前掉用
  componentWillUnmount() {
    Dimensions.removeEventListener('change', this.dimensionListener);
    this.didFocusListener.remove();
    this.didBlurListener.remove();
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }
  //全体渲染
  render() {
    //首次进入人脸时的引导页
    let FaceViewCloseView = <View style={styles.container}>
    <View style={{ flexDirection: 'column', alignItems: "center", }}>
      <Image style={{ width: 174, height: 174, marginTop: 20 }} source={require('../../Resources/Images/icon_face_manager_first_larg.png')}>

      </Image>
      <View>
        <Text style={{ fontSize: 12, textAlign: 'center', marginHorizontal: 40, marginTop: 20 }}>
          {LocalizedStrings["face_manager_first_tips"]}
        </Text>
      </View>

    </View>
    <View style={{
      height: 0.5,
      marginHorizontal: 24,
      backgroundColor: "dark" == DarkMode.getColorScheme() ? "xm#ffffff15" : "xm#e5e5e5",
      marginBottom: 10,
      marginTop: 20,
    }}>

    </View>

    <View >
      <View style={{ flexDirection: 'row', alignItems: "center", marginLeft: 20, marginVertical: 10 }} >
        <Image
          source={require('../../Resources/Images/icon_face_manager_first.png')}
          style={{ width: 25, height: 25, tintColor: "dark" == DarkMode.getColorScheme() ?'#DDDDDDFF':"" }}
        >

        </Image>
        <View style={{ width: kWindowWidth - 65 }}>
          <Text style={{ fontSize: 15, textAlign: 'left', marginLeft: 10 }}>
            {LocalizedStrings["face_manager_first_tips1"]}
          </Text>
        </View>


      </View>
      <View style={{ flexDirection: 'row', alignItems: "center", marginLeft: 20, marginVertical: 10 }} >
        <Image
          source={require('../../Resources/Images/icon_face_manager_first_mark.png')}
          style={{ width: 25, height: 25, tintColor: "dark" == DarkMode.getColorScheme() ?'#DDDDDDFF':"" }}
        >

        </Image>
        <View style={{ width: kWindowWidth - 65 }}>
          <Text style={{ fontSize: 15, textAlign: 'left', marginLeft: 10 }}>
            {LocalizedStrings["face_manager_first_tips2"]}
          </Text>
        </View>
      </View>
      <View style={{ flexDirection: 'row', alignItems: "center", marginLeft: 20, marginVertical: 10 }} >
        <Image
          source={require('../../Resources/Images/icon_face_manager_first_share.png')}
          style={{ width: 25, height: 25, tintColor: "dark" == DarkMode.getColorScheme() ?'#DDDDDDFF':"" }}
        >

        </Image>
        <View style={{ width: kWindowWidth - 65 }}>
          <Text style={{ fontSize: 15, textAlign: 'left', marginLeft: 10 }}>
            {LocalizedStrings["face_manager_first_tips3"]}
          </Text>
        </View>
      </View>
      <View style={{
        height: 0.5,
        marginHorizontal: 24,
        backgroundColor: "dark" == DarkMode.getColorScheme() ? "xm#ffffff15" : "xm#e5e5e5",
        marginBottom: 10,
        marginTop: 20,
      }}>

      </View>
      <View style={{ marginHorizontal: 24 }}>
        <Text style={{ fontSize: 11, color: '#999999', lineHeight: 18 }}>
          {LocalizedStrings["low_power_agreement"]}
        </Text>
      </View>




    </View>
    <View style={{ position: 'absolute', height: 48, width: '100%', flexDirection: 'row', bottom: 20, justifyContent: 'center', alignItems: 'center', }}
    >
      <TouchableOpacity
        style={{ width: 306, height: 48, backgroundColor: '#32BAC0', alignItems: 'center', borderRadius: 10 }}
        onPress={() => {
          this.setState({ showNoOpenFaceManager: false }, () => {
            this._onGetData()
          });
          StorageKeys.IS_AI_FACE_OPEN = true;
        }}>
        <Text style={{ fontSize: 16, textAlign: 'center', color: 'white', lineHeight: 48 }}>
          {LocalizedStrings["face_manager_to_user"]}
        </Text>
      </TouchableOpacity>



    </View>
  </View>
  if (this.state.showNoOpenFaceManager&&!this.is051) {
    return (FaceViewCloseView)
  }else{
    return (
      <View style={styles.container}>
        {this._renderFaceView()}
        {this._renderLoadingView()}
        {this._renderChoiceAddMarkedFaceWayDialog()}
        {this._renderTipsDialogView()}
        { this._renderPermissionDialog()}
        {this.state.commentDlg ? this._renderCommentDlg() : null}
      </View>
    );
  }
  }

  _renderFaceView() {
    if (this.state.showMarkView) {
      return (
        <View style={{ width: '100%', flex: 1 }}>
          {/* 已备注人脸列表 */}
          {/* <ListItem
            title={LocalizedStrings["face_marked"].replace("%d", this.state.figureInfosLength)}
            // value={this.state.figureInfos.length - 1 == 0 ? '' : LocalizedStrings["string_face_manager"]}
            showSeparator={false}
            dotStyle={{ marginTop: 8 }}
            titleStyle={{ fontSize: 12, paddingTop: 1, marginBottom: 1, color: '#8C93B0' }}
            valueStyle={{ fontSize: 12, color: '#666666'}}
            allowFontScaling={false}
            unlimitedHeightEnable={true}
            titleNumberOfLines={0}
            valueNumberOfLines={0}
            hideArrow={this.state.figureInfos.length - 1 == 0}
            onPress={() => {
              return this.props.navigation.navigate('FaceManagerNumber') //跳到人脸管理数量        
            }}
          /> */}
          <View
            style={{ display: 'flex', flexDirection: "row", justifyContent: 'space-between', alignItems: "center", marginHorizontal: 20 }}
          >
            <View style={{ display: "flex", flexDirection: 'row', justifyContent: 'flex-start', alignItems: "center" }}>
              <View style={{ display: "flex", flexDirection: 'column', justifyContent: 'flex-start' }}>
                <Text style={{ fontSize: 12, paddingTop: 1, marginBottom: 1, color: '#8C93B0' }}>
                  {LocalizedStrings["face_marked"].replace("%d", this.state.figureInfosLength)}
                </Text>
              </View>
            </View>
            <TouchableOpacity
              style={{ flexDirection: 'row', justifyContent: 'flex-end', alignItems: "center" }}
              onPress={() => { return this.props.navigation.navigate('FaceManagerNumber') }}
            >
              <Text style={{ fontSize: 12, color: '#666666' }}>
                {this.state.figureInfos.length - 1 == 0 ? '' : LocalizedStrings["string_face_manager"]}
              </Text>
              <Image
                style={{ width: 20, height: 40 }}
                source={require('../../Resources/Images/button_next_nor.png')}
              />
            </TouchableOpacity>
          </View>
          {this._renderMarkFacesListView()}
          {/* 空白组件 */}
          <View style={styles.whiteblank}>
          </View>


          {/* 最近出现的人脸列表 */}
          {/* <ListItem
            title={LocalizedStrings["face_shown_recently"]}
            showSeparator={false}
            dotStyle={{ marginTop: 1 }}
            titleStyle={{ fontSize: 12, marginBottom: 10, color: '#8C93B0' }}
            valueStyle={{ fontSize: 12, color: '#666666' }}
            allowFontScaling={false}
            unlimitedHeightEnable={true}
            titleNumberOfLines={0}
            valueNumberOfLines={0}
            hideArrow={true}
          /> */}
          <View style={{ display: 'flex', flexDirection: "row", justifyContent: 'space-between', alignItems: "center", marginHorizontal: 20,marginBottom:30 }}>
            <View style={{ display: "flex", flexDirection: 'row', justifyContent: 'flex-start', alignItems: "center" }}>
              <View style={{ display: "flex", flexDirection: 'column', justifyContent: 'flex-start' }}>
                <Text style={{ fontSize: 12, paddingTop: 1, marginBottom: 1, color: '#8C93B0' }}>
                {LocalizedStrings["face_shown_recently"]}
                </Text>
              </View>
            </View>
          </View>
          {this._renderRecentFacesListView()}
        </View>
      )
    }
  }
  // 已备注的人脸数据渲染列表
  _renderMarkFacesListView() {
    if (this.state.showMarkView) {
      // console.log(this.state.figureInfos.length,'this.state.figureInfos.length')
      return (
        // marginHorizontal相同于同时设置marginLeft和marginRight。
        <View style={{ marginHorizontal: 16 }}>
          <FlatList
            horizontal={true}  //开启水平布局模式
            data={this.state.figureInfos}
            renderItem={({ item, index }) => this._renderMarkFacesView(item, index)}
            keyExtractor={(item, index) => index}
            showsHorizontalScrollIndicator={false} //关闭滚动条
          />
        </View>
      )
    }
  }
  // 已备注的人脸数据渲染列表源头
  _renderMarkFacesView(item, index) {
    if (this.state.showMarkView) {
      let path = null
      path = this.coverFaceImags[item.coverFaceId];
      let containerWidth = Dimensions.get("window").width / 4
      //这里弄个列表最后一个是添加 
      return (
        <View style={{ marginTop: 15 }}>
          <TouchableOpacity
            style={{ width: containerWidth, paddingBottom: 5, alignItems: "center", justifyContent: "center", marginBottom: 5 }}
            onPress={() => {
              //跳转已备注人脸的事件页面
              this._onPressFigureInfo(item, index)
            }}
          >
            { path ?
              <Image style={{ width: 50, height: 50, borderRadius: 25 }}
                source={path ? path : require('../../Resources/Images/home_icon_add3_pres.png')}
              /> :
              <View style={{ width: 50, height: 50, borderRadius: 180, backgroundColor: this.isDark ? '#EEEEEE' : '#f5f5f5', alignItems: "center", justifyContent: "center" }}>
                <Image style={{ width: 30, height: 30, borderRadius: 180, backgroundColor: this.isDark ? '#EEEEEE' : '#f5f5f5' }}
                  source={require('../../Resources/Images/home_icon_add2_pres.png')}/>
              </View>
            }
            <Text style={{ fontSize: 12, color: '#000000', marginTop: 10 }}>
              {item.name ? item.name : LocalizedStrings["add"]}
            </Text>
          </TouchableOpacity>
        </View>
      )
    }
  }
  //点击已备注人脸头像触发的事件                                               
  _onPressFigureInfo(item, index) {
    console.log(index, 'index')
    if (!this.state.isSelectMode) {
      console.log(this.state.isSelectMode, this.state.figureInfos.length, 'this.state.figureInfos.lengththis.state.figureInfos.length')
      if (index == this.state.figureInfos.length - 1) {
        if (this.state.figureInfosLength < 10) {
          this.setState({
            addMarkedFaceDialogVisible: true
          })
        } else {
          Toast.show(LocalizedStrings["figure_max_tips"])    // 人物上限
        }
      }
      else {
        let faceInfo = null;
        if (this.figureIdMap.has(item.figureId)) {
          faceInfo = this.figureIdMap.get(item.figureId);
        } else {
          faceInfo = item;
        }
        faceInfo.faceUrl = this.coverFaceImags[item.coverFaceId];
        this.props.navigation.navigate('FaceEvents', { figureInfo: faceInfo }) //跳转已备注人脸的事件页面 并传参
      }
    }
    else {
      if (index == this.state.figureInfos.length - 1) {
        this.onSelectAllChanged(false);// 将选择所有重置
        this.setState({
          isSelectMode: false,
          addMarkedFaceDialogVisible: true
        })
        return
      }
      let figureInfos = this.state.figureInfos[index];
      figureInfos.isSelected = !figureInfos.isSelected;
      let selectedCount = 0;
      for (let file of this.state.figureInfos) {
        if (file.isSelected) {
          selectedCount++;
        }
      }
      // 在这里重新设置标题栏 
      if (selectedCount == 0) {
        this.onSelectAllChanged(false);
      } else if (selectedCount == this.state.figureInfos.length) {
        this.onSelectAllChanged(true);
      } else {
        this.setState({ figureInfos: this.state.figureInfos });// 刷新页面 状态不要保留在ui控件里
      }
    }
  }
  //最近出现的人脸数据渲染列表
  _renderRecentFacesListView() {
    if (this.state.showRecentFacesListView && this.state.recentFaces != 0) {
      return (
        <FlatList
          style={{ marginLeft: 5, flex: 1 }}
          data={this.state.recentFacesImgList}
          renderItem={({ item, index }) => this._renderRecentFacesView(item, index)}
          numColumns={1}
          keyExtractor={(item, index) => index}
          //下拉刷新
          refreshing={this.state.isRefreshing}
          onRefresh={() => {
            this.setState({
              isRefreshing: true
            })
            this._onGetData();
          }}
          // 上拉加载更多数据
          // onEndReachedThreshold={.1}
          // onEndReached={() => {
          //   this._onGetData()
          // }}
        // ListFooterComponent={
        //   <Text style={{ textAlign: "center", marginBottom: 10 }}> {LocalizedStrings["to_end"]}</Text>} //没有更多了
        />
      )
    }
    if (this.state.recentFaces == 0) {
      return(
        <View style={{ display: "flex", justifyContent: "center", alignItems: 'center', marginTop: 70 }}>
          <Image
            style={{ width: 100, height: 100 }}
            source={require('../../resources2/images/icon_ev_empty.png')}
          />
          <Text style={{ color: '#999999' }}>{LocalizedStrings["face_no_unmarked"]}</Text>
        </View>
      )
    }
  }
  //最近出现的人脸数据渲染列表源头     
  _renderRecentFacesView(item, index) {
    let date = item.updateTime
    // let time = item.updateTimer
    // let newDate = date.split('-').join("月").split(' ').join("日")
    let imageResource = this.faceImags[item.faceId];
    if (this.state.showRecentFacesListView) {
      return (
        <TouchableOpacity
          style={{ display: 'flex', flexDirection: "row", justifyContent: 'space-between', alignItems: "center", marginHorizontal: 21, marginBottom: 42 }}
          onPress={() => {
            //跳转到最近出现的人脸的事件页面                         
            let figureInfoItem = this.state.recentFacesImgList[index];
            figureInfoItem.faceUrl = imageResource;
            this.props.navigation.navigate('FaceEvents', { figureInfo: figureInfoItem })
          }}
        >
          <View style={{ display: "flex", flexDirection: 'row', justifyContent: 'flex-start', alignItems: "center" }}>

            <View>
              <Image
                style={{ width: 50, height: 50, borderRadius: 25, marginRight: 15 }}
                source={imageResource}
              />
            </View>

            <View style={{ display: "flex", flexDirection: 'column', justifyContent: 'flex-start' }}>

              <Text style={{ fontSize: 16,color: "#000000"}}>
                {item.figureName == false ? <Text>{LocalizedStrings["face_unmarked"]}</Text> : <Text>{item.figureName}</Text>}
              </Text>

              <Text style={{ fontSize: 12, color: '#999999' }}>
                {date}
              </Text>

            </View>
          </View>

          {/* 最近出现的人脸列表右侧 */}
          {
            item.figureName == false ?
              <TouchableOpacity
                style={{ width: 76, height: 28, borderRadius: 14, backgroundColor: '#f5f5f5', color: '#666666', alignItems: 'center', justifyContent: 'center' }}
                onPress={() => { this._showCommentdlg(item) }} //点击添加备注
              >
                <Text style={{fontSize: kIsCN ? 12 : 10}}>{LocalizedStrings["add_notes"]}</Text>
              </TouchableOpacity>
              :
              <Image
                style={{ width: 20, height: 40 }}
                source={require('../../Resources/Images/button_next_nor.png')}
              />
          }
        </TouchableOpacity>
      )
    }
  }
  //最近出现的人脸点击添加备注后触发这个让commentDlg: true,
  _showCommentdlg(item) {
    console.log(item, 'item1111')
    let imageResource = this.faceImags[item.faceId];
    if (!imageResource) {
      imageResource = this.coverFaceImags[item.coverFaceId];
    }
    this.setState({
      commentDlg: true,
      unMarkfaceUrl: imageResource ? imageResource.uri : "",
      unMarkfaceId: item.faceId
    })
    console.log(this.state.unMarkfaceId, "unMarkfaceId");
  }
  //点击最近出现的人脸未备注的人（添加备注）         
  _renderCommentDlg() {
    let obj = {}
    obj.uri = this.state.unMarkfaceUrl;
    this.state.mAllFigureInf.forEach((item) => {
      item.faceUrl = this.coverFaceImags[item.coverFaceId];
    });
    if (this.state.commentDlg)
      return (
        <InputDlgEx
          title={LocalizedStrings["cloud_comment_dlg_title"]} //"cloud_comment_dlg_title": "备注名称",
          visible={this.state.commentDlg}
          icon={obj}
          listData={this.state.mAllFigureInf}
          onPressed={(aDat) => {
            this.setState({ defVal: aDat.name, faceFigureInfo:aDat });
            console.log(aDat, "11111111111111111111111111111111")
          }}
          onDismiss={(_) => {
            this.renameItem = null;
            this.setState({ commentDlg: false, isRename: false });
          }}
          inputWarnText={this.state.commentErr}
          inputs={[{
            onChangeText: (text) => {
              console.log(this.tag, "onChangeText", text);
              if (this.state.commentErr != null) {
                this.setState({ commentErr: null });
              }
            },
            textInputProps: {
              maxLength: 8,
              returnKeyType: "done",
              autoFocus: Util.isHeightPt() ? true : false
            },
            defaultValue: this.state.defVal,
            type: 'DELETE',
            isCorrect: this.state.commentErr == null
          }]}
          buttons={[
            {
              text: LocalizedStrings["action_cancle"], //"action_cancle": "取消"
              callback: (_) => {
                this.renameItem = null;
                this.setState({ commentDlg: false, isRename: false });
              }
            },
            {
              text: LocalizedStrings["csps_right"],  //"csps_right": "确定",
              callback: (result) => {
                let text = result.textInputArray[0].trim();
                console.log(this.tag, "input changed", text, text.length);
                if (text.length > 0 && !this.containsEmoji(text)) {
                  let cmd = null;

                  cmd = Util.commentFace(text, this.state.unMarkfaceId);
                  if (cmd) {
                    cmd.then((aRet) => {
                      this._onGetData()
                      this.setState({
                        commentDlg: false,
                        defVal: ''
                      });
                    })
                      .catch((aErr) => {
                        this.setState({ commentDlg: false });
												// 400302 人物上限
												// 400302 人物上限
												//400303 人物名称已经存在
											  let errCode = aErr.code;
                        // 400302 人物上限
                        let errMap = { 400302: "figure_max_tips", 400305: "face_max_tips" };
                        let err = errMap[errCode] || "action_failed";
                        Toast.fail(err, err, true);
                        console.log(TAG, "comment failed", aErr);
                      });
                  } else {
                    console.log(this.tag, "nothing changed");
                    this.setState({ commentErr: LocalizedStrings["add_modify_feature_exist"] }) //"add_modify_feature_exist": "该备注名称已存在，请重新输入"
                  }
                } else {
                  if (this.containsEmoji(text)) {
										this.setState({ commentErr: LocalizedStrings["no_emoij_tips"] });
									}
									else {
										this.setState({ commentErr: LocalizedStrings["cloud_comment_null"] });
									}
                }
              }
            }
          ]}
        />);
  }
  containsEmoji(str) {
		let length = str.length;
		for (let i = 0; i < length; ++i) {
			let c = str.charCodeAt(i);
			if (this.isEmojiCharacterV2(c)) {
				return true;
			}
		}
		return false;
	}
  isEmojiCharacterV2(codePoint) {
		return !((codePoint == 0x0) ||
			(codePoint == 0x9) ||
			(codePoint == 0xA) ||
			(codePoint == 0xD) ||
			((codePoint >= 0x20) && (codePoint <= 0xD7FF)) ||
			((codePoint >= 0xE000) && (codePoint <= 0xFFFD)) ||
			((codePoint >= 0x10000))) ||
			(codePoint == 0xa9 || codePoint == 0xae || codePoint == 0x2122 ||
				codePoint == 0x3030 || (codePoint >= 0x25b6 && codePoint <= 0x27bf) ||
				codePoint == 0x2328 || (codePoint >= 0x23e9 && codePoint <= 0x23fa))
			|| ((codePoint >= 0x1F000 && codePoint <= 0x1FFFF))
			|| ((codePoint >= 0x2702) && (codePoint <= 0x27B0))
			|| ((codePoint >= 0x1F601) && (codePoint <= 0x1F64F));
	}
  //已备注人脸点击添加后弹窗样式
  _renderChoiceAddMarkedFaceWayDialog() {
    return (
      <ChoiceDialog
        style={{ width: 100 }}
        dialogStyle={{ itemSubtitleNumberOfLines: 3 }}
        useNewType={false}
        visible={this.state.addMarkedFaceDialogVisible}
        title={LocalizedStrings["select_dialog_title"]}
        options={[
          { title: LocalizedStrings["select_dialog_camera"] },
          { title: LocalizedStrings["select_dialog_album"] },
          { title: LocalizedStrings["irs_left_text"] }
        ]}
        selectedIndexArray={[0]}
        onDismiss={(_) => this.setState({ addMarkedFaceDialogVisible: false })}
        onSelect={(result) => {
          this.selectedIndexArray = result;
          this._choiceAddMarkedFaceWay(result);
          this.setState({ addMarkedFaceDialogVisible: false });
        }}
        buttons={[
        ]}
      />
    );
  }
  //已备注人脸点击添加后弹窗功能
  _choiceAddMarkedFaceWay(result) {
    console.log(result, 'result 弹窗点击参数')
    if (result ==0) {
      TrackUtil.reportClickEvent("Face_CameraInput_ClickNum");
    } else if (result == 1) {
      TrackUtil.reportClickEvent("Face_GalleryInput_ClickNum");
    }
    if (result == 0) {
      // //0 是拍照录入
      // this.props.navigation.navigate('FaceCamera')
      this._startSelectPhoto(result);
    }
    if (result == 1) {
      // 1 是从手机相册选择
      this._startSelectPhoto(result);
    }
    if (result == 2) {
      //2 是取消
      this.props.navigation.navigate('FaceManager2')
    }
  }
  //拍照识别照片
  _startSelectPhoto(result){
    let permissionAndoid = null, permissionResult = result, permissionIOS
		if (result == 0) {
			permissionAndoid = PermissionsAndroid.PERMISSIONS.CAMERA;
			permissionIOS = 'camera';
		}
		else if (result == 1) {
			permissionAndoid = PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE;
			permissionIOS = 'photos';
		}
		if (Platform.OS === "android") {
			console.log('Platform.OS === "android"')
			this.isCheckingPermission = true;
			PermissionsAndroid.request(permissionAndoid, null)
				.then((granted) => {
					this.isCheckingPermission = false;
					console.log("granted", granted);
					if (granted === PermissionsAndroid.RESULTS.GRANTED) {
						if (permissionResult == 0) {
							this.props.navigation.navigate('FaceCamera'
								, {
									figureId: this.state.figureId,
									callback: (data) => {
										// console.log('datashuaxxin')
										//这里刷新 但是头部没有刷新
                    if (data) {
                      this.setState({
                        commentDlg: true,
                        unMarkfaceUrl: data.faceUrl.uri,
                        unMarkfaceId: data.faceId
                      })
                    }
										this._onGetData()
									}
								}
							)
						}
						else if (permissionResult == 1) {
							StorageKeys.IS_AI_FACE_OPEN_TOAST
								.then((result) => {
									// let ret = `$(JSON.stringify(ret))`
									// 当前的设备和用户和是否开启
									if (result == true) {
										this.setState({ showphotoTip: false });
										this.selectPhotoTapped()
									}
									else {
										this.setState({
											showphotoTip: true
										})
										return;
									}

								})
								.catch(err => {
									console.log('errr', err)
									this.setState({ showphotoTip: true, });
								})
						}
					} else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
						// Toast.success("camera_no_write_permission");
						this.setState({ showPermissionDialog: true, permissionRequestState: permissionResult, });
					} else {
						Toast.success("auth_fail");
					}
				}).catch((error) => {
					this.isCheckingPermission = false;
					Toast.success("action_failed");
				});
		} else {
			// no ios's photos const use hardcode

      System.permission.request(permissionIOS).then((res) => {
				console.log('res', res)
				if (permissionResult == 0) {
					this.props.navigation.navigate('FaceCamera'
						, {
							figureId: this.state.figureId,
							callback: (data) => {
								// console.log('datashuaxxin')
								//这里刷新 但是头部没有刷新
                if (data) {
                  this.setState({
                    commentDlg: true,
                    unMarkfaceUrl: data.faceUrl.uri,
                    unMarkfaceId: data.faceId
                  })
                }

							}
						}
					)
				}
				else if (permissionResult == 1) {
					StorageKeys.IS_AI_FACE_OPEN_TOAST
						.then((result) => {
							// let ret = `$(JSON.stringify(ret))`
							console.log('result', result)
							// 当前的设备和用户和是否开启
							if (result == true) {
								this.setState({ showphotoTip: false });
								this.selectPhotoTapped();
							}
							else {
								this.setState({
									showphotoTip: true
								})
								return;
							}
						})
						.catch(err => {
							console.log('errr', err)
							this.setState({ showphotoTip: true, });
						})
				}

			}).catch((error) => {
				console.log('error', error)
				// Toast.success("camera_no_write_permission");
				this.setState({ showPermissionDialog: true, permissionRequestState: permissionResult });
			});

		}

  }

  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 camera
    // status == 1 存储卡/相册
    // 
    let message = "";
    if (this.state.permissionRequestState == 0) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_camera"]);
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
    }
    return (
    // <AbstractDialog

      <MessageDialog
        title={LocalizedStrings["tips"]}
        message={message}
        messageStyle={{
          fontSize: 14,
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );

  }

  //从手机相册中选择照片
  selectPhotoTapped() {
    const options = {
      quality: 1.0,
      maxWidth: 500,
      maxHeight: 500,
      storageOptions: {
        skipBackup: true
      }
    };
    setTimeout(() => {
      launchImageLibrary(options, (response) => {
        console.log(response, '相册照片response')
        if (response.didCancel) {
          console.log('User cancelled photo picker');
        }
        else if (response.error) {
          console.log('ImagePicker Error: ', response.error);
          this.setState({ showPermissionDialog: true, permissionRequestState: 1 });
        }
        else if (response.customButton) {
          console.log('User tapped custom button: ', response.customButton);
        }
        else {
          this.setState({
            commentDlg: false,
          });
          Toast.loading('c_setting');//请稍后
          let path = response.uri.slice(7)
          console.log(path,"path");

          Service.miotcamera.uploadImageToCameraServer(path).then((res) => {
            res = JSON.parse(res);
            console.log(res, 'res');

            if (res.result == "ok" && res.data !== null && res.data.faceInfoMetas[0] != null) {
              this.setState({
                commentDlg: true,
                unMarkfaceId: res.data.faceInfoMetas[0].faceId,
                unMarkfaceUrl: response.uri,
              });
            }
            else {
              this.setState({
                commentDlg: false,
              });
              Toast.fail("face_recognition_fail_tips", '', true)
            }
          }).catch((err) => {
            console.log('添加失败', err);
            Toast.show("添加失败") 
          })
        }
      })
    }, 100)

  }
   //首次从手机相册中选择照片引导页·
  _renderTipsDialogView() {
		return (
			<AbstractDialog
				visible={this.state.showphotoTip}
				useNewTheme
				onDismiss={() => { this.setState({ showphotoTip: false, }) }}
				buttons={[
					{
						text: LocalizedStrings["csps_right"],
						style: { color: '#f0ac3d' },
						callback: (_) => {
							this.setState({
								showphotoTip: false,
							}, () => [
								this.selectPhotoTapped()
							]);

							StorageKeys.IS_AI_FACE_OPEN_TOAST = true
							StorageKeys.IS_AI_FACE_OPEN_TOAST = true
						}
					}
				]}
			>
				<View
					style={{
						flex: 1,
						flexDirection: "column",
						// height: 200,
						alignItems: 'center',
						justifyContent: 'center',
					}}
				>
					<View>
						<Image style={{ width: 280, height: 200 }} source={require('../../Resources/Images/photo_placeholder.webp')}>
						</Image>
					</View>
					<View style={{ marginVertical: 5, textAlign: 'center', marginHorizontal: (Dimensions.get("window").width - 280) / 2 }}>
						<Text style={{ fontSize: 12, color: '#000000' }}>
							{LocalizedStrings["pick_album_tips"]}
						</Text>
					</View>

				</View>
			</AbstractDialog>
		)
	}
  //加载提示loading......
  _renderLoadingView() {
    if (this.state.showLoading) {
      return (
        <View
          style={{ position: "absolute", width: "100%", height: "100%", display: "flex", justifyContent: "center", alignItems: "center", backgroundColor: "white" }}
        >
          <ActivityIndicator
            style={{ width: 54, height: 54 }}
            color={DarkMode.getColorScheme() == "dark" ? "xm#ffffff" : "#000000"}
            size={"large"}
          />
          <Text
            style={{ marginTop: 10, fontSize: 12, color: "#000000" }}>
            {LocalizedStrings["camera_loading"]}
          </Text>
        </View>
      );
    }


  }
  //这里是没有文件的时候的页面
  _renderEmptyLayout() {
    if (this.state.showLoading) {
      return;
    }
    if (this.state.isCurrentDayEmpty) {
      return (
        <View
          style={{ width: "100%", flexGrow: 1, display: "flex", justifyContent: "center", alignItems: "center" }}
        >
          <Image
            source={require("../../Resources/Images/icon_camera_empty_files.png")}
            style={{ width: 79, height: 79 }}
          />
          <Text
            style={{ fontSize: 14, color: "#808080" }}
          >
            {LocalizedStrings["no_files"]}
          </Text>
        </View>
      );
    } else {
      return null;
    }

  }
}

//主要样式    
export const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    width: '100%',
    height: '100%',
    display: "flex",
    flexWrap: "nowrap"
  },
  whiteblank: {
    height: 0.5,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginBottom: 28,
    marginTop: 25,
  }
});