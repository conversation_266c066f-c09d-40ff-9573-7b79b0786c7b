'use strict';

import { Device, Service } from "miot";
import { ListItem, ListItemWithSwitch } from 'miot/ui/ListItem';
import Separator from 'miot/ui/Separator';
import React from 'react';
import { ScrollView, View, Text } from 'react-native';

import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import { styles } from './SettingStyles';
import StorageKeys from '../StorageKeys';
import Toast from '../components/Toast';
import VersionUtil from '../util/VersionUtil';
import WDRSetting from './WDRSetting';
import CameraConfig from "../util/CameraConfig";
import { CAMERA_CONTROL_SEPC_PARAMS } from "../Constants";
import NavigationBar from "miot/ui/NavigationBar";
import RPC from "../util/RPC";
import TrackUtil from '../util/TrackUtil';
import { DescriptionConstants } from "../Constants";

export default class ImageSetting extends React.Component {

  constructor(props, context) {
    super(props, context);

    this.state = {
      isWatermarkEnable: false,
      isLDCEnable: false, // lens distortion correction => LDC
      wdrMode: false
      
    };
  }

  
  render() {
    return (
      <View style={styles.container}>
        <ScrollView showsVerticalScrollIndicator={false}>

          {/* <View style={[styles.blank, { borderTopWidth: 0 }]} /> */}
          <View style={styles.featureSetting}>
            <ListItemWithSwitch
              accessibilityLabel={DescriptionConstants.sz_4_53}
              title={LocalizedStrings['is_watermark']}
              showSeparator={false}
              value={this.state.isWatermarkEnable}
              onValueChange={(value) => this._onWatermarkValueChange(value)}
              titleStyle={{ fontWeight: 'bold' }}
              onPress={() => {
              }}
              accessibilitySwitch={{
                accessibilityLabel: DescriptionConstants.sz_4_53
              }}
            />
            <ListItemWithSwitch
              accessibilityLabel={DescriptionConstants.sz_4_55}
              title={LocalizedStrings['is_lens_distortion_correction']}
              showSeparator={false}
              value={this.state.isLDCEnable}
              onValueChange={(value) => this._onLDCValueChange(value)}
              titleStyle={{ fontWeight: 'bold' }}
              unlimitedHeightEnable={true}
              titleNumberOfLines={2}
              onPress={() => {
              }}
              accessibilitySwitch={{
                accessibilityLabel: DescriptionConstants.sz_4_55
              }}
            />
          </View>

          {/* <View style={styles.blank} /> */}
          <View style={styles.featureSetting}>
            {CameraConfig.isSupportWDR(Device.model) ?
              <ListItem 
                title={LocalizedStrings['setting_wdr']}
                value={this.state.wdrMode ? LocalizedStrings['on'] : LocalizedStrings['off']}
                showSeparator={false}
                onPress={() => {
                  TrackUtil.reportClickEvent("WDRNumber");
                  this.props.navigation.navigate('WDRSetting', {
                    selectWDRCallBack: ((value) => {
                      this.setSelectWDR(value);
                    }),
                    wdrMode: this.state.wdrMode
                  });
                }}
                titleStyle={{ fontWeight: 'bold' }}
                valueNumberOfLines={3}
                titleNumberOfLines={2}
              /> : null
            }
            <ListItem 
              accessibilityLabel={DescriptionConstants.sz_4_59}
              title={LocalizedStrings['is_rotate_image']} 
              showSeparator={false}
              onPress={() => {
                TrackUtil.reportClickEvent("RoteScreenNumber");
                this.props.navigation.navigate('ImageRotateSetting');
              }}
              titleStyle={{ fontWeight: 'bold' }}

            />
          </View>
        </ScrollView>
      </View>
    );
  }

  componentDidMount() {
    this.props.navigation.setParams({
      // show:true
      title: LocalizedStrings['is_title'],
      type: this.state.darkMode ? NavigationBar.TYPE.black : NavigationBar.TYPE.white,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => { this.props.navigation.goBack(); }
        }
      ],
      titleStyle: {
        fontSize: 18,
        color: '#333333',
        fontWeight: 500
      }
    });

    if (VersionUtil.isUsingSpec(Device.model)) {
      Service.spec.getPropertiesValue([CAMERA_CONTROL_SEPC_PARAMS[5]])
        .then((res) => {
          if (res[0].code == 0) {
            this.setState({
              isWatermarkEnable: res[0].value
            });
            StorageKeys.IS_WATERMARK_OPEN = res[0].value;
          } else {
            Toast.fail('c_get_fail');
          }
        })
        .catch((err) => {
          Toast.fail('c_get_fail', err);

        });
    } else {
      RPC.callMethod("get_prop", [
        'watermark'
      ]).then((res) => {
        this.setState({
          isWatermarkEnable: res.result[0] == 'on'
        });
        StorageKeys.IS_WATERMARK_OPEN = (res.result[0] == 'on');
      }).catch((err) => {
        Toast.fail('c_get_fail', err);
      });
     
    }
    StorageKeys.IS_LENS_DISTORTION_CORREECTION.then((res) => {
      this.setState({
        isLDCEnable: res
      });
    }).catch(() => {
      this.setState({
        isLDCEnable: false
      });
    });  
    
    if (CameraConfig.isSupportWDR(Device.model)) {
      WDRSetting.get_wdr().then((res) => {
        console.log(`image get_wdr：${ res }`);
        this.setSelectWDR(res);
      }).catch((err) => {
        Toast.fail('c_get_fail', err);
      });

    }
  }

  setSelectWDR(value) {
    this.setState({
      wdrMode: value
    });
  }

  _onWatermarkValueChange(value) {
    TrackUtil.reportClickEvent("WatermarkOnOff_ClickNum");
    Toast.loading('c_setting');
    if (VersionUtil.isUsingSpec(Device.model)) {
      Service.spec.setPropertiesValue([{ ...CAMERA_CONTROL_SEPC_PARAMS[5], value: value }])
        .then((res) => {
          if (res[0].code == 0) {
            this.setState({
              isWatermarkEnable: value
            });
            StorageKeys.IS_WATERMARK_OPEN = value;
            Toast.success('c_set_success');
          } else {
            this.setState({
              isWatermarkEnable: !value
            });
            StorageKeys.IS_WATERMARK_OPEN = !value;
            Toast.fail('c_set_fail');
          }
        })
        .catch((err) => {
          this.setState({
            isWatermarkEnable: !value
          });
          StorageKeys.IS_WATERMARK_OPEN = !value;
          Toast.fail('c_set_fail', err);
        });
    } else {
      RPC.callMethod("set_watermark", [
        value ? 'on' : 'off'
      ]).then((res) => {
        this.setState({
          isWatermarkEnable: res.result[0] == 'OK' ? value : !value
        });
        StorageKeys.IS_WATERMARK_OPEN = (res.result[0] == 'OK' ? value : !value);
        Toast.success('c_set_success');
      }).catch((err) => {
        this.setState({
          isWatermarkEnable: !value
        });
        StorageKeys.IS_WATERMARK_OPEN = !value;
        Toast.fail('c_set_fail', err);
      });
    }
    
  }

  _onLDCValueChange(value) {
    TrackUtil.reportClickEvent("LensDistortionCorrectionOnOff_ClickNum");
    StorageKeys.IS_LENS_DISTORTION_CORREECTION = value;
    this.setState({
      isLDCEnable: value
    });
    Toast.success('c_set_success');
  }
}
