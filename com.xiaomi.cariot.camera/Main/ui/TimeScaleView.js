'use strict';

import React from 'react';
import { View, Image, Text, ART, Dimensions, PanResponder, TouchableOpacity, FlatList, StyleSheet } from 'react-native';
import PropTypes from 'prop-types';
import VersionUtil from '../util/VersionUtil';
import { Device } from 'miot';

const TAG = "TimeScaleView";
const { Surface, Shape, Path } = ART;

const Scale_Type_Max = 1;
const Scale_Type_Big = 2;
const Scale_Type_Middle = 3;
const Scale_Type_Small = 4;

const SCALING_FACTOR_MAX = 10; // 最大放大参数
const SCALING_FACTOR_MIN = 0.5; // 最小缩小参数

const SCROLLSTATE = {
  IDLE: 0,
  SCROLLING: 1
};

export default class TimeScaleView extends React.Component {

  constructor(props) {
    super(props);
    this.currentTime = 0;
    this.drawWidth = 0;
    this.initItemInterval = 20;
    this.scaleType = Scale_Type_Middle;
    this.intervalCount = 6; // 多少个小刻度间隔一个大刻度
    this.itemTime = 30 * 60 * 1000; // 大刻度之间间隔时间 毫秒
    this.itemInterval = this.initItemInterval; // 刻度间隔宽度
    this.intervalTime = this.itemTime / this.intervalCount; // 每一个格子间隔时间 毫秒
    this.oneUnitWidthTime = this.intervalTime / this.itemInterval; // 一个宽度占多少毫秒
    this.panResponder = null;
    this.dateTime = new Date();
    this.dataList = [];
    this.isDrawArea = false;
    this.drawColorX = 0;
    this.isScrolling = false;
    this.timeTextList = [];
    this.motionAreaList = [];
    this.isNeedInit = true;
    this.motionType = 1;

    this.timelineViewWidth = 0;
    this.viewWidth = 0;
    this.curScaleFactor = 1;
    this.dayArray = [];

    this.dayListOffset = 0;
    this.selectedDayTag = "";
    this.endTimeout = null;
    this.scrollState = SCROLLSTATE.IDLE;

    this.scrollDayIndexTimeout = null;


  }
  static propTypes = {
    style: PropTypes.any,
    data: PropTypes.array,
    time: PropTypes.number,
    landscape: PropTypes.bool,
    viewHeight: PropTypes.number,
    onTimeScaleChange: PropTypes.func,
    backgroundColor: PropTypes.string,
    onScrollEnd: PropTypes.func,
    onScrolling: PropTypes.func,
    isDisabled: PropTypes.bool
  }
  state = {
    showTimelineView: false,
    dateData: []
  }
  static defaultProps = {
    data: null,
    time: null,
    landscape: false,
    viewHeight: 60,
    onTimeScaleChange: null,
    onScrollEnd: null,
    onScrolling: null
  }
  // 重新赋值大刻度之间间隔时长
  initInterval() {
    this.intervalCount = 6;
    switch (this.scaleType) {
      case Scale_Type_Max:
        this.itemTime = 6 * 60 * 60 * 1000; // 间隔6小时
        break;
      case Scale_Type_Big:
        this.itemTime = 60 * 60 * 1000; // 间隔1小时
        break;
      case Scale_Type_Middle:
        this.itemTime = 30 * 60 * 1000; // 间隔30分钟
        break;
      case Scale_Type_Small:
        this.intervalCount = 10;
        this.itemTime = 10 * 60 * 1000; // 间隔10分钟
        break;
    }
    this.intervalTime = this.itemTime / this.intervalCount;
    this.oneUnitWidthTime = this.intervalTime / this.itemInterval;
  }

  componentWillMount() {
    console.log(TAG, `componentWillMount: `);
    this.currentTime = new Date().getTime();
    if (this.props.time != null && this.props.time != 0) {
      this.currentTime = this.props.time;
    }
    this.dataList = this.props.data;
    this.initPanResponder();
    // this.initTempList();
  }
  componentDidMount() {
    console.log(TAG, `componentDidMount: `);
    // this._testData();
  }

  // _testData() {
  //   let data = require("../../Resources/json/json.json");
  //   this.initData(data);
  // }

  initData(data) {
    this.dataList = data;
    this.setState({});
    let dayMillis = 24 * 60 * 60 * 1000;
    let lastTime = 0;
    this.dayArray = [];
    for (let i = 0; i < data.length; i++) {
      let item = data[i];
      if (item.startTime >= lastTime) {
        let day = {};
        day.startTime = item.startTime;
        this.dateTime.setTime(day.startTime);
        this.dateTime.setSeconds(0, 0);
        this.dateTime.setMinutes(0);
        this.dateTime.setHours(0);
        let month = this.dateTime.getMonth() + 1;
        let date = this.dateTime.getDate();
        let year = this.dateTime.getFullYear();
        day.str = (month > 9 ? month : `0${ month }`) + (date > 9 ? date : `0${ date }`);
        let tag = `${ year }-${ month }-${ date }`;
        day.tag = tag;
        if (tag === this.selectedDayTag) {
          day.isSelected = true;
        } else {
          day.isSelected = false;
        }
        this.dayArray.push(day);
        lastTime = this.dateTime.getTime() + dayMillis;
      }
    }
    this.setState({ dateData: this.dayArray });
  }

  // 外部的view同步时间轴进度的。
  scrollToTimestamp(timestamp) {
    // this.scrollToTimestampWithNotify(time, false);
    if (this.isLeftLongPressed || this.isRightLongPressed || this.scrollState != SCROLLSTATE.IDLE) {
      return;
    }

    this.currentTime = timestamp;
    this.setState({}); // 刷新页面
    this.refreshTopDateView(timestamp);
  }

  _calculateOffset(selectedDayIndex) { // copy horizonalScrollView.java computeScrollDeltaToGetChildRectOnScreen
    let width = this.viewWidth;
    let screenLeft = this.dayListOffset || 0;
    let screenRight = screenLeft + width;
    let itemLeft = selectedDayIndex * 64;
    let itemRight = (selectedDayIndex + 1) * 64;
    let scrollXDelta = 0;

    if (itemRight > screenRight && itemLeft > screenLeft) {
      // need to move right to get it in view: move right just enough so
      // that the entire rectangle is in view (or at least the first
      // screen size chunk).

      // get entire rect at right of screen
      scrollXDelta += (itemRight - screenRight);


      // make sure we aren't scrolling beyond the end of our content

    } else if (itemLeft < screenLeft && itemRight < screenRight) {
      // need to move right to get it in view: move right just enough so that
      // entire rectangle is in view (or at least the first screen
      // size chunk of it).
      scrollXDelta -= (screenLeft - itemLeft);
      // make sure we aren't scrolling any further than the left our content
    }
    return scrollXDelta;
  }




  // here to see what happened  should make a debugger
  refreshTopDateView(centerTimestamp) {
    console.log("refreshTopdate");

    this.dateTime.setTime(centerTimestamp);
    let year = this.dateTime.getFullYear();
    let month = this.dateTime.getMonth() + 1;
    let day = this.dateTime.getDate();
    let tag = `${ year }-${ month }-${ day }`;
    if (this.selectedDayTag === tag) { // 防止选择了新的日期，新的日期在两边跳
      return;
    }
    this.selectedDayTag = tag;

    console.log("refreshTopdate", "选中日期:", tag);
    this.centerTimestamp = centerTimestamp;
    let selectedDayIndex = -1;
    for (let i = 0; i < this.dayArray.length; i++) {

      let day = this.dayArray[i];
      if (day.tag === tag) {
        this.dayArray[i].isSelected = true;
        selectedDayIndex = i;
      } else {
        this.dayArray[i].isSelected = false;
      }
    }

    this.setState({ dateData: this.dayArray });
    clearTimeout(this.scrollDayIndexTimeout);

    let offset = this._calculateOffset(selectedDayIndex) + this.dayListOffset;
    console.log("refreshTopdate", "移动距离", offset);

    this.scrollDayIndexTimeout = setTimeout(() => {
      if (selectedDayIndex < 0) {
        return;
      }
      if (this.dayListRef != null) {
        this.dayListRef.scrollToOffset({
          animated: false,
          offset: offset
        });
      }
      
    }, 300);

  }

  onPressDayItem(dayItem) {
    if (this.props.isDisabled) {
      return;
    }
    let timestamp = dayItem.startTime;
    // 防止在两边的情况 做一下处理
    this.dateTime.setTime(timestamp);
    this.scrollToTimestampWithNotify(this.dateTime.getTime(), true);
    this.refreshTopDateView(this.dateTime.getTime());
  }

  dayKeyExtractor = (item) => item.startTime.toString();

  getDayItemLayout = (item, index) => {
    return {
      length: 64,
      offset: 64 * index,
      index
    };
  }

  componentWillUnmount() {
    console.log(TAG, `componentWillUnmount: `);
    clearTimeout(this.scrollDayIndexTimeout);
  }
  render() {
    if (this.isNeedInit) {
      this.isNeedInit = false;
      this.initInterval();
    }
    this.timeTextList = [];
    this.motionAreaList = [];
    return (
      <View style={{}}>
        <View
          style={{ width: "100%", height: 30, display: "flex", flexDirection: "row", alignItems: "center", backgroundColor: "#ffffff" }}
        >
          <FlatList
            ref={(ref) => { this.dayListRef = ref; }}
            style={{ width: "100%", height: 20 }}
            data={this.state.dateData}
            horizontal={true}
            renderItem={(item) => {
              return this.renderDayItem(item);
            }}
            onScroll={(event) => {
              this.dayListOffset = event.nativeEvent.contentOffset.x;
              console.log(`当前偏移量：${ this.dayListOffset }`);
            }}
            keyExtractor={this.dayKeyExtractor}
            getItemLayout={this.getDayItemLayout}
            showsHorizontalScrollIndicator={false}
          />
        </View>

        <View
          style={{ width: "100%", height: 54, display: "flex", flexDirection: "row", backgroundColor: "#ffffff" }}
          onLayout={(event) => {
            console.log(TAG, "onlayout", event.nativeEvent.layout);
            let width = event.nativeEvent.layout.width;
            let viewWidth = width - 60;
            
            console.log(TAG, `finalWidth:${ viewWidth }`);
            if (viewWidth == null || viewWidth <= 0) {
              return;
            }
            this.timelineViewWidth = viewWidth;
            this.viewWidth = width;
            if (this.state.showTimelineView) {
              this.setState(() => { return { showTimelineView: false }; }, () => {
                this.setState({ showTimelineView: true });
              });
            } else {
              this.setState({ showTimelineView: true });
            }
          }}
        >
          <TouchableOpacity
            style={{ width: 30, height: 54 }}
            onPress={() => this.onLeftPressed()}
            onPressIn={() => this.onLeftPressIn()}
            onPressOut={() => this.onLeftPressOut()}
            underlayColor={"#55555522"}
            activeOpacity={0.88}
          >
            <Image
              style={{ width: "100%", height: "100%" }}
              source={require("../../Resources/Images/button_prev_nor.png")}
            />
          </TouchableOpacity>
          {
            this._renderRulesView()
          }
          <TouchableOpacity
            style={{ width: 30, height: 54 }}
            onPress={() => this.onRightPressed()}
            onPressIn={() => this.onRightPressIn()}
            onPressOut={() => this.onRightPressOut()}
            underlayColor={"#55555522"}
            activeOpacity={0.88}
          >
            <Image
              style={{ width: "100%", height: "100%" }}
              source={require("../../Resources/Images/button_next_nor.png")}
            />
          </TouchableOpacity>
        </View>

      </View>

    );
  }

  renderDayItem({ item }) {
    let startTime = item.startTime;
    this.dateTime.setTime(startTime);
    let month = this.dateTime.getMonth() + 1;
    let day = this.dateTime.getDate();
    let str = `${ month > 9 ? month : `0${ month }` }/${ day > 9 ? day : `0${ day }` }`;
    return (
      <View style={item.isSelected ? styles.dayItemSelected : styles.dayItem}
      >
        <Text
          style={item.isSelected ? styles.dayItemTextSelected : styles.dayItemText}
          onPress={() => this.onPressDayItem(item)}
        >
          {str}
        </Text>
      </View>
    );
  }

  bindDataToTimeScaleView(data) {
    console.log(TAG, `bindDataToTimeScaleView: `);
    this.dataList = data;
    this.setState({});
  }
  clearList() {
    this.dataList = [];
    this.setState({});
  }
  getDataListItem(index) {
    if (index < 0 || this.state.dataList == null || index >= this.state.dataList.length) {
      return;
    }
    let item = this.dataList[index];
    return item;
  }
  
  onLeftPressed() {
    this.isLeftPressed = false;
    clearTimeout(this.longLeftPressTimeout);
    this.longLeftPressTimeout = null;
    if (!this.isLeftLongPressed) {
      this.onMovePrev();
      return;
    } else {
      this.isLeftLongPressed = false;
      // this.props.onScrollEnd && this.props.onScrollEnd(this.currentTime);
      this.scrollToTimestampWithNotify(this.currentTime, true);
    }
  }
  onLeftPressIn() { // 同一触摸序列  只会被调用一次。
    if (this.props.isDisabled) {
      return;
    }
    this.isLeftPressed = true;// 标记用户手指是否停在控件上
    this.isLeftLongPressed = false;
    clearTimeout(this.longLeftPressTimeout);
    this.longLeftPressTimeout = setTimeout(() => {
      console.log("on long press left");
      this.handleLongPressLeft();
    }, 1500);
  }
  handleLongPressLeft() {
    if (this.props.isDisabled) {
      return;
    }
    this.isLeftLongPressed = true;
    this.onFastMovePrev();
    this.longLeftPressTimeout = setTimeout(() => {
      console.log("on long press left");
      this.handleLongPressLeft();
    }, 50);
  }
  onLeftPressOut() {
    if (this.props.isDisabled) {
      return;
    }
    if (!this.isLeftLongPressed) { // 如果不是长按  要移除timeout  避免后续还在滑动
      clearTimeout(this.longLeftPressTimeout);
    }
    this.isLeftPressed = false;// 标记用户手指是否停在控件上
  }
  // touchableopcity组件调用顺序： onpressin onpressout onpress  如果是划出去的  onpress就不调用了
  onRightPressed() {
    if (this.props.isDisabled) {
      return;
    }
    this.isRightPressed = false;
    clearTimeout(this.longRightPressTimeout);
    this.longRightPressTimeout = null;
    if (!this.isRightLongPressed) {
      this.onMoveNext();
      return;
    } else {
      this.isRightLongPressed = false;
      // this.props.onScrollEnd && this.props.onScrollEnd(this.currentTime);
      this.scrollToTimestampWithNotify(this.currentTime, true);
    }
  }
  onRightPressIn() {
    if (this.props.isDisabled) {
      return;
    }
    this.isRightPressed = true;
    this.isRightLongPressed = false;
    clearTimeout(this.longRightPressTimeout);
    this.longRightPressTimeout = setTimeout(() => {
      console.log("on long press right");
      this.handleLongPressRight();
    }, 1500);
  }
  onRightPressOut() {
    if (this.props.isDisabled) {
      return;
    }
    if (!this.isRightLongPressed) {
      clearTimeout(this.longRightPressTimeout);
    }
    this.isRightPressed = false;
  }
  handleLongPressRight() {
    this.isRightLongPressed = true;
    this.onFastMoveNext();
    this.longRightPressTimeout = setTimeout(() => {
      this.handleLongPressRight();
    }, 50);
  }
  onFastMoveNext() {
    if (this.dataList.length == 0) {
      return;
    }
    let moveTime = 30000;
    let selectTime = this.currentTime + moveTime;
    if (selectTime > this.dataList[this.dataList.length - 1].endTime) {
      return;
    }
    if (selectTime >= this.dataList[this.dataList.length - 1].startTime) {
      this.scrollToTimestampWithNotify(selectTime, false);
      return;
    }
    for (let i = this.dataList.length - 2; i >= 0; i--) {
      let timeItem = this.dataList[i];
      this.dateTime.setTime(timeItem.startTime);
      if (selectTime > this.dateTime.getTime()) {
        if (selectTime < timeItem.endTime) {
          this.scrollToTimestampWithNotify(selectTime, false);// 滑动 但是不通知
        } else {
          this.scrollToTimestampWithNotify(this.dataList[i + 1].startTime, false);// 滑动但是不通知
        }
        break;
      }
    }
  }
  onFastMovePrev() {
    if (this.dataList.length == 0)
      return;
    let moveTime = 30000;
    let selectTime = this.currentTime - moveTime;
    this.dateTime.setTime(this.dataList[0].startTime);
    if (selectTime < this.dateTime.getTime()) {
      return;
    }
    if (selectTime < this.dataList[0].endTime) {
      this.scrollToTimestampWithNotify(selectTime, false);
      return;
    }
    for (let i = 1, len = this.dataList.length; i < len; i++) {
      let timeItem = this.dataList[i];
      if (selectTime < timeItem.startTime) {
        if (selectTime < this.dataList[i - 1].endTime - moveTime) {
          this.scrollToTimestampWithNotify(selectTime, false);
        } else {
          this.scrollToTimestampWithNotify(this.dataList[i - 1].endTime - moveTime, false);
        }
        break;
      }
    }
  }
  onMoveNext() {
    if (this.dataList.length <= 0) {
      return;
    }
    let centerValue = this.currentTime;
    if (centerValue >= this.dataList[this.dataList.length - 1].endTime) { // 超过最后一个了 不让继续跑了
      this.onEndReach();
      return;
    }
    if (centerValue > this.dataList[this.dataList.length - 1].startTime) {
      this.onEndReach();
      return;
    }
    let timeItem = this.getNeedItem(centerValue, true);
    if (timeItem != null) {
      this.dateTime.setTime(timeItem.startTime);
      this.scrollToTimestampWithNotify(this.dateTime.getTime(), true);
      return;
    }
    this.onEndReach();
  }
  onMovePrev() {
    if (this.dataList.length <= 0) {
      return;
    }
    let currentValue = this.currentTime;
    let selectTime = currentValue;
    let time = this.dataList[0].startTime;
    if (selectTime < time) {
      return;// 不让继续往前跑了
    }
    if (selectTime < this.dataList[0].endTime) {
      this.scrollToTimestampWithNotify(this.dataList[0].startTime, true);
      return;
    }
    let timeItem = this.getNeedItem(selectTime, false);
    if (timeItem != null) {
      this.dateTime.setTime(timeItem.startTime);
      this.scrollToTimestampWithNotify(this.dateTime.getTime(), true);
      return;
    }
    this.dateTime.setTime(this.dataList[0].startTime);
    this.scrollToTimestampWithNotify(this.dateTime.getTime(), true);
  }

    getNeedItem(time, next) {
    let lastTime = null;
    if (next) { // 找到下一个连续区域的起点。

      for (let i = 0, j = this.dataList.length - 1; i <= j; i++) {
        let timeItem = this.dataList[i];
        if (i < j) {//没有到文件末尾
          let nextItem = this.dataList[i + 1];
          if ((nextItem.startTime - timeItem.endTime) < 5001) {//如果隔得很近
            continue;//继续找，直到找到不连续的地方
          }
          if (nextItem.startTime > time) {//不连续区域的起点，比指定时间大，就认为找到了。
            lastTime = nextItem;
            break;
          }
        }
      }
    } else { // 找到上一个连续区域的的起点。
      for (let i = this.dataList.length - 1, j = 0; i >= j; i--) {
        let timeItem = this.dataList[i];
        if (i > 0) {
          let lastItem = this.dataList[i - 1];
          if ((timeItem.startTime - lastItem.endTime) <= 5001) {
            continue;
          }
        }
        this.dateTime.setTime(timeItem.startTime);
        if (this.dateTime.getTime() < time) {
          lastTime = timeItem;
          break;
        }
      }
    }
    return lastTime;
  }


  onEndReach() {
    if (this.dataList == null || this.dataList.length <= 0) {
      return;
    }
    let time = (this.dataList[this.dataList.length - 1].endTime);
    this.scrollToTimestampWithNotify(time, true);
  }

  scrollToTimestampWithNotify(timestamp, shouldNotify) {
    this.currentTime = timestamp;
    this.setState({}); // 刷新页面
    if (shouldNotify) {
      this.refreshTopDateView(this.currentTime);
      this.props.onScrollEnd && this.props.onScrollEnd(this.currentTime);
    } else {
      this.props.onScrolling && this.props.onScrolling(this.currentTime);
    }
  }

  _renderRulesView() {
    if (!this.state.showTimelineView) {
      return;
    }
    return (
      <View {...this.panResponder.panHandlers}
        style={{
          flex: 1, height: 54, justifyContent: 'center'
        }}
      >
        {this._renderNormalArea()}
        {this._renderAlarmArea()}
        {this._renderRuler()}
        {this._renderTimeText()}
        {this._renderDateText()}
        {this._renderTopBottomLine()}
        {this._renderCenterLine()}
      </View>
    );
  }
  _renderLine() {
    const path = new Path();
    path.moveTo(20, 0);
    path.lineTo(20, 30);
    return (
      <View style={{ position: 'absolute', width: "100%", height: "100%", flexDirection: 'column' }} >
        <Surface style={{ alignContent: 'center', justifyContent: 'center', alignSelf: 'center' }} width={this.drawWidth} height={54}>
          <Shape d={path} stroke="#000000" strokeWidth={1} />
        </Surface>
      </View>
    );
  }
  _renderTopBottomLine() {
    // console.log(TAG, `_renderTopBottomLine: `);
    const topPath = new Path();
    topPath.lineTo(this.timelineViewWidth, 0);
    const bottomPath = new Path();
    bottomPath.moveTo(0, 54);
    bottomPath.lineTo(this.timelineViewWidth, 54);
    return (
      <View style={{ position: 'absolute', width: this.timelineViewWidth, height: "100%" }} >
        <Surface width={this.timelineViewWidth} height={54}>
          <Shape d={topPath} stroke={this.props.landscape ? '#333333' : '#f2f2f2'} strokeWidth={1} />
          <Shape d={bottomPath} stroke={this.props.landscape ? '#333333' : '#f2f2f2'} strokeWidth={1} />
        </Surface>
      </View>
    );
  }
  getBigRulerDate(time) { // 间隔6个小时的刻度保证从00:00开始显示, 暂时想不到好的逻辑，先这样计算，多重判断减少损耗
    let date = null;
    if (this.scaleType == Scale_Type_Max) {
      this.dateTime.setTime(time);
      let year = this.dateTime.getFullYear();
      let month = this.dateTime.getMonth() + 1;
      let day = this.dateTime.getDate();
      let hour = this.dateTime.getHours();
      let min = this.dateTime.getMinutes();
      let sec = this.dateTime.getSeconds();
      let hourSec = hour * 60 * 60 * 1000;
      let rem = hourSec % this.itemTime;
      if (rem == 0) {
        if (month < 10) {
          month = `0${ month }`;
        }
        if (day < 10) {
          day = `0${ day }`;
        }
        if (hour < 10) {
          hour = `0${ hour }`;
        }
        if (min < 10) {
          min = `0${ min }`;
        }
        if (sec < 10) {
          sec = `0${ sec }`;
        }
        date = `${ hour }:${ min }`;
      }
    } else {
      let rem = time % this.itemTime;
      if (rem == 0) {
        this.dateTime.setTime(time);
        let year = this.dateTime.getFullYear();
        let month = this.dateTime.getMonth() + 1;
        let day = this.dateTime.getDate();
        let hour = this.dateTime.getHours();
        let min = this.dateTime.getMinutes();
        let sec = this.dateTime.getSeconds();
        if (month < 10) {
          month = `0${ month }`;
        }
        if (day < 10) {
          day = `0${ day }`;
        }
        if (hour < 10) {
          hour = `0${ hour }`;
        }
        if (min < 10) {
          min = `0${ min }`;
        }
        if (sec < 10) {
          sec = `0${ sec }`;
        }
        date = `${ hour }:${ min }`;
      }
    }
    return date;
  }
  _renderRuler() {
    const viewWidth = Number.parseInt(this.timelineViewWidth);
    const centerX = Number.parseInt(this.timelineViewWidth / 2);
    // console.log(TAG, `_renderRuler: viewWidth = ${ viewWidth } ${ centerX } ${ this.itemInterval }`);
    const indexCount = Math.ceil(viewWidth / this.itemInterval);// todo 这里的方式需要更新一下。
    // console.log(TAG, `_renderRuler: indexCount = ${ indexCount }`);
    let leftTimestamp = Number.parseInt(this.currentTime - centerX * this.oneUnitWidthTime); // 计算出左边的最小时间戳; 
    // console.log(TAG, `_renderRuler: leftTimestamp = ${ leftTimestamp }`);
    let drawX = 0; // 记录绘制刻度的位置
    let drawTimestamp = 0; // 记录所绘制刻度线代表的时间戳
    // 先计算出绘制的第一个刻度的位置和时间戳
    let temp = Number.parseInt(leftTimestamp / this.intervalTime);
    drawTimestamp = Number.parseInt((temp + 1) * this.intervalTime); // 得到最左边的那个刻度线的起点。
    drawX = Number.parseInt((drawTimestamp - leftTimestamp) / this.oneUnitWidthTime);// leftTimestamp是偏移量为0的位置，drawTimestamp比leftTimestamp小 为负数
    // console.log(TAG, `_renderRuler: drawTimestamp = ${drawTimestamp} drawX = ${drawX}`);
    const path = new Path();
    for (let i = 0; i < indexCount; i++) { // 绘制几个刻度线。
      this.dateTime.setTime(drawTimestamp);
      let year = this.dateTime.getFullYear();
      let month = this.dateTime.getMonth() + 1;
      let day = this.dateTime.getDate();
      let hour = this.dateTime.getHours();
      let min = this.dateTime.getMinutes();
      let sec = this.dateTime.getSeconds();
      let dateStr = null;
      let hourStr = null;
      if (hour == 0 && min == 0 && sec == 0) { // 需要添加日期
        dateStr = `${ month > 9 ? month : `0${ month }` }/${ day > 9 ? day : `0${ day }` }`;
        hourStr = `${ hour > 9 ? `${ hour }` : `0${ hour }` }:${ min > 9 ? min : `0${ min }` }`;
      } else if ((min == 0 || min == 30) && sec == 0) {
        hourStr = `${ hour > 9 ? `${ hour }` : `0${ hour }` }:${ min > 9 ? min : `0${ min }` }`;
      }
      // let showTime = this.getBigRulerDate(drawTimestamp); //这里是换算时间的，时或者分
      let lineHeight = Number.parseInt(54 * 0.375);
      let posX = drawX;
      if (hourStr != null) { // 大刻度
        lineHeight = Number.parseInt(54 * 0.625);
        let item = {};
        item.xPos = posX;
        item.hourText = hourStr;
        item.dateText = dateStr;
        this.timeTextList.push(item);
      }
      let posY = (54 - lineHeight) / 2;
      path.moveTo(posX, posY);
      path.lineTo(posX, lineHeight + posY);
      drawX = drawX + this.itemInterval;// 增加一个刻度所需的宽度。 
      drawTimestamp = drawTimestamp + this.intervalTime; // 增加一个刻度之间所需的时间差
    }
    path.close();
    return (
      // 拦截其他view手势
      <View style={{ position: 'absolute', width: this.timelineViewWidth, height: '100%' }} pointerEvents="box-only" >
        <Surface width={this.timelineViewWidth} height={54} >
          <Shape d={path} stroke={this.props.landscape ? '#6b707a' : '#a6b0c3'} strokeWidth={0.5} />
        </Surface>
      </View>
    );
  }
  _renderTimeText() {
    // console.log(TAG, `_renderTimeText: testList = ${this.timeTextList.length}`);
    // const viewWidth = Number.parseInt(this.timelineViewWidth);
    // const centerX = Number.parseInt(this.timelineViewWidth / 2);
    // console.log(TAG, `_renderTimeText: viewWidth = ${viewWidth} ${centerX} ${this.itemInterval}`);
    // const indexCount = Math.ceil(viewWidth / this.itemInterval);
    let lineHeight = Number.parseInt(54 * 0.375);
    let areaHeight = (54 - lineHeight) / 2;
    // console.log(TAG, `_renderTimeText: indexCount = ${indexCount}`);
    // console.log(TAG, `_renderTimeText: areaHeight = ${ areaHeight }`);
    let bottomVal = areaHeight - 10 - 4;
    if (bottomVal < 0) {
      bottomVal = 0;
    }
    return this.timeTextList.map((item, index) => {
      return (
        <Text
          key={index}
          style={{
            position: 'absolute', fontSize: 10, start: item.xPos + 2, bottom: bottomVal,
            color: this.props.landscape ? 'white' : '#808789'
          }} >{item.hourText}</Text>
      );
    });
  }
  _renderDateText() {
    let lineHeight = Number.parseInt(54 * 0.375);
    let areaHeight = (54 - lineHeight) / 2;
    // console.log(TAG, `_renderTimeText: indexCount = ${indexCount}`);
    // console.log(TAG, `_renderTimeText: areaHeight = ${ areaHeight }`);
    let bottomVal = areaHeight - 10 - 4;
    if (bottomVal < 0) {
      bottomVal = 0;
    }
    return this.timeTextList.map((item, index) => {
      if (item.dateText == null) {
        return;
      }
      return (
        <Text
          key={index}
          style={{
            position: 'absolute', fontSize: 10, start: item.xPos + 2, top: bottomVal,
            color: this.props.landscape ? 'white' : '#808789'
          }} >{item.dateText}</Text>
      );
    });
  }
  _renderNormalArea() {
    if (this.dataList == null || this.dataList.length <= 0) {
      return;
    }
    // const viewWidth = Math.ceil(this.timelineViewWidth);
    const centerX = Number.parseInt(this.timelineViewWidth / 2); // 得到一半的长度
    let leftTimestamp = Math.ceil(this.currentTime - centerX * this.oneUnitWidthTime); // 计算出最小时间戳
    let rightTimestamp = Math.ceil(this.currentTime + centerX * this.oneUnitWidthTime); // 计算出最大时间戳
    const path = new Path();
    let drawAreaX = 0; // 记录绘制的位置，过滤绘制重复的区域
    for (let i = 0; i < this.dataList.length; i++) {
      let dataItem = this.dataList[i];
      let startTime = dataItem.startTime;
      let endTime = dataItem.endTime;
      let type = dataItem.motion ? 1 : (dataItem.isSave ? 2 : (dataItem.eventType > 0 ? dataItem.eventType + 2 : 0));
      if (startTime >= leftTimestamp && endTime > leftTimestamp && startTime < rightTimestamp) {
        let drawStartX = (startTime - leftTimestamp) / this.oneUnitWidthTime;
        let drawEndX = (endTime - leftTimestamp) / this.oneUnitWidthTime;
        if (drawAreaX != 0 && drawAreaX >= drawStartX) { // 纠正数据重复的
          drawStartX = drawAreaX;
        }
        if (drawEndX <= drawStartX) {
          continue;
        }
        if (type == this.motionType && VersionUtil.judgeIsV1(Device.model)) { // 暂时先这么做 分开画。  v1区分颜色
          let item = {};
          item.drawStartX = drawStartX;
          item.drawEndX = drawEndX;
          item.type = type;
          this.motionAreaList.push(item);
        } else {
          path.moveTo(drawStartX, 0.5);
          path.lineTo(drawEndX, 0.5);
          path.lineTo(drawEndX, 54 - 0.5);
          path.lineTo(drawStartX, 54 - 0.5);
        }
        drawAreaX = drawEndX;
      } else if (startTime < leftTimestamp && endTime >= leftTimestamp) { // 只有某一部分在屏幕之内的
        let drawStartX = 0;
        let drawEndX = (endTime - leftTimestamp) / this.oneUnitWidthTime;
        if (drawAreaX != 0 && drawAreaX >= drawStartX) {
          drawStartX = drawAreaX;
        }
        if (drawEndX <= drawStartX) {
          continue;
        }
        if (type == this.motionType) { // 暂时没想到如何画两个颜色的区域，保存下来分开画
          let item = {};
          item.drawStartX = drawStartX;
          item.drawEndX = drawEndX;
          item.type = type;
          this.motionAreaList.push(item);
          // console.log(TAG, `_renderNormalArea: push alarm list`);
        } else {
          path.moveTo(drawStartX, 0.5);
          path.lineTo(drawEndX, 0.5);
          path.lineTo(drawEndX, 54 - 0.5);
          path.lineTo(drawStartX, 54 - 0.5);
        }
        drawAreaX = drawEndX;
      } else {
        // console.log(TAG, `_renderNormalArea: not logic ${ startTime } ${ endTime } ${ leftTimestamp } ${ rightTimestamp }`);
      }
    }
    path.close();
    return (
      <Surface style={{ position: "absolute" }} width={this.timelineViewWidth} height={54} >
        <Shape d={path} fill={'#FF5F00'} strokeWidth={1} opacity={0.2} />
      </Surface>
    );
  }
  _renderAlarmArea() {
    if (this.motionAreaList == null || this.motionAreaList.length <= 0) {
      return;
    }
    // console.log(TAG, `_renderAlarmArea: motionAreaList length = ${this.motionAreaList.length}`);
    const path = new Path();
    for (let i = 0; i < this.motionAreaList.length; i++) {
      let drawStartX = this.motionAreaList[i].drawStartX;
      let drawEndX = this.motionAreaList[i].drawEndX;
      path.moveTo(drawStartX, 0.5);
      path.lineTo(drawEndX, 0.5);
      path.lineTo(drawEndX, 54 - 0.5);
      path.lineTo(drawStartX, 54 - 0.5);
    }
    path.close();
    return (
      <Surface style={{ position: "absolute" }} width={this.timelineViewWidth} height={54} >
        <Shape d={path} fill={'#EA751E'} strokeWidth={1} opacity={0.2} />
      </Surface>
    );
  }
  // 弃用，代码保留
  _renderColorArea() {
    if (this.dataList == null || this.dataList.length <= 0) {
      return;
    }
    const viewWidth = Math.ceil(this.timelineViewWidth);
    const centerX = Number.parseInt(this.timelineViewWidth / 2);
    let leftTimestamp = Math.ceil(this.currentTime - centerX * this.oneUnitWidthTime); // 计算出最小时间戳
    let rightTimestamp = Math.ceil(this.currentTime + centerX * this.oneUnitWidthTime); // 计算出最大时间戳
    let data = [];
    console.log(TAG, `_renderColorArea: list length = ${ this.dataList.length }`);
    for (let i = 0; i < this.dataList.length; i++) {
      let dataItem = this.dataList[i];
      let startTime = dataItem.startTime;
      let endTime = dataItem.endTime;
      let type = dataItem.type;
      if (startTime >= leftTimestamp && endTime > leftTimestamp && startTime < rightTimestamp) {
        let drawStartX = Number.parseInt((startTime - leftTimestamp) / this.oneUnitWidthTime);
        let drawEndX = Number.parseInt((endTime - leftTimestamp) / this.oneUnitWidthTime);
        let item = {};
        item.startX = drawStartX;
        item.endX = drawEndX;
        item.type = type;
        data.push(item);
      } else if (startTime < leftTimestamp && endTime >= leftTimestamp) {
        let drawStartX = 0;
        let drawEndX = Math.ceil((endTime - leftTimestamp) / this.oneUnitWidthTime);
        let item = {};
        item.startX = drawStartX;
        item.endX = drawEndX;
        item.type = type;
        data.push(item);
      }
    }
    let drawAreaX = 0; // 记录绘制的位置，过滤绘制重复的区域
    return data.map((item, index) => {
      const path = new Path();
      let startX = item.startX;
      let endX = item.endX;
      let type = item.type;
      let val = endX - startX;
      let width = startX + val;
      if (drawAreaX != 0 && drawAreaX >= startX) { // 重复区域
        startX = drawAreaX;
      }
      path.moveTo(startX, 0);
      path.lineTo(width, 0);
      path.lineTo(width, 54);
      path.lineTo(startX, 54);
      path.close();
      let color = type == 2 ? "#ff0000" : "#6e9fff";
      drawAreaX = width;
      return (
        <Surface key={index} style={{ position: "absolute" }} width={viewWidth} height={54} >
          <Shape d={path} fill={color} strokeWidth={1} opacity={0.5} />
        </Surface>
      );
    });
  }
  
  _renderCenterLine() {
    // console.log(TAG, `_renderCenterLine: `);
    const viewWidth = Number.parseInt(this.timelineViewWidth);
    const centerX = Number.parseInt(this.timelineViewWidth / 2);
    const path = new Path();
    path.moveTo(centerX, 0);
    path.lineTo(centerX, 54 - 8);
    path.moveTo(centerX, 54 - 8);
    path.lineTo(centerX - 4, 54);
    path.lineTo(centerX + 4, 54);
    path.close();
    return (
      <Surface style={{ position: 'absolute' }} width={this.timelineViewWidth} height={54} >
        <Shape d={path} stroke={'#ff6021'} fill={'#ff6021'} strokeWidth={2} opacity={0.8} />
      </Surface>
    );
    // return (
    //   <View style={{ position: 'absolute', justifyContent: 'center', alignItems: 'center', width: this.timelineViewWidth, height: 54 }} >
    //     <Image style={{ width: 12, height: '100%' }} source={localImages['replay_icon_point']} />
    //   </View>
    // );
  }
  setScale(scale) {
    let diffScale = Number(((scale) / 100).toFixed(2));
    let tempScaleFactor = this.curScaleFactor + diffScale;
    if (tempScaleFactor > SCALING_FACTOR_MAX) {
      tempScaleFactor = SCALING_FACTOR_MAX;
    } else if (tempScaleFactor < SCALING_FACTOR_MIN) {
      tempScaleFactor = SCALING_FACTOR_MIN;
    }
    this.curScaleFactor = tempScaleFactor;
    this.itemInterval = this.initItemInterval * tempScaleFactor;
    this.oneUnitWidthTime = this.intervalTime / this.itemInterval; // 一个宽度占多少毫秒
    this.setState({});
  }

  initPanResponder() {

    let startX = 0;
    let lastScale = 0;
    let isMulti = false;

    this.panResponder = PanResponder.create({
      // 单机手势是否可以成为响应者
      onStartShouldSetPanResponder: (evt, gestureState) => true,
      // 移动手势是否可以成为响应者
      onMoveShouldSetPanResponder: (evt, gestureState) => true,
      // 拦截子组件的单击手势传递,是否拦截
      onStartShouldSetPanResponderCapture: (evt, gestureState) => true,
      // 拦截子组件的移动手势传递,是否拦截
      onMoveShouldSetPanResponderCapture: (evt, gestureState) => true,
      /** *************** 响应者事件回调处理 **************** */
      // 单击手势监听回调
      onPanResponderGrant: (e, gestureState) => {
        console.log(TAG, 'onPanResponderGrant==>' + '单击手势申请成功,开始处理手势');
        // console.log(TAG, "onPanResponderGrant: " + e.nativeEvent.locationX + " " + e.nativeEvent.locationY 
        // + " " + gestureState.dx + " " + gestureState.dy 
        // + " " + gestureState.vx + " " + gestureState.vy);
        isMulti = false;
        startX = 0;
        lastScale = 0;
        this.scrollState = SCROLLSTATE.IDLE;
        this.endTimeout && clearTimeout(this.endTimeout);
      },
      // 移动手势监听回调
      onPanResponderMove: (e, gestureState) => {
        this.scrollState = SCROLLSTATE.SCROLLING;
        // console.log(TAG, 'onPanResponderMove==>' + '移动手势申请成功,开始处理手势');
        // console.log(TAG, `onPanResponderMove: down length = ${e.nativeEvent.changedTouches.length}`);
        if (e.nativeEvent.changedTouches.length > 1) {
          isMulti = true;
          let value = Math.abs(e.nativeEvent.changedTouches[1].locationX - e.nativeEvent.changedTouches[0].locationX);
          if (lastScale != 0) {
            let scale = Number.parseInt(value - lastScale);
            this.setScale(scale);
          }
          lastScale = value;
        } else {
          if (!isMulti) {
            this.currentTime = this.currentTime - Math.ceil((gestureState.dx - startX) * this.oneUnitWidthTime);
            startX = gestureState.dx;
            // this.setState({});
            // this.notifyChangeTime(true, gestureState.dx < 0);
            this.scrollToTimestampWithNotify(this.currentTime, false);
          }
        }
      },
      // 手势动作结束回调
      onPanResponderEnd: (e, gestureState) => {
        console.log(TAG, 'onPanResponderEnd==>' + '手势操作完成了,用户离开');
        this.isScrolling = false;
        this.endTimeout && clearTimeout(this.endTimeout);
        this.endTimeout = setTimeout(() => {
          this.scrollState = SCROLLSTATE.IDLE;
          this.scrollToTimestampWithNotify(this.currentTime, true);
        }, 1200);
      },
      // 手势释放, 响应者释放回调
      onPanResponderRelease: (e) => {
        // 用户放开了所有的触摸点，且此时视图已经成为了响应者。
        // 一般来说这意味着一个手势操作已经成功完成。
        // console.log(TAG, 'onPanResponderRelease==>' + '放开了触摸,手势结束');
        // let year = this.dateTime.getFullYear();
        // let month = this.dateTime.getMonth() + 1;
        // let day = this.dateTime.getDate();
        // let hour = this.dateTime.getHours();
        // let min = this.dateTime.getMinutes();
        // let sec = this.dateTime.getSeconds();
        // if (month < 10) {
        //   month = `0${ month }`;
        // }
        // if (day < 10) {
        //   day = `0${ day }`;
        // }
        // if (hour < 10) {
        //   hour = `0${ hour }`;
        // }
        // if (min < 10) {
        //   min = `0${ min }`;
        // }
        // if (sec < 10) {
        //   sec = `0${ sec }`;
        // }
        // console.log(TAG, `onPanResponderRelease: date = ${ year }-${ month }-${ day } ${ hour }:${ min }:${ sec }`);
      },
      // 手势申请失败,未成为响应者的回调
      onResponderReject: (e) => {
        // 申请失败,其他组件未释放响应者
        console.log(TAG, 'onResponderReject==>' + '响应者申请失败');
      },
      // 当前手势被强制取消的回调
      onPanResponderTerminate: (e) => {
        if (this.props.isDisabled) {
          return;
        }
        // console.log(TAG, 'onPanResponderEnd==>' + '手势操作完成了,用户离开');
        this.isScrolling = false;
        // if (!isMulti) {

        // this.pan.setValue(0);

        this.endTimeout && clearTimeout(this.endTimeout);
        this.endTimeout = setTimeout(() => {
          this.scrollState = SCROLLSTATE.IDLE;
          this.scrollToTimestampWithNotify(this.state.currentTime, true);
        }, 1200);
        // 另一个组件已经成为了新的响应者，所以当前手势将被取消
        console.log(TAG, 'onPanResponderTerminate==>' + '由于某些原因(系统等)，所以当前手势将被取消');
      },
      onShouldBlockNativeResponder: (evt, gestureState) => {
        // 返回一个布尔值，决定当前组件是否应该阻止原生组件成为JS响应者
        // 默认返回true。目前暂时只支持android。
        return true;
      }
    });
  }
}

export const styles = StyleSheet.create({
  container: {
    backgroundColor: "#ffffff",
    width: '100%',
    height: '100%',
    display: "flex",
    flexWrap: "nowrap"
  },

  dayItemSelected: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    height: 20,
    width: 50,
    marginHorizontal: 7,
    paddingHorizontal: 10,
    backgroundColor: "#32bac0",
    borderRadius: 10
  },

  dayItem: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    height: 20,
    width: 50,
    marginHorizontal: 7,
    paddingHorizontal: 10,
    backgroundColor: "#eceef0",
    borderRadius: 10
  },

  dayItemText: {
    color: "#b2b2b2", fontSize: 9
  },

  dayItemTextSelected: {
    color: "#ffffff", fontSize: 9
  }
});