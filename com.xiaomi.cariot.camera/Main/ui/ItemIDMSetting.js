import React from 'react';
import PropTypes from 'prop-types';
import { View, Text, StyleSheet, Image, TouchableWithoutFeedback } from 'react-native';
import Checkbox from 'miot/ui/Checkbox';
import Switch from 'miot/ui/Switch';
import Radio from 'miot/ui/Radio';
export default class ItemLongTimeAlarm extends React.Component {
  static propTypes = {
    style: PropTypes.object,
    onPress: PropTypes.func,
    onCheckChange: PropTypes.func,
    onRadioChange: PropTypes.func,
    title: PropTypes.string,
    sub_title: PropTypes.string,
    isChecked: PropTypes.bool,
    isSingle: PropTypes.bool,
    isSwitch: PropTypes.bool,
    id: PropTypes.string,
    icon: PropTypes.object
  };

  static defaultProps = {
    style: {},
    isSingle: false,
    isChecked: false,
    icon: require("../../Resources/Images/icon_right_anchor_black.png")
  };

  constructor(props, context) {
    super(props, context);
  }

  render() {
    return (
      <TouchableWithoutFeedback
        onPress={() => { 
          if (this.props.onPress) {
            this.props.onPress(this.props.id); 
          }
        }}>
        <View style={[this.props.style, { borderRadius: 18, marginStart: 10, marginRight: 10, display: "flex", flexDirection: "column", justifyContent: "center", height: 75, backgroundColor: "#ffffff" }]}>
          <View style={{ display: "flex", flexDirection: "row", alignItems: "center", paddingRight: 20, width: "100%" }}>
            <Image
              style={{ width: 40, height: 40, marginStart: 20 }}
              source={this.props.icon}
            />
            <View style={{ display: "flex", flexDirection: "column", flexGrow: 1, paddingLeft: 20, paddingRight: 15, width: "70%" }}>
              <Text style={{ fontSize: 14 }}>{this.props.title}</Text>
              {this.props.sub_title ? <Text style={{ color: "#999999", fontSize: 11 }}>{this.props.sub_title}</Text> : null }
            </View>
            { this.props.isSwitch ?
              <Switch
                value={this.props.isChecked}
                disabled={false}
                onValueChange={(checked) => {
                  if (this.props.onPress) {
                    this.props.onPress(checked); 
                  }
                }}
              /> :
              !this.props.isSingle ?
                <Checkbox
                  style={{ width: 21, height: 21, borderRadius: 20 }}
                  checked={this.props.isChecked}
                  onValueChange={(checked) => {
                    if (this.props.onPress) {
                      this.props.onPress(checked); 
                    }
                  }}
                /> :
                <Radio
                  style={{ width: 21, height: 21 }}
                  isChecked={this.props.isChecked}
                  changeCheck={() => {
                    if (this.props.onPress) {
                      this.props.onPress(this.props.id); 
                    }
                  }}
                />}
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  }
}