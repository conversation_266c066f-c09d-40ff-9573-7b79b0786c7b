import * as React from 'react';
import { NavigationState } from 'react-navigation';
import {
  StyleSheet,
  Dimensions,
  Text,
  View,
  TouchableOpacity,
  Platform,
  StatusBar,
  Image, DeviceEventEmitter
} from 'react-native';
import StackNavigationInstance, { SD_CLOUD_FORCE_REFRESH_TAB } from '../StackNavigationInstance';
import { DarkMode } from 'miot/Device';
import Util from "../util2/Util";
import { DescriptionConstants } from '../Constants';
import StatusBarUtil from '../util/StatusBarUtil';


// import { BackgroundContainer } from '../components';

// enum PressTypes {
//   IN = 'in',
//   OUT = 'out',
// }

const screenWidth = Dimensions.get('window').width;
const defaultTabBarHeight = 49;
const statusBarHeight = _getStatusBarHeight();

function _getStatusBarHeight() {
  let statusBarHeight = 0;
  if (Platform.OS == 'android') {
    statusBarHeight = StatusBar.currentHeight || 0;
  }
  // } else {
  //   statusBarHeight = getStatusBarHeight();
  // }
  return StatusBarUtil._getInset("top");
}

export default class TabBarComponent extends React.Component {
  static defaultProps = {
    onPressInScale: 1.3,
    onPressOutScale: 1,
    allowFontScaling: true,
    defaultFlexValue: 1,
    activeFlexValue: 2,
    duration: 200,
    tabBarHeight: defaultTabBarHeight
  };

  itemWidth = 0;


  constructor(props) {
    super(props);

    const { navigation, onTabPress, activeFlexValue, defaultFlexValue } = props;
    const { state } = navigation;
    const { routes } = state;
    this.itemWidth = screenWidth / (props.navigation.state.routes.length + (activeFlexValue - defaultFlexValue));
    this.forceRefreshTablistener = DeviceEventEmitter.addListener(SD_CLOUD_FORCE_REFRESH_TAB, (data) => {
      // console.log("SD_CLOUD_FORCE_REFRESH_TAB data=", JSON.stringify(data));
      if (data && data.gotoPage) {
        let route = null;
        for (let item of routes) {
          if (item.key == data.gotoPage) {
            route = item;
            break;
          }
        }
        onTabPress({ route });
      } else {
        this.forceUpdate();
      }
    });
  }

  componentWillUnmount() {
    this.forceRefreshTablistener && this.forceRefreshTablistener.remove();
    this.forceRefreshTablistener = null;
  }

  shouldComponentUpdate(nextProps) {
    return nextProps.navigation.state.index !== this.props.navigation.state.index;
  }


  onPress = ({ index, type }) => {
    const { onPressInScale, onPressOutScale, navigation } = this.props;
    const { state } = navigation;

    if (state.index === index) {
      return;
    }

  };



  renderLabel = ({ index, focused, route }) => {
    const { getLabelText, navigation, activeTintColor, inactiveTintColor, allowFontScaling, labelStyle } = this.props;
    const { state } = navigation;
    const { routes } = state;

    const color = focused ? inactiveTintColor : activeTintColor;
    let text = getLabelText({ route });
    console.log(text);

    return (
      <Text
        style={[
          styles.text,
          { color: color, paddingVertical: 4, fontWeight: 'bold' },
          index == 0 ? { paddingRight: 10 } : { paddingLeft: 10}
        ]}
      >
        {text + " "}
      </Text>
    );
  };

  renderIcon = (props) => {
    const { renderIcon } = this.props;

    if (!renderIcon) {
      return null;
    }
    let { index, route, focused } = props;

    return (
      <View
        style={[
          styles.icon
        ]}
      >
        {renderIcon(props)}
      </View>
    );
  };


  render() {
    const { navigation, onTabPress, style, tabBarHeight } = this.props;
    const { state } = navigation;
    const { routes } = state;
    let isDark = DarkMode.getColorScheme() == "dark";
    return (
      <View style={[styles.container, style, { justifyContent: "center" }]}>
        <View
          style={{ height: "100%", display: "flex", alignItems: "center", flexDirection: "row" }}
        >

          {routes.map((route, key) => {
            console.log(route);
            const focused = key === state.index;
            return (
              <TouchableOpacity
                style={{ display: "flex", flexDirection: "row" }}
                delayPressIn={200}
                onPress={() => onTabPress({ route })}
                key={key}
              >
                {this.renderIcon({ index: key, route, focused })}
                {this.renderLabel({ index: key, route, focused })}
              </TouchableOpacity>
            );
          })}
        </View>

        <TouchableOpacity style={{ position: "absolute", height: "100%", width: 63, paddingLeft: 9, paddingRight: 14, display: "flex", justifyContent: "center", alignItems: "center", top: statusBarHeight, left: 0 }}
          onPress={() => {
            console.log("on press back button");
            StackNavigationInstance.goBack_ForSDcardCloudPage();
          }}
        >
          <Image
            style={{ width: 40, height: 40 }}
            source={!isDark ? require("../../Resources/Images/icon_back_black.png") : require("../../Resources/Images/icon_back_black_nor_dark.png")}
            accessibilityLabel={DescriptionConstants.zb_29}
          >
          </Image>
        </TouchableOpacity>
      </View>

    );
  }
}


const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    position: 'relative',
    flexDirection: 'row',
    display: "flex",
    width: "100%",
    height: 52 + statusBarHeight,
    paddingTop: statusBarHeight
  },
  tabBarContainer: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 5
  },
  tabBarContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 5
  },
  animatedBackground: {
    position: 'absolute',
    top: 0,
    left: 0
  },
  icon: {
    marginLeft: 10
  },
  text: {
    textAlign: 'center',
    fontSize: Util.isLanguageCN() ? 18 : 14,
    maxWidth: screenWidth * 0.5
  }
});