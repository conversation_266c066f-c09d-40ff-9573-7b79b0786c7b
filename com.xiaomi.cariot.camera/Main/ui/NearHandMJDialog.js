import React from 'react';
import {
  Dimensions,
  Modal,
  Platform,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
  TouchableHighlight
} from 'react-native';

import { Radius, PluginWindow, styles, Font } from 'micariot-ui-sdk';
import { ConfigContext } from "mhui-rn/dist/components/configProvider";
import DynamicColor from "mhui-rn/dist/styles/DynamicColor";

const {
  width,
  height
} = Dimensions.get('window');
const underlayColor = 'rgba(0,0,0,.05)';

/**
 * 按钮
 * @typedef {Object} Button
 * @property {string} text - 按钮的文字
 * @property {style} style - 按钮的文字样式 10045-此处不允许修改文字颜色
 * @param {bool} allowFontScaling - 10040新增 text是否支持大字体显示，即是否随系统字体大小变化而变化, 默认`true`
 * @param {number} numberOfLines - 10040新增 text文字的行数，默认 undefined (兼容旧版)  10044更新:按钮文字最多显示一行
 * @property {function} callback - 点击按钮的回调函数
 * @param {string} colorType - 10045新增 按钮的颜色类型,只在regular和medium按钮上使用 'blueLayerWhite' | 'grayLayerBlack' | 'grayLayerBlue'
 * @param {boolean} disabled - 10045新增 设为true，禁止交互。
 * @param {object} backgroundColor - 10045新增 自定义按钮背景颜色 { bgColorNormal: string; bgColorPressed: string }
 * @param {string} titleColor - 10045新增 文字颜色
 */

/**
 * @export
 * <AUTHOR>
 * @since 10021
 * @module AbstractDialog
 * @description 通用弹窗容器，包括头部标题和底部按钮，内容自定义 *[若其他弹窗能够满足业务需求，则尽量不要使用AbstractDialog]*
 * @param {string} animationType - modal 显示动效, 默认`'fade'`，参考 https://facebook.github.io/react-native/docs/0.54/modal#animationtype
 * @param {bool} visible - 是否显示 modal, 默认`false`，参考 https://facebook.github.io/react-native/docs/0.54/modal#visible
 * @param {style} style - modal 的自定义样式
 * @param {string} title - 标题
 * @param {string} subtitle - 副标题
 * @param {bool} showTitle - 是否显示标题，如果`false`，整个标题都不显示（包括副标题），默认`true`
 * @param {bool} showSubtitle - 是否显示副标题，默认`false`
 * @param {bool} canDismiss - 是否允许点击蒙层背景隐藏 Modal，默认`true`
 * @param {Button[]} buttons - 按钮数组，定义底部按钮的属性，只能显示1～2个按钮，多传将失效。默认左取消右确定，左灰右绿，点击回调都是隐藏 Modal
 * @param {bool} showButton - 是否显示按钮，默认`true`
 * @param {Object} dialogStyle - 10040新增 控制dialog 一些特有的样式
 * @param {bool} dialogStyle.unlimitedHeightEnable - 10040新增 设置控件高度是否自适应。 默认为false，即默认高度
 * @param {bool} dialogStyle.allowFontScaling - 10040新增 dialog中text是否支持大字体显示，即是否随系统字体大小变化而变化, 默认`true`
 * @param {number} dialogStyle.titleNumberOfLines - 10040新增 控制title 文字的行数， 默认 1行
 * @param {number} dialogStyle.subTitleNumberOfLines - 10040新增 控制subTitle 文字的行数，默认 1行
 * @param {ViewPropTypes.style} dialogStyle.titleStyle - 10040新增 控制title 文字的样式
 * @param {ViewPropTypes.style} dialogStyle.subTitleStyle - 10040新增 控制subTitle 文字的样式
 * @param {function} onDismiss - 点击`Modal`内容外面/取消按钮/确定按钮，Modal隐藏时的回调函数
 * @param {boolean} useNewTheme - 10045新增 是否使用新样式，默认false  10045后 *!必须!* 使用新样式 旧样式将被废弃
 * @param {bool} hasShade - 是否有遮罩层，默认`true`
 * @param {function} onModalHide - 对话框关闭后的回调。onDismiss只会在onRequestClose和button的callback未定义时触发
 * @param {function} onModalShow - 对话框打开后的回调
 */
class NearHandMJDialog extends React.Component {
  static defaultProps = {
    animationType: 'fade',
    visible: false,
    cancelable: true,
    showTitle: true,
    showSubtitle: false,
    dialogStyle: {
      unlimitedHeightEnable: false,
      allowFontScaling: true,
      titleNumberOfLines: 1,
      subTitleNumberOfLines: 1,
      titleStyle: {},
      subTitleStyle: {}
    },
    canDismiss: true,
    buttons: null,
    showButton: true,
    hasShade: true,
    useNewTheme: false
  };

  constructor(props, context) {
    super(props, context);
    this.state = {
      visible: this.props.visible
    };
    this.MARGIN_MODAL = this.props.useNewTheme ? 0 : 10;
    this.responsiveContentStyle = this.context.media?.screenType === 'tablet' ? {
      width: width * 0.75
    } : {};
    this.responsiveDialogStyle = this.context.media?.screenType === 'tablet' ? {
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'column'
    } : {};
    this.responsiveDialogStyle = {
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'column'
    };
  }

  componentDidMount() {
    if (this.state.visible === true) {
      const {
        onModalShow
      } = this.props;

      if (typeof onModalShow === 'function') {
        onModalShow();
      }
    }
  }

  UNSAFE_componentWillReceiveProps(newProps) {
    if (newProps.visible !== this.state.visible) {
      this.setState({
        visible: newProps.visible
      });
    }
  }

  componentDidUpdate(prevProps, prevState) {
    if (prevState.visible === false && this.state.visible === true) {
      const {
        onModalShow
      } = this.props;

      if (typeof onModalShow === 'function') {
        onModalShow();
      }
    }

    if (prevState.visible === true && this.state.visible === false) {
      const {
        onModalHide
      } = this.props;

      if (typeof onModalHide === 'function') {
        onModalHide();
      }
    }
  }

  /**
   * 判断 控件高度是否自适应，  true： 自适应，高度不固定， false： 高度固定
   * @private
   */

  _checkUnlimitedHeightEnable() {
    let result = false;

    if (this.props.dialogStyle && this.props.dialogStyle.hasOwnProperty('unlimitedHeightEnable')) {
      result = this.props.dialogStyle.unlimitedHeightEnable;
    }

    return result;
  }

  /**
   * 中间内容
   */


  renderContent() {
    if (this.props.children) return this.props.children;
    let messageNumberOfLines = 15;

    if (this.props.dialogStyle && this.props.dialogStyle.hasOwnProperty('messageNumberOfLines')) {
      messageNumberOfLines = this.props.dialogStyle.messageNumberOfLines;
    }
    return <View>
      <View style={ [stylesDialog.content] }>
        <Text
          numberOfLines={ messageNumberOfLines } allowFontScaling={ this.props.dialogStyle.allowFontScaling }
          style={ [stylesDialog.message, {
            color: styles.buttonTextStyle.color,
            fontSize: Font.Size._28
          }, this.props.messageStyle] }>
          { this.props.message || '' }
        </Text>
      </View>
    </View>;
  }

  render() {

    let width, height;

    width = Dimensions.get('window').width;
    height = Dimensions.get('window').height;
    return <Modal animationType={ this.props.animationType } transparent visible={ this.state.visible }
                  onRequestClose={ () => this.dismiss() }>
      <View style={ [{
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.4)'
      }, this.responsiveDialogStyle] }>
        <TouchableWithoutFeedback onPress={ () => this.layerDismiss() }>
          <View style={ {
            width: '100%',
            height: '100%'
          } }/>
        </TouchableWithoutFeedback>

        <View style={ [{
          position: 'absolute',
          bottom: null,
          borderRadius: 20,
        }, this.props.style] }>
          { this.renderContent() }
        </View>
      </View>
    </Modal>;
  }

  /**
   * 隐藏 Modal
   */
  dismiss() {
    if (this.props.cancelable) {
      this.setState({
        visible: false
      });
      this.props.onDismiss && this.props.onDismiss();
    }
  }

  layerDismiss() {
    if (this.props.canDismiss) {
      this.setState({
        visible: false
      });
      this.props.onDismiss && this.props.onDismiss();
    }
  }

}

const stylesDialog = StyleSheet.create({
  titleContainer: {
    justifyContent: 'center',
    alignItems: 'center'
  },
  content: {
    width: width - 10 * 2,
    // height: 150,
    paddingTop: 24,
    paddingBottom: 56,
    justifyContent: 'center',
    alignItems: 'center'
  },
  modalDeprecated: {
    bottom: 20,
    paddingBottom: 0,
    borderRadius: 20
  },
  buttons: {
    height: 46,
    // 底部按钮的高度
    flexDirection: 'row',
    backgroundColor: 'transparent',
    justifyContent: 'center',
    // 'space-around',
    alignItems: 'center',
    flex: 1
  }
});
export default NearHandMJDialog;