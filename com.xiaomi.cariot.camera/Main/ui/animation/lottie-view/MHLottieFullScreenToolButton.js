import React from "react";
import { Animated, Easing } from 'react-native';
import MHLott<PERSON>BaseButton from "./base/MHLottieBaseButton";

/**
 * 创建自己的行为枚举值
 * @type  { enum } 
 */
export const MHLottieFullScreenToolBtnDisplayState = {
  NORMAL: "NORMAL"
};

export default class MHLottieFullScreenToolButton extends MHLottieBaseButton {

  /**
   * 创建自己的资源文件
   * @type  { object } 
   */
  static JSONFiles = {
    normal: {
      light: require("../lottie-json/btn_fullscreen.json")
    }
  };

  /**
   * 创建自己的基于行为枚举值的链式动画
   * @param  { enum } displayState
   * @param  { bool } transition
   */
  switchDisplayState(displayState, transition) {

    if (displayState == MHLottieFullScreenToolBtnDisplayState.NORMAL) {

      if (transition) {
        this.setState({
          file: MHLottieFullScreenToolButton.JSONFiles.normal,
          loop: false
        });
  
        this.progress.setValue(0);
        Animated.timing(
          this.progress,
          {
            toValue: 1,
            duration: transition ? 1000 : 1
          }
        )
          .start();
      } else {
        this.setState({
          file: MHLottieFullScreenToolButton.JSONFiles.normal,
          loop: false
        });
  
        this.progress.setValue(0);
      }
    }
  }

  constructor(props) {
    super(props);

    this.replayAnimationWhenPress = true;
    this.replayAnimationWhenFocus = false;
  }

  componentWillUnmount() {
    super.componentWillUnmount();
  }

  componentDidMount() {
    super.componentDidMount();
  }

  componentDidUpdate(prevProps) {
    super.componentDidUpdate(prevProps);
  }
}
