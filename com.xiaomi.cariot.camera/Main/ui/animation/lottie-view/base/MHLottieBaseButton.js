import React from "react";
import { Animated, Easing } from 'react-native';
import MHLott<PERSON>Button from "./MHLottieButton";

export default class MHLottieBaseButton extends React.Component {

  /**
   * 创建自己的资源文件
   * 由子类实现
   * @type  { object } 
   */
  static JSONFiles = {
    
  };

  /**
   * 创建自己的基于行为枚举值的链式动画
   * 由子类实现
   * @param  { enum } displayState
   */
  switchDisplayState(displayState, transition) {

    // 示例代码
    // if(state == ?){
    //  
    // this.setState({
    //   file: MHLottieAudioToolButton.JSONFiles.normal,
    //   loop: false,
    // });

    // 第三方库存在BUG，不要在初始状态调用这句代码，动画控件会消失
    // this.progress.setValue(1);

    // 取而代之在下面标记 from: 1
    // Animated.timing(
    //   this.progress,
    //   {
    //       from: 1(可选),
    //       toValue: 0,
    //       duration: transition ? 1000 : 1,
    //   }
    // )
    // .start();
    //
    // }

  }

  constructor(props) {
    super(props);

    this.state = {
      file: null,
      loop: false,
      speed: 1
    };

    this.replayAnimationWhenPress = false;
    this.replayAnimationWhenFocus = false;

    this.lottieButton = null;
    this.progress = new Animated.Value(0);
  }

  componentWillUnmount() {

  }

  componentDidMount() {
    this.switchDisplayState(this.props.displayState, false);
  }

  componentDidUpdate(prevProps) {
    if (this.props.displayState != prevProps.displayState) {
      this.switchDisplayState(this.props.displayState, true);
    }
  }

  render() {

    return (
      <MHLottieButton
        ref={(lottieButton) => {
          this.lottieButton = lottieButton;
        }}

        style={ Object.assign({}, this.props.style, this.props.disabled ? { opacity: 0.3 } : { opacity: 1 }) }
        darkMode={this.props.darkMode}
        accessibilityState={this.props.accessibilityState}
        accessibilityLabel={this.props.accessibilityLabel}
        accessibilityValue={this.props.accessibilityValue}
        label={this.props.label}
        description={this.props.description}

        file={this.state.file}
        loop={this.state.loop}
        speed={this.state.speed}

        disabled={this.props.disabled}
        
        onPress={() => {
          if (this.props.disabled) {
            return;
          }
          this._onPress();
        }}
        onFocus={() => {
          this._onFocus();
        }}

        displayState={this.props.displayState}
        progress={this.progress}
      />
    );
  }

  replayAnimation() {
    this.switchDisplayState(this.props.displayState, true);
  }

  _onPress() {
    if (this.replayAnimationWhenPress) {
      this.replayAnimation();
    }
    this.props.onPress();
  }

  _onFocus() {
    if (this.replayAnimationWhenFocus) {
      this.replayAnimation();
    }
    this.props.onFocus();
  }
}
