import React from "react";
import { Animated, Easing } from 'react-native';
import MHLott<PERSON>BaseButton from "./base/MHLottieBaseButton";

/**
 * 创建自己的行为枚举值
 * @type  { enum } 
 */
export const MHLottieHdrToolBtnDisplayState = {
  AUTO: "AUTO",
  CLOSE: "CLOSE",
  OPEN: "OPEN"
};

export default class MHLottieHdrToolButton extends MHLottieBaseButton {

  /**
   * 创建自己的资源文件
   * @type  { object } 
   */
  static JSONFiles = {
    auto: {
      light: require("../lottie-json/btn_hdr_auto.json")
    },
    auto_landscape: {
      light: require("../lottie-json/btn_hdr_auto_landscape.json")
    },
    close: {
      light: require("../lottie-json/btn_hdr_off.json")
    },
    close_landscape: {
      light: require("../lottie-json/btn_hdr_off_landscape.json")
    },
    open: {
      light: require("../lottie-json/btn_hdr_on.json")
    },
    open_landscape: {
      light: require("../lottie-json/btn_hdr_on_landscape.json")
    }
  };

  /**
   * 创建自己的基于行为枚举值的链式动画
   * @param  { enum } displayState
   * @param  { bool } transition
   */
  switchDisplayState(displayState, transition) {

    transition = false;

    if (displayState == MHLottieHdrToolBtnDisplayState.AUTO) {
        
      this.setState({
        file: this.props.landscape ? MHLottieHdrToolButton.JSONFiles.auto_landscape : MHLottieHdrToolButton.JSONFiles.auto,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();

    } else if (displayState == MHLottieHdrToolBtnDisplayState.CLOSE) {
      
      this.setState({
        file: this.props.landscape ? MHLottieHdrToolButton.JSONFiles.close_landscape : MHLottieHdrToolButton.JSONFiles.close,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();

    } else if (displayState == MHLottieHdrToolBtnDisplayState.OPEN) {
      
      this.setState({
        file: this.props.landscape ? MHLottieHdrToolButton.JSONFiles.open_landscape : MHLottieHdrToolButton.JSONFiles.open,
        loop: false
      });
  
      this.progress.setValue(0);
      Animated.timing(
        this.progress,
        {
          toValue: 1,
          duration: transition ? 1000 : 1
        }
      )
        .start();

    }
  }

  constructor(props) {
    super(props);
  }

  componentWillUnmount() {
    super.componentWillUnmount();
  }

  componentDidMount() {
    super.componentDidMount();
  }

  componentDidUpdate(prevProps) {
    super.componentDidUpdate(prevProps);
  }
}
