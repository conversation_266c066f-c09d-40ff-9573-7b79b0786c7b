import React from "react";
import { Animated, Easing } from 'react-native';
import MHLott<PERSON>B<PERSON>Button from "./base/MHLottieBaseButton";
import { PackageEvent } from "miot";

/**
 * 创建自己的行为枚举值
 * @type  { enum } 
 */
export const MHLottieVoiceBtnDisplayState = {
  NORMAL: "NORMAL",
  CHATTING: "CHATTING"
};

export default class MHLottieVoiceButton extends MHLottieBaseButton {

  /**
   * 创建自己的资源文件
   * @type  { object } 
   */
  static JSONFiles = {
    normal: {
      light: require("../lottie-json/btn_voice.json"),
      dark: require("../lottie-json/btn_voice_dark.json")
    },
    chatting: {
      light: require("../lottie-json/btn_voice_chatting.json")
    }
  };

  /**
   * 创建自己的基于行为枚举值的链式动画
   * @param  { enum } displayState
   * @param  { bool } transition
   */
  switchDisplayState(displayState, transition) {

    if (displayState == MHLottieVoiceBtnDisplayState.NORMAL) {
      if (transition) {
        this.setState({
          file: MHLottieVoiceButton.JSONFiles.normal,
          loop: false
        });
      
        Animated.timing(
          this.progress,
          {
            from: 1, 
            toValue: 0,
            duration: 700
          }
        )
          .start(
            
          );
      } else {
        this.setState({
          file: MHLottieVoiceButton.JSONFiles.normal,
          loop: false
        });
      }
    } else if (displayState == MHLottieVoiceBtnDisplayState.CHATTING) {
      if (transition) {
        this.setState({
          file: MHLottieVoiceButton.JSONFiles.normal,
          loop: false
        });
        
        this.progress.setValue(0);
        Animated.timing(
          this.progress,
          {
            toValue: 1,
            duration: 700
          }
        )
          .start(
            () => {
              this.setState({
                file: MHLottieVoiceButton.JSONFiles.chatting,
                loop: true
              });
        
              this.lottieButton && this.lottieButton.playAnimation();
            }
          );
      } else {
        this.setState({
          file: MHLottieVoiceButton.JSONFiles.chatting,
          loop: true
        });
    
        this.lottieButton && this.lottieButton.playAnimation();
      }
    }
  }

  constructor(props) {
    super(props);

    // this.state = {
    //   file: MHLottieVoiceButton.JSONFiles.normal,
    // };
  }

  componentWillUnmount() {
    this.didResumeListener && this.didResumeListener.remove();
    super.componentWillUnmount();
  }

  componentDidMount() {
    super.componentDidMount();
    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      this.progress && this.progress.setValue && this.progress.setValue(0);
      this.switchDisplayState(MHLottieVoiceBtnDisplayState.NORMAL, false);
      this.lottieButton && this.lottieButton.resetAnimation && this.lottieButton.resetAnimation();
    });
  }

  componentDidUpdate(prevProps) {
    super.componentDidUpdate(prevProps);
  }

}
