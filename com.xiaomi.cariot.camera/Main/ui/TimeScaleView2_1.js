'use strict';

import React from 'react';
import { View, Image, Text, ART, Dimensions, PanResponder, TouchableOpacity, FlatList, StyleSheet, Animated, BackHandler, DeviceEventEmitter } from 'react-native';
import PropTypes from 'prop-types';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import Host from 'miot/Host';
import EventTypeConfig, { EVENT_TYPE, EVENT_TYPE_COLOR, TIMELINE_EVENT_TYPE } from '../sdcard/util/EventTypeConfig';
import CloudVideoUtil from '../sdcard/util/CloudVideoUtil';
import { PixelRatio } from 'react-native';
import { DescriptionConstants } from '../Constants';
import TrackUtil from '../util/TrackUtil';
import { DarkMode } from 'miot';
import Util, { areaColors } from '../util2/Util'
import dayjs from 'dayjs';
import { ImageButton } from 'mhui-rn';
import { aiColor, areaMoveColor, babyCryColor, cameraCallingColor, defaultColor, faceColor, fencePassColor, louderSoundColor, peopleCoughColor, peopleMoveColor, petColor } from '../util2/Util';
import EventTypeUtil from '../sdcard/util/EventTypeUtil';
import { TileLineScaleChangeEvent } from './TimeScaleView3';
import Toast from "../components/Toast";

const kIsCN = Util.isLanguageCN();
const TAG = "TimeScaleView2_1";
const { Surface, Shape, Path } = ART;

const Scale_Type_Max = 1;
const Scale_Type_Big = 2;
const Scale_Type_Middle = 3;
const Scale_Type_Small = 4;
const Scale_Type_Small_Min = 5;

const SCALING_FACTOR_MAX = 1.5; // 最大放大参数
const SCALING_FACTOR_MIN = 1; // 最小缩小参数

const DAY_ITEM_WIDTH = 49;

const WEEKS = [LocalizedStrings["sun"], LocalizedStrings["mon"], LocalizedStrings["tue"], LocalizedStrings["wed"], LocalizedStrings["thu"], LocalizedStrings["fri"], LocalizedStrings["sat"]];
const WEEKENDS = [LocalizedStrings["sunday1"], LocalizedStrings["monday1"], LocalizedStrings["tuesday1"], LocalizedStrings["wednesday1"], LocalizedStrings["thursday1"], LocalizedStrings["friday1"], LocalizedStrings["saturday1"]];
const TODAY = LocalizedStrings["today"];
const SCROLLSTATE = {
  IDLE: 0,
  SCROLLING: 1,
  SCALING: 2
};

const drawDefault = 1;
const drawAreaMove = 1 << 1;
const drawPeopleMove = 1 << 2;
const drawBabyCry = 1 << 3;
const drawAi = 1 << 4;
const drawFace = 1 << 5;
const kWindowHeight = Math.max(Dimensions.get("screen").height, Dimensions.get("screen").width);
const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width);

export default class TimeScaleView2_1 extends React.Component {

  constructor(props) {
    super(props);
    this.date = new Date();
    this.drawWidth = 0;
    this.initItemInterval = 60;
    this.scaleType = Scale_Type_Small;
    this.preScaleType = 0;
    this.intervalCount = 6; // 多少个小刻度间隔一个大刻度
    this.itemTime = 30 * 60 * 1000; // 大刻度之间间隔时间 毫秒
    this.itemInterval = this.initItemInterval; // 刻度间隔宽度
    this.intervalTime = this.itemTime / this.intervalCount; // 每一个格子间隔时间 毫秒
    this.oneUnitWidthTime = this.intervalTime / this.itemInterval; // 一个宽度占多少毫秒
    this.panResponder = null;
    this.dateTime = new Date();
    this.dataList = [];
    this.isDrawArea = false;
    this.isLeftPressed = false;
    this.isRightPressed = false;
    this.drawColorX = 0;
    this.isScrolling = false;
    this.timeTextList = [];
    this.motionAreaList = [];
    this.isNeedInit = true;
    this.motionType = 1;
    this.selectedDayTime = new Date().getTime();

    this.timelineViewWidth = 0;
    this.viewWidth = 0;
    this.viewHeight = 0;
    this.curScaleFactor = 1;

    this.dayListOffset = 0;
    this.selectedDayTag = "";
    this.endTimeout = null;
    this.scrollState = SCROLLSTATE.IDLE;

    this.scrollDayIndexTimeout = null;

    this.textSize = 12; // 文字的大小
    this.textTop = Number.parseInt(120 / 14); // 1/14的高度 控制文字的顶部距离控件的顶部的距离；控制时间轴的高度
    this.hourTextHalfWidth = 15;// 11:00 这个文字的宽度的一半，用于控制绘制时间文本时 起始处的x坐标。 
    this.timelineTop = this.textTop + this.textSize * 3;// 时间轴顶部的y偏移
    this.timelineBottom = this.timelineTop + this.textTop;// 时间轴底部的y偏移
    this.dotPosition = this.timelineBottom + (120 - this.timelineBottom) / 2; // 时间轴组件里底部的小圆点圆心y坐标
    this.leftRightButtonWidth = 20; // 左右button的宽高
    this.leftRightButtonTop = this.dotPosition - this.leftRightButtonWidth / 2; // 左右button的上边缘y坐标
    this.leftRightButtonBottom = this.dotPosition + this.leftRightButtonWidth / 2; // 左右button的下边缘y坐标
    this.leftRigthButtonPadding = 17; // 左右button距离左右边缘的padding。

    this.drawFlags = drawAreaMove | drawDefault | drawPeopleMove | drawBabyCry;

    this.pan = new Animated.Value(0);


    // here we put some areas here;
    this.allAreas = {};
    this.displayCutoutTop = 0;

    Host.getPhoneScreenInfo()
      .then((result) => {
        this.displayCutoutTop = PixelRatio.roundToNearestPixel(result.displayCutoutTop / PixelRatio.get() || 0);
        if (isNaN(this.displayCutoutTop)) {
          this.displayCutoutTop = 0;
        }
        //console.log(TAG, "result:", result);
      })
      .catch((error) => {

      });
    this.listener = DeviceEventEmitter.addListener(TileLineScaleChangeEvent, (res) => {
      if (res && res.fromVertical) {
        this.scaleType = res.scaleType;
        this.isNeedInit = true;
        this.updateScale(1, false);
      }
    });
    this.lastTime = 0;
  }
  static propTypes = {
    style: PropTypes.any,
    // data: PropTypes.array,
    // time: PropTypes.number,
    landscape: PropTypes.bool,
    // onTimeScaleChange: PropTypes.func,
    // backgroundColor: PropTypes.string,
    onScrollEnd: PropTypes.func,
    onScrolling: PropTypes.func,
    isDisabled: PropTypes.bool,
    eventTypeFlags: PropTypes.number,
    isCloud: PropTypes.bool,
    onPlayStateChange: PropTypes.func,
    isRecording: PropTypes.bool,
    isPlaying: PropTypes.bool
  }
  state = {
    showTimelineView: false,
    dateData: [],
    isMoving: false,
    currentTime: 0,
    showDayList: false,
    showDayIndicator: true,
    selectedDayIndex: 0,
    eventTypeFlags: this.props.eventTypeFlags
  }
  static defaultProps = {
    data: null,
    time: null,
    landscape: false,
    onTimeScaleChange: null,
    onScrollEnd: null,
    onScrolling: null,
    eventTypeFlags: EVENT_TYPE.Default | EVENT_TYPE.PeopleMotion | EVENT_TYPE.ObjectMotion | EVENT_TYPE.BabyCry | EVENT_TYPE.Face | EVENT_TYPE.AI,
    isCloud: false
  }

  // 重新赋值大刻度之间间隔时长
  initInterval() {
    this.intervalCount = 1;
    switch (this.scaleType) {
      case Scale_Type_Max:
        this.itemTime = 2 * 60 * 60 * 1000; // 间隔6小时
        break;
      case Scale_Type_Big:
        this.itemTime = 60 * 60 * 1000; // 间隔1小时
        break;
      case Scale_Type_Middle:
        this.itemTime = 30 * 60 * 1000; // 间隔30分钟
        break;
      case Scale_Type_Small:
        this.itemTime = 10 * 60 * 1000; // 间隔10分钟
        break;
      case Scale_Type_Small_Min:
        this.itemTime = 60 * 1000; // 间隔100秒，一格10秒
        break;
    }
    this.intervalTime = this.itemTime / this.intervalCount;
    this.oneUnitWidthTime = this.intervalTime / this.itemInterval;
    console.log("this.intervalTime==", this.intervalTime, " this.oneUnitWidthTime=", this.oneUnitWidthTime);
  }

  componentWillMount() {
    //console.log(TAG, `componentWillMount: `);
    this.dataList = this.props.data;
    this.initPanResponder();
    // this.initTempList();
  }

  componentDidUpdate() {

    if (this.props.eventTypeFlags == this.state.eventTypeFlags) {
      if (this.props.landscape == !this.state.showDayIndicator) {
        return;
      }
      if (this.props.landscape) {
        this.setState({ showDayIndicator: false });
      } else {
        this.setState({ showDayIndicator: true });
      }
    } else {
      this.setState({ eventTypeFlags: this.props.eventTypeFlags });
    }
  }

  componentDidMount() {
    //console.log(TAG, `componentDidMount: `);
    // this._testData();
    this.scrollToTimestamp(new Date().getTime());
  }

  initData(data) {
    this.dataList = data;
    this.setState({});
    this.cloudSdMaxTime = this.dataList[this.dataList.length - 1].endTime;
    this.cloudSdMinTime = this.dataList[0].startTime;
  }

  onReceiveCloudDatas() {
    // 要先生成dateData();
    let dayItems = CloudVideoUtil.cloudDayList;
    this.setState({ dateData: dayItems });
    this.setSelectedDay(this.state.currentTime);

    let videoMap = CloudVideoUtil.cloudVideoDayMap;
    if (videoMap == null || videoMap.size <= 0) {
      return null;
    }
    let videoKeys = [...videoMap.keys()];
    if (videoKeys == null || videoKeys.length == 0) {
      return null;
    }
    videoKeys.sort((a1, a2) => {
      return a1 - a2;
    });
    let maxVideoKey = videoKeys[videoKeys.length - 1];
    let minVideoKey = videoKeys[0];
    let maxVideoArray = videoMap.get(maxVideoKey);
    if (maxVideoArray == null || maxVideoArray.length <= 0) {
      return null;
    }
    let minVideoArray = videoMap.get(minVideoKey);
    if (minVideoArray == null || minVideoArray.length <= 0) {
      return null;
    }
    this.cloudSdMaxTime = maxVideoArray[0].endTime;
    this.cloudSdMinTime = minVideoArray[minVideoArray.length - 1].startTime;
  }

  // 外部的view同步时间轴进度的。
  scrollToTimestamp(timestamp) {
    // //console.log(TAG, `scroll to timestmap:${ this.formatTimeInMMSS(timestamp) }  last scrollTime: ${ this.formatTimeInMMSS(this.lastTime) } leftPressed:${ this.isLeftLongPressed } rightPressed:${ this.isRightLongPressed } state:${ this.scrollState != SCROLLSTATE.IDLE }`);
    // this.scrollToTimestampWithNotify(time, false);
    if (this.isLeftLongPressed || this.isRightLongPressed || this.scrollState != SCROLLSTATE.IDLE) {
      return;
    }
    if (timestamp <= 0) {
      return;
    }
    if (Math.abs(this.lastTime - timestamp) > 2000) { // 如果外面通知太快，refreshtopdateview就不能正常滚动了
      this.refreshTopDateView(timestamp);
    }
    if (this.lastTime == timestamp) {
      return;// 避免快速刷新
    }
    this.lastTime = timestamp;
    this.setState({ currentTime: timestamp }); // 刷新页面
  }

  isTimelineIdle() {
    if (this.isLeftLongPressed || this.isRightLongPressed || this.scrollState != SCROLLSTATE.IDLE) {
      return false;
    }
    return true;
  }

  _calculateOffset(selectedDayIndex) { // copy horizonalScrollView.java computeScrollDeltaToGetChildRectOnScreen
    // selectedDayIndex = selectedDayIndex;
    let width = timeWidth + timeMargin * 2;
    let screenLeft = this.dayListOffset || 0;
    let screenRight = screenLeft + width;
    let itemLeft = selectedDayIndex * timeWidth_2;
    let itemRight = (selectedDayIndex + 1) * timeWidth_2;
    let scrollXDelta = 0;

    if (itemRight > screenRight && itemLeft > screenLeft) {
      // need to move right to get it in view: move right just enough so
      // that the entire rectangle is in view (or at least the first
      // screen size chunk).

      // get entire rect at right of screen
      scrollXDelta += (itemRight - screenRight + timeWidth_2 + 12);


      // make sure we aren't scrolling beyond the end of our content

    } else if (itemLeft < screenLeft && itemRight < screenRight) {
      // need to move right to get it in view: move right just enough so that
      // entire rectangle is in view (or at least the first screen
      // size chunk of it).
      scrollXDelta -= (screenLeft - itemLeft - timeWidth_2);
      // make sure we aren't scrolling any further than the left our content
    }
    return scrollXDelta;
  }


  setSelectedDay(centerTimestamp) {
    this.selectedDayTime = centerTimestamp;
    this.dateTime.setTime(centerTimestamp);
    let year = this.dateTime.getFullYear();
    let month = this.dateTime.getMonth() + 1;
    let day = this.dateTime.getDate();
    let str = (month > 9 ? `${month}` : `0${month}`) + (day > 9 ? `${day}` : `0${day}`);

    // //console.log("refreshTopdate", "选中日期:", str);
    let selectedDayIndex = -1;
    let dayArray = this.state.dateData;
    for (let i = 0; i < dayArray.length; i++) {

      let day = dayArray[i];
      if (day.dateStr === str) {
        dayArray[i].isSelected = true;
        selectedDayIndex = i;
      } else {
        dayArray[i].isSelected = false;
      }
    }
    this.setState({ dateData: dayArray });
    if (selectedDayIndex == this.selectedDayIndex) {
      this.selectedDayIndex = selectedDayIndex;
      return selectedDayIndex;// 如果一样。
    }
    this.selectedDayIndex = selectedDayIndex;

    this.setState({ dateData: dayArray });
    return this.selectedDayIndex;
  }

  // here to see what happened  should make a debugger
  refreshTopDateView(centerTimestamp) {
    // //console.log("refreshTopdate");
    this.selectedDayTime = centerTimestamp;
    let selectedDayIndex = this.setSelectedDay(centerTimestamp);
    if (selectedDayIndex == -1) {
      return;
    }
    clearTimeout(this.scrollDayIndexTimeout);

    if (!this.state.showDayList) {
      return;
    }
    this.selectedDayIndex = selectedDayIndex;
    clearTimeout(this.scrollDayIndexTimeout);

    let offset = this._calculateOffset(selectedDayIndex) + this.dayListOffset;

    this.scrollDayIndexTimeout = setTimeout(() => {
      if (selectedDayIndex < 0) {
        return;
      }
      if (this.dayListRef != null) {
        // console.log("refreshTopdate", "移动距离", offset);
        this.dayListRef.scrollToOffset({
          animated: false,
          offset: offset
        });
      }

    }, 300);

  }

  _getSelectedDayIndex() {
    let index = -1;
    for (let i = 0; i < this.state.dateData.length; i++) {
      if (this.state.dateData[i].isSelected) {
        index = i;
        break;
      }
    }
    return index;
  }

  _getPrevDayIndex(currentSelectedIndex) {
    for (let i = currentSelectedIndex - 1; i >= 0; i--) {
      if (this.state.dateData[i].hasVideo) {
        return i;
      }
    }
    return -1;
  }

  _getNextDayIndex(currentSelectedIndex) {
    if (currentSelectedIndex == -1) {
      return -1;
    }
    for (let i = currentSelectedIndex + 1; i < this.state.dateData.length; i++) {
      if (this.state.dateData[i].hasVideo) {
        return i;
      }
    }
    return -1;
  }

  _onPressDayItem(timestamp) {
    // console.log("onpressDayItem:", JSON.stringify(dayItem));
    TrackUtil.reportClickEvent("TimeSlider_Date_ClickNum");
    if (this.props.isDisabled) {
      return;
    }
    // if (!dayItem.hasVideo) {
    //   return;
    // }
    // let timestamp = dayItem.startTimestamp;
    // console.log(`selected item:${CloudVideoUtil.convertTimestampToTimeStr(timestamp)}`);
    // 防止在两边的情况 做一下处理
    this.scrollToTimestampWithNotify(timestamp, true);
    // console.log("time diff:", time2 - time1);
  }

  _dayKeyExtractor = (item) => item.startTimestamp.toString();

  _getDayItemLayout = (item, index) => {
    return {
      length: (timeWidth + 2 * timeMargin),
      offset: (timeWidth + 2 * timeMargin) * index,
      index
    };
  }

  componentWillUnmount() {
    // console.log(TAG, `componentWillUnmount: `);
    clearTimeout(this.scrollDayIndexTimeout);
  }
  _renderPauseView() {
    let pauseIcons = [
      {
        source: require("../../Resources/Images/camera_icon_center_pause.png"),
        onPress: () => {
          this.props.onPlayStateChange && this.props.onPlayStateChange(false);
        }
      },
      {
        source: require("../../Resources/Images/camera_icon_center_play.png"),
        onPress: () => {
          this.props.onPlayStateChange && this.props.onPlayStateChange(true);
        }
      }
    ];
    let index = this.props.isPlaying ? 0 : 1;

    return (
      <View style={{ width: 64, height: "100%", backgroundColor: "#262626", justifyContent: "center", alignItems: "center" }}>
        <ImageButton
          style={{ width: 50, height: 50, alignSelf: "center", marginBottom: 23 }}
          source={pauseIcons[index].source}
          highlightedSource={pauseIcons[index].highlightedSource}
          onPress={pauseIcons[index].onPress}
          accessibilityLabel={this.props.isPlaying ? DescriptionConstants.rp_23 : DescriptionConstants.yc_16}
        />
      </View>
    );
  }

  render() {
    if (this.isNeedInit) {
      this.isNeedInit = false;
      this.initInterval();
    }
    this.timeTextList = [];
    this.motionAreaList = [];
    return (

      <View
        // style={[{ width: "100%", display: "flex", flexDirection: "row" }]}
        style={[{ width: "100%", display: "flex", flexDirection: "row" }, this.state.showDayIndicator ? null : { paddingStart: this.displayCutoutTop / 2, paddingEnd: this.displayCutoutTop + 64 }]}>
        {this._renderPauseView()}
        <View style={[{ flexGrow: 1, display: "flex", flexDirection: "column", backgroundColor: "#ffffff", overflow: "hidden" }]}
          removeClippedSubviews={true}
        >
          {/* {this._renderDayIndicator()} */}

          {/* {this._renderDayList()} */}

          {this._renderTimelineView()}
        </View>

      </View>

    );
  }
  // 时间轴
  _renderTimelineView() {
    let timelineHeight = 120;
    if (kWindowHeight < 700) {
      timelineHeight = 100;
    }
    return (

      <View
        // style={{ width: "100%", borderTopColor: "#f0f0f0", borderTopWidth: 0.8, height: timelineHeight, display: "flex", flexDirection: "row", backgroundColor: this.state.showDayIndicator ? "#F7F7F7" : "#262626" }}
        style={{ width: "100%", height: timelineHeight, display: "flex", flexDirection: "row", backgroundColor: this.state.showDayIndicator ? "#F7F7F7" : "#262626" }}
        onLayout={(event) => {
          // console.log(TAG, "onlayout", event.nativeEvent.layout);
          let width = event.nativeEvent.layout.width;
          let height = event.nativeEvent.layout.height;
          if (width == 0 || height == 0 || width <= kWindowWidth + 5) {
            this.showCenterLine = false;
            this.forceUpdate();
            return;
          }
          this.showCenterLine = true;
          this.forceUpdate();
          if (this.viewHeight == height && (this.viewWidth == width)) {
            return;// onlayout 布局发生了改变，但是view宽高没有改变，不需要重新渲染
          }
          let paddingWidth = width - this.leftRightButtonWidth * 2 - this.leftRigthButtonPadding * 2;
          this.viewHeight = event.nativeEvent.layout.height;

          // console.log(TAG, `finalWidth:${ width }`);
          if (paddingWidth == null || paddingWidth <= 0) {
            this.setState({ showTimelineView: false });
            return;
          }
          this.timelineViewWidth = paddingWidth;
          this.viewWidth = width;

          if (this.state.showTimelineView) {
            this.setState(() => { return { showTimelineView: false }; }, () => {
              this.setState({ showTimelineView: true });
            });
          } else {
            this.setState({ showTimelineView: true });
          }
        }}
      >

        {this._renderHours()}
        {this._renderAlarmAreas()}
        {this._renderCenterLine()}
        {this._renderLeftRightButton()}

      </View>
    );
  }

  _renderAlarmAreas() {

    this.allAreas = {};
    
    if (!this.state.showTimelineView) {
      return null;
    }

    // const viewWidth = Math.ceil(this.timelineViewWidth);
    const centerX = Number.parseInt(this.viewWidth / 2); // 得到一半的长度
    let leftTimestamp = Math.ceil(this.state.currentTime - centerX * this.oneUnitWidthTime); // 计算出最小时间戳
    let rightTimestamp = Math.ceil(this.state.currentTime + centerX * this.oneUnitWidthTime); // 计算出最大时间戳

    if (!this.props.isCloud) {
      this._calculteSdEvents(leftTimestamp, rightTimestamp);
    } else {
      this._calculateCloudEvents(leftTimestamp, rightTimestamp);
    }

    let allPaths = {};
    Object.keys(EVENT_TYPE).forEach((item, index) => {
      let eventTypeFlag = (item == "KnownFace" ? EVENT_TYPE["Face"] : EVENT_TYPE[item]);
      let flag = this.state.eventTypeFlags & eventTypeFlag;
      let areas = this.allAreas[TIMELINE_EVENT_TYPE[item]];
      if (!areas) {
        return;
      }
      if (!allPaths[eventTypeFlag]) {
        allPaths[eventTypeFlag] = new Path();
      }
      if (!allPaths[EVENT_TYPE.Default]) {
        allPaths[EVENT_TYPE.Default] = new Path();
      }
      if (!flag) {
        for (let i = 0; i < areas.length; i++) {
          let area = areas[i];
          allPaths[EVENT_TYPE.Default].moveTo(area.startX, this.timelineTop);
          allPaths[EVENT_TYPE.Default].lineTo(area.endX, this.timelineTop);
          allPaths[EVENT_TYPE.Default].lineTo(area.endX, this.timelineTop + this.textTop);
          allPaths[EVENT_TYPE.Default].lineTo(area.startX, this.timelineTop + this.textTop);
        }
      } else {
        for (let i = 0; i < areas.length; i++) {
          let area = areas[i];
          allPaths[eventTypeFlag].moveTo(area.startX, this.timelineTop);
          allPaths[eventTypeFlag].lineTo(area.endX, this.timelineTop);
          allPaths[eventTypeFlag].lineTo(area.endX, this.timelineTop + this.textTop);
          allPaths[eventTypeFlag].lineTo(area.startX, this.timelineTop + this.textTop);
        }
        allPaths[eventTypeFlag].close();
  
      }
    });
    if (allPaths[EVENT_TYPE.Default]) allPaths[EVENT_TYPE.Default].close();

    return (
      <Surface style={{ position: "absolute" }}
        width={this.viewWidth}
        height={120}
      >
        {
          Object.values(EVENT_TYPE).map((item) => {
            if (!allPaths[item]) {
              return null;
            }
            return <Shape d={allPaths[item]} fill={areaColors[item]} strokeWidth={1} key={item} />;
          })
        }
      </Surface>
    );
  }
  // 这里是竖线
  _renderCenterLine() {
    if (!this.showCenterLine) {
      return;
    }
    if (this.props.isDisabled && this.props.isCloud) {
      return null;
    }
    this.date.setTime(this.state.currentTime);
    let hour = this.date.getHours();
    let minute = this.date.getMinutes();
    let seconds = this.date.getSeconds();
    let centerText = `${ hour > 9 ? hour : `0${ hour }` }:${ minute > 9 ? minute : `0${ minute }` }:${ seconds > 9 ? seconds : `0${ seconds }` }`;

    let isDark = DarkMode.getColorScheme() == "dark";
    // 来区分是否全屏
    let color = (this.state.showDayIndicator) ? "xm#333333" : "xm#ffffff";
    if (isDark) {
      color = "xm#ffffff";
    }
    return (
      <View
        style={{ width: "100%", height: "100%", display: "flex", flexDirection: "row", justifyContent: "center", alignItems: "flex-end", position: "absolute" }}

      >
        <Text style={{
          position: "absolute", fontWeight: 'bold', fontSize: 13, top: this.textTop, color: color
        }}>{centerText}</Text>
        <View style={{ position: "absolute", backgroundColor: color, width: 2, height: 95 }}
        // accessibilityLabel={DescriptionConstants.rp_10}

        />
      </View>
    );
  }

  _renderLeftRightButton() {

    let leftButton = (
      <TouchableOpacity
        style={{ width: this.leftRightButtonWidth + this.leftRigthButtonPadding, height: 20, paddingLeft: this.leftRigthButtonPadding, left: 0, marginTop: this.leftRightButtonTop }}
        onPressIn={() => this.onLeftPressIn()}
        onPressOut={() => this.onLeftPressOut()}
        underlayColor={"#55555522"}
        activeOpacity={0.88}
      >
        <Image
          style={{ width: "100%", height: "100%" }}
          source={require("../../Resources/Images/progress_button_last_nor2.png")}
          accessibilityLabel={DescriptionConstants.rp_8}

        />
      </TouchableOpacity>
    );

    let rightButton = (
      <TouchableOpacity
        style={{ width: this.leftRightButtonWidth + this.leftRigthButtonPadding, height: 20, paddingRight: this.leftRigthButtonPadding, right: 0, marginTop: this.leftRightButtonTop }}
        onPressIn={() => this.onRightPressIn()}
        onPressOut={() => this.onRightPressOut()}
        underlayColor={"#55555522"}
        activeOpacity={0.88}
      >
        <Image
          style={{ width: "100%", height: "100%" }}
          source={require("../../Resources/Images/progress_button_next_nor2.png")}
          accessibilityLabel={DescriptionConstants.rp_9}

        />
      </TouchableOpacity>
    );
    return (
      <View style={{ width: "100%", height: "100%", display: "flex", flexDirection: "row" }}>
        {leftButton}
        <View style={{ flexGrow: 1, height: "100%", backgroundColor: "#00000000" }} // 这里添加backgroundolor的原因：android端会默认没有背景色的占位View 导致panResponder不生效
          {...this.panResponder.panHandlers}

        >
        </View>
        {rightButton}
      </View>
    );


  }

  _renderHours() {
    // 这里绘制一条条整点时刻竖着的时间轴；底部的小圆圈；时间值
    let viewWidth = Number.parseInt(this.viewWidth);
    let centerX = Number.parseInt(this.viewWidth / 2);
    let leftTimestamp = Number.parseInt(this.state.currentTime - centerX * this.oneUnitWidthTime); // 计算出左边的最小时间戳; 
    let drawX = 0; // 记录绘制刻度的位置
    let drawTimestamp = 0; // 记录所绘制刻度线代表的时间戳
    // 先计算出绘制的第一个刻度的位置和时间戳
    let temp = Number.parseInt(leftTimestamp / this.intervalTime);
    drawTimestamp = Number.parseInt((temp + 1) * this.intervalTime); // 得到最左边的那个刻度线的时间值。
    drawX = Number.parseInt((drawTimestamp - this.state.currentTime) / this.oneUnitWidthTime) + centerX; // 离屏绘制 距离屏幕开始处左边一个屏幕宽度的位置
    let rulerViews = [];
    let index = 0;
    while (drawX < (viewWidth)) { // 绘制左右两边超出分别一个屏幕的区域
      this.dateTime.setTime(drawTimestamp);
      let year = this.dateTime.getFullYear();
      let month = this.dateTime.getMonth() + 1;
      let day = this.dateTime.getDate();
      let hour = this.dateTime.getHours();
      let min = this.dateTime.getMinutes();
      let sec = this.dateTime.getSeconds();
      let dateStr = null;
      let hourStr = null;
      let shouldDraw = true;
      // if (this.scaleType >= Scale_Type_Small_Min) {
      //   hourStr = `${hour > 9 ? `${hour}` : `0${hour}`}:${min > 9 ? min : `0${min}`}:${sec > 9 ? sec : `0${sec}`}`;
      // } else {
      hourStr = `${ hour > 9 ? `${ hour }` : `0${ hour }` }:${ min > 9 ? min : `0${ min }` }`;
      // }
      if (Math.abs(drawTimestamp - this.state.currentTime) < this.intervalTime * 0.75) {
        shouldDraw = false;
      }
      if (shouldDraw) { // 绘制小圆点 小竖线 文字
        // let dotView = (<View key={index} style={{ width: 4, height: 4, backgroundColor: Util.isDark() ? "#444444" : "#DBDBDB", position: "absolute", left: drawX - 1, top: this.dotPosition - 1, borderRadius: 4 }} />);
        let dotView = (<View key={index} style={{ width: 4, height: 4, backgroundColor: Util.isDark() ? "#444444" : "#444444", position: "absolute", left: drawX - 1, top: this.dotPosition - 1, borderRadius: 4 }} />);
        rulerViews.push(dotView);
        index++;

        let textView = (<Text key={index}
          accessibilityLabel={DescriptionConstants.rp_65 + hourStr}
          style={{
            fontSize: 12,
            position: "absolute",
            left: drawX - this.hourTextHalfWidth,
            top: this.textTop,
            color: this.props.landscape ? 'xm#999' : '#000'
          }}>{hourStr}
        </Text>);
        index++;
        // let lineView = (<View key={index} style={{ width: 1, height: this.textTop, position: "absolute", left: drawX - 1, backgroundColor: Util.isDark() ? "#999999" : "#999999" }} />);
        let lineView = (<View key={index} style={{ width: 1, height: this.textTop, position: "absolute", left: drawX - 1, backgroundColor: Util.isDark() ? "#444444" : "#444444" }} />);
        index++;
        rulerViews.push(textView);
        rulerViews.push(lineView);
      }
      drawX = drawX + this.itemInterval;// 增加一个刻度所需的宽度。 
      drawTimestamp = drawTimestamp + this.intervalTime; // 增加一个刻度之间所需的时间差

    }
    return (
      <View style={{ width: "100%", height: "100%", position: "absolute" }}
      >
        {rulerViews}
      </View>
    );
  }
  // 这里是星期几
  _renderDayList() {

    return (
      <View
        style={(this.state.showDayList && this.state.showDayIndicator) ? { width: "100%", height: 60, display: "flex", flexDirection: "row" } : [{ width: "100%", height: 60, display: "flex", flexDirection: "row" }, { display: "none" }]}
      >
        <FlatList
          ref={(ref) => { this.dayListRef = ref; }}
          style={{ width: "100%", height: 60 }}
          data={this.state.dateData}
          horizontal={true}
          renderItem={({ item, index }) => {
            return this._renderDayItem(item, index);
          }}
          onScroll={(event) => {
            this.dayListOffset = event.nativeEvent.contentOffset.x;
            // console.log(`当前偏移量：${this.dayListOffset}`);
          }}
          keyExtractor={this._dayKeyExtractor}
          getItemLayout={this._getDayItemLayout}
          showsHorizontalScrollIndicator={false}
        />
      </View>

    );
  }

  _renderDayIndicator() {
    if (!this.state.showDayIndicator) {
      return null;
    }
    // 渲染顶部的时间
    let dayArrays = this.state.dateData;
    let timestamp = 0;
    let currentTimestamp = new Date().getTime();
    let index = this._getSelectedDayIndex();
    if (index != -1) {
      timestamp = dayArrays[index].startTimestamp;
    }
    if (timestamp == 0) {
      timestamp = currentTimestamp;
    }
    this.selectedDayTime = timestamp;
    this.dateTime.setTime(timestamp);
    let day = this.dateTime.getDate();
    let month = this.dateTime.getMonth() + 1;
    let week = this.dateTime.getDay();
    let year = this.dateTime.getFullYear();
    this.dateTime.setTime(currentTimestamp);
    let currentDay = this.dateTime.getDate();
    let currentMonth = this.dateTime.getMonth() + 1;
    let currentYear = this.dateTime.getFullYear();
    let isToday = false;
    if (day == currentDay && month == currentMonth && year == currentYear) {
      isToday = true;
    }
    let str = "";
    if (isToday) {
      str = TODAY;
    } else {
      let isChinaMainland = (Host.locale.language || "").toLocaleLowerCase() == "zh";
      str = dayjs.unix(timestamp / 1000).format(LocalizedStrings["mmdd"]);
      str = `${str} ${WEEKENDS[week]}`;
    }
    let dateHeight = 50;
    return (
      <View style={{ width: "100%", height: dateHeight, display: "flex", flexDirection: "row", alignItems: "center", marginTop: 20, marginBottom: 20 }}>
        <TouchableOpacity
          style={{ paddingLeft: 23, width: 42, height: 22 }}
          onPress={() => {
            // 跳到左边的日期 或者跳到右边的日期
            // console.log("跳到左边一天");
            let index = this._getSelectedDayIndex();
            let prevIndex = this._getPrevDayIndex(index);
            if (prevIndex < 0) {
              return;
            }

            let prevItem = this.state.dateData[prevIndex];
            this._onPressDayItem(prevItem);
          }}
        >

          <Image
            style={{ width: 22, height: 22 }}
            source={Util.isDark() ? require("../../Resources/Images/norL.png") : require("../../Resources/Images/time_line_icon_pre_day.png")}
            accessibilityLabel={DescriptionConstants.rp_6}

          >

          </Image>
        </TouchableOpacity>
        <TouchableOpacity
          style={{ flex: 1, display: "flex", alignItems: "center" }}
          onPress={() => {
            if (this.props.isDisabled) {
              return;
            }
            this.setState((state) => {
              return {
                showDayList: !state.showDayList
              };
            }, () => {
              if (this.state.showDayList) {
                this.refreshTopDateView(this.state.currentTime);
              } else {
                this.selectedDayIndex = -1;
              }
            });
          }}
        >
          <Text style={{ fontSize: kIsCN ? 19 : 17, color: "black", fontWeight: "bold" }}>
            {str}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{ paddingRight: 20, width: 42, height: 22 }}
          onPress={() => {
            // 跳到左边的日期 或者跳到右边的日期
            // console.log("跳到右边一天");
            let index = this._getSelectedDayIndex();
            let nextIndex = this._getNextDayIndex(index);
            if (nextIndex < 0) {
              return;
            }
            let nextItem = this.state.dateData[nextIndex];
            this._onPressDayItem(nextItem);
          }}
        >
          <Image
            style={{ width: 22, height: 22 }}
            source={Util.isDark() ? require("../../Resources/Images/norR.png") : require("../../Resources/Images/time_line_icon_next_day.png")}
            accessibilityLabel={DescriptionConstants.rp_7}

          >

          </Image>
        </TouchableOpacity>

      </View>
    );
  }
  // 具体的星期几
  _renderDayItem(item, index) {
    let startTime = item.startTimestamp;
    this.dateTime.setTime(startTime);
    let month = this.dateTime.getMonth() + 1;
    let day = this.dateTime.getDate();
    let str = day > 9 ? `${day}` : `0${day}`;
    let week = WEEKS[item.week];
    return (
      <View
        style={item.isSelected ? styles.dayItemSelected : styles.dayItem}
        key={index}
      >
        <TouchableOpacity
          style={{ display: "flex", flexDirection: "column", alignItems: "center", paddingTop: 2 }}
          onPress={() => this._onPressDayItem(item)}
        >
          <Text
            style={item.hasVideo ? styles.dayItemTextWeek : { color: "#CCCCCC", fontSize: kIsCN ? 11 : 10 }}
          >
            {week}
          </Text>

          <Text
            style={[styles.dayItemTextDay, { fontWeight: "bold", color: item.hasVideo ? "black" : "#CCCCCC" }]}
          >
            {str}
          </Text>

        </TouchableOpacity>

      </View>
    );
  }

  _calculteSdEvents(leftTimestamp, rightTimestamp) {
    // //here nothing todo
    // return null;
    if (this.dataList == null || this.dataList.length <= 0) {
      return;
    }
    // 对于事件类型 与上一个item的type不一样，就记录startPosition, 并把上一个item的endPosition与之前的startPosition一起结合。
    // 如果
    let lastType = -8888;
    let startPosition = -8888;
    let lastEndPosition = 0;
    let drawAreaX = 0; // 记录绘制的位置，过滤绘制重复的区域
    let positionArray = [];
    for (let i = 0; i < this.dataList.length; i++) {
      let dataItem = this.dataList[i];
      let startTime = dataItem.startTime;
      let endTime = dataItem.endTime;
      let type = dataItem.eventType;// 这里需要根据021修改规则。
      // 获取开始时间和结束时间

      // if (this.selectedDayTime >= this.dataList[0].startTime && this.selectedDayTime <= this.dataList[this.dataList.length - 1].endTime) {
      //   this.cloudSdMaxTime = this.dataList[this.dataList.length - 1].endTime;
      //   this.cloudSdMinTime = this.dataList[0].startTime;
      // }
      if (endTime > leftTimestamp && startTime < rightTimestamp) { // 只对绘制区域内的作处理。
        let drawStartX = (startTime - leftTimestamp) / this.oneUnitWidthTime;
        let drawEndX = (endTime - leftTimestamp) / this.oneUnitWidthTime;
        if (drawAreaX != 0 && drawAreaX >= drawStartX) { // 纠正数据重复的
          drawStartX = drawAreaX;
        }
        if (drawEndX <= drawStartX) {
          continue;
        }
        // TODO 比较进的地方 就画成一样的吧。
        if (type != lastType || drawStartX != lastEndPosition) { // 事件类型不一样了，起始位置跟上一次的终止不一样 
          if (startPosition != -8888) { // 不是第一个
            let position = {};
            position.type = lastType;
            position.startX = startPosition;
            position.endX = lastEndPosition;

            this.putIntoAlarmAreaList(lastType, startPosition, lastEndPosition);

            // positionArray.push(position);
          }
          startPosition = drawStartX;
          lastType = type;
        }
        drawAreaX = drawEndX;
        lastEndPosition = drawEndX;
      }

    }
    // 需要补上最后一个item
    if (lastType != -8888) {
      let position = {};
      position.startX = startPosition;
      position.endX = lastEndPosition;
      position.type = lastType;
      this.putIntoAlarmAreaList(lastType, startPosition, lastEndPosition);
      // positionArray.push(position);
    }
  }


  // 绘制云存时间轴；
  _calculateCloudEvents(leftTimestamp, rightTimestamp) {
    // console.log("begin calculate\n\n\n");
    let lastDrawTime = 0;
    let lastDrawPos = 0;

    let lastDrawEventEndPos = -20;// 上一次绘制event结束的地方；
    let lastDrawEventStartPos = -20; // 上一次绘制event开始的地方；
    let lastDrawEventEndTime = 0;
    let lastDrawEventStartTime = 0;
    let lastEventType = 0;
    // first draw all cloud events with default color;  default color always draws.  perform as usual
    let videoMap = CloudVideoUtil.cloudVideoDayMap;
    let eventMap = CloudVideoUtil.alarmEventDayMap;
    if (videoMap == null || videoMap.size <= 0) {
      return null;
    }
    let videoKeys = [...videoMap.keys()];
    videoKeys.sort((a1, a2) => {
      return a1 - a2;
    });
    for (let videoKey of videoKeys) {
      let cloudVideoArrays = videoMap.get(videoKey);
      if (cloudVideoArrays == null || cloudVideoArrays.length == 0) { // 数据是逆序的
        continue;
      }
      if (cloudVideoArrays[0] == null) {
        continue;
      }
      let maxTime = cloudVideoArrays[0].endTime;// 数据逆序的，第一个最大；
      let minTime = cloudVideoArrays[cloudVideoArrays.length - 1].startTime; // 最后一个最小。

      if (minTime > rightTimestamp || maxTime < leftTimestamp) { // 时间戳不在这个范围内。不绘制了
        continue;
      }

      // 开始绘制默认的云存事件
      for (let i = cloudVideoArrays.length - 1; i >= 0; i--) { // 先绘制所有的云存储事件；
        let cloudVideo = cloudVideoArrays[i];
        let startTime = cloudVideo.startTime;
        let endTime = cloudVideo.endTime;
        if (startTime > rightTimestamp || endTime < leftTimestamp) { // 不在显示范围内
          continue;
        }
        // TODO 比较近的画到一起；

        if (endTime < lastDrawTime) {
          continue; // 终止时间比上一次的终止时间还晚一些
        }
        if (startTime < lastDrawTime) {
          startTime = lastDrawTime;
        }
        let drawStartX = (startTime - leftTimestamp) / this.oneUnitWidthTime;
        let drawEndX = (endTime - leftTimestamp) / this.oneUnitWidthTime;
        this.putIntoAlarmAreaList(-1, drawStartX, drawEndX);
        lastDrawPos = drawEndX;
        lastDrawTime = endTime;
      }

      if (eventMap == null || eventMap.size <= 0) {
        continue;
      }

      let alarmEvents = eventMap.get(videoKey);
      if (alarmEvents == null || alarmEvents.length <= 0) {
        continue;
      }
      let videoItemIndex = cloudVideoArrays.length - 1;
      // 开始绘制事件
      for (let i = alarmEvents.length - 1; i >= 0; i--) { // 从小到大访问
        let lastEventTypeIndex = -1;
        let alarmEvent = alarmEvents[i];// 依旧是逆序的
        let startTime = alarmEvent.createTime;
        // let needRePosition = false;
        // if (startTime > lastDrawEventStartTime) {
        //   let diff = startTime - lastDrawEventStartTime;
        //   if (diff < 40001) { // 距离太近，就把这个事件提前到跟之前的一样吧 // 之前是50001，会有个别事件太近不画的问题
        //     // console.log("距离太近", startTime, "============", lastDrawEventStartTime);
        //     startTime = lastDrawEventStartTime;
        //     needRePosition = true;
        //   } else if (diff < 120000) { // 两分钟之内的，要判断事件优先级，如果优先级比上一次的优先级高，就覆盖上一次的view；
        //     needRePosition = true;
        //   }
        // }
        let endTime = alarmEvent.createTime + 120000;// 两分钟内只上报一次报警事件。
        // here search event 是否存在于map里；
        if (startTime > rightTimestamp || endTime < leftTimestamp) {
          continue;
        }

        let matched = false;

        for (let j = videoItemIndex; j >= 0; j--) { // 看看在云存视频里，该view是否存在。
          let cloudVideo = cloudVideoArrays[j];
          if (startTime >= cloudVideo.startTime && startTime < cloudVideo.endTime) {
            matched = true;
            if (endTime > cloudVideo.endTime) {
              endTime = cloudVideo.endTime;
            }
            break;
          }
          // } else if ((alarmEvent.createTime - cloudVideo.startTime) >= -10 && (alarmEvent.createTime - cloudVideo.endTime) <= 10) { // 如果原始看家视频的开始时间在范围内，就要调整startTime
          //   matched = true;

          //   startTime = alarmEvent.createTime;
          //   endTime = startTime + 120000;
          //   if (endTime > cloudVideo.endTime) {
          //     endTime = cloudVideo.endTime;
          //   }
          // }
        }
        if (!matched) {
          continue;// 在云存视频里找不到这个信息；
        }

        // if (endTime < lastDrawEventEndTime) { // 本次小于 上一次绘制的；
        //   continue;
        // }

        if (startTime > lastDrawEventEndTime && (startTime - lastDrawEventEndTime < 5001)) {
          startTime = lastDrawEventEndTime;
        }
        let drawStartX = (startTime - leftTimestamp) / this.oneUnitWidthTime;
        let drawEndX = (endTime - leftTimestamp) / this.oneUnitWidthTime;
        let eventTypes = alarmEvent.eventTypes;
        for (let j = 0; j < eventTypes.length; j++) {
          let eventType = eventTypes[j];
          let eventTypeIndex = TIMELINE_EVENT_TYPE[eventType];
          if (eventTypeIndex == null) {
            eventTypeIndex = -1;
          }
          // if (needRePosition) {
          //   let lastEventTypeIndex = TIMELINE_EVENT_TYPE[lastEventType] || -1;
          //   if (eventTypeIndex < lastEventTypeIndex) { // 如果本次的报警事件距离上一次的很近，而且优先级小于上一次的，就修改startPosition为上一次draw的end位置；
          //     startTime = lastDrawEventEndTime;
          //     drawStartX = (startTime - leftTimestamp) / this.oneUnitWidthTime;
          //   }
          // }

          // console.log(eventType, startTime, endTime, alarmEvent.createTime, alarmEvent.createTime + 120000);

          // 开始判断绘制类型了；
          lastEventTypeIndex = EventTypeUtil.getEventType(this.state.eventTypeFlags, eventType, eventTypeIndex);
          if (lastEventTypeIndex > -1) {
            break;
          }
        }
        this.putIntoAlarmAreaList(lastEventTypeIndex, drawStartX, drawEndX);
        lastDrawEventStartTime = startTime;
        lastDrawEventEndTime = endTime;

      }
    }
  }

  clearList() {
    this.dataList = [];
    this.setState({});
  }
  getDataListItem(index) {
    if (index < 0 || this.state.dataList == null || index >= this.state.dataList.length) {
      return;
    }
    let item = this.dataList[index];
    return item;
  }

  onLeftPressed() {

    if (this.props.isDisabled) {
      return;
    }
    this.isLeftPressed = false;
    clearTimeout(this.longLeftPressTimeout);
    this.longLeftPressTimeout = null;
    if (!this.isLeftLongPressed) {
      this.onMovePrev();
      return;
    } else {
      this.isLeftLongPressed = false;
      // this.props.onScrollEnd && this.props.onScrollEnd(this.state.currentTime);
      this._onTimeScaleScrolled(this.state.currentTime);
    }
  }
  onLeftPressIn() { // 同一触摸序列  只会被调用一次。
    console.log("left press in");
    this.endTimeout && clearTimeout(this.endTimeout);
    this.isLeftPressed = true;// 标记用户手指是否停在控件上
    // console.log("on left press in");
    if (this.props.isDisabled) {
      return;
    }
    if (this.props.isRecording) {
      Toast.success('camera_recording_block');
      return;
    }
    this.isLeftLongPressed = false;
    clearTimeout(this.longLeftPressTimeout);
    this.longLeftPressTimeout = setTimeout(() => {
      // console.log("on long press left");
      this.handleLongPressLeft();
    }, 1500);
  }

  handleLongPressLeft() {
    if (this.props.isDisabled || !this.isLeftPressed) {
      if (this.props.touchMovement) {
        this.onLeftPressed();
      }
      return;
    }
    if (this.props.isRecording) {
      Toast.success('camera_recording_block');
      return;
    }
    this.isLeftLongPressed = true;
    this.onFastMovePrev();
    this.longLeftPressTimeout = setTimeout(() => {
      // console.log("on long press left");
      this.handleLongPressLeft();
    }, 50);
  }

  onLeftPressOut() {
    TrackUtil.reportClickEvent("TimeSlider_LastNext_ClickNum");
    console.log("onLeftPressOut");
    if (this.props.isDisabled) {
      return;
    }
    if (this.props.isRecording) {
      Toast.success('camera_recording_block');
      return;
    }
    this.isLeftPressed = false;
    clearTimeout(this.longLeftPressTimeout);
    this.longLeftPressTimeout = null;
    if (!this.isLeftLongPressed) {
      this.onMovePrev();
      return;
    } else {
      this.isLeftLongPressed = false;
      // this.props.onScrollEnd && this.props.onScrollEnd(this.state.currentTime);
      this._onTimeScaleScrolled(this.state.currentTime);
    }
  }
  // touchableopcity组件调用顺序： onpressin onpressout onpress  如果是划出去的  onpress就不调用了
  onRightPressed() {

    if (this.props.isDisabled) {
      return;
    }
    if (this.props.isRecording) {
      Toast.success('camera_recording_block');
      return;
    }
    this.isRightPressed = false;
    clearTimeout(this.longRightPressTimeout);
    this.longRightPressTimeout = null;
    if (!this.isRightLongPressed) {
      this.onMoveNext();
      return;
    } else {
      this.isRightLongPressed = false;
      // this.props.onScrollEnd && this.props.onScrollEnd(this.state.currentTime);
      // this.scrollToTimestampWithNotify(this.state.currentTime, true);
      this._onTimeScaleScrolled(this.state.currentTime);
    }
  }
  onRightPressIn() {
    TrackUtil.reportClickEvent("TimeSlider_LastNext_ClickNum");
    this.endTimeout && clearTimeout(this.endTimeout);
    this.isRightPressed = true;
    if (this.props.isDisabled) {
      return;
    }
    if (this.props.isRecording) {
      Toast.success('camera_recording_block');
      return;
    }
    this.isRightLongPressed = false;
    clearTimeout(this.longRightPressTimeout);
    this.longRightPressTimeout = setTimeout(() => {
      // console.log("on long press right");
      this.handleLongPressRight();
    }, 1500);
  }

  onRightPressOut() {
    if (this.props.isDisabled) {
      return;
    }
    if (this.props.isRecording) {
      Toast.success('camera_recording_block');
      return;
    }
    this.isRightPressed = false;
    clearTimeout(this.longRightPressTimeout);
    this.longRightPressTimeout = null;
    if (!this.isRightLongPressed) {
      this.onMoveNext();
      return;
    } else {
      this.isRightLongPressed = false;
      // this.props.onScrollEnd && this.props.onScrollEnd(this.state.currentTime);
      // this.scrollToTimestampWithNotify(this.state.currentTime, true);
      this._onTimeScaleScrolled(this.state.currentTime);
    }
  }
  handleLongPressRight() {
    if (this.props.isDisabled || !this.isRightPressed) {
      if (this.props.touchMovement) {
        this.onRightPressed();
      }
      return;
    }
    if (this.props.isRecording) {
      Toast.success('camera_recording_block');
      return;
    }
    this.isRightLongPressed = true;
    this.onFastMoveNext();
    this.longRightPressTimeout = setTimeout(() => {
      this.handleLongPressRight();
    }, 50);
  }
  onFastMoveNext() {
    if (this.props.isCloud) {
      let timestamp = CloudVideoUtil.getFastMoveNextTime(this.state.currentTime);
      if (timestamp == -1) {
        return;// 没有数据；
      } else if (timestamp == -2) {
        // 超出检测范围了 不继续跳了
      } else {
        // 找到了；
        this.scrollToTimestampWithNotify(timestamp, false);// 滑动 但是不通知
      }
      return;
    }
    if (this.dataList.length == 0) {
      return;
    }
    let moveTime = 30000;
    let selectTime = this.state.currentTime + moveTime;
    if (selectTime > this.dataList[this.dataList.length - 1].endTime) {
      return;
    }
    if (selectTime >= this.dataList[this.dataList.length - 1].startTime) {
      this.scrollToTimestampWithNotify(selectTime, false);
      return;
    }
    for (let i = this.dataList.length - 2; i >= 0; i--) {
      let timeItem = this.dataList[i];
      this.dateTime.setTime(timeItem.startTime);
      if (selectTime > this.dateTime.getTime()) {
        if (selectTime < timeItem.endTime) {
          this.scrollToTimestampWithNotify(selectTime, false);// 滑动 但是不通知
        } else {
          this.scrollToTimestampWithNotify(this.dataList[i + 1].startTime, false);// 滑动但是不通知
        }
        break;
      }
    }
  }
  onFastMovePrev() {
    if (this.props.isCloud) {
      let timestamp = CloudVideoUtil.getFastMovePrevTime(this.state.currentTime);
      if (timestamp == -1) {
        return;// 没有数据；
      } else if (timestamp == -2) {
        // 超出检测范围了 不继续跳了
      } else {
        // 找到了；
        this.scrollToTimestampWithNotify(timestamp, false);// 滑动 但是不通知
      }
      return;
    }


    if (this.dataList.length == 0)
      return;
    let moveTime = 30000;
    let selectTime = this.state.currentTime - moveTime;
    this.dateTime.setTime(this.dataList[0].startTime);
    if (selectTime < this.dateTime.getTime()) {
      return;
    }
    if (selectTime < this.dataList[0].endTime) {
      this.scrollToTimestampWithNotify(selectTime, false);
      return;
    }
    for (let i = 1, len = this.dataList.length; i < len; i++) {
      let timeItem = this.dataList[i];
      if (selectTime < timeItem.startTime) {
        if (selectTime < this.dataList[i - 1].endTime - moveTime) {
          this.scrollToTimestampWithNotify(selectTime, false);
        } else {
          this.scrollToTimestampWithNotify(this.dataList[i - 1].endTime - moveTime, false);
        }
        break;
      }
    }
  }

  onMoveNext() {

    if (this.props.isCloud) {
      let timestamp = CloudVideoUtil.moveNext(this.state.currentTime);
      if (timestamp == -1) {
        return;// 没有数据；
      } else if (timestamp == -2) {
        // 超出检测范围了 不继续跳了
        this.onEndReach();
      } else {
        // 找到了；
        this._onTimeScaleScrolled(timestamp);
      }
      return;
    }

    if (this.dataList.length <= 0) {
      return;
    }
    let centerValue = this.state.currentTime;
    if (centerValue >= this.dataList[this.dataList.length - 1].endTime) { // 超过最后一个了 不让继续跑了
      this.onEndReach();
      return;
    }
    if (centerValue > this.dataList[this.dataList.length - 1].startTime) {
      this.onEndReach();
      return;
    }
    let timeItem = null;
    // if (this.state.eventTypeFlags == EVENT_TYPE.Default) {
    //   timeItem = this.getNeedItem(centerValue, true);
    // } else {
    //   timeItem = this.getNeedItemNew(centerValue, true);
    // }
    timeItem = this.getNeedItemNew(centerValue, true);
    if (timeItem != null) {
      this._onTimeScaleScrolled(timeItem.startTime);
      return;
    }
    this.onEndReach();
  }

  onMovePrev() {
    if (this.props.isCloud) {
      let timestamp = CloudVideoUtil.movePrev(this.state.currentTime);
      if (timestamp == -1) {
        return;// 没有数据；
      } else if (timestamp == -2) {
        // 超出检测范围了 不继续跳了

      } else {
        // 找到了；
        this._onTimeScaleScrolled(timestamp);
      }
      return;
    }

    if (this.dataList == null) {
      return;
    }
    if (this.dataList.length <= 0) {
      return;
    }
    let currentValue = this.state.currentTime;
    let selectTime = currentValue;
    let time = this.dataList[0].startTime;
    if (selectTime < time) {
      return;// 不让继续往前跑了
    }
    if (selectTime < this.dataList[0].endTime) {
      this._onTimeScaleScrolled(this.dataList[0].startTime);
      return;
    }
    let timeItem = null;
    // if (this.state.eventTypeFlags == EVENT_TYPE.Default) {
    //   timeItem = this.getNeedItem(selectTime, false);
    // } else {
    //   timeItem = this.getNeedItemNew(selectTime, false);
    // }
    timeItem = this.getNeedItemNew(selectTime, false);
    if (timeItem != null) {
      this._onTimeScaleScrolled(timeItem.startTime);
      return;
    }
    this._onTimeScaleScrolled(this.dataList[0].startTime);
    return;
  }

  _onTimeScaleScrolled(timestamp) {

    this.scrollState = SCROLLSTATE.SCROLLING;
    this.scrollToTimestampWithNotify(timestamp, false);// 滑动 但是不通知
    this.endTimeout && clearTimeout(this.endTimeout);
    this.endTimeout = setTimeout(() => {
      this.scrollState = SCROLLSTATE.IDLE;
      this.scrollToTimestampWithNotify(this.state.currentTime, true);
    }, 1200);
  }

  getNeedItem(time, next) {
    let lastTime = null;
    if (next) { // 找到下一个连续区域的起点。

      for (let i = 0, j = this.dataList.length - 1; i <= j; i++) {
        let timeItem = this.dataList[i];
        if (i < j) { // 没有到文件末尾
          let nextItem = this.dataList[i + 1];
          if ((nextItem.startTime - timeItem.endTime) < 5001) { // 如果隔得很近
            continue;// 继续找，直到找到不连续的地方
          }
          if (nextItem.startTime > time) { // 不连续区域的起点，比指定时间大，就认为找到了。
            lastTime = nextItem;
            break;
          }
        }
      }
    } else { // 找到上一个连续区域的的起点。
      for (let i = this.dataList.length - 1, j = 0; i >= j; i--) {
        let timeItem = this.dataList[i];
        if (i > 0) {
          let lastItem = this.dataList[i - 1];
          if ((timeItem.startTime - lastItem.endTime) <= 5001) {
            continue;
          }
        }
        this.dateTime.setTime(timeItem.startTime);
        if (this.dateTime.getTime() < time) {
          lastTime = timeItem;
          break;
        }
      }
    }
    return lastTime;
  }

  getNeedItemNew(time, next, needEvent = false) {
    if (next) { // 找到下一个连续区域的起点。
      for (let i = 0, j = this.dataList.length - 1; i <= j; i++) {
        let curItem = this.dataList[i];
        let startTime = curItem.startTime;
        let eventType = curItem.eventType;
        if (startTime > time) {
          if (needEvent) {
            let shouldDraw = EventTypeUtil._shouldDrawEvent(this.state.eventTypeFlags, eventType);
            if (shouldDraw) {
              return curItem;
            }
          } else {
            return curItem;
          }

        }
      }
      return null;
    } else { // 找到上一个连续区域的的起点。
      for (let i = this.dataList.length - 1, j = 0; i >= j; i--) {
        let curItem = this.dataList[i];
        let startTime = curItem.startTime;
        let eventType = curItem.eventType;
        if (startTime < time) {
          if (needEvent) {
            let shouldDraw = EventTypeUtil._shouldDrawEvent(this.state.eventTypeFlags, eventType);
            if (shouldDraw) {
              return curItem;
            }
          } else {
            return curItem
          }
        }
      }
      return null;
    }
  }

  onEndReach() {

    if (this.props.isCloud) {
      let cloudItem = CloudVideoUtil.getLastestVideo();
      if (cloudItem == null) {
        return;
      }
      this._onTimeScaleScrolled(cloudItem.endTime);
    } else {
      if (this.dataList == null || this.dataList.length <= 0) {
        return;
      }
      this._onTimeScaleScrolled(this.dataList[this.dataList.length - 1].endTime);
    }
  }

  scrollToTimestampWithNotify(timestamp, shouldNotify) {

    if (shouldNotify) { // todo 暂时只有通知的时候会刷新，其他的时候 例如左右长按 点按不启用刷新，看看后续效果
      this.setState(() => {
        return { currentTime: timestamp };
      }, () => {
        this.refreshTopDateView(this.state.currentTime);
        this.props.onScrollEnd && this.props.onScrollEnd(this.state.currentTime);
      });
    } else {
      // let time = this.state.currentTime - Math.ceil((this.pan.__getValue()) * this.oneUnitWidthTime)
      this.setState(() => { return { currentTime: timestamp }; }, () => {
        this.props.onScrolling && this.props.onScrolling(this.state.currentTime);
      });
    }
  }

  setScale(scale) {
    let diffScale = Number(((scale) / 100).toFixed(2));
    let tempScaleFactor = this.curScaleFactor + diffScale;
    if (tempScaleFactor > SCALING_FACTOR_MAX) {
      if (this.scaleType < Scale_Type_Small_Min) {
        this.isNeedInit = true;
        tempScaleFactor = 1.1;
        this.scaleType += 1;
      } else {
        tempScaleFactor = SCALING_FACTOR_MAX;
      }
    } else if (tempScaleFactor < SCALING_FACTOR_MIN) {
      if (this.scaleType > Scale_Type_Max) {
        this.isNeedInit = true;
        tempScaleFactor = 1.4;
        this.scaleType -= 1;
      } else {
        tempScaleFactor = SCALING_FACTOR_MIN;
      }
    }
    this.updateScale(tempScaleFactor);
  }

  updateScale(tempScaleFactor, sendEmitter = true) {
    this.curScaleFactor = tempScaleFactor;
    this.itemInterval = this.initItemInterval * tempScaleFactor;
    this.oneUnitWidthTime = this.intervalTime / this.itemInterval; // 一个宽度占多少毫秒
    this.setState({});
    if (sendEmitter) {
      DeviceEventEmitter.emit(TileLineScaleChangeEvent, { scaleType: this.scaleType, fromVertical: false });
    }
  }

  putIntoAlarmAreaList(type, startX, endX) {
    if (Number.isNaN(startX) || Number.isNaN(endX)) {
      return;
    }
    if (type == EventTypeConfig.SD_CAT) type = EventTypeConfig.SD_DOG;
    if (type == EventTypeConfig.SD_KNOWN_FACE) type = EventTypeConfig.SD_FACE;
    if (!this.allAreas[type]) {
      this.allAreas[type] = [];
    }
    this.allAreas[type].push({ startX: startX, endX: endX });
  }

  initPanResponder() {

    let startX = 0;
    let lastScale = 0;
    let isMulti = false;
    let panResponderStartX = -1;
    let panResponderStartY = -1;


    this.panResponder = PanResponder.create({
      // 单机手势是否可以成为响应者
      onStartShouldSetPanResponder: (evt, gestureState) => {
        // //console.log("onStartShouldSetPanResponder");
        panResponderStartX = evt.nativeEvent.changedTouches[0].locationX;
        panResponderStartY = evt.nativeEvent.changedTouches[0].locationY;
        return false;
      },
      // 移动手势是否可以成为响应者
      onMoveShouldSetPanResponder: (evt, gestureState) => true,
      // 拦截子组件的单击手势传递,是否拦截
      onStartShouldSetPanResponderCapture: (evt, gestureState) => false,
      // 拦截子组件的移动手势传递,是否拦截
      onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
        return true;
      },
      /** *************** 响应者事件回调处理 **************** */
      // 单击手势监听回调
      onPanResponderGrant: (e, gestureState) => {
        if (this.props.isDisabled) {
          return;
        }

        if (this.props.isRecording) {
          Toast.success('camera_recording_block');
          return;
        }
        // //console.log(TAG, 'onPanResponderGrant==>' + '单击手势申请成功,开始处理手势');
        // //console.log(TAG, "onPanResponderGrant: " + e.nativeEvent.locationX + " " + e.nativeEvent.locationY 
        // + " " + gestureState.dx + " " + gestureState.dy 
        // + " " + gestureState.vx + " " + gestureState.vy);
        isMulti = false;
        startX = 0;
        lastScale = 0;
        this.scrollState = SCROLLSTATE.IDLE;
        this.endTimeout && clearTimeout(this.endTimeout);
        this.pan.setValue(0);
      },
      // 移动手势监听回调
      onPanResponderMove: (e, gestureState) => {
        if (this.props.isDisabled) {
          return;
        }
        if (this.props.isRecording) {
          Toast.success('camera_recording_block');
          return;
        }
        // //console.log(TAG, 'onPanResponderMove==>' + '移动手势申请成功,开始处理手势');
        // //console.log(TAG, `onPanResponderMove: down length = ${e.nativeEvent.changedTouches.length}`);
        if (e.nativeEvent.changedTouches.length > 1) {
          if(!this.preScaleType){
            this.preScaleType = this.scaleType;
          } 
          this.scrollState = SCROLLSTATE.SCALING;

          isMulti = true;
          let value = Math.abs(e.nativeEvent.changedTouches[1].locationX - e.nativeEvent.changedTouches[0].locationX);
          if (lastScale != 0) {
            let scale = Number.parseInt(value - lastScale);
            this.setScale(scale);
          }
          lastScale = value;
        } else {
          if (!isMulti) {
            this.scrollState = SCROLLSTATE.SCROLLING;

            // //console.log("横向偏移量", gestureState.dx);
            // this.state.currentTime = this.state.currentTime - Math.ceil((gestureState.dx - startX) * this.oneUnitWidthTime); //这里暂时不修改
            // this.setState({});
            // this.notifyChangeTime(true, gestureState.dx < 0);

            // 等效于 this.pan = gestureState.dx && notify
            // this.pan.setValue(startX);
            let distance = gestureState.dx - startX;
            let time = this.state.currentTime - (distance < 0 ? Math.floor(distance) : Math.ceil(distance)) * this.oneUnitWidthTime;
            // let limitTime = new Date
            // 在云存 SD卡上 左划
            // 右滑
            time = time < this.cloudSdMaxTime ? (time > this.cloudSdMinTime ? time : this.cloudSdMinTime) : this.cloudSdMaxTime;
            this.setState(() => { return { currentTime: time }; }, () => {
              this.scrollToTimestampWithNotify(this.state.currentTime, false);
            });
            // let time = this.state.currentTime - Math.ceil(gestureState.dx - startX) * this.oneUnitWidthTime;
            // //console.log("time:",time);
            // this.setState(() => { return { currentTime: time }; }, () => {
            //   this.scrollToTimestampWithNotify(this.state.currentTime, false);
            // });
            startX = gestureState.dx;
          }
        }
      },
      // 手势动作结束回调
      onPanResponderEnd: (e, gestureState) => {
        if (this.props.isDisabled) {
          return;
        }
        if (this.props.isRecording) {
          Toast.success('camera_recording_block');
          return;
        }
        // //console.log(TAG, 'onPanResponderEnd==>' + '手势操作完成了,用户离开');
        this.isScrolling = false;
        // if (!isMulti) {

        // this.pan.setValue(0);

        this.endTimeout && clearTimeout(this.endTimeout);
        this.endTimeout = setTimeout(() => {
          this.scrollState = SCROLLSTATE.IDLE;
          this.scrollToTimestampWithNotify(this.state.currentTime, true);
        }, 1200);
        // }
      },
      // 手势释放, 响应者释放回调
      onPanResponderRelease: (e) => { //
        console.log(TAG, 'onPanResponderRelease==>' + '手势操作完成了,用户离开', this.props.isCloud, this.scaleType, this.preScaleType);
        let isCloud = this.props.isCloud;
        // 埋点 -- 云存时间轴刻度/SD卡时间轴 放大/缩小       在onPanResponderMove回调中记录之前的
        if(this.preScaleType != this.scaleType){
          if(isCloud){
            this.preScaleType > this.scaleType ? TrackUtil.reportClickEvent("Monitoring_CloudStorageTab_ViewTimeSlider_ZoomIn_Num") : TrackUtil.reportClickEvent("Monitoring_CloudStorageTab_ViewTimeSlider_ZoomOut_Num")
          } else {
            this.preScaleType > this.scaleType ? TrackUtil.reportClickEvent("Monitoring_PlayBackTab_ViewTimeSlider_ZoomIn_Num") : TrackUtil.reportClickEvent("Monitoring_PlayBackTab_ViewTimeSlider_ZoomOut_Num")
          }
          this.preScaleType = 0;
        }
        // 缩放手势释放，埋点--云存时间轴/SD卡时间轴1min、10min、30min、1h、2h曝光
        switch(this.scaleType){
          case Scale_Type_Max:
            isCloud ? TrackUtil.reportClickEvent("Monitoring_CloudStorageTab_ViewTimeSlider_Show5") : TrackUtil.reportClickEvent("Monitoring_PlayBackTab_ViewTimeSlider_Show5")
            break;
          case Scale_Type_Big:
            isCloud ? TrackUtil.reportClickEvent("Monitoring_CloudStorageTab_ViewTimeSlider_Show4") : TrackUtil.reportClickEvent("Monitoring_PlayBackTab_ViewTimeSlider_Show4")
            break;
          case Scale_Type_Middle:
            isCloud ? TrackUtil.reportClickEvent("Monitoring_CloudStorageTab_ViewTimeSlider_Show3") : TrackUtil.reportClickEvent("Monitoring_PlayBackTab_ViewTimeSlider_Show3")
            break;
          case Scale_Type_Small:
            isCloud ? TrackUtil.reportClickEvent("Monitoring_CloudStorageTab_ViewTimeSlider_Show2") : TrackUtil.reportClickEvent("Monitoring_PlayBackTab_ViewTimeSlider_Show2")
            break;
          case Scale_Type_Small_Min:
            isCloud ? TrackUtil.reportClickEvent("Monitoring_CloudStorageTab_ViewTimeSlider_Show1") : TrackUtil.reportClickEvent("Monitoring_PlayBackTab_ViewTimeSlider_Show1")
            break;
        }
      },
      // 手势申请失败,未成为响应者的回调
      onResponderReject: (e) => {
        // 申请失败,其他组件未释放响应者
        // //console.log(TAG, 'onResponderReject==>' + '响应者申请失败');
      },
      // 当前手势被强制取消的回调
      onPanResponderTerminate: (e) => { // 取消的时候也要处理一下。
        if (this.props.isDisabled) {
          return;
        }
        // //console.log(TAG, 'onPanResponderEnd==>' + '手势操作完成了,用户离开');
        this.isScrolling = false;
        // if (!isMulti) {

        // this.pan.setValue(0);

        this.endTimeout && clearTimeout(this.endTimeout);
        this.endTimeout = setTimeout(() => {
          this.scrollState = SCROLLSTATE.IDLE;
          this.scrollToTimestampWithNotify(this.state.currentTime, true);
        }, 1200);
        // 另一个组件已经成为了新的响应者，所以当前手势将被取消
        // //console.log(TAG, 'onPanResponderTerminate==>' + '由于某些原因(系统等)，所以当前手势将被取消');
      },
      onShouldBlockNativeResponder: (evt, gestureState) => {
        // 返回一个布尔值，决定当前组件是否应该阻止原生组件成为JS响应者
        // 默认返回true。目前暂时只支持android。
        return true;
      }
    });
  }

  formatTimeInMMSS(timestamp) {
    this.dateTime.setTime(timestamp);
    return `${this.dateTime.getDay()} ${this.dateTime.getHours()} ${this.dateTime.getMinutes()} ${this.dateTime.getSeconds()}`;
  }
}

let timeMargin = 8;

let timeWidth = (kWindowWidth - 30 - timeMargin * 8) / 7;
let timeWidth_2 = kWindowWidth / 7;
// let eventMarginLeft = (kWindowWidth-timeWidth*8-eventMargin*4)/2
export const styles = StyleSheet.create({
  container: {
    backgroundColor: "#ffffff",
    width: '100%',
    height: '100%',
    display: "flex",
    flexWrap: "nowrap"
  },
  // 每天的样式是这里
  dayItemSelected: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    height: timeWidth,
    width: timeWidth,
    marginHorizontal: timeMargin,
    borderRadius: timeWidth / 2,
    backgroundColor: "#f0f0f0"
  },

  dayItem: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    height: timeWidth,
    width: timeWidth,
    marginHorizontal: timeMargin
    // borderRadius: timeWidth/2


  },

  dayItemTextWeek: {
    color: "#7f7f7f", fontSize: kIsCN ? 11 : 9
  },

  dayItemTextDay: {
    color: "#000000", fontSize: kIsCN ? 16 : 14
  }
});
