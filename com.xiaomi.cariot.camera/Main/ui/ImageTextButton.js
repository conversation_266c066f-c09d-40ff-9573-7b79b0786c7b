/**
 * @export
 * @description 图片文字按钮
 *
 */
import React from 'react';
import {Image, Text, View,TouchableWithoutFeedback} from 'react-native';
export default class ImageTextButton extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      buttonPressed: false,
    }
  }
  static initialState = {
    buttonPressed: false,
  };
  static defaultProps = {
    source: null,
    highlightedSource: null,
    onPress: null,
  };
  _buttonPressIn() {
    this.setState({ buttonPressed: true });
  }
  _buttonPressOut() {
    this.setState({ buttonPressed: false });
  }
  _isButtonPressed() {
    return this.state.buttonPressed;
  }
  render() {
    let source = this.props.source;
    if (this._isButtonPressed() && this.props.highlightedSource) {
      source = this.props.highlightedSource;
    }
    // const Touchable =
    //   Platform.OS === 'android' ? TouchableNativeFeedback : TouchableWithoutFeedback;
    return (
      <TouchableWithoutFeedback
        disabled={this.props.disabled}
        onPress={this.props.onPress}
        onPressIn={this._buttonPressIn.bind(this)}
        onPressOut={this._buttonPressOut.bind(this)}
        style={this.props.containerStyle}
      >
        <View style={[{alignItems:'center',justifyContent:'center'},this.props.style]}>
          <Image
          style={this.props.imageStyle}
          source={source} />

          <Text numberOfLines={1} ellipsizeMode={'tail'} style={[this.props.textStyle]}>{this.props.text}</Text>
        </View>
      </TouchableWithoutFeedback>
    );
  }
}
// module.exports = ImageButton;