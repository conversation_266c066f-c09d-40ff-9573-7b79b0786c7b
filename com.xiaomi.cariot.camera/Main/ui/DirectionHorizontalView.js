import { DarkMode } from 'miot';
import CameraConfig, { IMG_DARKMODE_TINT } from '../util/CameraConfig';

import {
  View, PanResponder, Image
} from 'react-native';
import PropTypes from 'prop-types';

import React from 'react';
import { DescriptionConstants } from '../Constants';




export const DirectionViewConstant = {
  DIRECTION_lEFT: 1,
  DIRECTION_UP: 3,
  DIRECTION_RIGHT: 2,
  DIRECTION_BOTTOM: 4,
  CMD_CHECK: 5,
  CMD_GET: 6,
  CMD_OFF: -1001,
  CMD_CHECK_END: -5
};

export default class DirectionHorizontalView extends React.Component {

  static propTypes = {
    isPortrait: PropTypes.bool,
    onActionDown: PropTypes.func,
    onActionUp: PropTypes.func,
    onClickDirection: PropTypes.func
  };

  static defaultProps = {
    isPortrait: true
  }

  constructor(props) {
    super(props);


    // let isPortrait = this.props.isPortrait || true;// 默认为竖直的
    let isPortrait = this.props.isPortrait;
    this.bgImg = isPortrait ? require("../../Resources/Images/direction_h_portrait.png") : require("../../Resources/Images/direction_h_land.png");
    this.leftImg = isPortrait ? require("../../Resources/Images/direction_h_portrait_left_pres.png") : require("../../Resources/Images/direction_h_land_left_pres.png");
    this.rightImg = isPortrait ? require("../../Resources/Images/direction_h_portrait_right_pres.png") : require("../../Resources/Images/direction_h_land_right_pres.png");

    this.bgImgDark = require("../../Resources/Images/direction_h_portrait_dark.png");
    this.leftImgDark = require("../../Resources/Images/direction_h_portrait_left_pres_dark.png");
    this.rightImgDark = require("../../Resources/Images/direction_h_portrait_right_pres_dark.png");


    this.d = isPortrait ? 206 : 140;
    this.r = this.d / 2;


    this.downTime = 0;
    this.isPress = false;
    this.directionTimeout = null;

    this.panResponder = PanResponder.create({
      onStartShouldSetPanResponder: (evt) => { // 刚开始的时候
        let x = evt.nativeEvent.locationX;
        let y = evt.nativeEvent.locationY;

        let dx = x - this.r;
        let dy = y - this.r;
        let distance = Math.sqrt(dx * dx + dy * dy);
        if (distance > this.r) {
          return false;
        }
        return true;
      }, 
      onMoveShouldSetPanResponder: () => true,
      onShouldBlockNativeResponder: () => false,
      onPanResponderTerminationRequest: () => false, // 不允许其他人抢占。
      onPanResponderGrant: (evt) => {
        //
        clearTimeout(this.directionTimeout);
        // 判断区域
        this.downTime = new Date().getTime();
        let x = evt.nativeEvent.locationX;
        let y = evt.nativeEvent.locationY;

        console.log(`why!, this.d: ${ this.d }`);
        console.log(`why!, x: ${ x }`);
        console.log(`why!, y: ${ y }`);

        let dx = x - this.r;
        let dy = y - this.r;
        let distance = Math.sqrt(dx * dx + dy * dy);
        if (distance > this.r) {
          return;
        }

        if (x < this.d / 2) {
          this.isPress = true;// 按压了
          this.setState({ direction: DirectionViewConstant.DIRECTION_lEFT });
        } else {
          this.isPress = true;// 按压了
          this.setState({ direction: DirectionViewConstant.DIRECTION_RIGHT });
        }
        if (this.isPress && this.props.onActionDown != null) {
          this.props.onActionDown();
        }
        if (this.isPress) {
          this.directionTimeout = setTimeout(() => {
            this.onPressDirection();
          });
        }

      },

      onPanResponderMove: () => {
        // ignore
      },

      onPanResponderRelease: () => {
        // release
        this.cancleDown();
      },
      onPanResponderTerminate: () => {
        //   
        this.cancleDown();
      }
    });
  }

  cancleDown() {
    this.isPress = false;// 没有按压了
    if (this.props.onActionUp != null) {
      let temp = new Date().getTime() - this.downTime;
      this.props.onActionUp(temp > 500);
    }
    clearTimeout(this.directionTimeout);
    this.setState({ direction: -1 });
  }

  onPressDirection() {
    if (this.isPress) {
      clearTimeout(this.directionTimeout);
      if (this.props.onClickDirection != null) {
        this.props.onClickDirection(this.state.direction);
      }
      this.directionTimeout = setTimeout(() => {
        this.onPressDirection();
      }, 200);
    }
  }

  state = {
    direction: -1
  }

  componentDidMount() {

  }

  componentWillUnmount() {

  }

  onLayout = (event) => {
    console.log("why!, onLayout onLayout");
    this.d = event.nativeEvent.layout.width;
    this.r = this.d / 2;
    console.log(`why!, this.d: ${ this.d }`);
  }

  render() {
    let width = "100%";
    let colorScheme = DarkMode.getColorScheme();
    let isDark = false;
    if (colorScheme == 'dark' && this.props.isPortrait) {
      isDark = true;
    }

    let bgImage = isDark ? this.bgImgDark : this.bgImg;
    let pressView = null;
    if (this.state.direction == DirectionViewConstant.DIRECTION_lEFT) {
      pressView = isDark ? this.leftImgDark : this.leftImg;
    } else if (this.state.direction == DirectionViewConstant.DIRECTION_RIGHT) {
      pressView = isDark ? this.rightImgDark : this.rightImg;
    }

    let imgStyle = { width: "100%", height: "100%", position: "absolute" };

    let containerStyle = {
      display: "flex", 
      flexDirection: "column",
      width: width, 
      height: width, 
      position: "relative", 
      alignItems: "center",
      justifyContent: "center"
    };

    if (this.props.isPortrait) {
      if (isDark) {
        containerStyle.backgroundColor = "#EEEEEE";
      } else {
        containerStyle.backgroundColor = "#ffffff";
      }
    }
    let label = this.props.accessibilityLabel;

    return (
      <View style={containerStyle}
        accessibilityLabel={label}
      >
        <View style={{ display: "flex", width: "100%", height: "50%", position: "relative", alignItems: "center" }}
          onLayout={this.onLayout}
          {...this.panResponder.panHandlers}
        >
          <View style={{ width: "80%", height: "100%", position: "relative" }}
          >
            <Image style={imgStyle}
              source={bgImage}
            />
            <Image style={{ width: "100%", height: "100%", position: "absolute" }}
              source={pressView}
            />
          </View>

        </View>
      </View>
    );
  }
}