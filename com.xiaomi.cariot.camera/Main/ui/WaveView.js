import React from 'react';
import PropTypes from 'prop-types';

import {
  Dimensions, View, ART, Animated, Easing
} from 'react-native';

const {
  Surface, Shape, Path
} = ART;

const ScreenWidth = Dimensions.get('window').width;
const ScreenHeight = Dimensions.get('window').height;

const tempX1 = 50; // 波浪1的步长
const tempX2 = 50; // 波浪2的步长
const tempX3 = 50; // 波浪3的步长

const baseRatiio = 0.3; // 浪高比例

export default class WaveView extends React.Component {

  static propTypes = {
    waveHeight: PropTypes.number,
    waveWidth: PropTypes.number,
    volume: PropTypes.number
  };

  static defaultProps = {
    waveHeight: Math.ceil(ScreenHeight * 0.15),
    waveWidth: ScreenWidth,
    volume: 1
  };

  state = {
    baseX1: new Animated.Value(0),
    baseX2: new Animated.Value(0),
    baseX3: new Animated.Value(0),
    controlY: 15, // 波浪的振幅度
    startY: 0,
    up: true
    // volume:this.props.volume
  };

  /* componentWillReceiveProps(nextProps) {
      this.setState({
        volume: nextProps.volume,
      })
  } */


  componentDidMount() {

    this._changecontrolY();
    this._startAnimation1();
    this._startAnimation2();
    this._startAnimation3();
  }

  componentWillUnmount() {
    this.intervalTimer && clearTimeout(this.intervalTimer);
  }

  // 让控制点的Y坐标动态变化
  _changecontrolY= () => {
    this.intervalTimer = setInterval(() => {
      const { controlY, startY, up } = this.state;

      let p = controlY;
      const y = startY;
      let up0 = up;

      if (up) {
        p = controlY + 0.5;
        up0 = p <= 15;
      } else {
        p = controlY - 0.5;
        up0 = p < 10;
      }
      this.setState({
        controlY: p,
        startY: y,
        up: up0
      });
    }, 100);
  }

  _startAnimation1 = () => {
    const baseX = this.state.baseX1;

    baseX.setValue(-4 * tempX1);
    Animated.timing(baseX, {
      toValue: 0, // 目标值
      duration: 3000 - (this.props.volume - 1) * 500, // 动画时间 这个控制了波浪的频率
      easing: Easing.linear // 缓动函数
    }).start(() => {
      this._startAnimation1();
    });
  };

  _startAnimation2 = () => {
    const baseX2 = this.state.baseX2;

    baseX2.setValue(-4 * tempX2);
    Animated.timing(baseX2, {
      toValue: 0, // 目标值
      duration: 4000 - (this.props.volume - 1) * 500, // 动画时间
      easing: Easing.linear // 缓动函数
    }).start(() => {
      this._startAnimation2();
    });
  };

  _startAnimation3 = () => {
    const { baseX3 } = this.state;

    baseX3.setValue(-4 * tempX3);

    Animated.timing(baseX3, {
      toValue: 0, // 目标值
      duration: 5000 - (this.props.volume - 1) * 500, // 动画时间
      easing: Easing.linear // 缓动函数
    }).start(() => {
      this._startAnimation3();
    });
  };


  /**
   * 获取贝塞尔曲线路径
   */
  _getBezierPath= (temp, controlY) => {
    const waveHeight = this.props.waveHeight;
    const startX = -4 * temp;
    const startY = waveHeight * (1 - baseRatiio);
    const number = ScreenWidth / temp + 1;

    let speedStr = `M${ startX } ${ waveHeight } L${ startX } ${ startY }`;

    for (let i = 0; i <= number; i++) {
      speedStr += `Q${ startX + (i * 4 + 1) * temp } ${ startY
      - controlY }, ${ startX + (i * 4 + 2) * temp } ${ startY }`;
      speedStr += `Q${ startX + (i * 4 + 3) * temp } ${ startY
      + controlY }, ${ startX + (i * 4 + 4) * temp } ${ startY }`;
    }
    const path = new Path(speedStr);
  

    // 形成闭合区域
    // path.lineTo(startX + (number * 4 + 4) * temp, waveHeight).close();

    return path;
  }

  render() {
    const {
      waveHeight, waveWidth
    } = this.props;

    const { baseX1: baseX, baseX2, baseX3, controlY } = this.state;

    const _path1 = this._getBezierPath(tempX1, controlY);
    const _path2 = this._getBezierPath(tempX2, controlY / 1.5);
    const _path3 = this._getBezierPath(tempX3, controlY / 2);

    return (
      <View
        style={{
          overflow: 'hidden'
        }}
      >
        <View
          style={{
            height: waveHeight,
            width:
                    waveWidth + 4 * tempX1 > tempX2 ? tempX1 : tempX2
          }}
        >
          <Animated.View
            style={{
              height: waveHeight,
              width: waveWidth + 4 * tempX1,
              position: 'absolute',
              left: baseX
            }}
          >
            <Surface
              height={waveHeight}
              width={waveWidth + 8 * tempX1}
            >
              <Shape d={_path1} stroke={'#6495ED'} strokeWidth={0.5}/>
            </Surface>
          </Animated.View>

          <Animated.View
            style={[
              {
                height: waveHeight,
                width: waveWidth + 4 * tempX2,
                position: 'absolute',
                left: baseX2
              }
            ]}
          >
            <Surface
              height={waveHeight}
              width={waveWidth + 8 * tempX2}
            >
              <Shape d={_path2} stroke={'#6495ED'} strokeWidth={0.5}/>
            </Surface>
          </Animated.View>

          <Animated.View
            style={[
              {
                height: waveHeight,
                width: waveWidth + 4 * tempX3,
                position: 'absolute',
                left: baseX3
              }
            ]}
          >
            <Surface
              height={waveHeight}
              width={waveWidth + 8 * tempX3}
            >
              <Shape d={_path3} stroke={'#87CEFA'} strokeWidth={0.5}/>
            </Surface>
          </Animated.View>
        </View>
      </View>
    );
  }
}