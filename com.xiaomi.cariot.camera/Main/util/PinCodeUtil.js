import { Device, Service } from "miot";
import StorageKeys from "../StorageKeys";
import LogUtil from "./LogUtil";

export default class PinCodeUtil {

  static queryShouldPopPinCodeSeting() {
    return new Promise((resolve, reject) => {
      if (!Device.isOwner) {
        resolve(false);// 不需要处理密码弹框;
        return;
      }
      
      Service.getServerName()
        .then((country) => {
          LogUtil.logOnAll("PincodeUtil", "country:" + country.countryCode);
          if (country.countryCode.toLocaleLowerCase() != "kr") {
            resolve(false);
            return;
          }
          if (Device.isSetPinCode) {// 只有韩国地区才需要设置，其他地方都不需要设置。
            this.setPincodeSet();
            resolve(false);// 已经设置了密码，没有必要再弹  (第一次进来的时候其实不会这样子，主要处理后续的步骤)
            return;
          }
          return StorageKeys.IS_PINCODE_SETTING_FORCE.catch((err) => { return true; });

        })
        .then((result) => {
          LogUtil.logOnAll("PincodeUtil", "local prop:" + result);
          if (result == true && !Device.isNew) { // 之前已经弹过了 & 不是刚绑定进来的设备
            resolve(false);// 没有必要再弹了。
          } else {// 强制设置缓存为false，强制去弹
            StorageKeys.IS_PINCODE_SETTING_FORCE = false;
            return Service.smarthome.batchGetDeviceDatas([{ did: Device.deviceID, props: ["prop.s_force_pin_code_for_korean"] }]).catch((error) => { return JSON.stringify(error); });
          }
        })
        .then((result) => {
          LogUtil.logOnAll("PincodeUtil", "server value:" + JSON.stringify(result));
          if (typeof (result) == "string") { // 走到这里说明本地和服务器都认为没有出错 或者没有弹过
            resolve(false);// 强制弹
          } else {
            let forcePincode = false;
            result = result[Device.deviceID];
            let config;
            if (result && result['prop.s_force_pin_code_for_korean']) {
              config = result['prop.s_force_pin_code_for_korean'];
            }
            if (config) { // 代表是绑定后进来插件的 需要弹弹框
              config = JSON.parse(config);
              config = config["forcePinCode"];
              forcePincode = config;
            }
            resolve(forcePincode);// 密码没有设置，返回true，密码设置了，返回false。
          }
        })
        .catch((err) => {
          resolve(false);
          // 获取国家码出错 不弹 
        });


    });
    
  }

  // 走到这里是肯定要标记服务器状态了。
  static setPincodeSet() {
    StorageKeys.IS_PINCODE_SETTING_FORCE
      .then((result) => {
        if (result == null || result == "") { // 没有弹过
          StorageKeys.IS_PINCODE_SETTING_FORCE = true;
          Service.smarthome.batchSetDeviceDatas([{ did: Device.deviceID, props: { "prop.s_force_pin_code_for_korean": JSON.stringify({ "forcePinCode": false }) } }]);// 设置一次属性。
        } else if (result == false) { // 没有弹过
          Service.smarthome.batchSetDeviceDatas([{ did: Device.deviceID, props: { "prop.s_force_pin_code_for_korean": JSON.stringify({ "forcePinCode": false }) } }]);// 设置一次属性。
        }
      })
      .catch((err) => {
        StorageKeys.IS_PINCODE_SETTING_FORCE = true;
        Service.smarthome.batchSetDeviceDatas([{ did: Device.deviceID, props: { "prop.s_force_pin_code_for_korean": JSON.stringify({ "forcePinCode": false }) } }]);// 设置一次属性。
      });
  }

}