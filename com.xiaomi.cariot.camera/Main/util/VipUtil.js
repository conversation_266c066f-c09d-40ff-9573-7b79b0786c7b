import API from '../API';
import StorageKeys from '../StorageKeys';
import CameraConfig from '../util/CameraConfig';
import dayjs from "dayjs";
import Util from "../util2/Util";

export default class VipUtil {
  // 上传点击信息  reportClickEvent("xxxx");
  // static async getVipStatus() {
  //   return new Promise((resolve, reject) => {
  //     API.get("/miot/camera/app/v1/vip/status", "business.smartcamera")
  //       .then((result) => {
  //         if (result.code != 0) {
  //           reject(-1);
  //         }
  //         let data = result.data;
  //         if (data == null) {
  //           reject(-2);
  //         }
  //         let vip = data["vip"];
  //         let inWindow = !data["closeWindow"]; // false表示在窗口内，true表示不在
  //         StorageKeys.IS_VIP_STATUS = vip;
  //         StorageKeys.IN_CLOSE_WINDOW = inWindow;
  //         StorageKeys.VIP_DETAIL = data;
  //         this.isVip = vip;
  //         CameraConfig.isVip = vip;
  //         CameraConfig.isToUpdateVipStatue = false;
  //         resolve({ isVip: this.isVip, inCloseWindow: inWindow, data: data });
  //       })
  //       .catch((err) => {
  //         console.log("VipUtil getVipStatus", err);
  //         StorageKeys.IN_CLOSE_WINDOW = false;
  //         reject(-3);
  //       });
  //   });
  // }

  static async getVipStatus() {
    return new Promise((resolve, reject) => {
      API.get("/miot/camera/app/v1/vip/status", "business.smartcamera")
        .then((result) => {
          console.log("vip data",result);
          if (result.code != 0) {
            reject(-1);
          }
          let data = result.data;
          if (data == null) {
            reject(-2);
          }
          let vip = data["vip"];
          let inWindow = !data["closeWindow"]; // false表示在窗口内，true表示不在
          let vipBindStatus = data["vipBindStatus"];
          let status = data["status"];
          let endTime = data["endTime"];
          this.updateUserType(vip, status, endTime);
          StorageKeys.IS_VIP_STATUS = vip;
          StorageKeys.IN_CLOSE_WINDOW = inWindow;
          StorageKeys.VIP_DETAIL = data;
          CameraConfig.vipDetail = data;
          this.isVip = vip;
          CameraConfig.isVip = vip;
          CameraConfig.isToUpdateVipStatue = false;
          let showCloudForExpired = false;
          if (!vip) {
            showCloudForExpired = this.showCloudContentforExpiredUser(data);
          }
          CameraConfig.showCloudForExpired = showCloudForExpired;
          resolve({ isVip: this.isVip, inCloseWindow: inWindow, data: data, vipBindStatus: vipBindStatus });
        })
        .catch((err) => {
          console.log("VipUtil getVipStatus", err);
          StorageKeys.IN_CLOSE_WINDOW = false;
          reject(-3);
        });
    });
  }
  static showCloudContentforExpiredUser(vipDat) {
    if (vipDat.pacakgeType) {
      let days = 0;
      if (vipDat.pacakgeType.indexOf('7') != -1) {
        days = 7;
      } else if (vipDat.pacakgeType.indexOf('30') != -1) {
        days = 30;
      }
      if (days == 0) {
        return false;
      }
      return !Util.isDaysAgo(vipDat.endTime, days);
    }
    return false;
  }

  static _userType;
  static updateUserType(isVip, status, endTime) {
    let user_type = 0;
    if (isVip) {
      let curTime = new Date().getTime();
      let willEndDays = dayjs(endTime).endOf('day').diff(dayjs(curTime), 'day');
      user_type = willEndDays > 7 ? 1 : 2;
    } else {
      user_type = status == 0 ? 0 : 3;
    }
    this._userType = user_type;
  }

  static getUserType() {
    return this._userType;
  }

  static async getVipValue() {
    return new Promise((resolve, reject) => {
      API.get("/miot/camera/app/v1/vip/status", "business.smartcamera")
        .then((result) => {
          if (result.code != 0) {
            reject(false);
          }
          let data = result.data;
          if (data == null) {
            reject(false);
          }
          let vip = data["vip"];
          resolve(vip);
        })
        .catch((err) => {
          reject(false);
        });
    });
  }
}