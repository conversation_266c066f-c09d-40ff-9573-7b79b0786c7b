import Toast from "../components/Toast";

let isCalled = false, timer;
let lastClickTime = null;
/**
 * @Author: byh
 * @Date: 2024/5/15
 * @explanation:
 * @param functionTobeCalled method 对调函数体
 * @param interval  定时器
 * 防止快速点击多次重复进入同一页面
 *********************************************************/
export function handlerOnceTap(functionTobeCalled, interval = 600) {
  if (!isCalled) {
    isCalled = true;
    clearTimeout(timer);
    timer = setTimeout(() => {
      isCalled = false;
    }, interval);
    return functionTobeCalled();
  }
}

export function handlerOnceTapWithToast(functionTobeCalled, interval = 600) {
  return () => {
    const currentTime = new Date().getTime();
    if (lastClickTime == null || currentTime - lastClickTime >= interval) {
      lastClickTime = currentTime;
      functionTobeCalled();
      return;
    }
    Toast.success('execute_often');
  };
}
