import React, {Component} from 'react'
import { Platform, PermissionsAndroid } from 'react-native';
import { System } from "miot";
import Toast from '../components/Toast';

export default class PermissionUtil extends Component {
  // check读取存储权限
  static checkStoragePermission() {
    return new Promise((resolve, reject) => {
      if (Platform.OS == "android") {
    
        PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE)
          .then((result) => {
            console.log("🚀 ~ file: FaceManagerNumber.js ~ line 539 ~ FaceManagerNumber ~ .then ~ result", result);
            if (result === PermissionsAndroid.RESULTS.GRANTED) {
              resolve();
            }
            else if (result === PermissionsAndroid.RESULTS.DENIED) {
              Toast.success('camera_no_write_permission');
              reject(PermissionsAndroid.RESULTS.DENIED);
            } else {
              reject(PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN);
            }
          })
          .catch((err) => {
            reject(err);
            Toast.success("action_failed");
          });
      } else {
        System.permission.request("photos").then((res) => {
          // alert(`requestPermission,result:${ res }`);
          if (res) {
            resolve();
          } else {
            Toast.success('auth_fail');
          }
    
        }).catch((error) => {
          // alert(`requestPermission,error:${ JSON.parse(error) }`);
          reject(PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN);
        });
      }
    });
  }

  // check相机权限
  static checkCameraPermission() {
    return new Promise((resolve, reject) => {
      if (Platform.OS == "android") {

        PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.CAMERA)
          .then((result) => {
            console.log("🚀 ~ file: FaceManagerNumber.js ~ line 574 ~ FaceManagerNumber ~ .then ~ result", result)
            if (result === PermissionsAndroid.RESULTS.GRANTED) {
              resolve();
            } else if (result === PermissionsAndroid.RESULTS.DENIED) {
              Toast.success('please_open_camera');
              reject(result);
            } else {
              reject(PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN);
            }
          })
          .catch((err) => {
            reject(err);
            Toast.success("action_failed");
          })
      } else {
        System.permission.request("camera").then((res) => {
          // alert(`requestPermission,result:${ res }`);
          if (res) {
            resolve();
          }
          else {
            Toast.success('auth_fail');
          }

        }).catch((error) => {
          // alert(`requestPermission,error:${ JSON.parse(error) }`);
          reject(PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN);
        });
      }
    });
  }

}