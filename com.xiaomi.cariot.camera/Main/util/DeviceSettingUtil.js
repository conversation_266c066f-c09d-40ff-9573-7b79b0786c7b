import { Device, Service } from "miot";
export const CLICK_SETTING = "CLICK_SETTING";
export default class DeviceSettingUtil {

  // 前缀
  static clickCallSetting = "call_setting";
  static clickCallPrefixKey = "clickCall_";
  static singleClickSuffixKey = "single";
  static callPrefixKey = "call_";

  // 手势通话 前缀
  static gestureCallKey = "gestureCall_";
  static gestureCallKeySuffix = "gestureCall_";

  static judgeToAddOwnerToSingleClickCall() {
    if (!Device.isOwner) {
      return;
    }
    this.getDeviceSettingByPrefix(DeviceSettingUtil.clickCallPrefixKey).then(response => {
      if (response.code === 0 && JSON.stringify(response.result.settings) === '{}') {
        // 换取openID
        // 没有一个设置项，设置一个单击呼叫为当前主账号
        // 需要先load用户信息，否则可能拿不到用户信息，比如昵称等
        Service.account.load().then(account=>{
          let value = {userid: Service.account.ID,nickname: Service.account.nickName, icon: Service.account.avatarURL }
          console.log("]]]]]][[[[[[[[[[[[[")
          this.setDeviceSetting(`${DeviceSettingUtil.clickCallPrefixKey}${DeviceSettingUtil.singleClickSuffixKey}`,JSON.stringify(value));
        })
      }
    }).catch(error => {
      console.log("=======error: ",error)
    });
  }

  static setDeviceSetting(key, value) {
    return new Promise((resolve, reject) => {
      let params = { did: Device.deviceID, settings: { [key]: value } };
      console.log(`setDeviceSetting:request:${ JSON.stringify(params) }`);
      Service.smarthome.setDeviceSetting(params).then((response) => {
        console.log(`setDeviceSetting:response:${ JSON.stringify(response) }`);
        resolve(response);
      }).catch((err) => {
        console.log(`setDeviceSetting:err:${ err }`);
        reject(err);
      });
    });
  }

  /**
   * @Author: byh
   * @Date: 2024/8/8
   * @explanation:
   * valueArray ==>对象 {key1: stringValue1,key2: stringValue2}
   *********************************************************/
  static setDeviceSettingArray(valueArray) {
    return new Promise((resolve, reject) => {
      let params = { did: Device.deviceID, settings: valueArray };
      console.log(`setDeviceSettingArray:request:${ JSON.stringify(params) }`);
      Service.smarthome.setDeviceSetting(params).then((response) => {
        console.log(`setDeviceSettingArray:response:${ JSON.stringify(response) }`);
        resolve(response);
      }).catch((err) => {
        console.log(`setDeviceSettingArray:err:${ JSON.stringify(err) }`);
        reject(err);
      });
    });

  }
  /**
   *
   * @param key 获取的key对应的deviceSetting值
   * @return {Promise<unknown>}
   */
  static getDeviceSettingByKey(key) {
    return new Promise((resolve, reject) => {
      let params = { did: Device.deviceID, settings: [key] };
      console.log(`getDeviceSettingByKey:request:${ JSON.stringify(params) }`);
      Service.smarthome.getDeviceSettingV2(params).then((response) => {
        console.log(`getDeviceSettingByKey:response:${ JSON.stringify(response) }`);
        resolve(response);
      }).catch((err) => {
        reject(err);
      });
    })
  }
  /**
   * 获取服务器中 device 对应的数据，内部调用米家代理接口 /v2/device/getsettingv2
   * @param {object} prefix 前缀
   * @param {string} last_id   上一次请求返回的id，用于分页
   * @return {Promise}
   */
  static getDeviceSettingByPrefix(prefix,last_id = '') {
    return new Promise((resolve, reject) => {
      let params = { did: Device.deviceID, prefix_filter: prefix };
      if (last_id) {
        params.last_id = last_id;
      }
      console.log(`getDeviceSettingByPrefix:request:${ JSON.stringify(params) }`);
      Service.smarthome.getDeviceSettingV2(params).then((response) => {
        console.log(`getDeviceSettingByPrefix:response:${ JSON.stringify(response) }`);
        resolve(response);
      }).catch((err) => {
        reject(err);
      });
    })
  }

  /**
   * 获取app上报云端的所有数据
   * @param did
   */
  static getAllDeviceSetting() {
    return new Promise((resolve, reject) => {
      let params = { did: Device.deviceID };
      console.log(`getAllDeviceSetting:request:${ JSON.stringify(params) }`);
      Service.smarthome.getDeviceSettingV2(params).then((response) => {
        console.log(`getAllDeviceSetting:response:${ JSON.stringify(response) }`);
        resolve(response);
      }).catch((err) => {
        reject(err);
      });
    });
  }

  /**
   * 删除服务器中 device 对应的数据，内部调用米家代理接口/device/delsetting
   * @param {json} params  - 请求参数
   * @param {string} params.did did
   * @param {object} params.settings 指定要删除的key数组
   */
  static delDeviceSetting(settings, callback) {
    return new Promise((resolve, reject) => {
      let params = { did: Device.deviceID, settings: settings };
      console.log(`delDeviceSetting:request:${ JSON.stringify(params) }`);
      Service.smarthome.delDeviceSetting(params).then((response) => {
        console.log(`delDeviceSetting:response:${ JSON.stringify(response) }`);
        resolve(response);
      }).catch((err) => {
        console.log(`delDeviceSetting:error:${ JSON.stringify(response) }`);
        reject(err);
      });
    });
  }

  /**
   * @Author: byh
   * @Date: 2024/7/17
   * @explanation:
   * 添加通话记录
   * 暂时针对设备通话，发起通话
   *********************************************************/
  static addCallNote() {
    //{"did": "1067669099", "key": "18.2", "time": 1721194798, "type": "event", "uid": "2681052717", "value": "[\"1721194784\",\"1\",\"测试部\",\"2681052717\",\"0\",\"13\",\"0\"]"}
    let time = Math.round(new Date().getTime()/1000);
    let value = [time+"","1","测试添加","2681052717","0","13","0"];
    let params = {
      did: Device.deviceID,
      key: "18.2",
      type: 'event',
      uid: "2681052717",
      value: JSON.stringify(value),
      time: time
    }
    Service.smarthome.setDeviceData(params).then((res)=>{
      console.log("add call note success",res)
    }).catch((err) => {
      console.log("====================",err)
    })
  }

  static deleteCallNote() {
    let params = {
      did: Device.deviceID,
      key: "18.2",
      type: 'event',
      time: 1721615253,
      value: "[\"1721615253.678\",\"1\",\"测试添加\",\"2681052717\",\"0\",\"13\",\"0\"]"
    }
    Service.smarthome.delDeviceData(params).then((res)=>{
      console.log("delete call note success",res)
    }).catch((err) => {
      console.log("====================delete",err)
    })
  }
}