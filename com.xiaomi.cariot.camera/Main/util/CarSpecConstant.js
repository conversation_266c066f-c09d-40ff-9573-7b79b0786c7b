import { Service, Device } from "miot";
import CameraConfig from "./CameraConfig";
import API from '../API';
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import Toast from "../components/Toast";
import LogUtil from "./LogUtil";

export const CAMERA_CONTROL_CAR_SIID = 2;
export const CAMERA_CONTROL_SLEEP_PIID = 1;
export const CAMERA_CONTROL_TIME_WATERMARK_PIID = 5;
export const CAMERA_CONTROL_HDR_PIID = 17;
export const CAMERA_CONTROL_CORRECTION_PIID = 14;

export const COCKPIT_SERVICE_SIID = 20;
export const COCKPIT_SERVICE_STORAGE_SWITCH_PIID = 1;
export const COCKPIT_SERVICE_WORK_MODE_PIID = 2;
export const COCKPIT_SERVICE_LIVE_SWITCH_PIID = 3;
export const COCKPIT_SERVICE_TEMPERATURE_STATUS = 14;
export const COCKPIT_SERVICE_RECORD_VOICE_PIID = 16;
export const COCKPIT_SERVICE_CROSS_PLAT_PIID = 18;
export const COCKPIT_SERVICE_PRIVACY = 21;

export const MEMORY_CARD_MANAGEMENT_SIID = 4;
export const MEMORY_CARD_MANAGEMENT_STATUS_PIID = 1;
export const MEMORY_CARD_MANAGEMENT_TOTAL_PIID = 2;
export const MEMORY_CARD_MANAGEMENT_FREE_PIID = 3;
export const MEMORY_CARD_MANAGEMENT_USED_PIID = 4;
export const MEMORY_CARD_MANAGEMENT_DURATION_PIID = 6;
export const MEMORY_CARD_MANAGEMENT_FORMAT_AIID = 1;
export const MEMORY_CARD_MANAGEMENT_POP_AIID = 2;
export const MEMORY_CARD_STORAGE_TIME_PIID = 8;

export const MOTION_DETECTION_SIID = 5;
export const MOTION_DETECTION_ALARM_INTERVAL_PIID = 2;
export const MOTION_DETECTION_MOTION = 1;

export const CAMERA_SPEAKER_SIID = 6;
export const CAMERA_SPEAKER_VOLUME_PIID = 1;
