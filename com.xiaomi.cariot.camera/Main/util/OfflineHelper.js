import dayjs from "dayjs";
import { Service } from "miot";
import { Device } from "miot";
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
export default class OfflineHelper {
  static lastOfflineTime = null;

  static getLastOnlineTime() {
    return new Promise((resolve, reject) => {
      if (this.lastOfflineTime != null) {
        resolve(this.lastOfflineTime);
        return;
      }
      Service.callSmartHomeAPI('/appgateway/miot/appdeviceinfo_service/AppDeviceInfoService/get_last_online', { dids: [Device.deviceID] })
        .then((res) => {
          let timeStamp = res.info[0].last_online;
          let time = this._getFormattedOfflineTime(timeStamp * 1000);
          this.lastOfflineTime = time;
          resolve(time);
        })
        .catch((e) => {
          console.log(`error:_getLastOnlineTime`);
          reject(e);
        });
    });
    
  }

  static _getFormattedOfflineTime(timestamp) {
    return dayjs.unix(timestamp/1000).format(LocalizedStrings["yyyymmdd"] + " HH:mm:ss");
    
  }

  static resetLastOfflineTime() {
    this.lastOfflineTime = null;
  }
}