import VersionUtil from "./VersionUtil";
import { MISSCodec } from 'miot/ui/CameraRenderView';
import { MISSSampleRate, MISSDataBits, MISSAudioChannel } from 'miot/ui/CameraRenderView';
import { AlarmEventType } from "miot/service/miotcamera";
import AlarmUtil from "./AlarmUtil";
import { Device } from "miot";
import Host from "miot/Host";
import Orientation from "react-native-orientation";
import Util, { CloudServerCluster } from "../util2/Util";
import { Event } from '../config/base/CfgConst';

export const IMG_DARKMODE_TINT = "#DDDDDDFF";

export default class CameraConfig {

  static isInternationalServer = true; //默认是国际服，避免特异情况导致的问题
  static isEuropeServer = false; //默认不是是欧洲服
  static isCloudServer = false; // 默认是否支持海外云存
  static isIndiaServer = false;
  static isVip = false;
  static isToUpdateVipStatue = false;
  static force026c02BabyCry = false;
  static showCloudForExpired = false;
  static isEuCloudCountry = false;
  static isFaceSupportedCountry = false; // 人脸是否只会在大陆上？

  static unitMute = true;
  static checkNasVersion = true; // 是否nas可能更新过，要更新nas状态
  static nasUpgradeTips = -1; // no tipes, can be 1, 2, 3
  static nasUpgradeTipsShown = -1; // 标记那个tips本次插件使用已经展示过了
  static nasUpgradeDlgBtnChecked = false; // nas 升级dlg按钮checked
  static nasTransferExceptionDueToSdException = false; // 如果nas设为转存并且sd卡异常，就只弹一次窗
  static needRefreshCloudVideo = false; // 需要重新刷新云存视频
  static needCheckCloudVideoExist = false; // 需要检查云存视频是否存在
  static isDeviceTemperatureHigh = false; // 设备是否温度过高
  static carLoginState = 0; // 0 车主登录 1 共驾人 2 代客 3未登录
  static isLocalNetwork = false; // 手机与设备是否处于同一局域网
  static isShowDownloadHint = false;
  static SETTING_PROP = {};
  static Model_Camera_V1 = "mijia.camera.v1";
  static Model_Camera_V3 = "mijia.camera.v3";
  static Model_Camera_hl6 = "isa.camera.hlc6";
  static Model_chuangmi_009 = "chuangmi.camera.ipc009";
  static Model_chuangmi_019 = "chuangmi.camera.ipc019";
  static Model_chuangmi_021 = "chuangmi.camera.ipc021";
  static Model_chuangmi_021a04 = "chuangmi.camera.021a04";
  static Model_chuangmi_022 = "chuangmi.camera.ipc022";
  static Model_chuangmi_026 = "chuangmi.camera.ip026c";
  static Model_chuangmi_026c02 = "chuangmi.camera.026c02";
  static Model_chuangmi_026c05 = "chuangmi.camera.026c05";
  static Model_chuangmi_046a01 = "chuangmi.camera.046a01";
  static Model_chuangmi_029 = "chuangmi.camera.ip029a";
  static Model_chuangmi_029a02 = "chuangmi.camera.029a02";
  static Model_chuangmi_039a01 = "chuangmi.camera.039a01";
  static Model_chuangmi_039a04 = "chuangmi.camera.039a04";
  static Model_chuangmi_069a01 = "chuangmi.camera.069a01";
  static Model_chuangmi_049a01 = "chuangmi.camera.049a01";
  static Model_chuangmi_051a01 = "chuangmi.camera.051a01"
  static Model_xiaomi_c01a01 = "xiaomi.camera.c01a01";//海外  没有本地宝宝哭声，没有人形/移动追踪
  static Model_xiaomi_c01a02 = "xiaomi.camera.c01a02";//大陆  大陆有本地宝宝哭声，类似029a01
  static Model_chuangmi_046c04 = "chuangmi.camera.046c04";
  static Model_chuangmi_066a01 = "chuangmi.camera.066a01"
  static Model_chuangmi_086ac1 = "chuangmi.camera.086ac1"
  static Model_chuangmi_078ac1 = "careco.camera.650"

  static fromSdCardErrorPush = false;
  static shouldDownloadSdcardFile(model) {
    return true;// any 
  }

  static shouldDisplayEventType(model) {
    return this.isNewChuangmi(model) || this.isCamera009(model) || this.isCamera019(model);
  }

  static shouldDisplayNewStorageManage(model) {
    // 暂时针对v3开启这个功能。
    if (model == this.Model_Camera_V3 || model == this.Model_Camera_V1 || model == VersionUtil.Model_chuangmi_051a01) {
      return true;
    }
    if (this.isNewChuangmi(model)) {
      return true;
    }
    if (model == this.Model_chuangmi_009 || model == this.Model_chuangmi_019){
      return true;
    }
    if (model == this.Model_chuangmi_078ac1) {
      return true;
    }
    return false;
  }
  // 将4/16倍速修改为2/3倍数 只针对v1/v3开放
  static isCameraV1orV3(model) {
    if (model == this.Model_Camera_V3 || model == this.Model_Camera_V1) {
      return true;
    }
    return false;
  }

  static getCameraCorrentParam(model) {
    let videoParam = {};
    if (CameraConfig.Model_Camera_V1 === model) {
      videoParam.osdx = 0.40625;
      videoParam.osdy = 0.04411;
      videoParam.radius = 1.2;
    } else {
      videoParam.osdx = 0.243;
      videoParam.osdy = 0.03964;
      videoParam.radius = 1.2;
    }

    switch (model) {
      case CameraConfig.Model_Camera_V1:
        videoParam.osdx = 0.40625;
        videoParam.osdy = 0.04411;
        videoParam.radius = 1.2;
        break;
      case CameraConfig.Model_Camera_V3:
        videoParam.osdx = 0.243;
        videoParam.osdy = 0.03964;
        videoParam.radius = 1.2;
        break;
      case CameraConfig.Model_Camera_hl6:
        videoParam.osdx = 0.271;
        videoParam.osdy = 0.03964;
        videoParam.radius = 1.2;
        break;
      case CameraConfig.Model_chuangmi_039a01:
      case CameraConfig.Model_chuangmi_039a04:
        videoParam.osdx = 0.253;
        videoParam.osdy = 0.044;
        videoParam.radius = 1.2;
        break;
      case CameraConfig.Model_chuangmi_069a01:
        videoParam.osdx = 0.250;
        videoParam.osdy = 0.044;
        videoParam.radius = 1.0;
        break;
      case CameraConfig.Model_chuangmi_049a01:
        videoParam.osdx = 0.240;
        videoParam.osdy = 0.044;
        videoParam.radius = 1.2;
        break;
      case CameraConfig.Model_chuangmi_051a01:
        videoParam.osdx = 0.252;
        videoParam.osdy = 0.044;
        videoParam.radius = 1.2;
        break;
      case CameraConfig.Model_xiaomi_c01a01:
      case CameraConfig.Model_xiaomi_c01a02:
        videoParam.osdx = 0.243;
        videoParam.osdy = 0.04444;
        break;
      case CameraConfig.Model_chuangmi_086ac1:
        videoParam.osdx = 0.250;
        videoParam.osdy = 0.044;
        videoParam.radius = 1.2;
        break;
    }
    return videoParam;
  }

  static support8xSpeed(model) {
    if (model == CameraConfig.Model_chuangmi_039a04) {
      return false;
    }
    return this.isCamera039(model) || this.isCameraV3(model);
  }

  static useOpusCodec(model, version) {
    if (this.isCamera039(model)) {
      let mikeVersion = version.includes("_") ? version.split("_")[0] : version;
      if (mikeVersion >= "5.0.9") {
        return true;
      }
      return false;
    }
    return false;
  }

  static getCameraCoderParam(model, version) {
    let coderParam = {};
    coderParam.videoCodec = MISSCodec.MISS_CODEC_VIDEO_H265;
    coderParam.audioCodec = MISSCodec.MISS_CODEC_AUDIO_G711A;
    if (model == VersionUtil.Model_chuangmi_051a01) {
      coderParam.audioCodec = MISSCodec.MISS_CODEC_AUDIO_OPUS;
    }

    if (model === CameraConfig.Model_Camera_V1 || model === CameraConfig.Model_Camera_V3) {
      coderParam.videoCodec = MISSCodec.MISS_CODEC_VIDEO_H264;
    }
    if (this.isSupportOpus(model) || CameraConfig.useOpusCodec(model, version)) {
      coderParam.audioCodec = MISSCodec.MISS_CODEC_AUDIO_OPUS;
    }
    return coderParam;
  }

  static getCameraAudioSampleRate(model) {
    let audioSampleRate = MISSSampleRate.FLAG_AUDIO_SAMPLE_16K;
    if (model === CameraConfig.Model_Camera_V1
      || model === CameraConfig.Model_Camera_V3
      || model === CameraConfig.Model_chuangmi_009) {
      audioSampleRate = MISSSampleRate.FLAG_AUDIO_SAMPLE_8K;
    }

    return audioSampleRate;
  }

  static isPTZCamera(model) {
    let isPtz = false;
    if (
      model === CameraConfig.Model_chuangmi_021
      || model === CameraConfig.Model_chuangmi_021a04
      || model === CameraConfig.Model_chuangmi_022
      || model === CameraConfig.Model_chuangmi_026
      || model === CameraConfig.Model_chuangmi_026c02
      || model === CameraConfig.Model_chuangmi_046a01
      || model === CameraConfig.Model_chuangmi_026c05
      || model === CameraConfig.Model_chuangmi_029
      || model === CameraConfig.Model_chuangmi_009
      || model === CameraConfig.Model_chuangmi_019
      || model === CameraConfig.Model_chuangmi_029a02
      || model === CameraConfig.Model_chuangmi_039a01
      || model === CameraConfig.Model_chuangmi_039a04
      || model == CameraConfig.Model_chuangmi_069a01
      || model === CameraConfig.Model_chuangmi_049a01
      || model === CameraConfig.Model_xiaomi_c01a01
      || VersionUtil.isAiCameraModel(model)
      || model === CameraConfig.Model_xiaomi_c01a02) {
      isPtz = true;
    }
    return isPtz;
  }

  static isNewCameraCall(model) {
    let isNew = false;
    if (model === CameraConfig.Model_chuangmi_086ac1) {
      isNew = true;
    }
    return isNew;
  }
  
  static isSupportSuperDefinition(model) {
    let isSupport = false;
    if (model === CameraConfig.Model_chuangmi_069a01) {
      isSupport = true;
    }
    return isSupport;
  }
  static supportSDCardV2(model) {
    let isSupport = false;
    if (model === CameraConfig.Model_chuangmi_069a01
      || model === CameraConfig.Model_chuangmi_086ac1
      || model === CameraConfig.Model_chuangmi_066a01
      || model === CameraConfig.Model_chuangmi_078ac1) {
      isSupport = true;
    }
    return isSupport;
  }

  static supportSDProgress(model) {
    let isSupport = false;
    if (model === CameraConfig.Model_chuangmi_086ac1) {
      isSupport = true;
    }
    return isSupport;
  }

  static isSupport2K(model) {
    let isSupport = false;
    if (model === CameraConfig.Model_chuangmi_021
      || model === CameraConfig.Model_chuangmi_021a04
      || model === CameraConfig.Model_chuangmi_022
      || model === CameraConfig.Model_chuangmi_029 
      || model === CameraConfig.Model_chuangmi_022
      || model === CameraConfig.Model_chuangmi_029a02) {
      isSupport = true;
    }
    if (model === CameraConfig.Model_xiaomi_c01a02 || model === CameraConfig.Model_xiaomi_c01a01) {
      return true;
    }
    return isSupport;
  }

  static isSupport25K(model) {
    if (model == CameraConfig.Model_chuangmi_039a01 || model == CameraConfig.Model_chuangmi_049a01
      || model == CameraConfig.Model_chuangmi_039a04 || model == CameraConfig.Model_chuangmi_086ac1) {
      return true;
    }

    return false;
  }

  static isSupport3K(model) {
    let isSupport = false;
    return isSupport;
  }
  static isSupport4K(model) {
    return true;
  }
  static isNormalFHD() { // 使用不带数字的清晰度icon
    let isSupport = false;
    if (this.Model_chuangmi_069a01 == Device.model) {
      return true;
    }
    return isSupport;
  }

  static isHorizontalPTZ(model) {
    let isPtz = false;
    if (model === CameraConfig.Model_chuangmi_026) {
      isPtz = true;
    }
    return isPtz;
  }

  static isNewChuangmi(model) {
    let isPtz = false;
    if (model === CameraConfig.Model_chuangmi_021
      || model === CameraConfig.Model_chuangmi_021a04
      || model === CameraConfig.Model_chuangmi_022
      || model === CameraConfig.Model_chuangmi_026
      || model === CameraConfig.Model_chuangmi_026c02
      || model === CameraConfig.Model_chuangmi_046a01
      || model === CameraConfig.Model_chuangmi_026c05
      || model === CameraConfig.Model_chuangmi_046a01
      || model === CameraConfig.Model_chuangmi_029
      || model === CameraConfig.Model_chuangmi_029a02
      || model === CameraConfig.Model_chuangmi_039a01
      || model === CameraConfig.Model_chuangmi_039a04
      || model == CameraConfig.Model_chuangmi_069a01
      || model === CameraConfig.Model_chuangmi_049a01
      || VersionUtil.isAiCameraModel(model)
      || model === CameraConfig.Model_xiaomi_c01a02
      || model === CameraConfig.Model_xiaomi_c01a01
      || model === CameraConfig.Model_chuangmi_086ac1
      || model === CameraConfig.Model_chuangmi_066a01
      || model === CameraConfig.Model_chuangmi_078ac1
    ) {
      isPtz = true;
    }
    return isPtz;
  }

  static is16K(model) {
    let ret = false;
    if (model === CameraConfig.Model_chuangmi_019 || this.isNewChuangmi(model)) {
      ret = true;
    }
    return ret;
  }

  static isCamera026(model) {
    return (model === CameraConfig.Model_chuangmi_026 || model === CameraConfig.Model_chuangmi_026c02 || model === CameraConfig.Model_chuangmi_026c05 || model === CameraConfig.Model_chuangmi_046a01);
  }

  static isCamera029(model) {
    return (model === CameraConfig.Model_chuangmi_029 || model === CameraConfig.Model_chuangmi_029a02);
  }

  static isCamera019(model) {
    return model == CameraConfig.Model_chuangmi_019;
  }

  static isCameraV3(model) {
    return model == CameraConfig.Model_Camera_V3;
  }

  static isCameraV1(model) {
    return model == CameraConfig.Model_Camera_V1;
  }

  static isCamera009(model) {
    return model == CameraConfig.Model_chuangmi_009;
  }

  static isCamera026C05(model) {
    return model == CameraConfig.Model_chuangmi_026c05;
  }

  static isCamera039(model) {
    return model == CameraConfig.Model_chuangmi_039a01 || model == CameraConfig.Model_chuangmi_049a01
     || model == CameraConfig.Model_chuangmi_039a04;
  }
  static isSupport480P(model) {
    return model == CameraConfig.Model_chuangmi_039a01 || model == CameraConfig.Model_chuangmi_049a01 
    || model == CameraConfig.Model_chuangmi_069a01 || model == CameraConfig.Model_chuangmi_051a01
      || model == CameraConfig.Model_chuangmi_086ac1;
  }
  static isCamera022(model) {
    return model == CameraConfig.Model_chuangmi_022;
  }

  static isSupportPhysicalCover(model) {
    let isPtz = false;
    if (model === CameraConfig.Model_chuangmi_021
      || model === CameraConfig.Model_chuangmi_021a04
      || VersionUtil.isAiCameraModel(model)) {
      isPtz = true;
    }
    return isPtz;
  }

  // 人形追踪 只有这几台；
  // 049a01 039a01 021 021a04 051a01 022
  static isSupportHumanTack(model) {
    if (this.isSupportPhysicalCover(model)) {
      return true;
    }
    if (this.isCamera039(model) || this.Model_chuangmi_069a01 == model) {
      return true;
    }
    return false;
  }


  static isSupportMotionTrack(model) {
    let isPtz = false;
    if (model === CameraConfig.Model_chuangmi_026
      || model === CameraConfig.Model_chuangmi_026c02
      || model === CameraConfig.Model_chuangmi_046a01
      || model === CameraConfig.Model_chuangmi_029
      || model === CameraConfig.Model_chuangmi_029a02
      || model === CameraConfig.Model_chuangmi_009
      || model === CameraConfig.Model_chuangmi_019
      || model === CameraConfig.Model_xiaomi_c01a02) { // 国内支持  国外不支持
      isPtz = true;
    }
    return isPtz;
  }

  static isSupportPtzCheck(model) {
    let isSupport = false;
    if (this.isPTZCamera(model)) {
      isSupport = true;
    }
    return isSupport;
  }

  static isSupportNonVipBabyCry(model) {
    let isSupport = false;
    if (this.isNewChuangmi(model)) {
      isSupport = true;
    }
    if (CameraConfig.isCamera039(model) || CameraConfig.isCamera026C05(model)) { // chuangmi-9895 本地宝宝哭声能力不行。
      isSupport = false;
    }

    if (model == this.Model_chuangmi_021a04 && this.isIndiaServer) {
      isSupport = false;// 印度服务器 021a04 不支持宝宝哭声本地检测，因为场测说能力弱，识别成功率低
    }
    if (model == this.Model_chuangmi_026c02 || model === CameraConfig.Model_chuangmi_046a01) {
      isSupport = false;
    }
    if (this.force026c02BabyCry) {//非云存也是需要支持的
      isSupport = true;
    }
    
    return isSupport;
  }

  static isSupportPet(model) {
    let isSupport = false;
    if (model == this.Model_chuangmi_078ac1) {
      isSupport = true;
    }
    if (VersionUtil.isAiCameraModel(model)) {
      isSupport = true;
    }
    return isSupport;
  }

  static isDailySupportSelectFamilyOrPet(model) {
    let isSupport = true;
    if (model == this.Model_chuangmi_086ac1) {
      isSupport = false;
    }
    return isSupport;
  }

  static isSupportCameraCall(model) {
    let isSupport = false;
    if (model == this.Model_chuangmi_051a01) {
      isSupport = true;
    }
    return isSupport;
  }
  static isSupportAutoCruise(model) {
    let isSupport = false;
    if (model == this.Model_chuangmi_051a01) {
      isSupport = true;
    }
    return isSupport;
  }

  static isSupportImageSet(model) {
    let isSupport = true;
    if (model == this.Model_chuangmi_086ac1) {
      isSupport = false;
    }
    return isSupport;
  }

  static isSupportGlimmerColor(model) {
    let isSupport = false;
    if (model === CameraConfig.Model_chuangmi_021
      || model === CameraConfig.Model_chuangmi_021a04
      || model === CameraConfig.Model_chuangmi_009
      || model === CameraConfig.Model_chuangmi_019
      || model === CameraConfig.Model_chuangmi_029
      || model === CameraConfig.Model_chuangmi_029a02
      || VersionUtil.isAiCameraModel(model)
      || model === CameraConfig.Model_chuangmi_039a01
      || model === CameraConfig.Model_chuangmi_039a04
      || model == CameraConfig.Model_chuangmi_069a01
      || model === CameraConfig.Model_xiaomi_c01a02
      || model === CameraConfig.Model_xiaomi_c01a01
      || model === CameraConfig.Model_chuangmi_049a01) {
      isSupport = true;
    }
    return isSupport;
  }

  static survelillanceTimeWithSecond(model) {
    if (model === CameraConfig.Model_Camera_V1
      || model === CameraConfig.Model_Camera_V3) {
      return true;
    }
    return false;
  }
  static isSupportHDR(model) {
    let isSupport = false;
    if (model === CameraConfig.Model_chuangmi_066a01 || model === CameraConfig.Model_chuangmi_078ac1) {
      isSupport = true;
    }
    return isSupport;
  }

  static isSupportWDR(model) {
    let isSupport = false;
    if (model === CameraConfig.Model_Camera_V1 || this.isPTZCamera(model) || model === CameraConfig.Model_chuangmi_086ac1 || model === CameraConfig.Model_chuangmi_078ac1) {
      isSupport = true;
    }
    return isSupport;
  }

  static isSupportLight(model) {
    let isSupport = true;
    if (model === CameraConfig.Model_chuangmi_086ac1) {
      isSupport = false;
    }
    return isSupport;
  }

  static isSupportBtGateWay(model) {
    let isSupport = false;
    if (model === CameraConfig.Model_Camera_V1 || model === CameraConfig.Model_chuangmi_021 || model === CameraConfig.Model_chuangmi_021a04
      || VersionUtil.isAiCameraModel(model)) {
      isSupport = true;
    }
    return isSupport;
  }

  static isSupportAlarmGuide(model) {
    return true;
  }

  static isSupportWeChat(model) {
    let isSupport = false;
    if (model === CameraConfig.Model_Camera_V3) {
      isSupport = true;
    }
    return isSupport;
  }
  static isSupportSpanSensitivity(model) {
    let isSupport = true;
    if (model === CameraConfig.Model_chuangmi_086ac1) {
      isSupport = false;
    }
    return isSupport;
  }



  static getRecordingVideoParam(model = Device.model) {
    let recordingParam = { width: 1920, height: 1080 };
    if (this.isSupport2K(model)) {
      recordingParam = { width: 2304, height: 1296 };
    }
    if (model === CameraConfig.Model_chuangmi_039a01 || model === CameraConfig.Model_chuangmi_049a01 
      || model === VersionUtil.Model_chuangmi_051a01 || model === VersionUtil.Model_chuangmi_086ac1) {
      recordingParam = { width: 2560, height: 1440 };
    }
    if (model === CameraConfig.Model_chuangmi_069a01) {
      recordingParam = { width: 2960, height: 1666 };
    }
    if (model === CameraConfig.Model_chuangmi_078ac1) {
      // 078 800万
      recordingParam = { width: 3840, height: 2160 };
    }
    return recordingParam;
  }

  static getAlarmTypes(model, isVip) {
    let types = AlarmEventType.EventType_All | AlarmEventType.EventType_ObjectMotion | AlarmEventType.EventType_PeopleMotion;
    if (!this.isNewChuangmi(model) || CameraConfig.isCamera039(model) || model === CameraConfig.Model_xiaomi_c01a01) { //国外c01a01不支持本地宝宝哭声.
      if (isVip) {
        types = types | AlarmEventType.EventType_BabyCry | AlarmEventType.EventType_Face;
      }
    } else if (this.isNewChuangmi(model)) {
      types = types | AlarmEventType.EventType_BabyCry;
      if (VersionUtil.isAiCameraModel(model)) {
        types = types | AlarmEventType.EventType_AI | (1 << 9) | AlarmEventType.EventType_Face; // 022具备所有本地能力，ios 1 << 9是宠物的，还没有加上 需要更新miotcamera.js    
        if (VersionUtil.Model_chuangmi_051a01 == model) {
          types = types | (1 << 10); // CameraCalling
        }
      } else if (isVip) {
        types = types | AlarmEventType.EventType_Face;
      }
    }
    return types;
  }

  static getPanoramaParam(model, versionNumber) {
    let ret = false;
    let panoInfo = {};
    if (model === CameraConfig.Model_chuangmi_021) {
      if (versionNumber > 160) {
        panoInfo.isSupport = true;
        panoInfo.isSupportAngle = true;
      } else {
        panoInfo.isSupport = false;
        panoInfo.isSupportAngle = false;
      }
    } else if (model === CameraConfig.Model_chuangmi_026) {
      panoInfo.isSupport = true;
      if (versionNumber >= 166) {
        panoInfo.isSupportAngle = true;
      } else {
        panoInfo.isSupportAngle = false;
      }
    } else if (model === CameraConfig.Model_chuangmi_029) {
      if (versionNumber >= 171) {
        panoInfo.isSupport = true;
        panoInfo.isSupportAngle = true;
      } else {
        panoInfo.isSupport = false;
        panoInfo.isSupportAngle = false;
      }
    } else if (model === CameraConfig.Model_chuangmi_029a02
      || model === CameraConfig.Model_chuangmi_021a04
      || model === CameraConfig.Model_chuangmi_026c02
      || model === CameraConfig.Model_chuangmi_046a01
      || VersionUtil.isAiCameraModel(model)) {
      panoInfo.isSupport = true;
      panoInfo.isSupportAngle = true;
    } else if (model === CameraConfig.Model_xiaomi_c01a01 || model === CameraConfig.Model_xiaomi_c01a02) {
      panoInfo.isSupport = true;
      panoInfo.isSupportAngle = true;
    } else {
      panoInfo.isSupport = false;
      panoInfo.isSupportAngle = false;
    }

    return panoInfo;
  }


  static shouldRequestEventType(model) {
    return true;
  }

  static setIsInternationalServer(isInternationalServer) {
    this.isInternationalServer = isInternationalServer;
  }

  static setIsEuropeServer(isEuropeServer) {
    this.isEuropeServer = isEuropeServer;
  }

  static setIsCloudServer(isCloudServer) { // 支持新品海外云存入口功能的服务器
    const cloudServer = ['us', 'de', 'sg'];
    this.isCloudServer = cloudServer.includes(isCloudServer);
  }

  static setIsIndiaServer(isIndiaServer) {
    this.isIndiaServer = isIndiaServer;
  }

  /*
  * _isSupportCloud: 表示是否支持云存区域，默认是 undefined
  * isEuropeServer: 表示是否是海外云存区域，默认是false，表示时中国大陆区域或海外无云存的地区，true表示海外有云存的区域
  * 用户的状态：根据上面状态和vip状态，分为国内云存非云存用户，海外云存非云存用户，海外无云存用户
  */
  static updateCloudSupportCountry(serverCode) { //设置是否是支持云存的国家
    if (CloudServerCluster.includes(serverCode.toUpperCase())) {
      this._isSupportCloud = true;
      if (serverCode.toUpperCase() !== "CN") {
        this.isEuropeServer = true;
      }
    } else {
      this._isSupportCloud = false;
      if (serverCode.toUpperCase() !== "CN") {
        this.isEuropeServer = false;
      }
    }
    
  }

  static isSupportCloud() { // 获取是否改区域支持云存
    return this._isSupportCloud;
  }

  /**
   * 业务需要，特殊设备即使支持的云存区域，业务上也不需要云存
   * 后续支持后再放开
   */
  static isDeviceSupportCloud() {
    return false;
  }

  // sdcard 页面展示宝宝哭声筛选项
  static displayBabyCryInTimeline(model) {

    if (model === this.Model_Camera_V1 || model === this.Model_Camera_V3 || model === this.Model_chuangmi_009 || model === this.Model_chuangmi_019 
      || this.isCamera039(model) || model == this.Model_chuangmi_069a01 || this.isCamera026C05(model) || model === CameraConfig.Model_xiaomi_c01a01) { // 国内支持本地宝宝哭声
      return false;
    }
    if (model == this.Model_chuangmi_021a04 && this.isIndiaServer) {
      return false;
    }
    if (this.force026c02BabyCry) {
      return true;
    }
    if ((model == this.Model_chuangmi_026c02 || model === CameraConfig.Model_chuangmi_046a01) && this.isVip) {
      return true;
    }
    if (model == this.Model_chuangmi_026c02 || model === CameraConfig.Model_chuangmi_046a01) {
      return false;// 026c02的问题
    }
    if (model == this.Model_chuangmi_086ac1) {
      return false;
    }

    if (model == this.Model_chuangmi_078ac1) {
      return false;
    }
    
    return true;
  }

  // 026c02 046a01有特定的逻辑：如果开启了云存，就认为具有本地宝宝哭声识别能力    如果之前打开了宝宝哭声开关，也认为有本地宝宝哭声识别能力，其他情况一律认为不支持宝宝哭声能力
  static babyCrySpecialModel(model) {
    if (model == this.Model_chuangmi_026c02 || model === CameraConfig.Model_chuangmi_046a01) {
      if (this.force026c02BabyCry || this.isVip) {
        return true;
      }
      return false;// 026c02的问题
    }

    if (model == this.Model_chuangmi_021a04) { // 印度地区精准度太低，被人投诉，下掉了
      if (!this.isIndiaServer) {
        return true;
      }
      return false;
    }

    return -1; // 不是特殊model
  }

  static displayPetInTimeline(model) {
    if (CameraConfig.isSupportPet(model)) {
      return false;
    }
    return false;
  }

  static displayChild(model) {
    if (model == this.Model_chuangmi_078ac1) {
      return false;
    }
    return false;
  }
  static displayCameraCallingTimeline(model) {
    if (CameraConfig.Model_chuangmi_051a01 == model
    || CameraConfig.Model_chuangmi_086ac1 == model) {
      return true;
    }
    return false;
  }

  static displayFaceInTimeline(model) {
    if (VersionUtil.isAiCameraModel(model)) {
      return true;
    }
    return false;
  }
  static displayLOUDER_SOUND_Timeline(model) {
    return CameraConfig.Model_chuangmi_086ac1 == model;
  }

  static displayIgnoreEvent(model) {
    return true;
  }

  static displayPeopleInTimeline(model, version) {
    if (model === this.Model_Camera_V1 || model === this.Model_Camera_V3 || model === this.Model_chuangmi_009 || model === this.Model_chuangmi_019) {
      return false;
    }
    if (model === this.Model_chuangmi_026 || model === this.Model_chuangmi_026c02 || model === this.Model_chuangmi_026c05 || model === CameraConfig.Model_chuangmi_046a01) {
      return false;
    }
    if (model === this.Model_chuangmi_029 || model === this.Model_chuangmi_029a02) {
      return false;
    }
    if (model === this.Model_chuangmi_078ac1) {
      return false;
    }
    if (this.isCamera039(model) || model == this.Model_chuangmi_069a01) {
      return true;
    }
    if (model === CameraConfig.Model_xiaomi_c01a02 || model === CameraConfig.Model_xiaomi_c01a01) { // 当做029的话，就设置为没有本地人形检测吧
      return false;
    }
    return true;
  }

  static displayMotionInTimeline(model) {
    if (model === this.Model_Camera_V3) {
      return false;
    }
    if (model === this.Model_chuangmi_078ac1) {
      return false;
    }
    return true;
  }

  static getIsEuropeServer() { // 获取是否是海外云存国家, false 是海外无云存国家或者中国大陆
    return this.isEuropeServer;
  }
  static getIsCloudServer() {
    return this.isCloudServer;
  }

  static getInternationalServerStatus() {
    return this.isInternationalServer;
  }

  // static isSupportCloud(model) {
  //   return VersionUtil.isFirmwareSupportCloud(model) && CameraConfig.isSupportCloud();
  // }

  static supportLargeSdcard(model) {
    let version = Device.lastVersion;
    let isNeedVersion = VersionUtil.isVersionBiggerThanExpected(version, '4.3.4_0360');
    if (this.isCamera039(model) || model === this.Model_chuangmi_022 || model === this.Model_xiaomi_c01a01 || model === CameraConfig.Model_xiaomi_c01a02
      || model == this.Model_chuangmi_069a01 || VersionUtil.isAiCameraModel(model) || (model === CameraConfig.Model_chuangmi_029a02 && isNeedVersion)
      || model == this.Model_chuangmi_086ac1 || model == this.Model_chuangmi_078ac1
    ) {// 都支持大sdcard
      return true;
    }
    return false;
  }

  static supportGestureSwitchSetting() {
    if (Device.model == CameraConfig.Model_chuangmi_051a01) {
      if (VersionUtil.isVersionBiggerThanExpectedV2(Device.lastVersion, "5.1.5_0247") >= 0) {
        return true;
      }
    }
    return false;
  }

  static setUnitMute(isMute) {
    console.log('setUnitMute: ', isMute);
    this.unitMute = isMute;
  }

  static getUnitMute() {
    return this.unitMute;
  }

  static supportOnlinePrivacy(model) {
    return true;// 所有的都支持
  }
  
  static shouldDisplayBannerTips(model) {
    return this.isNewChuangmi(model);
  }

  // 访客信息尚未同步到其他model
  static isSupportVisitInfo(model) {

    if (model === this.Model_chuangmi_029 || model === this.Model_chuangmi_021
      || model === this.Model_chuangmi_069a01 || model === this.Model_chuangmi_051a01
      || model === this.Model_chuangmi_086ac1) {
      return true;
    }
    return false;
  }

  // 设备端做畸变纠正
  static isDeviceCorrect(model){
    if (model == this.Model_chuangmi_066a01 || model == this.Model_chuangmi_078ac1) {
      return true;
    }
    return false;
  }
  static isSupportAIFrame(model) {
    if (model === this.Model_chuangmi_021) {
      return true;
    }
    return false;
  }

  static force026c02BabyCryEnable(enabled) {
    this.force026c02BabyCry = enabled;
  }

  // 026c02 出了个要求：大陆海外均没有宝宝哭声开关了； important 海外如果宝宝哭声开关打开了，就认为026c02具有本地宝宝哭声检测能力，需要开放入口，没有开，就关闭入口
  static fetchCloudBabyCryStatus(model, isVip) {
    if (model !== CameraConfig.Model_chuangmi_026c02 || model !== CameraConfig.Model_chuangmi_046a01) {
      return;
    }
    if (isVip) {
      return;
    }
    // 人脸开关是保存在这里的，跟spec无关，所以需要重新访问一次
    AlarmUtil.getAlarmConfig().then((res) => {
      if (res.code == 0) {
      } else {
        return;
      }
      let babyCryEnable = res.data.babyCrySwitch;
      if (babyCryEnable) {
        // 026c02 && 海外 && 宝宝声音打开
        CameraConfig.force026c02BabyCryEnable(babyCryEnable);
      }
    }).catch((err) => {
      // ignore
    });
  }

  static isXiaomiCamera(model) {
    if (model == CameraConfig.Model_xiaomi_c01a01 || model == CameraConfig.Model_xiaomi_c01a02
      || model == VersionUtil.Model_chuangmi_051a01) {
      return true;
    }
    return false;
  }
  static sdcardEventNoDate(model) {
    if (model == CameraConfig.Model_chuangmi_039a04) {
      return true;
    }
    return false;
  }

  static setSupportCloudCountry(isSupportCloudCountry) {
    this.supportCloudCountry = isSupportCloudCountry;
  }

  static isSupportCloudCountry() {
    return this.supportCloudCountry;
  }
  
  static restore() {
    this.isInternationalServer = true;
    this.isVip = false;
    this.isToUpdateVipStatue = false;
    this.force026c02BabyCry = false;// 默认false
    this.unitMute = true;// 退出的时候要重置
    this.supportCloudCountry = false;

  }

  static isSupportOpus(model) {
    if (model === CameraConfig.Model_xiaomi_c01a02 || model === CameraConfig.Model_xiaomi_c01a01
      || model == CameraConfig.Model_chuangmi_051a01 || model == CameraConfig.Model_chuangmi_069a01
      || model == CameraConfig.Model_chuangmi_086ac1 || model == CameraConfig.Model_chuangmi_078ac1) {
      return true;
    }
  }
  static overseasFreeHousekeeping(model){// 海外免费看家
    if (model === CameraConfig.Model_chuangmi_039a04 || model === CameraConfig.Model_chuangmi_046c04 ) {
      return true;
    }
  }
  static deviceFrameRate(model) { // 不同设备的帧率
    if (model === CameraConfig.Model_chuangmi_039a04 || model === CameraConfig.Model_chuangmi_046c04  || model === CameraConfig.Model_chuangmi_086ac1) {
      return true;
    }
  }
  static panorama(model) { // 全景图功能
    if (model === CameraConfig.Model_chuangmi_046c04) {
      return false;
    }
    return true;
  }
  static CloudBabyCry(model = Device.model) { // 云端宝宝哭声功能
    if (model === CameraConfig.Model_chuangmi_046c04 || model === CameraConfig.Model_chuangmi_039a04 || !this.isVip) {
      return false;
    }

    if (model === CameraConfig.Model_chuangmi_086ac1) {
      return false;
    }

    return true;
  }
  static mustWatchman(model) { // 看家入口必现功能（老看家，不支持一周内有数据显示，无数据则不显示）
    if (model === CameraConfig.Model_chuangmi_046c04 || model === CameraConfig.Model_chuangmi_039a04) {
      return true;
    }
    if (model === CameraConfig.Model_xiaomi_c01a01) {
      return true;
    }
    return false;
  }
  static intelligentScenePush(model = Device.model) { // 智能场景推送功能禁用
    if (model === CameraConfig.Model_chuangmi_039a04) {
      return false;
    }
    return true;
  }

  static isNASV123(model) { // 是否支持nas v2/v3 升级提醒功能
    if (model === 'chuangmi.camera.061a01' // c500 pro
      || model === this.Model_chuangmi_069a01
      || model === this.Model_chuangmi_086ac1) { // m300
      return true;
    }
  }
  /**
   * android中locktodirection的作用：小窗方向和设备方向一致时以lock无主，会锁定方向，而且不再向插件发送方向消息。
   * 和设备方向不一致时，以后面的direction为主。
   * 为了全屏时通过locktoPortrait切换成横屏下的竖屏，所以横屏下locktoPortrait没有锁定屏幕。
   * https://jira.n.xiaomi.com/browse/IOTMIW-2361
   * 这里通过补偿的方式对Android pad 的lockToPortrait去掉了锁定方向功能，保证总是能收到方向变化。
   * 放开了方向锁定后，方向监听中同时添加了防止循环旋转的代码。
   */
  static lockToPortrait() {
    Orientation.lockToPortrait();
    if (Host.isPad && Host.isAndroid) {
      Orientation.unlockAllOrientations();
    }
  }

  static overexposureTip(model = Device.model) { // 过曝提示功能
    if (model === CameraConfig.Model_chuangmi_069a01) {
      return false;
    }
    return true;
  }

  // 不需要vip即可使用每日故事。
  static supportNonVipDailyStory(model) {
    if (model == this.Model_chuangmi_022) {
      return true;
    }
    return false;
  }

  // 需要vip才可以使用每日故事
  static supportVipDailyStory(model) {
    if (model == this.Model_chuangmi_051a01 || model == "chuangmi.camera.061a01" || model == this.Model_chuangmi_086ac1) {
      return true;
    }
    return false;
  }

  /**
   * @Author: byh
   * @Date: 2024/3/8
   * @explanation:
   * 支持按键通话状态查询
   *********************************************************/
  static supportCallStatusCheck(model) {
    if (model == this.Model_chuangmi_051a01 || model == this.Model_chuangmi_086ac1) {
      return true;
    }
    return false;
  }

  /**
   * 是否支持音视频通话
   * @param model
   * @return {boolean}
   */
  static isSupportVideoCall(model) {
    if (model == this.Model_chuangmi_086ac1) {
      return true;
    }
    return false;
  }

  static useHighResolution(model) {
    if (model == this.Model_chuangmi_069a01 || model == this.Model_chuangmi_086ac1) {
      return true;
    }
    return true;
  }
  static isNewNightVision(model) {
    if (model == this.Model_chuangmi_086ac1) {
      return true;
    }
    return false;
  }

  // 是否支持画面翻转
  static isSupportImageRotate(model) {
    if (model == this.Model_chuangmi_086ac1) {
      return false;
    }
    return true;
  }

  // 闹钟提醒功能
  static shouldDisplayClockAlarm(model) {
    if (model == this.Model_chuangmi_086ac1) {
      return true;
    }
    return false;
  }

  static supportFavArea(model = Device.model) {
    if (model == this.Model_chuangmi_078ac1) {
      return true;
    }
    return false;
  }

  static supportCruise(model = Device.model) {
    if (model == this.Model_chuangmi_086ac1) {
      return false;
    }
    return false;
  }

  /**
   * @Author: byh
   * @Date: 2024/11/4
   * @explanation:
   * 配置每个项目支持的事件类型
   *********************************************************/
  static pgSupportEvents(model = Device.model) {
    // 后面
    if (this.Model_chuangmi_078ac1 === model) {
      return [Event.IgnoreEvent];
    }
    // if (this.Model_chuangmi_078ac1 === model) {
    //   return [Event.ChildDetected, Event.Pet];
    // }
  }

  static supportVoiceMute() {
    let version = Device.lastVersion;
    let isSupportVersion = VersionUtil.isVersionBiggerThanExpected(version, '5.3.2_0669');
    if (isSupportVersion) {
      return true;
    }
    return false;
  }

  /**
   * 车端拆分SD卡存储和云存开关
   */
  static supportDivideStorage() {
    let version = Device.lastVersion;
    let isSupportVersion = VersionUtil.isVersionBiggerThanExpected(version, '5.3.2_0669');
    if (isSupportVersion) {
      return true;
    }
    return false;
  }

  static supportCrossPlatView() {
    let version = Device.lastVersion;
    let isSupportVersion = VersionUtil.isVersionBiggerThanExpected(version, '5.3.2_0675');
    if (isSupportVersion) {
      return true;
    }
    return false;
  }

  static supportOneKeyPrivacy() {
    let version = Device.lastVersion;
    let isSupportVersion = VersionUtil.isVersionBiggerEqualThanExpected(version, '5.3.2_0681');
    if (isSupportVersion) {
      return true;
    }
    return false;
  }

  static isSupportLocal() {
    return true;
  }

  static debugShowEvent() {
    return false;
  }
  // 特殊插件版本 宏控
  static debugSpecialSupport() {
    return false;
  }
}
