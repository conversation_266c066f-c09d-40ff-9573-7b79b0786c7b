
export default class NumberUtil {
  static stringToUtf8ByteArray(str) {
    let out = [], p = 0;
    for (let i = 0; i < str.length; i++) {
      let c = str.charCodeAt(i);
      if (c < 128) {
        out[p++] = c;
      } else if (c < 2048) {
        out[p++] = (c >> 6) | 192;
        out[p++] = (c & 63) | 128;
      } else if (((c & 0xFC00) == 0xD800) && (i + 1) < str.length &&
        ((str.charCodeAt(i + 1) & 0xFC00) == 0xDC00)) {
        c = 0x10000 + ((c & 0x03FF) << 10) + (str.charCodeAt(++i) & 0x03FF);
        out[p++] = (c >> 18) | 240;
        out[p++] = ((c >> 12) & 63) | 128;
        out[p++] = ((c >> 6) & 63) | 128;
        out[p++] = (c & 63) | 128;
      } else {
        out[p++] = (c >> 12) | 224;
        out[p++] = ((c >> 6) & 63) | 128;
        out[p++] = (c & 63) | 128;
      }
    }
    return out;
  }

  static intToByteArray(data) {
    return new Uint8Array([data & 0xff, (data >> 8) & 0xff, (data >> 16) & 0xff, (data >> 24) & 0xff]);
  }

  static toHexString(arrBytes) {
    let array = [];
    for (let i = 0; i < arrBytes.length; i++) {
      let tmp;
      let num = arrBytes[i];
      if (num < 0) {
        // 此处填坑，当byte因为符合位导致数值为负时候，需要对数据进行处理
        tmp = (255 + num + 1).toString(16);
      } else {
        tmp = num.toString(16);
      }
      if (tmp.length == 1) {
        tmp = `0${ tmp }`;
      }
      array.push(tmp);
    }
    return array.join("");
  }

  
  static byteArrayToInt(data, position) {
    return (0xff & data[position]) | (0xff & data[position + 1]) << 8 | (0xff & data[position + 2])
      << 16 | (0xff & data[position + 3]) << 24;
  }

  // byte数组转换为无符号short整数
  static byte2ToUnsignedShort(bytes, off) {
    let high = bytes[off + 1];
    let low = bytes[off];
    return (high << 8 & 0xFF00) | (low & 0xFF);
  }


}