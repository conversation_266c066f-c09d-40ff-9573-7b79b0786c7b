'use strict';

import React from 'react';
import {Device, Host, Service} from 'miot';

/**
 * @Author: byh
 * @Date: 2024/5/20
 * @explanation:
 * fds相关
 *********************************************************/
export default class FDSUtil {

    static uploadAvatarToServer(filename) {
        return new Promise((resolve, reject) => {
            let suffix = 'jpg';
            Host.file.generateObjNameAndUrlForFDSUpload(Device.deviceID, suffix).then(obj => {
                console.log("==============",obj);
                let param = {
                    uploadUrl: obj.jpg.url,
                    method: obj.jpg.method,
                    // headers: { "Content-Type": "application/octet-stream" },
                    headers: { "Content-Type": "" },
                    files: [{ filename: filename }]
                };
                console.log("============",param);
                Host.file.uploadFileToFDS(param).then(rr => {
                    console.log('upload file success', rr);
                    Host.file.getFDSFileInfoWithObjName(obj.jpg.obj_name).then((res) => {
                        //{"method": "GET", "obj_name": "2024/07/25/2681052717/1067669099_112202648.jpg", "ok": true, "out_time": 1721879942, "url": "https://cnbj1.fds.api.xiaomi.com/086ac1-file-storage/2024/07/25/2681052717/1067669099_112202648.jpg?Expires=1721879942000&GalaxyAccessKeyId=AKHUDAZNG2U6QF34JG&Signature=z0ZerYSdUJPhDSTD7xmcew576rI="}
                        resolve({ objName: obj.jpg.obj_name, downloadUrl: res.url });
                    }).catch((err) => {
                        reject(err);
                    });
                }).catch(err => {
                    console.log('upload file failed', err);
                    reject(err);
                });
            }).catch((error) => {
                console.log("============error=========",error);
                reject(error);
            });
        });
    }

    static getLocalImgUrl(objName, downloadUrl = null) {
        return new Promise(async(resolve, reject) => {
            let fileName = objName.substring(objName.lastIndexOf("/") + 1);
            fileName = `avatar/${fileName}`
            console.log("Last filename", fileName);
            let fileExist = await Host.file.isFileExists(fileName);
            if (fileExist) {
                resolve(fileName);
            } else {
                if (downloadUrl == null) {
                    let fdsInfo = await Host.file.getFDSFileInfoWithObjName(objName);
                    downloadUrl = fdsInfo.url;
                }
                Host.file.downloadFile(downloadUrl,fileName).then((res) => {
                    console.log("download fds avatar success", fileName);
                    resolve(fileName);
                }).catch((err) => {
                    reject(err);
                });
            }

        });
    }

    static getGenerateObjNameAndUrlForFDSUpload(data) {
        return new Promise((resolve, reject) => {
            // 2024/06/20/2201452029/1104640725_204038046.jpg
            let suffix = 'jpg';
            let saveFilename = "test.jpg"
            Host.file.generateObjNameAndUrlForFDSUpload(Device.deviceID, suffix).then(obj => {
                console.log("==============",obj);
                Host.file.writeFileThroughBase64(saveFilename, data.data).then(result => {
                    console.log("writeFileThroughBase64 readFile...result", result);
                    let param = {
                        uploadUrl: obj.jpg.url,
                        method: obj.jpg.method,
                        // headers: { "Content-Type": "application/octet-stream" },
                        headers: { "Content-Type": "" },
                        files: [{ filename: saveFilename }]
                    }
                    console.log("============",param);
                    Host.file.uploadFileToFDS(param).then(rr => {
                        // alert('上传成功' + JSON.stringify(rr))
                        console.log('upload file success', rr);
                        resolve()
                    }).catch(err => {
                        // alert('上传失败' + JSON.stringify(err))
                        console.log('upload file failed', err);
                        reject(err);
                    })

                }).catch(err => {
                    console.log("writeFileThroughBase64 readFile...error", err);
                    reject(err);
                })


                // if (res.hasOwnProperty(suffix) && res[suffix]) {
                //     let obj = res[suffix];
                //     let obj_name = obj.obj_name;
                //     let name = obj_name.substring(obj_name.length - 22)
                //     let content = "AC";
                //     let time = obj.time;
                //     this.file_obj_name = obj_name;
                //     console.log("pre upload", res)
                //     Host.file.writeFile(name, content).then(r => {
                //         let param = {
                //             uploadUrl: obj.url,
                //             method: obj.method,
                //             headers: { "Content-Type": "application/octet-stream" },
                //             files: [{ filename: name }]
                //         }
                //         Host.file.uploadFileToFDS(param).then(rr => {
                //             alert('上传成功' + JSON.stringify(rr))
                //             console.log('upload file success', rr)
                //         }).catch(err => {
                //             alert('上传失败' + JSON.stringify(err))
                //             console.log('upload file failed', err)
                //         })
                //     }).catch(err => {
                //         alert('存储临时文件失败' + JSON.stringify(err))
                //         console.log("write file failed", err)
                //     })
                // }
            }).catch((error) => {
                console.log("============error=========",error);
            });

        });
    }
    /**
     * @Author: byh
     * @Date: 2024/5/20
     * @explanation:
     * 获取fds的上传地址
     *********************************************************/
    static getFDSUpLoadUrl(useV2 = true){
        Host.file.getFDSFileInfoWithObjName('2024/06/20/2201452029/1104640725_204038046.jpg')
          .then((res) => {
              console.log("++++++++++++++++success",res);

          }).catch((error)=>{
            console.log("++++++++++++++++error",error);
        });
        // return Service.smarthome.callThirdPartyAPI(param);
    }


    static getFDSDownLoadUrl(fileName, useV2 = true){
        Host.file.getFDSFileInfoWithObjName('2024/06/20/2201452029/1104640725_204038046.jpg')
          .then((res) => {
              console.log("++++++++++++++++success",res);
              Host.file.downloadFile(res.url,"test_download.jpg").then((res1) =>{
                  console.log("++++++++++++++++download success",res1);
              }).catch((error1) => {
                  console.log("++++++++++++++++download error",error1);
              })
          }).catch((error)=>{
            console.log("++++++++++++++++error",error);
        });
    }

    /**
     * 信息中存入的文件
     * @param filePath "imgPath":"preset/2643757240_1023863587/2643757240_1023863587_962ddadc96734d658986249451c06b38.jpg"
     */
    static deleteFDSFile(filePath,useV2 = true){
        let param = {
            "app_id": 10017,
            "dids": null,
            "params": {
                "method": useV2 ? "file.delete_file_did":"file.delete_file",
                "id": parseInt(Device.deviceID),
                "params": {
                    'deviceId': Device.deviceID,
                    "userId": Service.account.ID,
                    "fileName":filePath,
                    "model": Device.model,
                    'type':useV2 ? "preset-new":"preset"
                }
            }
        };
        console.log("delete file params",param);
        Service.smarthome.callThirdPartyAPI(param)
          .then(res=>console.log("AngleManger","delete server file success",res))
          .catch(error => console.log("AngleManger","delete server file error",error))
    }

    /**
     * 信息中存入的文件
     * @param filePath "imgPath":"preset/...xxxx.jpg"
     */
    static deleteFDSFileDir(){

        let param = {
            "app_id": 10017,
            "dids": null,
            "params": {
                "method": "file.delete_dir",
                "id": parseInt(Device.deviceID),
                "params": {
                    'deviceId': Device.deviceID,
                    "userId": Service.account.ID,
                    "model": Device.model,
                    'type':"preset"
                }
            }
        };
        let paramDirDid = {
            "app_id": 10017,
            "dids": null,
            "params": {
                "method": "file.delete_dir_did",
                "id": parseInt(Device.deviceID),
                "params": {
                    'deviceId': Device.deviceID,
                    "userId": Service.account.ID,
                    "model": Device.model,
                    'type':"preset-new"
                }
            }
        };
        console.log("params",param);
        Service.smarthome.callThirdPartyAPI(param).then(res=>{
            console.log("delete dir success");
        }).catch(error => console.log("delete dir false",error));
        console.log("params did",paramDirDid);
        Service.smarthome.callThirdPartyAPI(paramDirDid).then(res=>{
            console.log("delete dir did success");
        }).catch(error => console.log("delete dir did false",error));

    }

    /**
     * 设置SDS事件
     */
    static setFavoriteSDSEvent(position, fileName, key){
        let settings = {};
        settings["preset_" + position] = fileName;
        settings["preset_key_" + position] = key;

        let params = {"did": Device.deviceID, "settings": settings}

        console.log("setFavoriteSDSEvent params=",params);

        return Service.smarthome.setDeviceSetting(params);
    }

    /**
     * 获取SDS事件
     */
    static getFavoriteSDSEvent(positions){
        //let params = {"did":"123245796","settings":["preset_2","preset_key_2","preset_1","preset_key_1"]}
        //let settings = ["preset_2","preset_key_2","preset_1","preset_key_1"];

        let settings = [];
        for (let i = 0; i < positions.length; i++) {
            settings.push("preset_"+positions[i]);
            settings.push("preset_key_"+positions[i]);
        }

        let params = {"did":Device.deviceID,"settings":settings}

        console.log("getFavoriteSDSEvent params=",params);

        return Service.smarthome.getDeviceSettingV2(params);
    }

    static fdsDemo(){

        let did = Device.deviceID;
        let suffix = "jpg";
        Host.file.generateObjNameAndUrlForFDSUpload(did, suffix).then((res) => {
            console.log("--------------------fdsDemo------------------- res",res)

            if (res.hasOwnProperty(suffix) && res[suffix]) {
                let obj = res[suffix];
                let param = {
                    uploadUrl: obj.url,
                    method: obj.method,
                    headers: { "Content-Type": "" },
                    files: [{ filename: obj.fileName }]
                };
                Host.file.uploadFileToFDS(param).then((rr) => {
                    console.log('upload file success', rr);
                }).catch((err) => {
                    console.log('upload file failed', err);
                });
            } else {
                console.log('upload file failed', err);
            }
        }).catch((error) => {
            console.log("--------------------fdsDemo------------------- error",error)
        });
    }


    /*
     *
     *   上传图片请求
     *   @param
     *   @returns
     * */

    static uploadImage(url,imgAry) {
        let formData = new FormData();    //因为需要上传多张图片,所以需要遍历数组,把图片的路径数组放入formData中
        for (let i = 0; i < imgAry.length; i++) {
            let file = {uri: imgAry[i], type: 'multipart/form-data', name: 'image.png'};   //这里的key(uri和type和name)不能改变,
            formData.append("files", file);   //这里的files就是后台需要的key
        }

        //curl -v -X PUT -H "Content-Type:" 'https://awsbj0.fds.api.xiaomi.com/fds-demo/test.txt?GalaxyAccessKeyId=5xxxxxxx45&Expires=1486192975555&Signature=H3xxxxxxxQ=' -d 'Testing presigned url'
        let headers = {
            'Content-Type': 'multipart/form-data',
            "Accept": "*/*",
        }

        console.log("uploadImage headers=",headers);

        return fetch(url, {
            method: 'PUT',
            headers: headers,
            body: formData,
        });
    }
}



