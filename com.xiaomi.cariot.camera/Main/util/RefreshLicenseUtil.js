import React from 'react';
import StorageKeys from '../StorageKeys';
import API from "../API";
import { Device, Service } from "miot";

/**
 * @Author: byh
 * @Date: 2023/12/11
 * @explanation:
 * IPC微信视频通话-APP端接口文档
 * 接口
 * /app/v2/device/check_bind_wx_license
 * 方法get
 * 功能
 * 1. 检查设备的license有效期，
 * 2. 有效期不足30天时，绑定新的license，并返回最新有效期
 * 3. 有效期大于30天时，返回现有license有效期
 * 字段类型说明
 * 参数
 * did string
 * 结果
 * code in 0: 成功，-1: 没有权限，-4: 系统错误，?: 其他错误
 * license_expire_time int64 license过期时间戳，单位s
 *********************************************************/
export default class RefreshLicenseUtil {

  // 后台刷新就OK
  static async toRefreshLicense() {
    StorageKeys.WX_LICENSE.then((res) => {
      const thirtyDays = 30 * 24 * 60 * 60;
      let curTime = new Date().getTime() / 1000;
      if (!res || curTime - res >= thirtyDays) {
        this.refreshLicenseNow();
      }
    }).catch(() => {
      // 去刷新
      this.refreshLicenseNow();
    });
  }

  static async refreshLicenseNow() {
    API.get("/app/v2/device/check_bind_wx_license", "business.smartcamera", { did: Device.deviceID })
      .then((result) => {
        console.log("refreshLicenseNow", JSON.stringify(result));
        if (result.code == 0) {
          StorageKeys.WX_LICENSE = result.result.license_expire_time;
        }
      })
      .catch((error) => {
        console.log("refreshLicenseNow:", error);
      });
  }
}