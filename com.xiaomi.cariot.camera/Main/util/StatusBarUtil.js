import { StatusBar } from "react-native";
import { Dimensions, Platform } from "react-native";
import { Host } from "miot";

// See https://mydevice.io/devices/ for device dimensions
// https://www.ios-resolution.com/
// https://www.jianshu.com/p/3c1dddb0e5b0 不同设备的状态栏高度

const X_WIDTH = 375;
const X_HEIGHT = 812; // xs 11pro
const XSMAX_WIDTH = 414;
const XSMAX_HEIGHT = 896; // 11promax XR 11 
const PAD_WIDTH = 768;
const PAD_HEIGHT = 1024;
const TW_WIDTH = 390;
const TW_HEIGHT = 844;
const TWP_WIDTH = 428;
const TWP_HEIGHT = 926;
const MINI_WIDTH = 375;
const MINI_HEIGHT = 812;


const { height: D_HEIGHT, width: D_WIDTH } = Dimensions.get('screen');
const isIphone12 = (() => {
	if (Platform.OS === 'web') return false;
	return (
		Platform.OS === 'ios' &&
		((D_HEIGHT === TW_HEIGHT && D_WIDTH === TW_WIDTH) ||
			(D_HEIGHT === TW_WIDTH && D_WIDTH === TW_HEIGHT)
		)

	);
})();
const isIphone12P = (() => {
	if (Platform.OS === 'web') return false;
	return (
		Platform.OS === 'ios' &&
		((D_HEIGHT === TWP_HEIGHT && D_WIDTH === TWP_WIDTH) ||
			(D_HEIGHT === TWP_WIDTH && D_WIDTH === TWP_HEIGHT)
		)

	);
})();

const isIphone12mini = (() => {
	if (Platform.OS === 'web') return false;
	return (Platform.OS === 'ios' && Host.systemInfo.mobileModel === "iPhone13,1");
})();

const isIphone11 = (() => {
	if (Platform.OS === 'web') return false;
	return (Platform.OS === 'ios' && Host.systemInfo.mobileModel === "iPhone12,1");
})();



const isIPhoneX = (() => {
	if (Platform.OS === 'web') return false;
	return (
		Platform.OS === 'ios' &&
		((D_HEIGHT === X_HEIGHT && D_WIDTH === X_WIDTH) ||
			(D_HEIGHT === X_WIDTH && D_WIDTH === X_HEIGHT)) ||
		((D_HEIGHT === XSMAX_HEIGHT && D_WIDTH === XSMAX_WIDTH) ||
			(D_HEIGHT === XSMAX_WIDTH && D_WIDTH === XSMAX_HEIGHT))
	);
})();

const isIPad = (() => {
	if (Platform.OS !== 'ios' || isIPhoneX) return false;

	// if portrait and width is smaller than iPad width
	if (D_HEIGHT > D_WIDTH && D_WIDTH < PAD_WIDTH) {
		return false;
	}

	// if landscape and height is smaller that iPad height
	if (D_WIDTH > D_HEIGHT && D_HEIGHT < PAD_WIDTH) {
		return false;
	}

	return true;
})();

/**  44的刘海屏高度：
iphonex: 375 x 812	
iphone 11 pro: 375 x 812
iphone 11 pro max:   414 x 896	
*/

/**  48的刘海屏高度：
iphone xr:  414 x 896	
iphone 11:   414 x 896	
*/

/** 47的刘海屏幕高度
 * iphone 12          390 x 844
 * iphone 12 pro      390 x 844	
 * iphone 12 pro max  428 x 926
 * iphone 13          390 x 844	
 * iphone 13 pro      390 x 844	
 * iphone 13 pro max  428 x 926	
 * iphone 14          390 x 844	
 * iphone 14 plus     428 x 926	
 */

/**
 * 50
 * iphone 13 mini  375 x 812
 * iphone 12 mini  375 x 812
 */

/** 灵动到 54
 * iphone 14 pro        393 * 852
 * iphone 14 pro max    430 * 932
 */

/**
 * 其余设备 例如iphone se iphonse2 iphonse3 iphone 8 都是普通的状态栏高度 20
 * 宽高 375 x 667	  or  414 x 736	  最高高度也就是iphone 8+  736
 */




let _customStatusBarHeight = null;
const statusBarHeight = (isLandscape) => {
  //   console.log("current status bar height:", _customStatusBarHeight);
  if (_customStatusBarHeight !== null) {
    return _customStatusBarHeight;
  }

  /**
     * This is a temporary workaround because we don't have a way to detect
     * if the status bar is translucent or opaque. If opaque, we don't need to
     * factor in the height here; if translucent (content renders under it) then
     * we do.
     */
  if (Platform.OS === 'android') {
    return StatusBar.currentHeight || 0;
  }

  if (D_HEIGHT <= 736) { // iphone 8+ 及以下的设备，包括了se系列
    return 20;
  }

  if (Host.systemInfo.mobileModel === "iPhone13,1" || Host.systemInfo.mobileModel === "iPhone14,4") { // 12 mini 13 mini
    return 50;
  }

  if (D_HEIGHT == 844 || D_HEIGHT == 926) {
    return 47;
  }

  if (D_HEIGHT == 896) {
    return 48;
  }

  if (D_HEIGHT == 812) {
    return 44;
  }


  if (D_HEIGHT == 852 || D_HEIGHT == 932) {
    return 54;
  }

  return 44; //默认44吧

  // if (isIphone12mini) {
  //   return isLandscape ? 0 : 50;
  // }

  // if (isIphone11) {
  //   return isLandscape ? 0 : 48;
  // }

  // if (isIphone12 || isIphone12P) {
  //   return isLandscape ? 0 : 47;
  // }

  // if (isIPhoneX) {
  //   return isLandscape ? 0 : 44;
  // }

  // if (isIPad) {
  //   return 20;
  // }

  // return isLandscape ? 0 : 20;
};


export class _StatusBarUtil {



	_getInset = (key, isLandscape = false) => {
		switch (key) {
			case 'horizontal':
			case 'right':
			case 'left': {
				return isLandscape ? (isIPhoneX ? 44 : 0) : 0;
			}
			case 'vertical':
			case 'top': {
				return statusBarHeight(isLandscape);
			}
			case 'bottom': {
				return isIPhoneX ? (isLandscape ? 24 : 34) : 0;
			}
		}
	};

	statusBarHeight = (isLandscape) => {
		console.log("current status bar height:", _customStatusBarHeight);
		if (_customStatusBarHeight !== null) {
			return _customStatusBarHeight;
		}

		/**
		   * This is a temporary workaround because we don't have a way to detect
		   * if the status bar is translucent or opaque. If opaque, we don't need to
		   * factor in the height here; if translucent (content renders under it) then
		   * we do.
		   */
		if (Platform.OS === 'android') {
			if (global.Expo) {
				return global.Expo.Constants.statusBarHeight;
			} else {
				return 0;
			}
		}

		if (isIPhoneX) {
			return isLandscape ? 0 : 44;
		}

		if (isIPad) {
			return 20;
		}
		if (isIphone12mini) {
			return isLandscape ? 0 : 50;
		}

		if (isIphone11) {
			return isLandscape ? 0 : 48;
		}

		if (isIphone12 || isIphone12P) {
			return isLandscape ? 0 : 47;
		}

		if (isIPhoneX) {
			return isLandscape ? 0 : 44;
		}


		return isLandscape ? 0 : 20;
	};

}

const StatusBarUtil = new _StatusBarUtil();

export default StatusBarUtil;
