import { Service } from "miot";
import { Device } from "miot/device";

export default class TrackConnectionHelper {

  static enterPluginTime = 0;
  static enterLivePageTime = 0;
  static startConnectTime = 0;
  static connectedTime = 0;
  static IFrameReceivedTime = 0;
  static frameRenderedTime = 0;
  static powerCheckTime = 0;
  static powerCheckedTime = 0;
  static privacyCheckTime = 0;
  static privacyCheckedTime = 0;
  static networkCheckTime = 0;
  static networkCheckedTime = 0;
  static isReported = false;

  static clear() {
    this.enterPluginTime = 0;
    this.enterLivePageTime = 0;
    this.startConnectTime = 0;
    this.connectedTime = 0;
    this.IFrameReceivedTime = 0;
    this.frameRenderedTime = 0;
    this.powerCheckTime = 0;
    this.powerCheckedTime = 0;
    this.privacyCheckTime = 0;
    this.privacyCheckedTime = 0;
    this.networkCheckTime = 0;
    this.networkCheckedTime = 0;
    this.isReported = false;
  }
  
  static trackEnterPlugin() {
    if (this.enterPluginTime != 0) {
      return;
    }
    this.enterPluginTime = Date.now();
  }
  
  static trackLivePageEnter() {
    if (this.enterLivePageTime != 0) {
      return;
    }
    this.enterLivePageTime = Date.now();
  }

  static startConnect() {
    if (this.startConnectTime != 0) {
      return;
    }
    this.startConnectTime = Date.now();
  }

  static onConnected() {
    if (this.connectedTime != 0) {
      return;
    }
    this.connectedTime = Date.now();
  }

  static onDisconnected() {
    if (this.connectedTime != 0) {
      return;
    }
    this.connectedTime = Date.now();
  }

  static onIFrameReceived() {
    if (this.IFrameReceivedTime != 0) {
      return;
    }
    this.IFrameReceivedTime = Date.now();
  }

  static onFrameRendered() {
    if (this.frameRenderedTime != 0) {
      return;
    }
    this.frameRenderedTime = Date.now();
  }

  static onPowerBeginChecked() {
    if (this.powerCheckTime != 0) {
      return;
    }
    this.powerCheckTime = Date.now(); 
  }

  static onPowerEndChecked() {
    if (this.powerCheckedTime != 0) {
      return;
    }
    this.powerCheckedTime = Date.now();
  }

  static onPrivacyBeginCheck() {
    if (this.privacyCheckTime != 0) {
      return;
    }
    this.privacyCheckTime = Date.now();
  }

  static onPrivacyEndCheck() {
    if (this.privacyCheckedTime != 0) {
      return;
    }
    this.privacyCheckedTime = Date.now();
  }

  static onNetworkCheck() {
    if (this.networkCheckTime != 0) {
      return;
    }
    this.networkCheckTime = Date.now();
  }

  static onNetworkChecked() {
    if (this.networkCheckedTime != 0) {
      return;
    }
    this.networkCheckedTime = Date.now();
  }


  static report() {
    if (this.isReported) {
      return;
    }
    this.isReported = true;
    let str = ` enter plugin time：${ this.enterPluginTime
    }, enter live page time: ${ this.enterLivePageTime
    }, begin privacy check:${ this.privacyCheckTime
    }, end privacy check:${ this.privacyCheckedTime
    }, start check power:${ this.powerCheckTime
    }, end check power:${ this.powerCheckedTime
    }, start check network:${ this.networkCheckTime
    }, end check network:${ this.networkCheckedTime
    }, start connect:${ this.startConnectTime
    }, connected time:${ this.connectedTime
    }, receive iFrame time:${ this.IFrameReceivedTime
    }, render Frame time:${ this.frameRenderedTime
    }, total_duration: ${ (this.frameRenderedTime - this.enterPluginTime) }`;
    Service.smarthome.reportLog(Device.model, str);

  }


}