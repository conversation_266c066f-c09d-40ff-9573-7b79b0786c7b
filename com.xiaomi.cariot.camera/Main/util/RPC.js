import { Service } from "miot";
import { Device } from "miot";

export class _RPC {
  callMethod(method, args, extraPayload = {}) {
    let startTime = new Date().getTime();
    return new Promise((resolve, reject) => {
      Device.getDeviceWifi().callMethod(method, args, extraPayload)
        .then((result) => {
          let endTime = new Date().getTime();
          console.log(Device.model, `rpc method: ${ method } param:${ JSON.stringify(args) } result: success   cost time:${ endTime - startTime }`);
          Service.smarthome.reportLog(Device.model, `rpc method: ${ method } param:${ JSON.stringify(args) } result: success   cost time:${ endTime - startTime }`);
          resolve(result);
        })
        .catch((error) => {
          let endTime = new Date().getTime();
          Service.smarthome.reportLog(Device.model, `rpc method: ${ method } param:${ JSON.stringify(args) } result: failed ` + ` reason:${ JSON.stringify(error) }   cost time:${ endTime - startTime }`);
          reject(error);
        });
    });
  }
}

const RPC = new _RPC();
export default RPC;
