import { CLOUD_PRIVACY_EVENT_TYPES, Device, Package, PrivacyEvent, Service , UserExpPlanEvent ,USER_EXP_PLAN_EVENT_TYPES } from "miot";
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import React from 'react';
import { MessageDialog } from "mhui-rn";

// 使用sdk层提供的工具类
export default class PrivacySDKUtil {


  bindPrivacyCallback(privacyResult) {
    this.privacyResult = privacyResult;
  }

  

  getTimeoutDialog(showTimeoutDialog) {
    return (
      <MessageDialog
        message = {LocalizedStrings["network_fake_connected"]}
        buttons = {
          [
            {
              text: LocalizedStrings['action_confirm'],
              callback: () => {
                Package.exit();
              }
            }
          ]
        }
        visible = {showTimeoutDialog}
      >

      </MessageDialog>
    );
  }
  
  checkNeedPopPrivacyDialog = () => {
    /**
     * ************REMEMBER TO UPDATE,  HERE ONLY FOR TEST***************
     */
    this._cloudPrivacyEvent = PrivacyEvent.cloudPrivacyEvent.addListener((message) => {
      if (!message) {
        console.log(`收到云端隐私通知数据为空`);
        return;
      }
      switch (message.eventType) {
        case CLOUD_PRIVACY_EVENT_TYPES.AGREED:
          this.privacyResult && this.privacyResult.onPrivacyAuthed();
          break;
        case CLOUD_PRIVACY_EVENT_TYPES.POP_DIALOG_SUCCESS:
          this.privacyResult && this.privacyResult.onPrivacyDialogPoped();
          break;
        case CLOUD_PRIVACY_EVENT_TYPES.FAILED:
          this.privacyResult && this.privacyResult.onPrivacyTimeout();
          Device.setNativePrivacyConfirmationVersion({ 'version': '' });
          break;
        default:
          break;
      }
    });
    this._userExpPlanEvent = UserExpPlanEvent.userExpPlanEvent.addListener((message) => {
      console.log(`收到用户体验计划通知数据：${ JSON.stringify(message) }`);
      if (!message) {
        console.log(`收到用户体验计划通知数据为空`);
        return;
      }
      this.userExpPlanPopup = message.userExpPlanEventType;
      switch (message.userExpPlanEventType) {
        case USER_EXP_PLAN_EVENT_TYPES.POP_DIALOG_SUCCESS:
          this.privacyResult && this.privacyResult.userExpPlanPopupCallBack(false);
          break;
        case USER_EXP_PLAN_EVENT_TYPES.AGREED:
        case USER_EXP_PLAN_EVENT_TYPES.CANCELED:
          this.privacyResult && this.privacyResult.userExpPlanPopupCallBack(true);
          break;
        case USER_EXP_PLAN_EVENT_TYPES.DISABLED:
          break;
        default:
          break;
      }
    });
    
  }


  destroyPrivacyListener = () => {
    this._cloudPrivacyEvent && this._cloudPrivacyEvent.remove();
  }


}