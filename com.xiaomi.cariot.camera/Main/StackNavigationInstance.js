import { DeviceEventEmitter } from "react-native";
import Toast from './components/Toast';
import { getStack } from ".";
import Package from "miot/Package";
export const SD_CLOUD_STACK_NAVIGATION_ONPAUSE = "SD_CLOUD_STACK_NAVIGATION_ONPAUSE";
export const SD_CLOUD_STACK_NAVIGATION_ONRESUME = "SD_CLOUD_STACK_NAVIGATION_ONRESUME";
export const SD_CLOUD_STACK_NAVIGATION_ONBACK = "SD_CLOUD_STACK_NAVIGATION_ONBACK";
export const SD_CLOUD_STACK_NAVIGATION_SETTING = "SD_CLOUD_STACK_NAVIGATION_SETTING";
export const SD_CLOUD_FORCE_REFRESH_TAB = "SD_CLOUD_FORCE_REFRESH_TAB";
export const SD_CLOUD_FORCE_LOAD_TAB_DATA = "SD_CLOUD_FORCE_LOAD_TAB_DATA";

// 解决tabNavigation里的控件跳转其他页面 跳不到的问题，因为navigation不一样。
export default class StackNavigationInstance {
  
  static isRecording = false;
  static sSdcardCloudTimelinePage = null;
  static lLiveVideoPageV2 = null;

  static setStackNavigationInstance(navigation) {
    if (navigation != null && navigation.state != null && navigation.state.routeName != null) {
      if (navigation.state.routeName === "SdcardCloudTimelinePageV2") {
        this.sSdcardCloudTimelinePage = navigation;
      } else if (navigation.state.routeName === "MainPage") {
        this.lLiveVideoPageV2 = navigation;
      }
    }
  }

  static jumpToStackNavigationPage(pageName, param) {
    this.lLiveVideoPageV2 && this.lLiveVideoPageV2.navigate(pageName, param);
  }
  static jumpToStackNavigationPage_forSDCloudPage(pageName, param) {
    this.sSdcardCloudTimelinePage && this.sSdcardCloudTimelinePage.navigate(pageName, param);
  }
  static getStackNavigationInstance() {
    return this.sSdcardCloudTimelinePage;
  }

  static goBack_ForSDcardCloudPage() {
    if (this.isRecording) {
      Toast.success("camera_recording_block");
      return;
    }

    DeviceEventEmitter.emit(SD_CLOUD_STACK_NAVIGATION_ONBACK);
    let rootStack = getStack();
    const routes = rootStack._navigation.state.routes;
    if (routes.length > 1) {
      this.sSdcardCloudTimelinePage && this.sSdcardCloudTimelinePage.goBack();
    } else {
      Package.exit();
    }
  }

  static goSetting_ForSDcardCloudPage() {
    if (this.isRecording) {
      Toast.success("camera_recording_block");
      return;
    }

    DeviceEventEmitter.emit(SD_CLOUD_STACK_NAVIGATION_SETTING);
    this.sSdcardCloudTimelinePage && this.sSdcardCloudTimelinePage.navigate("SurvelillanceSettingV2", {});
  }
}