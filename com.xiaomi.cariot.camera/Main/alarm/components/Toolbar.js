import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity
} from 'react-native';
import PropTypes from 'prop-types';
import { localStrings as LocalizedStrings } from "../../MHLocalizableString";
import Toast from '../../components/Toast';
import { Separator } from 'mhui-rn';

export default class Toolbar extends React.Component {

  static propTypes = {
    online: PropTypes.bool,
    hasFaceRecognition: PropTypes.bool,
    onLiveVideoClick: PropTypes.func,
    onFaceRecognitionClick: PropTypes.func
  }

  static defaultProps = {
    online: false,
    hasFaceRecognition: false
  }

  constructor(props) {
    super(props);
    this.state = {
      online: this.props.online,
      hasFaceRecognition: this.props.hasFaceRecognition
    };
  }

  componentDidUpdate(prevProps, prevState, snapshot) {
    if (prevProps.online != this.props.online) {
      this.setState({
        online: this.props.online,
        hasFaceRecognition: this.props.hasFaceRecognition
      });
    }
  }

  render() {
    let powersave = this.props.powersave;
    let eco = this.props.eco;
    let hasNetwork = this.props.hasNetwork;
    return (
      <View style={styles.container}>
        <Separator/>
        <View style={styles.buttons}>
          <TouchableOpacity
            style={this.state.online || (!eco && !powersave && hasNetwork) ? styles.onlineButton : styles.offlineButton}
            onPress={() => {

              this.props.onLiveVideoClick();

              if (!eco && !hasNetwork) {
                // 无网络

                return;
              }
              if (this.state.online && this.props.onLiveVideoClick) {
                this.props.onLiveVideoClick();
              } else if (!this.state.online && !eco && !powersave && hasNetwork && this.props.onLiveVideoClick) {
                // 开启保活、门铃处于保活状态、有网络
                this.props.onLiveVideoClick();
              } else if (!this.state.online && eco && this.props.onKeepLiveDialogBlock) {
                // 关闭保活
                this.props.onKeepLiveDialogBlock(1);
              } else if (!this.state.online && !eco && powersave && hasNetwork && this.props.onKeepLiveDialogBlock) {
                // 开启保活、门铃处于非保活状态、
                this.props.onKeepLiveDialogBlock(2);
              } else if (!this.state.online && this.props.onLiveVideoClick) {
                Toast.fail('live_video_button_reminder');
              }
            }}
          >
            <Text style={this.state.online || (!eco && !powersave && hasNetwork) ? styles.normalText : styles.offlineText}>{LocalizedStrings['button_text_real_time_video']}</Text>
          </TouchableOpacity>
          {
            this.state.hasFaceRecognition ?
              <TouchableOpacity style={styles.faceButton}
                onPress={() => {
                  if (this.props.onFaceRecognitionClick) {
                    this.props.onFaceRecognitionClick();
                  }
                }}
              >
                <Text style={styles.normalText}>{LocalizedStrings['as_facial_recognized']}</Text>
              </TouchableOpacity>
              :
              null
          }

        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    height: 100,
    paddingHorizontal: 20
  },
  separator: {
    height: 0.5,
    backgroundColor: 'gray'
  },
  buttons: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: "center"
  },
  onlineButton: {
    flex: 1,
    height: 40,
    borderRadius: 5,
    borderColor: '#5db8be',
    borderWidth: 1,
    alignItems: "center",
    justifyContent: 'center',
    opacity: 1,
    marginHorizontal: 10
  },
  offlineButton: {
    flex: 1,
    height: 40,
    borderRadius: 5,
    borderColor: '#e4f4f5',
    borderWidth: 1,
    alignItems: "center",
    justifyContent: 'center',
    marginHorizontal: 10
  },
  normalText: {
    color: '#32bac0'
  },
  offlineText: {
    color: '#e4f4f5'
  },
  faceButton: {
    flex: 1,
    height: 40,
    borderRadius: 5,
    borderColor: '#5db8be',
    borderWidth: 1,
    color: '#5db8be',
    alignItems: "center",
    justifyContent: 'center',
    opacity: 1,
    marginHorizontal: 10
  }
});
