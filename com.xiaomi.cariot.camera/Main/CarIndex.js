import React from 'react';
import { createStackNavigator } from "react-navigation";
import MainCarPage from "./car/MainCarPage";
import RouteProxy from "./RouteProxy";
import { Device, DeviceEvent, Host, Package } from "miot";
import StackNavigationInstance from "./StackNavigationInstance";
import { NavigationBar } from "mhui-rn";
import CameraConfig from "./util/CameraConfig";
import Service from "miot/Service";
import CarSetting from "./car/CarStorage";
import CarStorage from "./car/CarStorage";
import VipUtil from "./util/VipUtil";
import {
  CAMERA_CONTROL_CAR_SIID,
  CAMERA_CONTROL_CORRECTION_PIID,
  CAMERA_CONTROL_HDR_PIID,
  CAMERA_CONTROL_TIME_WATERMARK_PIID,
  CAMERA_SPEAKER_SIID,
  CAMERA_SPEAKER_VOLUME_PIID, COCKPIT_SERVICE_CROSS_PLAT_PIID,
  COCKPIT_SERVICE_LIVE_SWITCH_PIID, COCKPIT_SERVICE_RECORD_VOICE_PIID,
  COCKPIT_SERVICE_SIID,
  COCKPIT_SERVICE_STORAGE_SWITCH_PIID, MEMORY_CARD_MANAGEMENT_SIID,
  MEMORY_CARD_STORAGE_TIME_PIID,
  MOTION_DETECTION_ALARM_INTERVAL_PIID, MOTION_DETECTION_MOTION,
  MOTION_DETECTION_SIID
} from "./util/CarSpecConstant";
import AlarmUtilV2 from "./util/AlarmUtilV2";
import { Animated, Easing } from "react-native";

let rootStack = null;

export function getStack() {
  return rootStack;
}
function interpolator(props) {
  const { layout, position, scene } = props;

  if (!layout.isMeasured) {
    return (props) => {
      const { navigation, scene } = props;

      const focused = navigation.state.index === scene.index;
      const opacity = focused ? 1 : 0;
      // If not focused, move the scene far away.
      const translate = focused ? 0 : 1000000;
      return {
        opacity,
        transform: [{ translateX: translate }, { translateY: translate }]
      };
    };
  }
  const interpolate = (props) => {
    const { scene, scenes } = props;
    const index = scene.index;
    const lastSceneIndexInScenes = scenes.length - 1;
    const isBack = !scenes[lastSceneIndexInScenes].isActive;

    if (isBack) {
      const currentSceneIndexInScenes = scenes.findIndex((item) => item === scene);
      const targetSceneIndexInScenes = scenes.findIndex((item) => item.isActive);
      const targetSceneIndex = scenes[targetSceneIndexInScenes].index;
      const lastSceneIndex = scenes[lastSceneIndexInScenes].index;

      if (
        index !== targetSceneIndex &&
        currentSceneIndexInScenes === lastSceneIndexInScenes
      ) {
        return {
          first: Math.min(targetSceneIndex, index - 1),
          last: index + 1
        };
      } else if (
        index === targetSceneIndex &&
        currentSceneIndexInScenes === targetSceneIndexInScenes
      ) {
        return {
          first: index - 1,
          last: Math.max(lastSceneIndex, index + 1)
        };
      } else if (
        index === targetSceneIndex ||
        currentSceneIndexInScenes > targetSceneIndexInScenes
      ) {
        return null;
      } else {
        return { first: index - 1, last: index + 1 };
      }
    } else {
      return { first: index - 1, last: index + 1 };
    }
  };

  if (!interpolate) return { opacity: 0 };
  const p = interpolate(props);
  if (!p) return;
  const { first, last } = p;
  const index = scene.index;
  const opacity = position.interpolate({
    inputRange: [first, first + 0.01, index, last - 0.01, last],
    outputRange: [0, 1, 1, 0.85, 0]
  });

  const width = layout.initWidth;
  const translateX = position.interpolate({
    inputRange: [first, index, last],
    // outputRange: false ? [-width, 0, width * 0.3] : [width, 0, width * -0.3]
    outputRange: [width, 0, width * -0.3]
  });
  const translateY = 0;

  return {
    opacity,
    transform: [{ translateX }, { translateY }]
  };
}

function createRootStack(initPage, routeParam) {
  return createStackNavigator({
      // 主页面
      MainCarPage,
      // 设置
      CarSetting: new RouteProxy("CarSetting"),
      CarStorage: new RouteProxy("CarStorage"),
      IntroducePage: new RouteProxy("IntroducePage")
    },

    {
      initialRouteName: initPage,
      // initialRouteName: 'CarStorage',
      initialRouteParams: routeParam,
      // 添加transitionConfig左侧进入
      transitionConfig: () => {
        return Host.isAndroid ? { screenInterpolator: interpolator } : null;
      },
      // transitionConfig: () => ({
      //   transitionSpec: {
      //     duration: 500,
      //     easing: Easing.out(Easing.poly(4)),
      //     timing: Animated.timing,
      //   },
      //   screenInterpolator: sceneProps => {
      //     const { layout, position, scene } = sceneProps;
      //     const { index } = scene;
      //
      //     const opacity = position.interpolate({
      //       inputRange: [index - 1, index, index + 1],
      //       outputRange: [0, 1, 0],
      //     });
      //
      //     return { opacity };
      //   },
      // }),
      navigationOptions: ({ navigation }) => {
        StackNavigationInstance.setStackNavigationInstance(navigation);
        if (navigation.state.params && navigation.state.params.show) {
          return { header: null };
        } else {
          return {
            header:
              <NavigationBar
                { ...navigation.state.params }
                containerStyle={ { minHeight: 53 } }
              />

          };
        }
      }
    });
}


export default class CarIndex extends React.Component {
  constructor(props) {
    super(props);
    this.initData();
  }

  initData() {
    this.initPage = "MainCarPage";
    Service.miotcamera.setUseIjkDecoderGlobal(false);
    // if (CameraConfig.isXiaomiCamera(Device.model)) {
    //   Service.miotcamera.setUseIjkDecoderGlobal(false);// 先都打开，有问题再说;
    // } else {
    //   Service.miotcamera.setUseIjkDecoderGlobal(true);// 先都打开，有问题再说;
    // }
  }

  componentDidMount() {
    Service.smarthome.reportLog(Device.model, "car index componentDidMount");
    Package.disableAutoCheckUpgrade = false;
    Service.miotcamera.closeFloatWindow();// 关闭悬浮窗

    // 取一下VIP状态
    VipUtil.getVipStatus();

    // 缓存一份设置页数据
    this.getSettings();
  }

  getSettings() {
    let params = [
      { did: Device.deviceID, siid: COCKPIT_SERVICE_SIID, piid: COCKPIT_SERVICE_STORAGE_SWITCH_PIID },
      { did: Device.deviceID, siid: COCKPIT_SERVICE_SIID, piid: COCKPIT_SERVICE_LIVE_SWITCH_PIID },
      { did: Device.deviceID, siid: CAMERA_SPEAKER_SIID, piid: CAMERA_SPEAKER_VOLUME_PIID },
      { did: Device.deviceID, siid: CAMERA_CONTROL_CAR_SIID, piid: CAMERA_CONTROL_CORRECTION_PIID },
      { did: Device.deviceID, siid: CAMERA_CONTROL_CAR_SIID, piid: CAMERA_CONTROL_HDR_PIID },
      { did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: MOTION_DETECTION_ALARM_INTERVAL_PIID },
      { did: Device.deviceID, siid: CAMERA_CONTROL_CAR_SIID, piid: CAMERA_CONTROL_TIME_WATERMARK_PIID },
      { did: Device.deviceID, siid: MEMORY_CARD_MANAGEMENT_SIID, piid: MEMORY_CARD_STORAGE_TIME_PIID }
    ];
    if (CameraConfig.supportVoiceMute()) {
      params.push({ did: Device.deviceID, siid: COCKPIT_SERVICE_SIID, piid: COCKPIT_SERVICE_RECORD_VOICE_PIID });
    }

    if (CameraConfig.supportDivideStorage()) {
      params.push({ did: Device.deviceID, siid: MOTION_DETECTION_SIID, piid: MOTION_DETECTION_MOTION });
    }
    // if (CameraConfig.supportCrossPlatView()) {
    //   params.push({ did: Device.deviceID, siid: COCKPIT_SERVICE_SIID, piid: COCKPIT_SERVICE_CROSS_PLAT_PIID });
    // }
    Service.spec.getPropertiesValue(params, 2).then((res) => {
      console.log("getPropertiesValue",res);
      let stateProps = {};
      if (res[0].code === 0) {
        stateProps.videoSaveSwitch = res[0].value;
      }
      if (res[1].code === 0) {
        stateProps.remoteSwitch = res[1].value;
      }
      if (res[2].code === 0) {
        stateProps.volumeValue = res[2].value;
      }
      if (res[3].code === 0) {
        stateProps.distortionCorrectionSwitch = res[3].value;
      }
      if (res[4].code === 0) {
        stateProps.hdrIndex = res[4].value;
      }
      if (res[5].code === 0) {
        stateProps.interval = res[5].value;
      }
      if (res[6].code === 0) {
        stateProps.waterMarkSwitch = res[6].value;
      }

      if (CameraConfig.supportDivideStorage()) {
        if (res[8].code === 0) {
          stateProps.recordVoiceSwitch = res[8].value;
        }

        if (res[9].code === 0) {
          stateProps.videoCloudSaveSwitch = res[9].value;
        }
      } else if (CameraConfig.supportVoiceMute() && res[8].code === 0) {
        stateProps.recordVoiceSwitch = res[8].value;
      }

      CameraConfig.SETTING_PROP = stateProps;

      if (res[7].code === 0) {
        AlarmUtilV2.SD_EARLIEST_TIME = res[7].value;
      }

    }).catch((err) => {
      console.log("++++++++++++++++++err", err);
    });

  }


  render() {
    let RootStack = createRootStack(this.initPage, {});
    return (<RootStack
      ref={ (ref) => {
        rootStack = ref; // 保留RootStack，获取state.nav.routes 得到当前正在展示页的名称；获取_navigation，得到全局跳转页面的工具类。
      } }/>);
  }

}