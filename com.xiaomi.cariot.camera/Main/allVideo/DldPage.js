import React from 'react';
import BasePage, { BaseStyles } from "../BasePage";
import EventLoaderInf from "../framework/EventLoaderInf";
import { Text, View, TouchableOpacity, SafeAreaView, StatusBar, Platform } from 'react-native';
import { localStrings as LocalizedStrings } from "../MHLocalizableString";
import EventList from "../widget/EventList";
import DldMgr from "../framework/DldMgr";
import DldEventV from "../widget/DldEventV";
import CommonMsgDialog from "../ui/CommonMsgDialog";
import ImageButton from "miot/ui/ImageButton";
import Util from "../util2/Util";
import Orientation from 'react-native-orientation';
import CameraConfig from '../util/CameraConfig';
import { DescriptionConstants } from '../Constants';
import { DarkMode, Device, Service } from 'miot';
import CameraPlayer from "../util/CameraPlayer";
import { MISSError } from "miot/service/miotcamera";
import LogUtil from "../util/LogUtil";
import TrackConnectionHelper from "../util/TrackConnectionHelper";
import OfflineHelper from "../util/OfflineHelper";
import SdFileManager from "../sdcard/util/SdFileManager";
const TAG = "DldPage";

export default class DldPage extends BasePage {
  constructor(props) {
    super(props);
    this.mTitle = LocalizedStrings["storage_local_dlding"].replace("…", '');
    this.mTitle = this.mTitle.replace("...", '');
    this.props.navigation.setParams({ title: this.mTitle, show: true });
    this.mLdr = new DldEvLdr();
    this.mDate = new Date();
    this.currentNetworkState = 2; // 默认非0 非-1
    this.initState({
      curDld: null,
      progress: 0,
      showCancelDlg: false
    });

    this.mDldL = DldMgr.addListener((aInfo) => {
      switch (aInfo.type) {
        case "progress":
          console.log(this.tag, "update progress", aInfo.data.curItem.fileId, aInfo.detail);
          this.setState({ curDld: aInfo.data.curItem, progress: aInfo.detail });
          break;
        case "status":
          if (0 == DldMgr.mDld.list.length) {
            // this.naviBack();
            console.log('landing102, 0 items and remove all in dldpage');
            this.setState({ curDld: null, progress: 0 });
            this.mEvLst.removeEvents((aEv) => { return false; }); // remove all
          } else {
            console.log('landing1021, update dldpage');
            this.setState({});
            this.mEvLst.mRefresh();
          }
          break;
        default:
          break;

      }
    });
  }

  onResume() {
    super.onResume();
    StatusBar.setHidden(false);
    DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('light-content') : StatusBar.setBarStyle('dark-content');
    DldMgr.dldPageIsActive(true);
  }

  componentDidMount() {
    super.componentDidMount();
    // 监听网络状态
    CameraPlayer.getInstance().bindNetworkInfoCallback(this._networkChangeHandler);

    StatusBar.setHidden(false);
    CameraConfig.lockToPortrait();
  }
  componentWillUnmount() {
    super.componentWillUnmount();
    CameraPlayer.getInstance().bindNetworkInfoCallback(null);

    this.mDldL.remove();
  }

  _networkChangeHandler = (networkState) => {
    LogUtil.logOnAll(TAG, "network change", this.currentNetworkState, networkState);

    if (this.currentNetworkState == networkState) {
      return;
    }

    this.currentNetworkState = networkState;
    if (networkState == 0 || networkState == -1) { // 网络断开了连接 showError?
      Service.smarthome.reportLog(Device.model, `${ TAG } network error ${ networkState }`);

    } else {
      Service.smarthome.reportLog(Device.model, `${ TAG } network ${ networkState } p2p state ${CameraPlayer.getInstance().isConnected()}`);
      if (!CameraPlayer.getInstance().isConnected()) {
        LogUtil.logOnAll(TAG, "reconnect");
        CameraPlayer.getInstance().startConnect();
      }
    }
  };

  renderHeader() {
    let marginTop = Platform.OS == 'ios' ? StatusBar.currentHeight : 0;
    return (
      <View style = {[BaseStyles.row, {
        position: "absolute", height: 50, paddingLeft: 12, marginTop: marginTop,
        backgroundColor: "#ffffff",
        width: "100%",
        paddingRight: this.state.editing ? 12 : 0 }]}>

        <ImageButton
          accessibilityLabel={`${DescriptionConstants.zb_29}`}
          style={BaseStyles.icon40}
          source={Util.isDark() ? require("../../Resources/Images/icon_back_black_nor_dark.png") : require("../../Resources/Images/icon_back_black.png")}
          onPress={() => {
            this.naviBack();
          }}/>
        <Text style={[BaseStyles.text18, { fontWeight: "bold" }]}>{this.mTitle}</Text>
        <View></View>
      </View>
    );
  }

  render() {
    return (
      <SafeAreaView style={[BaseStyles.pageRoot, { backgroundColor: "#ffffff", paddingTop: StatusBar.currentHeight }]}>
        <View style={[BaseStyles.pageRoot, { paddingHorizontal: 20, flexDirection: 'column' }]}>

          {this.renderHeader()}
          <View style={[BaseStyles.row, { height: 59, paddingHorizontal: 4, marginTop: 50 }]}>
            <Text  numberOfLines={2} style={[BaseStyles.text22, { fontWeight: "bold", maxWidth:"60%", textAlignVertical: "center", textAlign: "center" }]}>{LocalizedStrings["storage_local_dld_lst"]}</Text>
            {
              !(null == this.state.curDld && 0 == DldMgr.mDld.list.length) ?
                <TouchableOpacity style={{ height: 34, backgroundColor: "#F5F5F5", borderRadius: 17, alignItems: "flex-end", justifyContent: "center" , paddingVertical: 5 }}
                  onPress={() => this.setState({ showCancelDlg: true })}
                >
                  <Text style={[BaseStyles.text13, { color: "#666666", alignSelf: "center", marginHorizontal: 15 }]}>{LocalizedStrings["cancel_all"]}</Text>
                </TouchableOpacity>
                : null
            }
          </View>
          <EventList
            ref = {(ref) => { this.mEvLst = ref; }}
            loader = {this.mLdr}
            loaderArgs = {{ startDate: this.mDate, filter: null }}
            emptyDes = { LocalizedStrings["storage_local_dld_empty"] }

            evCard={(aEv) => {
              let progress = null;
              if (this.state.curDld != null && aEv.fileId == this.state.curDld.fileId) {
                progress = this.state.progress;
              }
              // console.log(this.tag, "evcard", aEv.fileId, "progress", progress);
              return (<DldEventV
                item = {aEv}
                cardPressed = {(aItm) => {
                  console.log(this.tag, "cancel dld", aItm);
                  let filter = (aEv) => { return aEv == null || aItm.fileId != aEv.fileId; };
                  DldMgr.remove(filter);
                  this.mEvLst.removeEvents(filter);
                  if (0 == DldMgr.mDld.list.length) {
                    this.setState({ curDld:  null });
                  }
                }}
                cardPaused = { (aItm) => {
                  DldMgr.pauseItem(aItm);
                }}
                dldProgress={progress}
              />);
            }}
          />

          { this.state.showCancelDlg ? <CommonMsgDialog
            title={LocalizedStrings["cancel_all_in_download_list"]}
            confirmText={LocalizedStrings["confirm_cancel"]}
            cancelText={LocalizedStrings["btn_no"]}
            onConfirmPress={(e) => {
              this.setState({ showCancelDlg: false });
              this.cancelAllPressed();
            }}
            onCancelPress={() => {
              this.setState({ showCancelDlg: false });
            }}
            visible={this.state.showCancelDlg} /> : null}
        </View>
      </SafeAreaView>
    );
  }

  cancelAllPressed() {
    console.log('landing101, cancelAllPressed');
    let filter = (aEv) => { return false; };
    DldMgr.remove(filter);
    setTimeout(() => {
      this.mEvLst.removeEvents(filter);
    }, 100);
  }
}

class DldEvLdr extends EventLoaderInf {
  constructor() {
    super(false, false);
  }

  getEventList(aDate, aEvent, aIsMore) {
    return Promise.resolve({
      hasMore: false,
      nextDate: null,
      items: DldMgr.getDldList() });
  }

  getThumb(aRec) {
    return aRec.playCfg.loader.getThumb(aRec);
  }
}
