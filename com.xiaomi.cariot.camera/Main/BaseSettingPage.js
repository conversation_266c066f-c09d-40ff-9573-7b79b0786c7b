'use strict';

import {NavigationBar, ListItemWithSwitch} from "mhui-rn";
import React from 'react';
import {ScrollView, Image, Text, View, TouchableOpacity, Dimensions, StyleSheet} from 'react-native';

import { styles } from '../Main/setting/SettingStyles';
import { localStrings as LocalizedStrings } from "./MHLocalizableString";
import CoverLayer from "./widget/CoverLayer";

const { width: screenWidth } = Dimensions.get("screen");
export default class BaseSettingPage extends React.Component {
  // static navigationOptions = ({ navigation }) => {
  //   return {
  //     header: (
  //         <NavigationBar
  //             type={NavigationBar.TYPE.LIGHT}
  //             left={[
  //               {
  //                 key: NavigationBar.ICON.BACK,
  //                 onPress: (_) => navigation.goBack()
  //               }
  //             ]}
  //             title={navigation.getParam("navTitle")}
  //             titleStyle={{
  //               fontSize: 18,
  //               color: '#333333',
  //               fontWeight: 500
  //             }}
  //         />
  //     )
  //   };
  // };



  constructor(props, context) {
    super(props, context);
    this.titleHeight = 38;
  }

  // 监听列表滚动事件
  scrollViewScroll = (event) => {
    const y = event.nativeEvent.contentOffset.y;
    // let flag = y > 28;
    let flag = y > this.titleHeight;
    if (this.showTitle == flag) {
      return;
    }
    if (flag) {
      this.showTitle = true;
      this.props.navigation.setParams({ title: this.getTitle() });
    } else {
      this.showTitle = false;
      this.props.navigation.setParams({ title: "" });
    }
  };
  getTitle() {
    return "";
  }
  render() {
    let containerBg = this.getTitleBackgroundColor() ? {backgroundColor: this.getTitleBackgroundColor()} :{}
    return (
      <View style={styles.container}>

        <ScrollView showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="handled" onScroll={this.scrollViewScroll} scrollEventThrottle={10} contentContainerStyle={[{paddingBottom:40, flexGrow: 1},containerBg]}>

          <View style={{ flexDirection: "row", flexWrap: "wrap" }} key={0} onLayout={({ nativeEvent: { layout: { height } } }) => {
            this.titleHeight = height - 30;
          }}>
            <Text style={{ fontSize: 30, color: "rgba(0, 0, 0, 0.80)", fontWeight: "300", position: "relative", marginLeft: 25, marginTop: 3, marginBottom:23, fontFamily:'MiSans W'  }}>
              {this.getTitle()}
            </Text>
          </View>
          {this.renderSettingContent()}
        </ScrollView>
        {this.renderSettingBottomContent()}
        <CoverLayer CoverLayerState={(value) => {this.coverLayerStateChange(value)}}  ref={(ref) => this.coverLayer = ref} />

      </View>
    );
  }
  coverLayerStateChange(value) {

  }
  renderSettingContent() {
    return null;
  }

  renderSettingBottomContent() {
    return null;
  }


  componentDidMount() {
    if (this.hasRightButton()) {
      this.props.navigation.setParams({
        title: "",
        titleNumberOfLines: 2,
        left: [
          {
            key: NavigationBar.ICON.CLOSE,
            onPress: () => {
              this.leftPress();
            }
          }
        ],
        right: [
          {
            key: NavigationBar.ICON.COMPLETE,
            onPress: () => {
              this.rightPress();
            }
          }
        ],
        titleStyle: {
          fontSize: 18,
          color: '#333333',
          fontWeight: 500
        },
        backgroundColor: this.getTitleBackgroundColor()
      });
    } else {
      this.props.navigation.setParams({
        title: "",
        titleNumberOfLines: 3,
        left: [
          {
            key: NavigationBar.ICON.BACK,
            onPress: () => {
              this.leftPress();
            }
          }
        ],
        titleStyle: {
          fontSize: 18,
          color: '#333333',
          fontWeight: 500
        },
        backgroundColor: this.getTitleBackgroundColor()
      });
    }
  }
  getTitleBackgroundColor() {
    return '';
  }

  hasRightButton() {
    return false;
  }
  leftPress() {
    this.props.navigation.goBack();
  }

  rightPress() {
  }
}



const style = StyleSheet.create({

  functionTitle:{
    marginLeft:27,
    marginTop:8,
    marginBottom:30,
    fontWeight:'bold',
    fontSize:12,
    color:"#666"
  },

  desc_title: {
    color: "#000000",
    fontSize: 18,
    fontWeight: "bold",
    paddingHorizontal: 28,
    marginTop:20,
    fontFamily: "MI Lan Pro"
  },
  desc_subtitle: {
    color: "rgba(0, 0, 0, 0.6)",
    fontSize: 14,
    marginTop: 10,
    fontFamily: "MI Lan Pro",
    lineHeight: 21,
    marginHorizontal: 28,
    marginBottom:18
  },

  whiteblank: {
    height: StyleSheet.hairlineWidth,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginVertical: 20
  },

});
