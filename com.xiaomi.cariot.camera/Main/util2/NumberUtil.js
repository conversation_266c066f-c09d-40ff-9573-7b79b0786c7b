
export default class NumberUtil {



  static fromHexString(hexString) {
    return new Uint8Array(hexString.match(/.{1,2}/g).map((byte) => parseInt(byte, 16)));
  }

  static toHexString(bytes) {
    return bytes.reduce((str, byte) => str + byte.toString(16).padStart(2, '0'), '');
  }

  static minToDay(number) {
    let min = number % 60;
    let hour = 0;
    let day = 0;
    if (number >= 60) {
      let hours = number / 60;
      hour = hours % 24;
      if (hours >= 24) {
        day = hours / 24;
      }
    }
    return `${ day > 0 ? `${ day }天` : hour > 0 ? `${ hour }小时` : min > 0 ? `${ min }分钟` : "" }`;
  }

  // static digits = [
  //   '0', '1', '2', '3', '4', '5',
  //   '6', '7', '8', '9', 'a', 'b',
  //   'c', 'd', 'e', 'f', 'g', 'h',
  //   'i', 'j', 'k', 'l', 'm', 'n',
  //   'o', 'p', 'q', 'r', 's', 't',
  //   'u', 'v', 'w', 'x', 'y', 'z'
  // ];
  // /**
  //    * 实现字节数组向十六进制的转换方法一
  //    * @param b
  //    * @return/common/app/data/get
  //    */
  // static byte2HexStr(byteArray) {
  //   let str = ""
  //   let stmp = "";
  //   for (let n = 0; n < byteArray.length; n++) {
  //     stmp = (this.toHexString(byteArray[n] & 0XFF));
  //     if (stmp.length() == 1) {
  //       str = str + "0";
  //       str = str + stmp;
  //     } else {
  //       str = str + stmp;
  //     }
  //   }
  //   return str.toUpperCase();
  // }

  // /**
  //  * 将16进制字符串转换为byte[]
  //  *
  //  * @param str
  //  * @return
  //  */
  // static hex2Bytes(hexStr) {
  //   if (hexStr == null || hexStr == "") {
  //     return new byte[0];
  //   }

  //   let bytes = [];
  //   for (let i = 0; i < str.length / 2; i++) {
  //     let subStr = str.substring(i * 2, i * 2 + 2);
  //     bytes[i] = (byte) Integer.parseInt(subStr, 16);
  //   }

  //   return bytes;
  // }

  // static parseInt(str, radix) {
  //   /*
  //    * WARNING: This method may be invoked early during VM initialization
  //    * before IntegerCache is initialized. Care must be taken to not use
  //    * the valueOf method.
  //    */

  //   if (str == null) {
  //     // Android-changed: Improve exception message for parseInt.
  //     return 0;
  //   }

  //   if (radix < 2) {
  //     return 0;
  //   }

  //   if (radix > 36) {
  //     return 0;
  //   }

  //   let result = 0;
  //   let negative = false;
  //   let i = 0;
  //   let len = str.length();
  //   let limit = - Number.MAX_VALUE;
  //   let multmin;
  //   let digit;

  //   if (len > 0) {
  //     let firstChar = str.charAt(0);
  //     if (firstChar < '0') { // Possible leading "+" or "-"
  //       if (firstChar == '-') {
  //         negative = true;
  //         limit = Number.MIN_VALUE;
  //       } else if (firstChar !== '+')
  //         return 0;

  //       if (len == 1) {// Cannot have lone "+" or "-"
  //         return 0;
  //       }
  //       i++;
  //     }
  //     multmin = limit / radix;
  //     while (i < len) {
  //       // Accumulating negatively avoids surprises near MAX_VALUE
  //       digit = this.digit(s.charAt(i++), radix);
  //       if (digit < 0) {
  //         throw NumberFormatException.forInputString(s);
  //       }
  //       if (result < multmin) {
  //         throw NumberFormatException.forInputString(s);
  //       }
  //       result *= radix;
  //       if (result < limit + digit) {
  //         throw NumberFormatException.forInputString(s);
  //       }
  //       result -= digit;
  //     }
  //   } else {
  //     throw NumberFormatException.forInputString(s);
  //   }
  //   return negative ? result : -result;
  // }

  // static digit(code, radix) {
  //   CharacterData.of(codePoint).digit(codePoint, radix);
  // }

  // /**
  //    * Format a long (treated as unsigned) into a character buffer.
  //    * @param val the unsigned int to format
  //    * @param shift the log2 of the base to format in (4 for hex, 3 for octal, 1 for binary)
  //    * @param buf the character buffer to write to
  //    * @param offset the offset in the destination buffer to start at
  //    * @param len the number of characters to write
  //    * @return the lowest character  location used
  //    */
  // static formatUnsignedInt(val, shift, buf, offset, len) {
  //   charPos = len;
  //   radix = 1 << shift;
  //   mask = radix - 1;
  //   do {
  //     buf[offset + (--charPos)] = this.digits[val & mask];
  //     val >>>= shift;
  //   } while (val != 0 && charPos > 0);

  //   return charPos;
  // }


  // static toHexString(i) {
  //   return toUnsignedString0(i, 4);
  // }

  // /**
  //  * Convert the integer to an unsigned number.
  //  */
  // static toUnsignedString0(val, shift) {
  //   // assert shift > 0 && shift <=5 : "Illegal shift value";
  //   let mag = 32 - this.numberOfLeadingZeros(val);
  //   let chars = Math.max(((mag + (shift - 1)) / shift), 1);
  //   let buf = new array[chars];

  //   this.formatUnsignedInt(val, shift, buf, 0, chars);

  //   // Android-changed: Use regular constructor instead of one which takes over "buf".
  //   // return new String(buf, true);
  //   return this.bin2String(buf);
  // }

  // static bin2String(array) {
  //   let result = "";
  //   for (let i = 0; i < array.length; i++) {
  //     result += String.fromCharCode(parseInt(array[i], 2));
  //   }
  //   return result;
  // }

  // static numberOfLeadingZeros(i) {
  //   // HD, Figure 5-6
  //   if (i == 0)
  //     return 32;
  //   let n = 1;
  //   if (i >>> 16 == 0) { n += 16; i <<= 16; }
  //   if (i >>> 24 == 0) { n += 8; i <<= 8; }
  //   if (i >>> 28 == 0) { n += 4; i <<= 4; }
  //   if (i >>> 30 == 0) { n += 2; i <<= 2; }
  //   n -= i >>> 31;
  //   return n;
  // }

}