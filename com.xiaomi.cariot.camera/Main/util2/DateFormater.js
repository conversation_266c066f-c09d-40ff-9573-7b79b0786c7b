import Host from "miot/Host";
import dayjs from "dayjs";

export default class DateFormater {

    static myInstacne = null

    static instance() {
      if (this.myInstacne === null) {
        this.myInstacne = new DateFormater();
      }
      return this.myInstacne;
    }
    
    formatMD(timestamp) {
      let date = new Date(timestamp);// 时间戳为10位需*1000，时间戳为13位的话不需乘1000
      let m = `${ date.getMonth() < 9 ? `0${ date.getMonth() + 1 }` : (date.getMonth() + 1) }-`;
      let d = (date.getDate() < 10 ? `0${ date.getDate() }` : date.getDate());
      return m + d;
    }

    format(timestamp) {
      let date = new Date(timestamp);// 时间戳为10位需*1000，时间戳为13位的话不需乘1000
      let h = `${ date.getHours() < 10 ? `0${ date.getHours() }` : date.getHours() }:`;
      let m = (date.getMinutes() < 10 ? `0${ date.getMinutes() }` : date.getMinutes());
      return h + m;
    }
    /**
     * 根据秒数返回时间字符串
     * @param seconds
     * @return {string}
     */
    timeToString(seconds) {
      let hourInt = parseInt(seconds/3600);
      let minuteInt = parseInt((seconds % 3600)/60);
      let secondInt = parseInt((seconds % 3600)%60);
      let minuteString = minuteInt>9?minuteInt:`0${minuteInt}`;
      let secondString = secondInt>9?secondInt:`0${secondInt}`;
      let hourString = hourInt>9?hourInt:`0${hourInt}`;
      if (hourInt>0){
        return `${hourString}:${minuteString}:${secondString}`;
      }
      return `${minuteString}:${secondString}`;
    }

    formatTimestampToMMDD(ml = new Date().getTime()) {
      let timestampInSec = Number.parseInt(ml / 1000);
      return dayjs.unix(timestampInSec).format('MM/DD');
    }


}

