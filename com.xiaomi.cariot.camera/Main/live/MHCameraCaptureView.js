/**
 * @description 摄像机预览组件
 * @property {number} encoderWidth 编码宽度
 * @property {number} encoderHeight 编码高度
 * @property {number} encoderFps 编码帧率
 * @property {number} cameraType 摄像头类型 0前置 1后置
 * @property {bool} fullScreen 是否是全屏状态 当前Android端使用
 * @property {number} averageBitRate 码率 ios端使用
 */

import React from 'react';
import PropTypes from 'prop-types';
import { requireNativeComponent, View, ViewPropTypes } from 'react-native';

const MHCameraCaptureView = requireNativeComponent("MHCameraCaptureView", {
  propTypes: {
    encoderWidth: PropTypes.number,
    encoderHeight: PropTypes.number,
    encoderFps: PropTypes.number,
    cameraType: PropTypes.number,
    did: PropTypes.number,
    fullScreen: PropTypes.bool,
    averageBitRate: PropTypes.number,
    ...View.propTypes
  }
});
module.exports = MHCameraCaptureView;
