import React from 'react';
import { Dimensions } from "react-native";
import { DarkMode, Device, DeviceEvent, Host, PackageEvent, Service, System } from 'miot';
import {
  StatusBar,
  Platform,
  BackHandler,
  Image,
  StyleSheet,
  View,
  Text
} from 'react-native';
import ImageButton from "miot/ui/ImageButton";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import Util from "../util2/Util";
import StatusBarUtil from '../util/StatusBarUtil';
import { CALL_CAR_STATE, CALL_COMMAND } from "../util2/Const";
import DateFormater from "../util2/DateFormater";
import CameraPlayer, { MISSCommand_ECO } from "../util/CameraPlayer";
import { MISSCommand } from "miot/service/miotcamera";
import base64js from "base64-js";
import AlarmUtil from "../util/AlarmUtil";
import LogUtil from "../util/LogUtil";
import Toast from "../components/Toast";
import { DirectionViewConstant } from "../ui/DirectionView";
import { AudioEvent } from "miot/host/audio";
import CameraConfig from "../util/CameraConfig";
import LoadingView from "../ui/LoadingView";
import { handlerOnceTap } from "../util/HandlerOnceTap";

const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width);// use this.winPortraitWidth instead
const navigationBarHeightFat = 53; // 导航栏高度，有副标题
const iconButtonSize = 50;
const kIsCN = Util.isLanguageCN();
const TAG = "LiveStatesPage";

/**
 * @Author: byh
 * @Date: 2024/10/26
 * @explanation:
 * 状态类
 * 未发起-发起状态
 * 发起后-等待、拒绝、超时未接听、通话挂断等
 *********************************************************/
export default class LiveStatePage extends React.Component {
  static navigationOptions = (navigation) => {
    return {
      headerTransparent: true,
      header: null
    };
  };

  constructor(props) {
    super(props);
    this.state = {
      stateType: CALL_CAR_STATE.NOT_INITIAL,
      callTimeEnd: 5670,
      showLoadingView: false
    };
    this.darkMode = DarkMode.getColorScheme() == "dark";
    this.startInervalTime = 0;
    this.isPageForeGround = true;
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        console.log(TAG, "didFocus");
        DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('dark-content') : StatusBar.setBarStyle('light-content');
        this._onResume();
        if (Platform.OS === "android") {
          BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
        }
      }
    );

    // didBlur在ios上调的时间晚，会在其他页面的onResume之后，换成willBlur
    this.willBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'willBlur',
      () => {
        console.log(TAG, "willblur");
        StatusBar.setBarStyle('dark-content');//去往了其他页面，要主动刷新状态栏 为黑色
        this.isPageForeGround = false;
        this._onPause();
      }
    );



    // 操作系统栏时，ios会调用packageDidResume 和 packageWillPause，android不会，所以android不会有pause和resume流程
    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      console.log(TAG, "didResumeListener", this.needGobackOnResume);
      if (Host.isAndroid && this.needGobackOnResume) {
        this.delayToGoBack && clearTimeout(this.delayToGoBack);
        this.delayToGoBack = setTimeout(() => {
          // 框架bug，Android端退到后台，再进入，如果返回上一页，
          this.props.navigation.goBack();
        }, 500);
      } else {
        this._onResume();
      }
    });

    this.willPauseListener = PackageEvent.packageWillPause.addListener(() => {
      console.log(TAG, "packageWillPause");
      this.isPageForeGround = false;
      this._onPause();

    });

    if (Platform.OS == "ios") {
      this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {// RN插件
        this._onResume();
      });

      this.willDisappearListener = PackageEvent.packageViewWillDisappearIOS.addListener(() => {
        this.isPageForeGround = false;
        setTimeout(() => {
          this._onPause();
        }, 500);
      });
    }


    Device.getRoomInfoForCurrentHome(Device.did).then((res) => {
      console.log("===============", JSON.stringify(res));
      this.homeName = res.data.homeName;
      this.roomName = res.data.roomName;
      this.forceUpdate();
    }).catch((err) => {
      console.log("getRoomInfoForCurrentHome===", JSON.stringify(err));
    });
  }

  componentDidMount() {
    // this._sendCommandToDevice(CALL_COMMAND.CANCEL);
    console.log(TAG, "release:", this.isPageRelease, this.needGobackOnResume);
    if (this.needGobackOnResume) {
      // 切后台页面销毁再回来
      console.log(TAG, "page is release");
      return;
    }
    if (!CameraPlayer.getInstance().isConnected()) {
      // 如果未连接，尝试去连接一次
      CameraPlayer.getInstance().startConnect();
    }

    // CameraPlayer.getInstance().bindConnectionCallback(this._connectionHandler);// 连接
    CameraPlayer.getInstance().bindP2pCommandCallback(this._p2pCommandHandler);// p2p连接
    // CameraPlayer.getInstance().bindNetworkInfoCallback(this._networkChangeHandler);

    let stateProps = {};
    let callCarState = this.props.navigation.getParam("type") || CALL_CAR_STATE.NOT_INITIAL;
    stateProps.stateType = callCarState;
    if (callCarState === CALL_CAR_STATE.CALL_END || callCarState === CALL_CAR_STATE.HANGUP) {
      // 需要展示结束时间
      stateProps.callTimeEnd = this.props.navigation.getParam('callEndTime') || 0;
      // 挂断后展示3秒后退出
      // 此时不需要去发cancel命令
      this.ignoreToCancel = true;
      this.startTimeoutToExitPage();
    }
    this.setState(stateProps);

    if (callCarState === CALL_CAR_STATE.NOT_INITIAL) {
      this._sendCommandToDevice(CALL_COMMAND.CALLING);
    }
    Dimensions.addEventListener('change', this.dimensionListener);
  }

  _p2pCommandHandler = ({ command, data }) => {
    // 扩展程序注册命令回复回调，command为返回的命令号值，data 为P2P命令的返回数据。
    if (command == MISSCommand_ECO.MISS_CMD_PLUGIN_CALL) {
      // 插件发送请求及设备回复指令相同
      // let ba = base64js.toByteArray(data);
      console.log(TAG, "MISS_CMD_PLUGIN_CALL", typeof (data), data);
      let ba = typeof (data) === 'string' ? JSON.parse(data) : data;
      LogUtil.logOnAll(`receive MISS_CMD_PLUGIN_CALL:${ command } data:${ data }`);
      if (ba.result == -1) {
        // 0 正常响应  -1 固件端忙碌 处理非0的情况 -2 车端暂不支持查看（比如未开启远程通话用户又发起了）
        // 提示忙线中，返回上级页面
        this.ignoreToCancel = true;
        Toast._showToast(LocalizedStrings['call_apply_busy']);
        this.props.navigation.goBack();
      } else if (ba.result == -2) {
        this.ignoreToCancel = true;
        Toast._showToast(LocalizedStrings['car_not_allow_to_view']);
        this.props.navigation.goBack();
      }
    } else if (command == MISSCommand_ECO.MISS_CMD_DEVICE_SEND) {
      // 设备端像插件发送数据
      let ba = typeof (data) === 'string' ? JSON.parse(data) : data;
      LogUtil.logOnAll(`receive MISS_CMD_DEVICE_SEND:${ command } data:${ data }`);
      switch (ba.event) {
        case 'busy':
          this.setState({ stateType: CALL_CAR_STATE.BUSY }, () => {
            // 1s后挂断并返回
            this.startTimeoutToExitPage(1000);
          });
          break;
        case 'answer':
          // 车机接听
          this.ignoreToCancel = true;
          this.ignorePauseToCancel = true;
          this.props.navigation.replace("LiveVideoPageV2");
          break;
        case 'refused':
          // 车机拒绝
          this.setState({ stateType: CALL_CAR_STATE.REFUSE }, () => {
            // 1s后挂断并返回
            this.startTimeoutToExitPage(1000);
          });
          break;
        case 'hangup':
          // 车机挂断  座舱各场景挂断：主动挂断、座舱来电挂断等等
          break;
        case 'timeout':
          // 车机处理超时
          this.setState({ stateType: CALL_CAR_STATE.NO_RESPONSE }, () => {
            // 1s后挂断并返回
            this.startTimeoutToExitPage(1000);
          });
          break;
      }
    } else {
      console.log(`receive other command:${ command } data:${ JSON.stringify(data) }`);
    }
  };

  _startPlayCallRing() {
    let params = {
      'streamType': 'ring',
      'updateAudioPlayerTimeInterval': 1,
      'audioPlayerUid': 'audioPlayerUid'
    };
    // 这个标记位的目的是安卓手机存在audioPlayerDidStartPlaying多次回调，导致一直震动
    this.canVibrate = false;
    this.ringEndLisener = AudioEvent.audioPlayerDidFinishPlaying.addListener(
      () => {
        // LogUtil.logOnAll(TAG, "one key call ringEnded!!!");
        this.canVibrate = true;
        Host.audio.startPlay(require("../../Resources/one_key_call_ring.mp3"), params);
      }
    );
    this.rinStartLisener = AudioEvent.audioPlayerDidStartPlaying.addListener(
      () => {
        // LogUtil.logOnAll(TAG, "one key call ringStarted!!!",this.canVibrate);
        if (this.canVibrate) {
          this.canVibrate = false;
          System.vibrate.vibrateLong();
          // System.vibrate.vibrateShort();
        }
      }
    );
    this.canVibrate = true;
    Host.audio.startPlay(require("../../Resources/one_key_call_ring.mp3"), params);
  }

  componentWillUnmount() {
    LogUtil.logOnAll(TAG,"componentWillUnmount");
    this.isPageRelease = true;
    if (!this.ignoreToCancel) {
      this._sendCommandToDevice(CALL_COMMAND.CANCEL, true);
    }
    Host.audio.stopPlay();
    this.ringEndLisener && this.ringEndLisener.remove();
    this.rinStartLisener && this.rinStartLisener.remove();
    this.willPauseListener && this.willPauseListener.remove();
    this.willAppearListener && this.willAppearListener.remove();
    this.willDisappearListener && this.willDisappearListener.remove();
    this.didResumeListener && this.didResumeListener.remove();
    this.didFocusListener && this.didFocusListener.remove();
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.delayToExit && clearTimeout(this.delayToExit);
    this.timeInterval && clearInterval(this.timeInterval);
    this.delayToCall && clearTimeout(this.delayToCall);
    this.setState = () => false;
  }

  startTimeoutToExitPage(delay = 2000) {
    this.delayToExit && clearTimeout(this.delayToExit);
    this.delayToExit = setTimeout(() => {
      this.props.navigation.goBack();
    }, delay);
  }

  startTimeInterval() {
    this.timeInterval && clearInterval(this.timeInterval);
    this.startInervalTime = new Date().getTime();
    this.timeInterval = setInterval(() => {
      let countTime = new Date().getTime() - this.startInervalTime;
      console.log("count is going", countTime);
      if (countTime >= 20000 && (this.state.stateType !== CALL_CAR_STATE.CAN_NOT_CONNECT && this.state.stateType !== CALL_CAR_STATE.NO_RESPONSE)) {
        this.setState({ stateType: CALL_CAR_STATE.CAN_NOT_CONNECT });
        // 测试20s无响应，跳到直播页
        // this.props.navigation.replace("LiveVideoPageV2");
      } else if (countTime >= 60000 && this.state.stateType !== CALL_CAR_STATE.NO_RESPONSE) {
        this.setState({ stateType: CALL_CAR_STATE.NO_RESPONSE }, () => {
          // 60s超时，发一个cancel
          this._sendCommandToDevice(CALL_COMMAND.CANCEL);
          // 1s后挂断并返回
          this.startTimeoutToExitPage(1000);
        });
      }
    }, 1000);
  }

  onBackHandler = () => {


  };

  _onResume() {

  }

  _onPause() {
    // CameraPlayer.getInstance().bindConnectionCallback(null);
    LogUtil.logOnAll(TAG, "onPause run");
    CameraPlayer.getInstance().bindP2pCommandCallback(null);
    // CameraPlayer.getInstance().bindPowerOffCallback(null);
    // CameraPlayer.getInstance().bindNetworkInfoCallback(null);
    // CameraPlayer.getInstance().bindWaterTimeCallback(null);
    if (!this.ignorePauseToCancel) {
      this._sendCommandToDevice(CALL_COMMAND.CANCEL);
    }
  }

  _getTitleBarPortraitHeight() {
    let titleBarHeight = navigationBarHeightFat;
    let statusBarHeight = this._getStatusBarHeight();

    titleBarHeight += statusBarHeight;
    return titleBarHeight;
  }

  _getStatusBarHeight() {
    let statusBarHeight = StatusBarUtil._getInset("top");
    return statusBarHeight;
  }

  // 请求
  _sendCommandToDevice(command = CALL_COMMAND.CALLING, justCancel = false) {
    Service.account.load().then((res) => {
      this._sendCommandRealToDevice(command, true, justCancel);
    }).catch((error) => {
      this._sendCommandRealToDevice(command, false, justCancel);
    });
  }

  _sendCommandRealToDevice(command = CALL_COMMAND.CALLING, isLoadInfoOk = true, justCancel = false) {
    LogUtil.logOnAll(TAG, "_sendCommandRealToDevice", command, isLoadInfoOk, justCancel);
    let params = {
      id: Math.floor(Math.random() * 1000),
      method: command,
      uid: Service.account.ID,
      ts: Math.floor( new Date().getTime() / 1000),
      name: isLoadInfoOk ? Service.account.nickName : 'unknow',
      url: isLoadInfoOk ? Service.account.avatarURL : ''
    };
    Service.miotcamera.sendP2PCommandToDevice(MISSCommand_ECO.MISS_CMD_PLUGIN_CALL, params).then((result) => {
      LogUtil.logOnAll("send apply remote review cmd", command, JSON.stringify(result));
      if (justCancel) {
        return;
      }
      if (command === CALL_COMMAND.CALLING) {
        // 发起请求
        this.setState({ stateType: CALL_CAR_STATE.INITIAL });
        // 开启计时器，记录20s 60s给相应不同状态
        this._startPlayCallRing();
        this.startTimeInterval();
      } else if (command === CALL_COMMAND.CANCEL) {
        this.timeInterval && clearInterval(this.timeInterval);
        Host.audio.stopPlay();
        if (Host.isAndroid && !this.isPageForeGround) {
          // 安卓设备处于后台，执行页面跳，再进入会执行此页面的didmount
          this.needGobackOnResume = true;
        } else {
          this.props.navigation.goBack();
        }
      }
    }).catch((error) => {
      LogUtil.logOnAll("send apply remote review cmd err", JSON.stringify(error));
      if (justCancel) {
        return;
      }
      if (command === CALL_COMMAND.CANCEL) {
        this.props.navigation.goBack();
      } else if (command === CALL_COMMAND.CALLING) {
        // 直接提示失败，不做重试处理
        if (!CameraPlayer.getInstance().isConnected()) {
          Toast._showToast(LocalizedStrings['talk_for_push_connecting']);
        } else {
          Toast._showToast(LocalizedStrings['camera_connect_retry']);
        }
        // 异常后就返回上级页面
        this.props.navigation.goBack();
        // 发送p2p指令失败
        // if (!CameraPlayer.getInstance().isConnected() && !this.tryAgain) {
        //   this.tryAgain = true;
        //   CameraPlayer.getInstance().startConnect();
        //   this.delayToCall && clearTimeout(this.delayToCall);
        //   this.delayToCall = setTimeout(() => {
        //     this._sendCommandToDevice(CALL_COMMAND.CALLING);
        //   }, 500);
        // } else {
        //   // this.setState({ showLoadingView: false });
        //   if (!CameraPlayer.getInstance().isConnected()) {
        //     Toast._showToast(LocalizedStrings['talk_for_push_connecting']);
        //   } else {
        //     Toast._showToast(LocalizedStrings['camera_connect_retry']);
        //   }
        // }
      }
    });
  }

  render() {
    let containerHeight = this._getTitleBarPortraitHeight();
    let statusBarHeight = this._getStatusBarHeight();
    return (
      <View style={ styles.main }>
        <View style={ { height: containerHeight } }>
          <View
            style={ {
              marginTop: statusBarHeight,
              paddingLeft: 12,
              height: 53,
              flexDirection: "row",
              alignItems: "center"
            } }>
            {/*{ this.state.stateType === CALL_CAR_STATE.NOT_INITIAL ?*/}
            <ImageButton
              style={ { width: 40, height: 40 } }
              source={ require('../../Resources/Images/miHome/close_white.png') }
              onPress={ () => {
                if (this.state.stateType === CALL_CAR_STATE.NOT_INITIAL) {
                  this.ignoreToCancel = true;
                  this.props.navigation.goBack();
                } else if (this.state.stateType === CALL_CAR_STATE.CALL_END) {
                  this.props.navigation.goBack();
                } else {
                  this._sendCommandToDevice(CALL_COMMAND.CANCEL);
                }

              } }/>


          </View>
        </View>

        <View style={ { flex: 0.2 } }></View>
        { this.state.stateType === CALL_CAR_STATE.CALL_END || this.state.stateType === CALL_CAR_STATE.HANGUP ? this._renderCallEndView() : this._renderCallStateView() }
        <View style={ { flex: 0.8 } }></View>
        { this._renderVideoCallButton() }
        { this._renderLoadingView() }
      </View>
    );
  }

  _renderLoadingView() {
    if (!this.state.showLoadingView) {
      return null;
    }
    return (
      <View style={{
        zIndex: 4,
        position: "absolute",
        width: "100%",
        height: "100%",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "#00000066"
      }}>
        <LoadingView style={{ width: 54, height: 54 }} />
        {/*<Text*/}
        {/*  style={{ marginTop: 10, fontSize: kIsCN ? 12 : 10, color: "#ffffff" }}>*/}
        {/*  {LocalizedStrings["camera_loading"]}*/}
        {/*</Text>*/}
      </View>
    );

  }
  _renderCallEndView() {
    let callTimeStr = DateFormater.instance().timeToString(this.state.callTimeEnd);
    return (
      <View style={ styles.stateContent }>
        <Text style={ { fontSize: 18, color: 'rgba(255, 255, 255, 0.6)' } }>{ LocalizedStrings['call_duration'] }</Text>
        <Text style={ { fontSize: 44, color: '#FFFFFF', marginTop: 18 } }>{ callTimeStr }</Text>
        <Text
          style={ {
            fontSize: 18,
            marginTop: 68,
            color: '#FFFFFF',
            fontWeight: 'bold'
          } }>{ this.state.stateType === CALL_CAR_STATE.HANGUP ? LocalizedStrings['call_disconnect'] : LocalizedStrings['call_hangup'] }</Text>
      </View>
    );
  }

  _renderCallStateView() {
    let title = Device.name ? Device.name : "";
    let stateStr = LocalizedStrings['apply_car_to_view'];
    switch (this.state.stateType) {
      case CALL_CAR_STATE.NOT_INITIAL:
        title = Device.name ? Device.name : "";
        stateStr = LocalizedStrings['apply_car_to_view'];
        break;
      case CALL_CAR_STATE.INITIAL:
        title = LocalizedStrings['car_apply_calling'];
        stateStr = LocalizedStrings['car_apply_waiting'];
        break;
      case CALL_CAR_STATE.REFUSE:
        title = LocalizedStrings['call_apply_refuse'];
        stateStr = "";
        break;
      case CALL_CAR_STATE.BUSY:
        title = LocalizedStrings['call_apply_busy'];
        stateStr = "";
        break;
      case CALL_CAR_STATE.CAN_NOT_CONNECT:
        // 20s 后无应该
        title = LocalizedStrings['call_apply_20s_no_answer'];
        stateStr = "";
        break;
      case CALL_CAR_STATE.NO_RESPONSE:
        // 60s无应答
        title = LocalizedStrings['call_apply_60s_no_answer'];
        stateStr = "";
        break;
    }
    return (
      <View style={ styles.stateContent }>
        <Image style={ { width: 140, height: 140 } } source={ { uri: Device.iconURL } }/>
        <Text style={ { fontSize: 18, color: '#FFFFFF', paddingHorizontal: 30, textAlign: 'center' } }>{ title }</Text>
        <Text
          style={ { fontSize: 13, marginTop: 8, color: '#898A88' } }>{ stateStr }</Text>
      </View>
    );
  }

  _renderVideoCallButton() {
    if (this.state.stateType === CALL_CAR_STATE.NOT_INITIAL) {
      return null;
    }
    let source = this.state.stateType === CALL_CAR_STATE.NOT_INITIAL ?
      require("../../Resources/Images/icon_call_jieting.png") : require("../../Resources/Images/icon_call_guaduan.png");
    let txtStr = this.state.stateType === CALL_CAR_STATE.NOT_INITIAL ? LocalizedStrings['apply_start_call'] : LocalizedStrings['car_call_end'];
    return (
      <View style={ {
        display: 'flex', flexDirection: 'row', flexWrap: 'nowrap', zIndex: 1,
        alignItems: "center", justifyContent: "center", width: "100%", paddingBottom: 50
      } }>
        <View style={ {
          display: 'flex',
          flexDirection: 'column',
          flexWrap: 'nowrap',
          zIndex: 1,
          alignItems: "center",
          justifyContent: "center"
        } }>
          <ImageButton
            style={ { width: 70, height: 70 } }
            source={ source }
            onPress={ () => handlerOnceTap(() => {
              console.log("===+====+=+=+==+=+=+=+==", this.state.stateType);

              switch (this.state.stateType) {
                case CALL_CAR_STATE.NOT_INITIAL:

                  // 失败后增加一次重新连接并且重试
                  this.tryAgain = false;
                  if (CameraConfig.debugSpecialSupport()) {
                    this.props.navigation.replace("LiveVideoPageV2");
                  } else {
                    // this.setState({ showLoadingView: true });
                    this._sendCommandToDevice(CALL_COMMAND.CALLING);
                  }

                  break;
                case CALL_CAR_STATE.CALL_END:
                  this.props.navigation.goBack();
                  break;
                default:
                  this._sendCommandToDevice(CALL_COMMAND.CANCEL);
                  break;
              }
            })}
          />
          <Text style={ {
            color: "white",
            fontSize: 13,
            marginTop: 15,
            fontWeight: 'bold',
            alignSelf: "center"
          } }>{ txtStr }</Text>
        </View>
      </View>
    );
  }


}

const styles = StyleSheet.create({
  main: {
    display: 'flex',
    flex: 1,
    flexDirection: 'column',
    flexWrap: 'nowrap',
    backgroundColor: "#000000",
    opacity: 0.75
  },

  stateContent: {
    alignItems: 'center'
  },

  controlLayout: {
    position: "relative",
    display: "flex",
    zIndex: 1,
    backgroundColor: "#EEEEEE",
    width: "100%",
    flexDirection: "column",
    flexWrap: 'nowrap',
    flexGrow: 1
  },

  panelOptionsViewLayout: {
    display: "flex",
    width: "100%",
    height: "100%",
    // marginTop: kWindowHeight >= 700 ? 25 : 5,
    flexGrow: 1,
    flexDirection: "column"
  },

  bgImageStyle: {
    width: "100%",
    aspectRatio: 1920.0 / 1080.0
  },

  panelOptionItemLayout: {
    display: "flex",
    position: "relative",
    width: "100%",
    height: 50,
    flexDirection: "row",
    alignItems: "center",
    marginTop: 20,
    marginBottom: 20,
    paddingLeft: 30,
    paddingRight: 30
  },

  videoControl: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    height: 80
  },

  videoControlBarPortrait: {
    display: "flex",
    position: "absolute",
    bottom: 0,
    marginBottom: 5,
    width: "100%",
    height: iconButtonSize,
    flexDirection: "row",
    alignItems: "center",
    paddingLeft: 25,
    paddingRight: 25,
    justifyContent: "space-between",
    backgroundColor: "transparent"
  }

});
