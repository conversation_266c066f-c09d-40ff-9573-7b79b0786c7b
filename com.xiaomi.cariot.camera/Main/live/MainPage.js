import React from 'react';
import {
  <PERSON><PERSON>,
  BackHandler,
  Dimensions, findNodeHandle,
  Image,
  PermissionsAndroid,
  Platform,
  ScrollView, StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View
} from 'react-native';
import NavigationBar from "miot/ui/NavigationBar";
import { DarkMode, Device, DeviceEvent, Host, HostEvent, Package, PackageEvent, Service, System } from "miot";
import { localStrings as LocalizedStrings } from '../MHLocalizableString';
import ImageButton from "miot/ui/ImageButton";
import Util from "../util2/Util";
import CameraPlayer, { MISSCommand_ECO } from '../util/CameraPlayer';
import CameraConfig from '../util/CameraConfig';
import LogUtil from '../util/LogUtil';
import { PAD_SCROLL_STRATEGY } from "miot/Host";
import Toast from '../components/Toast';
import SdcardEventLoader from '../framework/sdcard/SdcardEventLoader';
import TrackUtil from "../util/TrackUtil";
import StorageKeys from '../StorageKeys';
import API from '../API';
import dayjs from 'dayjs';
import CloudEventLoader, { CldDldTypes } from "../framework/CloudEventLoader";
import { isStartUpPush } from '../index';
import { MISSCommand, MISSConnectState, MISSError } from "miot/service/miotcamera";
import AlbumHelper from "../util/AlbumHelper";
import TrackConnectionHelper from "../util/TrackConnectionHelper";
import { DirectionViewConstant } from "../ui/DirectionView";
import VersionUtil from "../util/VersionUtil";
import base64js from "base64-js";
import AlarmUtil from "../util/AlarmUtil";
import OfflineHelper from "../util/OfflineHelper";

import PrivacySDKUtil from '../util/PrivacySDKUtil';
import DldMgr from "../framework/DldMgr";
import { CALL_CAR_STATE } from "../util2/Const";
import AlarmUtilV2, {
  PIID_AI_EXTENSION_HOUR_SWITCH, PIID_COCKPIT_BIND_INFO,
  PIID_COCKPIT_LIVE_SWITCH,
  PIID_COCKPIT_TEMPERATURE_STATUS,
  PIID_COCKPIT_WORK_MODE,
  PIID_ON,
  SIID_AI_EXTENSION,
  SIID_CAMERA_CONTROL,
  SIID_COCKPIT_SERVICE
} from "../util/AlarmUtilV2";
import { AbstractDialog, MessageDialog, StringSpinner } from "mhui-rn";
import DeviceOfflineDialog from "../ui/DeviceOfflineDialog";
import NoNetworkDialog from "../ui/NoNetworkDialog";
import { feedbackLogUploaderStatus, fetchLogUploaderStatus } from "../util/LogUploader";
import UriPlayer from "../framework/UriPlayer";
import Singletons from "../framework/Singletons";
import VipUtil from "../util/VipUtil";

const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width);
const kIsCN = Util.isLanguageCN();
const TAG = "MainPage";

export default class MainPage extends React.Component {

  constructor(props, context) {
    super(props, context);
    this.state = {
      showLoading: true,
      showCloudVipBuyTip: false,
      // pstate: MISSConnectState.MISS_Connection_Connected,
      pstate: MISSConnectState.MISS_Connection_Disconnected,
      workMode: 1,
      liveSwitch: false,
      showNotAllowDialog: false,  // 不支持远程查看
      isSleep: false, // 设备是否休闲
      permissionRequestState: 0,
      showPermissionDialog: false,
      hasNetWork: true,
      temperatureState: 0, // 0正常 1高温  2低温
      showHighTemperatureDialog: false,
      bindInfo: 1,
      showBindAbnormalDialog: false,
      showRedDot: false
    };
    this.isShowBindInfo = false;
    this.isShowHighTemperatureDialog = false;
    this.networkState = 1;
    this.privacySDKUtil = new PrivacySDKUtil();
    this.darkMode = DarkMode.getColorScheme() == "dark";
    this.sdcardCode = -1;
    this.isVip = false;
    this.titleHeight = 80;
    this.connRetry = 2;
    this.isFirstEnter = true;
    this.isPageForeGround = true;// 默认当前page在前台, 页面在后，plugin可前可后；plugin在后，页面可前可后。
    this.isPluginForeGround = true;// 默认当前插件在前台
    this.isAppForeround = true;// 米家app是否在前台
    DldMgr.addLdrs(SdcardEventLoader.getInstance());
    this.exitListener = PackageEvent.packageWillExit.addListener(() => {
      this.exitListener.remove();
      Service.miotcamera.disconnectToDevice();// 只调用一次。
    });
    this.didFocusListener = this.props.navigation.addListener(// 回到当前页面 或者第一次进来
      'didFocus',
      () => {
        console.log("test will focus");
        // DarkMode.getColorScheme() == "dark" ? StatusBar.setBarStyle('dark-content') : StatusBar.setBarStyle('light-content');
        // StatusBar.setBarStyle('dark-content');
        if (Platform.OS == "ios" && !this.isPluginForeGround) { // 如果是ios，插件跳到了原生页面，同时调用到了popToTop，package.willDisappear和didFocus都会被调用到，原来的逻辑就有问题。
          this.isPageForeGround = true;
          return;
        }
        if (Platform.OS == "ios" && isStartUpPush() && !Host.isPad) { // ios pad 不退出插件，避免从push点过来，跳到原生播放页，原生播放页是小画面。
          Package.exit();
          return;
        }
        this.isPageForeGround = true;
        this.isPluginForeGround = true;
        this.isAppForeround = true;
        console.log('testaaa', 'didFocusListener', 'page: ', this.isPageForeGround, ' plugin: ', this.isPluginForeGround);
        Host.setPadScrollDealStrategy({ strategy: PAD_SCROLL_STRATEGY.AUTO });
        this._onResume();
      }
    );

    this.willBlurListener = this.props.navigation.addListener(// //去往其他rn页面  相邻页面跳转 前面一个页面的onBlur先执行，后面一个页面的onFocus后执行
      'willBlur',
      () => {
        // if (Platform.OS === "android") {
        //   BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
        // }
        this.isPageForeGround = false;
        this._onPause();
      }
    );

    if (Platform.OS == "ios") {
      this.willAppearListener = PackageEvent.packageViewWillAppear.addListener(() => {
        console.log("============ packageViewWillAppear ==========");
        if (!this.isPageForeGround) {
          return;
        }
        this._onResume();
      });

      this.willDisappearListener = PackageEvent.packageViewWillDisappearIOS.addListener(() => {
        console.log("============ packageViewWillDisappearIOS==========")
        if (!this.isPageForeGround) { // 进入native页面，ios只调用这个页面。
          return;
        }

        // this.toPortrait();// ios强制切换到竖屏去
        setTimeout(() => {
          this.isPluginForeGround = false;// rnactivity调用了onpause
          // console.log('testaaa', 'willDisappearListener', 'page: ', this.isPageForeGround, ' plugin: ', this.isPluginForeGround);
          this._onPause();

        }, 0);
      });
    }
    this.willPauseListener = PackageEvent.packageWillPause.addListener(() => {
      LogUtil.logOnAll(TAG, "packageEvent.didPause: isPluginForeground:" + this.isPluginForeGround + " isPageForeround:" + this.isPageForeGround);
    });

    this.didResumeListener = PackageEvent.packageDidResume.addListener(() => {
      LogUtil.logOnAll(TAG, "did resume, this.isPageForeground:" + this.isPageForeGround + " this.isPluginForeground:" + this.isPluginForeGround + " isAppForeround:" + this.isAppForeround + " isOnRequestingPincode:" + this.isOnRequestingPincode + " isPowerOn:" + this.isPowerOn + " this.glview:" + (this.cameraGLView == null) + " isFirstEnter:" + this.isFirstEnter);
      if (!this.isPageForeGround) { // 如果当前插件不在前台
        return;
      }
      if (!this.isPluginForeGround && Platform.OS != "android") {
        return;
      }
      this._onResume();
    });
    // 监听手机网络状态变化
    this.cellPhoneNetworkStateChanged = HostEvent.cellPhoneNetworkStateChanged.addListener((networkInfo) => {
      if (this.networkState == networkInfo.networkState) {
        return;//状态一样，不要通知出去。
      }
      this.networkState = networkInfo.networkState;

      if (networkInfo.networkState === 1 || networkInfo.networkState === 2) {
        this.setState({hasNetWork : true});
      } else {
        this.setState({hasNetWork : false});
      }
    });

    this._deviceOnlineListener = DeviceEvent.deviceStatusChanged.addListener((device, newstatus) => {
      console.log(device.isOnline);
      console.log(`设备状态改变:${ JSON.stringify(newstatus) }，${ device.isOnline }`);
      if(newstatus.isOnline) {
        console.log('设备状态改变:设备上线了----');
        // 重新发起下p2p连接
        this.setState({});
        this.queryNetworkJob();
      } else {
        // 变成了离线
        this.setState({});
      }
    });

  }

  _onResume() {
    // 恢复时需重新注册监听
    CameraPlayer.getInstance().bindPowerOffCallback(this._powerOffHandler);
    CameraPlayer.getInstance().bindConnectionCallback(this._connectionHandler);// 连接
    CameraPlayer.getInstance().bindNetworkInfoCallback(this._networkChangeHandler);
    CameraPlayer.getInstance().bindRemoteViewStateCallback(this._remoteViewStateCallback);

    if (CameraConfig.isToUpdateVipStatue) {
      this._fetchVipStatus();
      CameraConfig.isToUpdateVipStatue = false;
    }

    CameraPlayer.getInstance().getSdcardStatus()
      .then(({ sdcardCode }) => {
        // let result = JSON.parse(res);//res优先转化为json 转化失败则是str
        LogUtil.logOnAll("-=-=-=-=-=-=-=-=-=-=-= sdcardCode:", sdcardCode, "-=-=-=showDialog:");
        this.sdcardCode = sdcardCode;
        if (this.sdcardCode == 0 && CameraConfig.fromSdCardErrorPush) {
          Toast.show("sds_format_success");
        }
        // if (showDialogs) {
        //   this.calledSdcardStatus = true;
        //   if (this.sdcardCode == 2) {
        //     this.onePopUp("SDCARD_FULL_DIALOG", 'sdcardFullDialog');
        //   } else if (this.sdcardCode == CameraPlayer.SD_CARD_TOO_SMALL_CODE) {
        //     this.onePopUp("SDCARD_SMALL_CAPACITY", 'sdcardSmallDialog');
        //   } else if (this.sdcardCode == CameraPlayer.SD_CARD_NEED_FORMAT_CODE
        //     || this.sdcardCode == CameraPlayer.SD_CARD_FILE_ERROR_CODE
        //     || this.sdcardCode == CameraPlayer.SD_CARD_INCOMPATIBLE_CODE
        //     || this.sdcardCode == 3) {
        //     this.onePopUp(`SDCARD_FORMAT_DIALOG_${ sdcardCode }`, 'sdcardFormatDialog');
        //   }
        // }
      })
      .catch(({ sdcardCode, error }) => {
        // fix MIIO-40229
        // error in this form {"error": {"code": -2003, "domain": "MiHomeNetworkErrorRemote", "localDescription": "The operation couldn’t be completed. (MiHomeNetworkErrorRemote error -2003.)"}, "message": "callMethod failedError Domain=MiHomeNetworkErrorRemote Code=-2003 \"(null)\" UserInfo={ot_cost=1570, id=10, code=-2003, net_cost=71, exe_time=100, message=default error, otlocalts=1598861669714605, error={code = \"-2003\"}}"};
        if (typeof (sdcardCode) === 'number' && sdcardCode >= 0) {
          this.sdcardCode = sdcardCode;
        }

        console.log("request sdcard status error", error);
      });
    // 请求看家事件
    this.loadCloudEvent();
    // 切换网络后，连接断开，影响远程访问指令下发
    if (CameraPlayer.getInstance().isDisconnected() && this.state.pstate == 2) {
      // 需要更新下连接状态
      LogUtil.logOnAll(TAG, "connect state is diff",this.state.pstate, CameraPlayer.getInstance().isConnected());
      this.setState({ pstate : MISSConnectState.MISS_Connection_Disconnected });
    }
    setTimeout(() => {
      Service.smarthome.reportLog(Device.model, "onresume, go connect");
      // 这里直接走连接的步骤吧  出现错误也会提示的
      this.queryNetworkJob();// 这里会处理是否连接成功之类的逻辑
    }, 500);

    this.loadLocalSettings();

    if (this.isFirstEnter) {
      return;// 刚进来的时候不请求connect  避免出现问题
    }
    this.loadCockService();



  }

  _onPause() {
    console.log("++++++++++++++++MainPage pause");
    CameraPlayer.getInstance().bindConnectionCallback(null);
    // CameraPlayer.getInstance().bindP2pCommandCallback(null);
    CameraPlayer.getInstance().bindPowerOffCallback(null);
  }

  loadCockService() {
    let params = [
      { sname: SIID_COCKPIT_SERVICE, pname: PIID_COCKPIT_WORK_MODE },
      { sname: SIID_COCKPIT_SERVICE, pname: PIID_COCKPIT_LIVE_SWITCH },
      // { sname: SIID_CAMERA_CONTROL, pname: PIID_ON },
      // { sname: SIID_CAMERA_CONTROL, pname: PIID_ON },
      { sname: SIID_CAMERA_CONTROL, pname: PIID_ON },
      { sname: SIID_COCKPIT_SERVICE, pname: PIID_COCKPIT_TEMPERATURE_STATUS },
      { sname: SIID_COCKPIT_SERVICE, pname: PIID_COCKPIT_BIND_INFO }
    ];
    AlarmUtilV2.getSpecPValue(params).then((res) => {
      this.isFirstEnter = false;
      let stateProps = {};
      if (res[0].code === 0) {
        stateProps.workMode = res[0].value;
        if (CameraConfig.debugSpecialSupport()) {
          // 为了让特殊插件版本可以正常看直播
          if (res[0].value == 1) {
            let params = [{ sname: SIID_COCKPIT_SERVICE, pname: PIID_COCKPIT_WORK_MODE, value: 0 }];
            AlarmUtilV2.setSpecPValue(params).then((res) => {
              if (res[0].code == 0) {
                this.setState({ workMode: 0 });
              }
            }).catch((err) => {});
          }
        }
      }

      if (res[1].code === 0) {
        stateProps.liveSwitch = res[1].value;
      }

      if (res[2].code === 0) {
        stateProps.isSleep = !res[2].value;
        CameraPlayer.getInstance().setPowerState(res[2].value);
      }


      if (res[3].code === 0) {
        stateProps.temperatureState = res[3].value;
        if (res[3].value === 1) {
          if (this.isShowHighTemperatureDialog) {
            stateProps.showHighTemperatureDialog = this.state.showHighTemperatureDialog ? true : false;
          } else {
            this.isShowHighTemperatureDialog = true;
            stateProps.showHighTemperatureDialog = true;
          }

          CameraConfig.isDeviceTemperatureHigh = true;
        } else {
          this.isShowHighTemperatureDialog = false;
          CameraConfig.isDeviceTemperatureHigh = false;
          stateProps.showHighTemperatureDialog = false;
        }
      }

      if (res[4].code === 0) {
        // 绑定信息
        if (CameraConfig.debugSpecialSupport()) {
          stateProps.bindInfo = 1;
        } else {
          stateProps.bindInfo = res[4].value;
          if (res[4].value == 0) {
            // 米家绑定
            if (!this.isShowBindInfo) {
              stateProps.showBindAbnormalDialog = true;
              this.isShowBindInfo = true;
            }
          }
        }
      }
      this.setState(stateProps);
    }).catch((error) => {
      this.isFirstEnter = false;
      console.log(TAG, "get spec error", error);
    });
  }

  async loadCloudEvent() {
    try {
      let mPlayCfg = { loader: Singletons.CloudEventLoader, player: UriPlayer };

      let data = await Util.getEventList(new Date(), "Default", false, 20, mPlayCfg, CldDldTypes.Events);
      // console.log('loadMonitoringDetail_rn', data);
      this.todayEvents = null;
      if (data && data.items.length > 0) {
        this.todayEvents = data.items.filter((item, index) => index < 3);
        this.todayEvents.map((item) => {
          Service.miotcamera.getFileIdImage(item.imgStoreId.toString()).then((res) => {
            item.imgStoreUrl = res;
            this.forceUpdate();
          }).catch((err) => {
            console.log("loadMonitoringDetail_rn getFileIdImage err=", JSON.stringify(err));
          });
          return item;
        });
        this.setState({ lastMonitorEvent: `${ data.items[0].eventTime } ${ data.items[0].desc }` });
      } else {
        this.setState({ lastMonitorEvent: null });
      }
    } catch (exception) {

    }
  }

  loadLocalSettings() {
    StorageKeys.IS_SHOW_SDCARD_PAGE_ALL_STORAGE.then((res) => {
      console.log("++++++++++++++++++++",res)
      if (res === "" || res == null) {
        StorageKeys.IS_SHOW_SDCARD_PAGE_ALL_STORAGE = false;
        res = false;
      }
      this.showSdcardPageAllStorage = res;
    });

    this._fetchFirmwareUpdateInfo();
  }

  _fetchFirmwareUpdateInfo() {
    Service.smarthome.getFirmwareUpdateInfo(Device.deviceID)
      .then((res) => {
        console.log("get firmware info: ", res);
        if (Device.isOwner) {
          this.setState({ showRedDot: res.needUpgrade }, () => {
            this.setNavigationBar();
          });
        }
        if (res.upgrading && res.upgrading == true) {
          this.isFirmwareUpdating = true;
          this.setState({ showErrorView: true, errTextString: LocalizedStrings["firmware_updating_desc"] });
          this._clearTimer(this.mFirmwareUpdatingTimer);
          this.mFirmwareUpdatingTimer = setTimeout(() => {
            this._fetchFirmwareUpdateInfo();
          }, 5000);
        } else if (this.isFirmwareUpdating && res.upgrading == false) {
          this.isFirmwareUpdating = false;
          this.setState({ showErrorView: false });
          CameraConfig.checkNasVersion = true;
          this._onResume();
        }
      })
      .catch((err) => {
        console.log(err);
        this.setState({ showRedDot: false });
      });
  }

  _fetchVipStatus() {
    // todo:需要框架端提供能够指定hostName前缀的api
    API.get("/miot/camera/app/v1/vip/status", "business.smartcamera")
      .then((result) => {
        if (result.code != 0) {
          return;
        }
        let data = result.data;
        if (data == null) {
          return;
        }
        let vip = data["vip"];
        let status = data["status"];
        let inWindow = !data["closeWindow"]; // false表示在窗口内，true表示不在
        let endTime = data["endTime"];
        StorageKeys.IS_VIP_STATUS = vip;
        StorageKeys.IN_CLOSE_WINDOW = inWindow;
        StorageKeys.VIP_DETAIL = data;
        CameraConfig.fetchCloudBabyCryStatus(Device.model, vip); // 26c02 需要根据是否是vip做这个事情
        this.isVip = vip;
        this.mVipStatus = status;
        CameraConfig.isVip = vip;
        this.inWindow = inWindow;
        let shouldShowBuyTip = false;
        this.freeHomeSurExpireTime = data.freeHomeSurExpireTime;// 免费看家结束时间
        this.freeHomSurStatus = data.freeHomSurStatus;// 免费看家弹窗状态
        LogUtil.logOnAll("vipStatus:", JSON.stringify(data));
        let showCloudForExpired = false;
        if (this.isVip) {
          let curTime = new Date().getTime();
          let willEndDays = dayjs(endTime).diff(dayjs(curTime), 'day') + 1; // +1 和云存管理页面保持一致
          this.cloudVipWillEndDays = willEndDays;
          if (Device.isOwner && this.inWindow && willEndDays <= 7) {
            this.cloudVipEndTime = endTime;
            shouldShowBuyTip = true;
          }
        } else {
          // if (Device.isOwner && status == 1 && this.inWindow) {
          //   shouldShowBuyTip = true;
          // }
          showCloudForExpired = VipUtil.showCloudContentforExpiredUser(data);
          if (Device.isOwner) {
            shouldShowBuyTip = true;
          }
        }
        CameraConfig.showCloudForExpired = showCloudForExpired;
        LogUtil.logOnAll(TAG, this.cloudVipWillEndDays, shouldShowBuyTip, this.mFirmwareSupportCloud, CameraConfig.isSupportCloud());
        this.setState({ showCloudVipBuyTip: shouldShowBuyTip });
      })
      .catch((err) => {
        console.log(err);
        // this.isVip = false;
        // this.inWindow = false;
        // StorageKeys.IN_CLOSE_WINDOW = false;
      }).finally(() => {
        this.getVipDone = true;
      });
  }

  componentDidMount() {
    this.checkPrivacyDialogLocal();
    CameraPlayer.getInstance().bindConnectionCallback(this._connectionHandler);// 连接
    CameraPlayer.getInstance().bindP2pCommandCallback(this._p2pCommandHandler);// p2p连接
    CameraPlayer.getInstance().bindNetworkInfoCallback(this._networkChangeHandler);
    CameraPlayer.getInstance().bindPowerOffCallback(this._powerOffHandler);
    CameraPlayer.getInstance().bindRemoteViewStateCallback(this._remoteViewStateCallback);

    this._fetchVipStatus();
    StorageKeys.IS_VIP_STATUS.then((res) => {
      if (typeof (res) === "string" || res == "" || res == null) {
        res = false;
        StorageKeys.IS_VIP_STATUS = false;
      }
      this.isVip = res;
      CameraConfig.isVip = res;
    })
      .catch((err) => {
        StorageKeys.IS_VIP_STATUS = false;
      });

    // 这里是旋转画面 没有起到作用 后续看用navigationBar怎们修改
    // const iconURL = useDeviceIcon();
    // const roomInfo = useDeviceRoomInfo();
    // const name = useDeviceName();
    this.setNavigationBar();

    if (VersionUtil.isFirmwareSupportColloctPrivacyLog(Device.model)) {
      this.startCheckLogTimer = setTimeout(() => {
        this.startCheckLog();
      }, 1000);// 延迟rpc，避免跟其他rpc一起打架
    }

  }

  setNavigationBar() {
    this.props.navigation.setParams({
      title: "",
      type: this.darkMode ? NavigationBar.TYPE.DARK : NavigationBar.TYPE.LIGHT,
      left: [
        {
          key: NavigationBar.ICON.BACK,
          onPress: () => {
            Package.exit();
          }
        }
      ],
      right: [
        {
          key: NavigationBar.ICON.MORE,
          showDot: this.state.showRedDot,
          onPress: () => {
            console.log("==========go setting");
            this.props.navigation.navigate('Setting', { hasFirmwareUpdate: this.state.showRedDot });
          }
        }
      ],
      titleStyle: {
        fontSize: 18,
        // color: '#333333',
        fontWeight: 500
      },
      backgroundColor: this.darkMode ? "#xm1a1a1a" : '#F6F6F6'
      // backgroundColor: '#F6F6F6'

    });
  }

  startCheckLog() {
    fetchLogUploaderStatus()
      .then((result) => {
        let { showDialog, msg } = result;
        Service.smarthome.reportLog(Device.model, `fetchLogUploaderStatus: ${ result.showDialog }`);
        clearInterval(this.logInterval);
        if (showDialog) {
          this.setState({ showLogDialog: true, logDialogContent: msg });
        }
      })
      .catch((err) => {
        Service.smarthome.reportLog(Device.model, `fetchLogUploaderStatus2: ${ JSON.stringify(err) }`);
        console.log(JSON.stringify(err));
      });
  }

  componentWillUnmount() {

    this.privacySDKUtil.destroyPrivacyListener();
    clearTimeout(this.callTimeout);
    // removeDarkListener();
    this.destroyed = true;
    OfflineHelper.resetLastOfflineTime();
    this._clearTimer(this.angleViewTimeout);
    this._clearTimer(this.mFirmwareUpdatingTimer);
    this._clearTimer(this._loadMonitoringDetailTimer);
    this._clearTimer(this.startCheckLogTimer);
    this._clearTimer(this.showNetworkDisconnectTimeout);
    this._clearTimer(this.reconnectTimeout);
    this._clearTimer(this.showPlayToolBarTimer);
    this._clearTimer(this.longPressTimer);
    this._clearTimer(this.angleViewTimeout);
    this._clearTimer(this.mRpcCallTimer);
    this._clearTimer(this.mGetRotationTimer);
    this._clearTimer(this.longPressTimer);
    this._clearTimer(this.mSetAudioBtnStateTimer);
    this._clearTimer(this.snapshotTimeout);
    this._clearTimer(this.mRecordOkTimer);


    if (Platform.OS === "android") {
      // BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.wllStopListener && this.wllStopListener.remove();
    Host.ui.keepScreenNotLock(true);
    this.didFocusListener && this.didFocusListener.remove();
    this.didBlurListener && this.didBlurListener.remove();
    this.willBlurListener && this.willBlurListener.remove();
    this.didResumeListener && this.didResumeListener.remove();
    this.willPauseListener && this.willPauseListener.remove();
    this.bpsListener && this.bpsListener.remove();
    this._deviceOnlineListener && this._deviceOnlineListener.remove();
    if (this.willAppearListener) {
      this.willAppearListener.remove();
    }
    if (this.willDisappearListener) {
      this.willDisappearListener.remove();
    }
    LogUtil.logOnAll("CameraPlayer.getInstance().bindOneKeyCallReceived(null) by componentWillUnmount");
    CameraPlayer.getInstance().bindOneKeyCallReceived(null);
    CameraPlayer.getInstance().bindCallStateReceived(null);

    this.frameQualityListener && this.frameQualityListener.remove();
    CameraPlayer.getInstance().bindConnectionCallback(null);
    CameraPlayer.getInstance().bindP2pCommandCallback(null);
    CameraPlayer.getInstance().bindPowerOffCallback(null);
    CameraPlayer.getInstance().bindNetworkInfoCallback(null);
    CameraPlayer.getInstance().bindWaterTimeCallback(null);
    CameraPlayer.getInstance().bindRemoteViewStateCallback(null);

    // Orientation.removeOrientationListener(this._orientationListener);

    this.delayGetPreSetPosition && clearTimeout(this.delayGetPreSetPosition);
    this.delayUpdatePreSetImg && clearTimeout(this.delayUpdatePreSetImg);
    if (this.rdtListener) {
      this.rdtListener.remove();
      this.rdtListener = null;
    }
    TrackConnectionHelper.report();
    this.add_often_look_position && this.add_often_look_position.remove();
    this.authPushWX && this.authPushWX.remove();
    DldMgr.clear(); // 退出的时候相关实例也都需要清空！！！
    DldMgr.removeLdrs(SdcardEventLoader.getInstance());
    SdcardEventLoader.destroyInstance();// 退出插件后  p2p就断开了，sdcard相关的监听都不需要存在。
    CameraPlayer.destroyInstance();
    // destroy的步骤尽量不要混乱，dldMgr持有sdcardEventLoader，SdcardEventLoader持有CameraPlayer，尽量一个一个按顺序的处理
    this.setState = () => false;
  }

  _clearTimer(timer) {
    if (timer) {
      clearTimeout(timer);
    }
  }

  // =======================================隐私相关 start=====================================
  checkPrivacyDialogLocal() {
    TrackConnectionHelper.onPrivacyBeginCheck();
    this.globalLoadingTimeout = setTimeout(() => {
      this.setState({ showGlobalLoading: true });
    }, 500);
    this.privacySDKUtil.bindPrivacyCallback(this);
    this.privacySDKUtil.checkNeedPopPrivacyDialog();
  }

  /**
   * @Author: byh
   * @Date: 2024/6/29
   * @explanation:
   * state true  表示弹框消失  即用户点击了同意或者取消
   * state false 表示弹框弹出
   *********************************************************/
  userExpPlanPopupCallBack(state) {
    LogUtil.logOnAll("userExpPlanPopupCallBack-=-=======", state);
    if (state) {
      this.privacyDialogPoped = false;
      // 往下执行
      this._privacyAgreed();
    }

  }

  // 授权了 进行后面的步骤
  onPrivacyAuthed() {
    Service.smarthome.reportLog(Device.model, "camera device auth success, start to connect");
    this._privacyAgreed();
  }

  _privacyAgreed() {
    LogUtil.logOnAll("privacy authed");
    TrackConnectionHelper.onPrivacyEndCheck();
    clearTimeout(this.globalLoadingTimeout);
    this.setState({ showGlobalLoading: false });
    // this.showLoadingView();
    // TODO 获取休眠状态
    // this.queryPowerOffProperty();// 查询 remote的网络状态。
    Service.smarthome.reportLog(Device.model, "authed, go connect");
    this.isPrivacyAuthed = true;
    this.queryNetworkJob();// 这里开始连接摄像头。
    // this._refreshRemoteProps();
    this.loadCockService();

  }

  // 授权拒绝了
  onPrivacyReject() {
    TrackConnectionHelper.onPrivacyEndCheck();
    TrackConnectionHelper.report();

    Package.exit();//授权拒绝了；
  }

  // 隐私弹框出现了，让loading 消失
  onPrivacyDialogPoped() {
    this.privacyDialogPoped = true;
    this.globalLoadingTimeout && clearTimeout(this.globalLoadingTimeout);
    this.setState({ showGlobalLoading: false });// 隐私对话框弹出来的时候应该隐藏全局的loading
  }

  // 从服务器是否授权状态拉取超时, loading消失，弹出退出对话框
  onPrivacyTimeout() {
    //这里又应该弹一个不可取消的对话框。 点确认 就退出插件。
    clearTimeout(this.globalLoadingTimeout);
    this.setState({ showGlobalLoading: false, showTimeoutDialog: true });
  }

  // =======================================隐私相关 end =====================================


  _connectionHandler = (connectionState) => {
    this.loadingRetryTimer = 0;
    Service.smarthome.reportLog(Device.model, TAG, `why!, _connectionHandler, connectionState.state: ${ connectionState.state }`);
    if (connectionState.state == MISSConnectState.MISS_Connection_ReceivedFirstFrame) {
      if (this.hasFirstFrame || Platform.OS === "ios") {
        this.setState({ showDefaultBgView: false, whiteTitleBg: false, showLoadingView: false, showPlayToolBar: true });
      } else {
      }
      if (!Host.isAndroid) {
        setTimeout(() => {
          AlbumHelper.snapshotForSetting(this.cameraGLView, this.state.isFlip);
        }, 100);
      }

      TrackConnectionHelper.onIFrameReceived();
    }

    if (this.state.pstate == connectionState.state && this.state.error == connectionState.error) {
      return;// 状态一样 没有必要通知
    }

    if (connectionState.state == MISSConnectState.MISS_Connection_Disconnected) {
      this.isConnecting = false;

      TrackConnectionHelper.onDisconnected();
      if (this.state.isSleep) {
        // 休眠状态下断开了连接，也不显示errorView
        return;
      }
      if (this.connRetry > 0 && this.state.pstate != connectionState.state) {
        this.connRetry = this.connRetry - 1;
        setTimeout(() => {
          Service.smarthome.reportLog(Device.model, `error retry connect: ${ this.connRetry }`);
          console.log("connection retry");
          this.queryNetworkJob();
        }, 300);
        return;
      }
      this.handleDisconnected(connectionState.error);
    }

    if (connectionState.state == MISSConnectState.MISS_Connection_Connected) { // onconnected 发送video-start
      this.loadingRetryTimer = new Date().getTime();
      if (!this.isConnecting) {
        return;
      }
      // this._sendDirectionCmd(DirectionViewConstant.CMD_GET);
      this.isConnecting = false;
      this.startVideoRetry = false;
      console.log("start send video start");
      // this._realStartVideo();

      TrackConnectionHelper.onConnected();

    }
    if (connectionState.state == MISSConnectState.MISS_Connection_Connecting) {
      this.isConnecting = true;
    }
    if (connectionState.state >= MISSConnectState.MISS_Connection_Connected) {
      // Service.miotcamera.bindRDTDataReceiveCallback(kRDTDataReceiveCallBackName);
      // this.setState({ showErrorView: false });
      // Service.miotcamera.bindBPSReceiveCallback(kBpsDataReceiveCallbackName);
    }

    if (connectionState.state >= MISSConnectState.MISS_Connection_ReceivedFirstFrame) {

      this.connRetry = 2;
      this.hasFirstFrame = true;
      if (this.showPlayToolBarTimer) {
        clearTimeout(this.showPlayToolBarTimer);
        this.showPlayToolBarTimer = null;
      }
    }
    this.setState({
      pstate: connectionState.state,
      error: connectionState.error
    });
  };

  handleDisconnected(errorCode) { // 已经断开了连接。
    console.log("disconnected");
    let errorStr = ((errorCode == 36 || errorCode == MISSError.MISS_ERR_MAX_SESSION) && VersionUtil.judgeIsMiss(Device)) ? LocalizedStrings["max_client_exceed"] : (errorCode == -6 && !VersionUtil.judgeIsMiss(Device) ? LocalizedStrings["max_client_exceed"] : `${ LocalizedStrings["camera_connect_error"] } ${ errorCode }, ${ LocalizedStrings["camera_connect_retry"] }`);
    this.setState({
      showPlayToolBar: false,
      isRecording: false,
      isCalling: false,
      showLoadingView: false,
      showPauseView: false,
      showPoweroffView: false,
      showErrorView: true,
      errTextString: errorStr
    });
    this.isClickCall = false;
  }

  _p2pCommandHandler = ({ command, data }) => {
    // 扩展程序注册命令回复回调，command为返回的命令号值，data 为P2P命令的返回数据。
    if (command == MISSCommand.MISS_CMD_SPEAKER_START_RESP) {
      if (this.forceSleep || this.state.showErrorView) {
        return;// 已经休眠或者显示了错误文案，就不打开麦克风
      }
      this.isClickCall = false;
      this.startSpeakerTime = new Date().getTime();
      console.log(' receive start speaker');
      let ba = base64js.toByteArray(data);

      if (ba.length > 0) {
        console.log('receive start speaker 0');
        console.log(ba[0]);
        if (Platform.OS === 'android') {
          if (ba[0] == 48) {
            console.log("start call in android");
            // this.isAudioMuteTmp = this.state.isMute;
            if (this.cameraGLView != null && !this.destroyed) {
              this.cameraGLView.startAudioRecord();
            }
            console.log("this.iscalling = true");
            this._toggleAudio(false);
            this.setState({ isCalling: true, showOneKeyCallDialog: false });
            AlarmUtil.putOneKeyCallStatus(2).then((res) => {
              LogUtil.logOnAll("putOneKeyCallStatus(2)", JSON.stringify(res));
            }).catch((err) => {
              LogUtil.logOnAll("putOneKeyCallStatus(2) err", JSON.stringify(err));
            });
            return;
          }
        } else {
          if (ba[0] == 0) {
            // this.isAudioMuteTmp = this.state.isMute;
            this._toggleAudio(false);
            this.callTimeout = setTimeout(() => {
              if (this.cameraGLView != null && !this.destroyed) {
                this.cameraGLView.startAudioRecord();
              }
            }, 800);// temp solution for bug MIIO-42838
            this.setState({ isCalling: true, showOneKeyCallDialog: false });
            AlarmUtil.putOneKeyCallStatus(2).then((res) => {
              LogUtil.logOnAll("putOneKeyCallStatus(2)", JSON.stringify(res));
            }).catch((err) => {
              LogUtil.logOnAll("putOneKeyCallStatus(2) err", JSON.stringify(err));
            });
            console.log("this.iscalling = true");
            return;
          }
        }
      }
      LogUtil.logOnAll("speak_failed because =", data);
      Toast.fail("speak_failed");
    } else if (command == MISSCommand.MISS_CMD_MOTOR_RESP) {
      console.log("received p2p angle resp");
      console.log(data); // {"ret":0,"angle":12,"elevation":1}
      if (this.isSendingRdtCmd) {
        LogUtil.logOnAll(TAG, "查询电机角度命令返回了：" + JSON.stringify(data));
      }
      try {
        if (typeof (data) == 'string') {
          data = JSON.parse(data);
        }
        this.angleData = data;

        let angleValue = Number(data.angle);
        let elevationValue = Number(data.elevation);
        let result = Number(data.ret);

        if (typeof (angleValue) == 'number' && typeof (elevationValue) == 'number') {
          if (this.ctrlCurrentLocation[0] != 0) {
            console.log(`preSetPosition update img current:${ angleValue } - ${ elevationValue } == h-v : ${ this.ctrlCurrentLocation[1] } - ${ this.ctrlCurrentLocation[2] }`);
            if ((angleValue > this.ctrlCurrentLocation[1] - 2 && angleValue < this.ctrlCurrentLocation[1] + 2) &&
              (elevationValue > this.ctrlCurrentLocation[2] - 2 && elevationValue < this.ctrlCurrentLocation[2] + 2)) {
              console.log(`preSetPosition update img h-v : ${ this.ctrlCurrentLocation[1] } - ${ this.ctrlCurrentLocation[2] }`);
              let imgPath = `${ this.preSetPositionImg }${ this.ctrlCurrentLocation[0] }.jpg`;
              this.delayUpdatePreSetImg && clearTimeout(this.delayUpdatePreSetImg);
              this.delayUpdatePreSetImg = setTimeout(() => {
                this._updatePreSetPositionImg(imgPath, this.ctrlCurrentLocation[0]);
                this.ctrlCurrentLocation[0] = 0;
              }, 2000);
            }
          }
          // if (this.angleViewTimeout) {
          //   clearTimeout(this.angleViewTimeout)
          //   this.angleViewTimeout = null
          // }
          // this.angleViewTimeout = setTimeout(() => {
          //   this.setState({ showCameraAngleView: false, angleViewShowScale: false });
          // }, 3000);
          // this.setState({showCameraAngleView: true, angleViewShowScale: false, angle: angleValue, elevation: elevationValue})
          // angle : left -> right 101 -> 1, 转换为 0 ~ 100
          // elevation : top -> bottom 101 -> 1, 转换为 0 ~ 100
          if (angleValue < 0 || angleValue > 101 || elevationValue < 0 || elevationValue > 101) {
            Service.smarthome.reportLog(Device.model, "illegal angle or elevation value:" + angleValue + " " + elevationValue);
            return;
          }
          if (this.isSendingRdtCmd) {// c01全景绘制后，获取电机方向改到这里了
            this.isSendingRdtCmd = false;
            this.setState({ angle: angleValue, elevation: elevationValue });
            if (this.showPanoAfterReceivedRotateAngle) {
              this.setState({ panoViewStatus: 3 });
              this.showPanoAfterReceivedRotateAngle = false;
              if (this.showPanoToastAfterReceivedRotateAngle) {
                Toast.success("pano_view_success");
                this.showPanoToastAfterReceivedRotateAngle = false;
              }
            }
            // if(this.state.showPanoView){
            //   this.setState({panoViewStatus:3, angle: positionX, elevation: positionY})
            // }
            this.isSendingRdtCmd = false;// end
            return;
          }
          if (Date.now() - this.lastLogTime > 1000) {
            LogUtil.logOnAll("receive ptz direction log: angleValue:" + angleValue + " elevationValue:" + elevationValue);
          }
          this.lastLogTime = Date.now();

          this.setState({ angleViewShowScale: false, angle: angleValue, elevation: elevationValue });
          if (result < 0) {
            if ((result == DirectionViewConstant.CMD_CHECK_END) && !this.state.showPoweroffView) {
              Toast.success("camera_celibrating");
            } else if (result <= -1 && result >= -4 && !this.state.showPoweroffView) {
              Toast.fail("camera_direction_end");
            }
          } else {
          }
        }

      } catch (exception) {
        console.log(`parse angle data error: ${ exception }`);
      }

    } else if (command == MISSCommand.MISS_CMD_CRUISE_STATE_RESP) {
      LogUtil.logOnAll("received cruise state resp", data);
      if (typeof (data) == 'string') {
        data = JSON.parse(data);
      }
      if (data.value == 1) {
        this.setState({ isCruising: true });
      } else {
        this.setState({ isCruising: false });
      }
    } else if (command == MISSCommand.MISS_CMD_CALL_STATUS_RESP) {
      LogUtil.logOnAll("received CALL_STATUS_RESP", data);
      if (typeof (data) == 'string') {
        data = JSON.parse(data);
      }
      if (data.type == "hang_up") {
        LogUtil.logOnAll("received CALL_STATUS_RESP do stopCall");
        this._stopCall();
      } else {
        LogUtil.logOnAll("received CALL_STATUS_RESP type=", data.type);
      }
    } else if (command == MISSCommand_ECO.MISS_CMD_NETWORK_STATUS) {
      let ba = base64js.toByteArray(data);
      LogUtil.logOnAll(`receive MISS_CMD_NETWORK_STATUS:${ command } data:${ ba }`);
      if (ba[0] === 0) {
        // this._getTargetPush(false, true);
      }
    } else {
      // console.log(`receive other command:${ command } data:${ JSON.stringify(data) }`);
    }
  };

  _networkChangeHandler = (networkState) => {
    console.log("+++++++++++=this is first +++++++");
    if (this.isFirstEnter) { // 放到后台的包  刚进来的时候
      return;
    }
    if (this.currentNetworkState == networkState) {
      return;
    }
    if (this.state.showPoweroffView) {
      return;// 已经休眠了，不需要走下面的逻辑
    }
    // 更新，增加重连
    this.connRetry = 2;//避免重连。
    Service.smarthome.reportLog(Device.model, "处理网络变化" + networkState);
    this.currentNetworkState = networkState;
    clearTimeout(this.showNetworkDisconnectTimeout);
    if (networkState == 0 || networkState == -1) { // 网络断开了连接 showError?
      Service.smarthome.reportLog(Device.model, "网络异常" + networkState);
      // CameraPlayer.getInstance().disconnectToDevice();// 去往其他注册了网络监听的页面，就不会走到这里了，如果走到这里，这里必须先执行断开的操作
      this.showNetworkDisconnectTimeout = setTimeout(() => {
        this.handleDisconnected(MISSError.MISS_ERR_CLOSE_BY_LOCAL);// 刷新UI，避免出现异常。
      }, 1300);
      return;
    }
    if (this.isPageForeGround) { // 有网络来了  发起重连吧
      this.setState({ showErrorView: false });
      this._clearTimer(this.reconnectTimeout);
      this.reconnectTimeout = setTimeout(() => {
        Service.smarthome.reportLog(Device.model, `on network changed:${ networkState }`);
        this.queryNetworkJob();
      }, 500);// 过一会再查询 ，那个查询网络类型的api有问题。
    }
  };

  _powerOffHandler = (isPowerOn) => {
    // 远端的下发的状态与本地的状态不一致 就回走到这里
    if (!isPowerOn) { // 变成了休眠
      CameraPlayer.getInstance().setPowerState(false);
      this.setState({ isSleep: true });
    } else { // 唤醒了
      this.setState({ isSleep: false }, () => {
        this.checkRemoteDialogNeedHide();
      });
      Service.smarthome.reportLog(Device.model, "on wake up");
      CameraPlayer.getInstance().setPowerState(true);
      // 需要连接下
      this.queryNetworkJob();
    }
  };

  _remoteViewStateCallback = (value) => {
    console.log("=========remote view is change==========",value);
    this.setState({ liveSwitch: value }, () => {
      if (value) {
        this.checkRemoteDialogNeedHide();
      }
    });

  }

  checkRemoteDialogNeedHide() {
    if (!this.state.showNotAllowDialog) {
      return;
    }
    if (this.state.liveSwitch && !this.state.isSleep) {
      this.setState({ showNotAllowDialog: false });
    }
  }

  queryNetworkJob() {
    console.log(TAG, "queryNetworkJob", "is do");

    if (!this.props.navigation.isFocused()) {
      console.log(TAG, "queryNetworkJob", "not focus");
      return;
    }
    if (!this.isPrivacyAuthed) {
      return;
    }
    Service.smarthome.reportLog(Device.model, "start query network");
    TrackConnectionHelper.onNetworkCheck();
    CameraPlayer.getInstance().queryShouldPauseOn4G()
      .then(({ state, pauseOnCellular }) => {
        Service.smarthome.reportLog(Device.model, "查询网络结果：当前网络类型" + state + " pauseOnCellular" + pauseOnCellular);
        if (state == "NONE" || state == "UNKNOWN") {
          this.currentNetworkState = 0;
          this.setState({ showErrorView: true });
          return;
        }
        TrackConnectionHelper.onNetworkChecked();
        Service.smarthome.reportLog(Device.model, "start query network success:" + state);
        // 其他网络条件 走连接的步骤吧
        this._startConnect();// 开始连接
      })
      .catch((err) => { // 获取网络状态失败 也直接走开始连接的流程
        TrackConnectionHelper.onNetworkChecked();
        console.log(err);
        Service.smarthome.reportLog(Device.model, `start query network error${ JSON.stringify(err) }`);
        this._startConnect();// 开始连接
      });
  }

  _startConnect() {
    if (!this.props.navigation.isFocused()) {
      return;
    }
    // 开始连接
    if (CameraPlayer.getInstance().isConnected()) {
      LogUtil.logOnAll(TAG, "already connected, start send video start");
      if (this.state.pstate < 2) {
        this.setState({ pstate: CameraPlayer.getInstance().connectionState.state });
      }
      return;
    }
    LogUtil.logOnAll(TAG, "not connected, try start connect");
    this.setState({ pstate: 1, error: 1 });
    TrackConnectionHelper.startConnect();
    this.isConnecting = true;
    CameraPlayer.getInstance().startConnect();
    if (!Device.isOnline) {
      this.setState({ showErrorView: true, errTextString: LocalizedStrings['device_offline'] });
      OfflineHelper.getLastOnlineTime()
        .then((result) => {
          this.setState({ lastOfflineTime: `${ LocalizedStrings['offline_time_str'] }: ${ result }` });
        })
        .catch((rr) => {
        });
    }
  }


  scrollViewScroll = (event) => {
    const y = event.nativeEvent.contentOffset.y;
    // let flag = y > 28;
    let flag = y > this.titleHeight;
    if (this.showTitle == flag) {
      return;
    }
    if (flag) {
      this.showTitle = true;

      this.props.navigation.setParams({ title: Device.name ? (Device.name.length > 15 ? `${ Device.name.substr(0, 15) }...` : Device.name) : "" });
    } else {
      this.showTitle = false;
      this.props.navigation.setParams({ title: "" });
    }
  };

  _startAllStorageWithPermissionCheck() {
    if (Platform.OS === "android") {
      this.isCheckingPermission = true;
      PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE, null)
        .then((granted) => {
          this.isCheckingPermission = false;
          console.log("granted", granted);
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            this._showAllStorage();
          } else if (granted === PermissionsAndroid.RESULTS.NEVER_ASK_AGAIN) {
            // Toast.success("camera_no_write_permission");
            this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
          } else {
            Toast.success("camera_no_write_permission");
          }
        }).catch((error) => {
        this.isCheckingPermission = false;
        Toast.success("action_failed");
      });
    } else {
      // no ios's photos const use hardcode
      System.permission.request("photos").then((res) => {
        this._showAllStorage();
      }).catch((error) => {
        // Toast.success("camera_no_write_permission");
        this.setState({ showPermissionDialog: true, permissionRequestState: 0 });
      });

    }
    TrackUtil.reportClickEvent('Camera_ScreenShot_ClickNum');
  }

  _showAllStorage() {
    TrackUtil.reportClickEvent("Camera_Storage_ClickNum"); // Camera_Storage_ClickNum

    let index = this.showSdcardPageAllStorage ? 2 : 0;
    if (this.showSdcardPageAllStorage) {
      if (!(this.sdcardCode == 0 || this.sdcardCode == 2) || this.isVip || this.inWindow) {
        // 不支持sdcard or 无卡，就不需要再跳记忆的sdcard页面了。
        index = 0;
      }
    } else { // 跳云存或者初次进来。
      index = 0;
    }

    let navigationParam = { initPageIndex: index, vip: this.isVip, isSupportCloud: true };
    LogUtil.logOnAll("AllStorage UI s param:", navigationParam, this.sdcardCode, " isConnected:", CameraPlayer.getInstance().isConnected());
    // 进入回看前 清空一次SdFileManager里的列表。避免缓存的问题
    // let currentMls = new Date().getTime();
    // if (SdcardEventLoader.getInstance().lastRequestSDListTime && currentMls - SdcardEventLoader.getInstance().lastRequestSDListTime > 30000) {
    //   console.log("+++++++++++++clear it++++++++++++++++");
    //   SdcardEventLoader.getInstance().clearSdcardFileList();
    // }
    SdcardEventLoader.getInstance().clearSdcardFileList();
    this.props.navigation.navigate("AllStorage", navigationParam);
  }

  render() {
    return (
      <ScrollView
        // scrollEnabled={ true }
        showsVerticalScrollIndicator={ false }
        onScroll={ this.scrollViewScroll }
        scrollEventThrottle={ 10 }
        style={ { backgroundColor: this.darkMode ? '#ffffff' : '#F6F6F6' } }>
        <View style={ { marginLeft: 20, marginRight: 20, marginTop: 10, marginBottom: 15 } }>
          <Image style={ { width: 114, height: 114 } } source={ { uri: Device.iconURL } }/>
          <Text style={ { fontSize: 30, color: 'rgba(0, 0, 0, 0.8)', fontWeight: "300" } }>{ Device.name ? Device.name : "" }</Text>
        </View>

        { this._renderBuyCloudTips() }
        { this._renderDisconnect() }
        { this._renderLiveItem() }
        { this._renderTemperatureItem() }
        { this._renderAlarmView() }
        { this._renderOptionItem() }
        { this._renderNotAllowDialog() }
        { this._renderHighTemperatureDialog() }
        { this._renderBindAbnormalDialog() }
        <DeviceOfflineDialog
          ref="powerOfflineDialog"
        />
        <NoNetworkDialog
          ref="noNetworkDialog"
        />
        {this._renderLogUploaderDialog()}
        {this._renderPermissionDialog()}

      </ScrollView>
    );
  }

  // 这里是弹出对话框
  _renderPermissionDialog() {
    if (!this.state.showPermissionDialog) {
      return null;
    }
    // status == 0 存储卡/相册
    // status == 1 麦克风
    // status == 2 摄像头
    let message = "";
    if (this.state.permissionRequestState == 0) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", Platform.OS === "android" ? LocalizedStrings["permission_name_storage"] : LocalizedStrings["s_photo_album"]);
    } else if (this.state.permissionRequestState == 1) {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_microphone"]);
    } else {
      message = LocalizedStrings["permission_tips_denied_msg"].replace("%s", LocalizedStrings["permission_name_camera"]);
    }
    return (
      // <AbstractDialog

      <MessageDialog
        title={LocalizedStrings["tips"]}
        message={message}
        modalStyle={{ width: "100%" }}
        messageStyle={{
          fontSize: 14,
        }}
        buttons={[
          {
            text: LocalizedStrings["action_cancle"],
            callback: () => {
              this.setState({ showPermissionDialog: false });
            }
          },
          {
            text: LocalizedStrings["setting"],
            callback: () => {
              Host.ui.openTerminalDeviceSettingPage(1);
              this.setState({ showPermissionDialog: false });
            }
          }
        ]}
        onDismiss={() => {
          this.setState({ showPermissionDialog: false });
        }}
        visible={this.state.showPermissionDialog} />
    );

  }

  _renderLogUploaderDialog() {
    return (
      <MessageDialog
        message={this.state.logDialogContent}
        cancelable={false}
        buttons={[
          {
            text: LocalizedStrings['action_cancle'],
            callback: () => {
              feedbackLogUploaderStatus(2);
              this.setState({ showLogDialog: false });
            }
          },
          {
            text: LocalizedStrings['action_confirm'],
            callback: () => {
              feedbackLogUploaderStatus(1);
              this.setState({ showLogDialog: false });
            }
          }

        ]}
        onDismiss={() => {
          console.log('onDismiss');
          this.setState({ showLogDialog: false });
        }}
        visible={this.state.showLogDialog} />
    );
  }

  _renderNotAllowDialog() {
    let msg = LocalizedStrings['car_not_allow_to_view'];
    if (this.state.error == 36 || this.state.error == MISSError.MISS_ERR_MAX_SESSION) {
      msg = LocalizedStrings['max_client_exceed'];
    }
    if (this.state.isSleep) {
      msg = LocalizedStrings['car_camera_off'];
    }
    return (
      <MessageDialog
        visible={ this.state.showNotAllowDialog }
        // title={ LocalizedStrings['tips'] }
        messageStyle={{ textAlign: 'center' }}
        message={ msg }
        buttons={ [
          {
            text: LocalizedStrings["offline_divice_ok"],
            colorType: "grayLayerBlack",
            callback: (_) => {
              this.setState({ showNotAllowDialog: false });
            }
          }
        ] }
        onDismiss={ () => {
          this.setState({ showNotAllowDialog: false });
        } }

      />
    );
  }

  _renderHighTemperatureDialog() {
    return (
      <MessageDialog
        visible={ this.state.showHighTemperatureDialog }
        title={ LocalizedStrings['high_temperature_tips'] }
        message={ LocalizedStrings['high_temperature_msg'] }
        buttons={ [
          {
            text: LocalizedStrings["offline_divice_ok"],
            colorType: "grayLayerBlack",
            callback: (_) => {
              this.setState({ showHighTemperatureDialog: false });
            }
          }
        ] }
        onDismiss={ () => {
          this.setState({ showHighTemperatureDialog: false });
        } }

      />
    );
  }


  _renderBindAbnormalDialog() {
    return (
      <AbstractDialog
        visible={this.state.showBindAbnormalDialog}
        showTitle={false}
        showSubtitle={false}
        onDismiss={() => {
          this.setState({ showBindAbnormalDialog: false });
        }}
        useNewTheme
        canDismiss={true}
        buttons={[
          {
            text: LocalizedStrings["go_homepage"],
            callback: () => {
              this.setState({ showBindAbnormalDialog: false });
              Package.exit();
            }
          },
          {
            text: LocalizedStrings['reconnect_button_text'],
            callback: () => {
              this.setState({ showBindAbnormalDialog: false });
              Host.ui.openResetAndConnectDevicePage();
            }
          }
        ]}>
        <View style={{ flexDirection: 'column', alignItems: 'center' }}>
          <Image style={ { width: 114, height: 114, marginTop: 20 } } source={ { uri: Device.iconURL } }/>
          <Text style={{ paddingHorizontal: 38, marginBottom: 14, fontSize: 16, color: '#000000CC', textAlign:  'center' }}>{LocalizedStrings['msg_bind_content']}</Text>
        </View>
      </AbstractDialog>
    );
  }


  _renderBuyCloudTips() {
    let isDark = this.state.darkMode;
    let tipsText, tipsUrl, backgroundColor, channel, tipsTextColor, backIcon;

    if (!Device.isOwner || !this.state.showCloudVipBuyTip) {
      return null;
    }
    backgroundColor = isDark ? "#25A9AF32" : '#32BAC019';
    channel = "videodetails_button";
    tipsText = this.isEuropeServer ? LocalizedStrings['eu_camera.alarm.cloud.tip.fullvideo.not.vip_new'] : LocalizedStrings['cloud_advise_buy'];
    if (this.freeHomSurStatus && (this.freeHomeSurExpireTime == -2 || this.freeHomeSurExpireTime == -1)) {
      tipsText = this.isEuropeServer ? LocalizedStrings['eu_camera.alarm.cloud.tip.fullvideo.not.vip'] : LocalizedStrings['camera.alarm.cloud.tip.fullvideo.not.vip'];
    }
    tipsTextColor = isDark ? "#25A9AF" : "#32BAC0";

    backIcon = require('../../Resources/Images/coud_banner_player_tip_expired.png');
    if (this.isVip && this.cloudVipWillEndDays > 0 && this.cloudVipWillEndDays <= 7) { //云存将要过期，或者云存过期且在续费窗口期
      tipsText = this.isEuropeServer ? LocalizedStrings['eu_c_cloudvip_end_tip'] : LocalizedStrings['cloud_advise_renewal'];
      tipsTextColor = isDark ? "#DB8E0D" : "#F5A623";
      channel = "videodetails_button_expire";
      backgroundColor = isDark ? "#DB8E0D32" : '#F5A62319';
      backIcon = require('../../Resources/Images/coud_banner_player_tip_inwindow.png');
    }


    let style = {
      flexDirection: "row",
      flexWrap: 'nowrap',
      alignItems: "center",
      justifyContent: "space-between",
      backgroundColor: backgroundColor,
      paddingLeft: 16,
      marginHorizontal: 12,
      paddingRight: 20,
      height: 44,
      borderRadius: 16
    };
    let mBgStyleBase = { marginTop: 0, marginBottom: 0, alignItems: "center", justifyContent: "center", flex: 1 };
    let mBgStyle = [mBgStyleBase, { paddingBottom: 0, paddingHorizontal: 12, backgroundColor: "transparent" }];
    return (
      <View style={ mBgStyle }>
        <TouchableOpacity
          style={ style }
          onPress={ () => {
            if (!Device.isOwner) {
              Toast.success("share_user_permission_hint");
              return;
            }

            Service.miotcamera.showCloudStorage(true, true, Device.deviceID, tipsUrl ? tipsUrl : "", true, { channel: channel });
            CameraConfig.isToUpdateVipStatue = true;
          }
          }
        >
          <Text
            numberOfLines={ 3 }
            style={ [{
              fontSize: kIsCN ? 13 : 11, paddingRight: 0, color: tipsTextColor,
              textAlign: "left", textAlignVertical: 'center', width: '100%'
            }] }>{ tipsText }</Text>
          <ImageButton
            style={ { width: 22, height: 22 } }
            source={ backIcon }
          />
        </TouchableOpacity>
      </View>
    );
  }

  /**
   * 设备是否离线，离线时显示
   * @private
   */
  _renderDisconnect() {
    if (Device.isOnline) {
      return null;
    }
    return (
      <View key={ 'offline_0' } style={ {
        marginHorizontal: 12,
        marginTop: 12,
        flexDirection: "row",
        alignItems: "center",
        height: 70,
        backgroundColor: this.darkMode ? "#EEEEEE" : "#ffffff",
        borderRadius: 16
      } }>
        <Image
          style={ { width: 40, height: 40, marginLeft: 20 } }
          source={ Util.isDark() ? require('../../Resources/Images/icon_disconnect.png') : require('../../Resources/Images/icon_disconnect.png') }
        />

        <Text
          style={ { fontSize: 17, fontWeight: '500', color: 'rgba(245, 166, 35, 1)', marginLeft: 8 } }>{ LocalizedStrings['not_connected'] }</Text>
      </View>
    );

  }

  _renderLiveItem() {
    let opacity = (this.state.isSleep
      // || this.state.workMode === 1
      || this.state.bindInfo == 0
      || !this.state.liveSwitch
      || this.state.pstate === MISSConnectState.MISS_Connection_Disconnected || this.state.pstate === MISSConnectState.MISS_Connection_Connecting
      || this.state.temperatureState === 1
      || !Device.isOnline
      || !this.state.hasNetWork) ? 0.3 : 1;

    return (
      <View key={ 'live_0' } style={ {
        // width: "97%",
        marginHorizontal: 12,
        marginTop: 12,
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: this.darkMode ? "#EEEEEE" : "#ffffff",
        borderRadius: 16
      } }>
        <TouchableOpacity
          disabled={ this.isLocalMode }
          style={ [styles.panelOptionItemLayout, { opacity: this.isLocalMode ? 0.2 : 1 }] }
          onPress={ () => {
            console.log(TAG, "camera state", this.state.isSleep, this.state.workMode, this.state.liveSwitch, this.state.pstate);
            // this.props.navigation.navigate("LiveVideoPageV2");
            if (this.state.temperatureState === 1) {
              Toast.success("high_temperature_cannot_use");
              return;
            }
            if (CameraConfig.debugSpecialSupport()) {
              this.props.navigation.navigate("LiveVideoPageV2");
            } else {
              if (!this.state.hasNetWork) {
                this.refs.noNetworkDialog.show();
              } else if (!Device.isOnline){
                this.refs.powerOfflineDialog.show();
              } else if (this.state.isSleep) {
                this.setState({ showNotAllowDialog: true });
              } else if (this.state.bindInfo == 0) {
                this.setState({ showBindAbnormalDialog: true });
              } else if (this.state.pstate === MISSConnectState.MISS_Connection_Disconnected || this.state.pstate === MISSConnectState.MISS_Connection_Connecting) {
                // this.refs.powerOfflineDialog.show();
                if (this.state.error == 36 || this.state.error == MISSError.MISS_ERR_MAX_SESSION) {
                  // 超过最大连接数
                  this.setState({ showNotAllowDialog: true });
                } else {
                  this.refs.noNetworkDialog.show();
                }

              } else if (!this.state.liveSwitch || this.state.isSleep) {
                // this.state.workMode === 1 ||
                this.setState({ showNotAllowDialog: true });
              } else {
                // @20250224 去除NOT_INITIAL状态，进入页面直接发起通话申请
                LogUtil.logOnAll(TAG, "start request live");
                this.props.navigation.navigate("LiveStatePage", { type: CALL_CAR_STATE.NOT_INITIAL });
              }
            }

            // this.props.navigation.navigate("LiveStatePage", { type: CALL_CAR_STATE.NOT_INITIAL });

          } }>
          <Image
            style={ { width: 28, height: 28, position: "relative" } }
            source={ Util.isDark() ? require('../../Resources/Images/miHome/icon_home_live_dark.png') : require('../../Resources/Images/miHome/icon_home_live.png') }
          />

          <View style={ {
            display: "flex",
            height: "100%",
            flex: 1,
            position: "relative",
            flexDirection: "column",
            justifyContent: "center",
            width: kWindowWidth - (7 + 40),
            paddingRight: 20
          } }>
            <View
              style={ { display: "flex", flexDirection: "row", width: "100%" } }
            >
              <Text numberOfLines={ 2 } style={ {
                marginLeft: 15,
                marginTop: 0,
                fontSize: kIsCN ? 16 : 14,
                color: "#000000",
                fontWeight: "700"
              } }>{ LocalizedStrings['live_view'] }</Text>
            </View>

            <Text
              style={ { marginLeft: 15, marginTop: 4, fontSize: kIsCN ? 13 : 11, color: "#7F7F7F" } }
              numberOfLines={ 2 }
              ellipsizeMode={ "tail" }>
              { LocalizedStrings['live_view_subtitle'] }
            </Text>
          </View>
          <View style={ {
            backgroundColor: this.darkMode ? '#25A9AF32' : '#32BAC019',
            borderRadius: 55, minHeight: 26,
            justifyContent: "center", alignItems: "center", paddingVertical: 9, paddingHorizontal: 20,
            opacity: opacity
          } }>
            <Text
              style={ {
                fontWeight: "500",
                fontSize: kIsCN ? 14 : 12,
                color: this.darkMode ? "#25A9AF" : "#32BAC0"
              } }>
              { LocalizedStrings["go_in"] }
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  }

  _renderTemperatureItem() {
    if (this.state.temperatureState !== 1) {
      return null;
    }
    return (
      <View key={ 'temperature_0' } style={ {
        marginHorizontal: 12,
        marginTop: 12,
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: this.darkMode ? "#EEEEEE" : "#ffffff",
        borderRadius: 16
      } }>
        <View
          style={ {
            display: "flex",
            width: "100%",
            minHeight: 50,
            flexDirection: "row",
            alignItems: "center",
            marginTop: 10,
            marginBottom: 10,
            paddingLeft: 20,
            paddingRight: 20
          } }
        >
          <Image
            style={ { width: 28, height: 28, position: "relative" } }
            source={ require("../../Resources/Images/car/car_warning.png") }
          />

          <View style={ {
            display: "flex",
            flex: 1,
            position: "relative",
            flexDirection: "column",
            justifyContent: "center",
            width: kWindowWidth -  40,
            paddingRight: 20
          } }>
              <Text numberOfLines={ 4 } style={ {
                marginLeft: 15,
                marginTop: 0,
                fontSize: kIsCN ? 16 : 14,
                color: "#000000",
                fontWeight: "700"
              } }>{ LocalizedStrings['high_temperature_protect_open'] } </Text>
          </View>
        </View>
      </View>
    );
  }

  _renderOptionItem() {
    let backIcon = Util.isDark() ? require('../../Resources/Images/icon_right_anchor_black_dark_mode.png') : require('../../Resources/Images/icon_right_anchor_black.png');
    let item = {
      source: Util.isDark() ? require('../../Resources/Images/miHome/icon_home_storage_dark.png') : require('../../Resources/Images/miHome/icon_home_storage.png'),
      title: LocalizedStrings['s_storage_setting'],
      subTitle: LocalizedStrings['storage_sub_title'],
      onPress: () => {
        this._startAllStorageWithPermissionCheck();
      }
    };
    return (
      <View key={ 'storage_0' } style={ {
        marginHorizontal: 12,
        marginTop: 12,
        marginBottom: 12,
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: this.darkMode ? "#EEEEEE" : "#ffffff",
        borderRadius: 16
      } }>
        <TouchableOpacity
          disabled={ this.isLocalMode }
          style={ [styles.panelOptionItemLayout, { opacity: this.isLocalMode ? 0.2 : 1 }] }
          onPress={ item.onPress }
          accessibilityLabel={ item.accessibilityLabel }
        >
          <Image
            style={ { width: 28, height: 28, position: "relative" } }
            source={ item.source }
          />

          <View style={ {
            display: "flex",
            height: "100%",
            flex: 1,
            position: "relative",
            flexDirection: "column",
            justifyContent: "center",
            width: kWindowWidth - (7 + 40),
            paddingRight: 20
          } }>
            <View
              style={ { display: "flex", flexDirection: "row", width: "100%" } }
            >
              <Text numberOfLines={ 2 } style={ {
                marginLeft: 15,
                marginTop: 0,
                fontSize: kIsCN ? 16 : 14,
                color: "#000000",
                fontWeight: "700"
              } }>{ item.title } </Text>
            </View>

            <Text
              style={ { marginLeft: 15, marginTop: 4, fontSize: kIsCN ? 13 : 11, color: "#7F7F7F" } }
              numberOfLines={ 2 }
              ellipsizeMode={ "tail" }>
              { item.subTitle }
            </Text>
          </View>

          <ImageButton
            style={ { width: 7, height: 12, position: "relative" } }
            source={ backIcon }/>
        </TouchableOpacity>
      </View>
    );
  }

  _renderAlarmView() {

    let fixContolViewStyle = {
      display: "flex",
      flex: 1,
      zIndex: 2,
      marginTop: 12,
      // margin: 8,
      // height: fixControlBarHeight,
      // marginHorizontal: 12,
      marginLeft: 12,
      width: kWindowWidth - 12 * 2,
      flexDirection: "column",
      alignItems: "center",
      // paddingLeft: 25,
      // paddingRight: 25,
      justifyContent: "center",
      backgroundColor: Util.isDark() ? "xm#1a1a1a" : "#ffffff",
      // paddingStart: 30, paddingEnd: 30,
      borderRadius: 16
    };

    if (this.darkMode) {
      fixContolViewStyle.backgroundColor = "#EEEEEE";
      // fixContolViewStyle.backgroundColor = "xm#000000";
    }
    let backIcon = Util.isDark() ? require('../../Resources/Images/icon_right_anchor_black_dark_mode.png') : require('../../Resources/Images/icon_right_anchor_black.png');
    let count = this.todayEventSize > 0 ? this.todayEventSize : 0;
    // let eventCountStr = LocalizedStrings["event_size_str"].replace("%d", count);
    // if (count <= 0) {
    //   eventCountStr = LocalizedStrings["event_empty_str"];
    // }

    return (
      <View style={ fixContolViewStyle } key={ "alarm_0" }>
        <TouchableOpacity
          style={ { width: "100%", paddingLeft: 25, paddingRight: 25 } }
          onPress={ () => {
            this._handleSdcardPtzClick();
          } }>
          <View style={ {
            width: "100%",
            display: "flex",
            flexDirection: "row",
            justifyContent: "space-between",
            paddingTop: 20,
            paddingBottom: 10
          } }>
            <View style={ { flexDirection: "row", alignItems: "center" } }>
              <Text style={ {
                fontSize: Host.locale.language.includes("zh") ? 17 : 15,
                fontWeight: "700"
              } }>{ LocalizedStrings["car_video"] }</Text>
            </View>
            <Image style={ { width: 7, height: 12, position: "relative" } } source={ backIcon }></Image>
          </View>
        </TouchableOpacity>
        { this.todayEvents && this.todayEvents.length > 0 ? this._renderAlarmView01() : this._renderAlarmView00() }
      </View>
    );
  }

  _renderAlarmView01() {
    return (
      <View style={ { width: "100%", paddingTop: 10, paddingBottom: 20, paddingHorizontal: 25 } }>
        {
          this.todayEvents && this.todayEvents.map((item, index) => {
            let imgStoreUrl = item.imgStoreUrl;
            let imgSource = imgStoreUrl != null ? { uri: `file://${ imgStoreUrl }` } : null;
            return <TouchableOpacity
              key={ item.createTime }
              onPress={ () => {
                this._handleSdcardPtzClick(item, "push");
              } }>
              <View style={ { flexDirection: "row", alignItems: "center" } }>
                <Text style={ {
                  width: 50,
                  fontSize: 15,
                  color: Util.isDark() ? "xm#FFFFFFE5" : "#333333",
                  marginRight: 5,
                  fontWeight: '700'
                } }>{ item.eventTime }</Text>
                <View style={ { alignItems: "center", justifyContent: "center", marginRight: 24, marginLeft: 10 } }>
                  <View style={ {
                    width: 1,
                    height: 30,
                    backgroundColor: index !== 0 ? Util.isDark() ? "xm#383838" : "#e8e8e8" : "#00000000"
                  } }></View>
                  <View
                    style={ { width: 5, height: 5, borderRadius: 5, backgroundColor: Util.isDark() ? "#666666" : "#D8D8D8" } }>
                  </View>
                  <View style={ {
                    width: 1,
                    height: 30,
                    backgroundColor: index !== this.todayEvents.length - 1 ? Util.isDark() ? "xm#383838" : "#e8e8e8" : "#00000000"
                  } }></View>
                </View>
                <Image style={ { width: 75, height: 45, borderRadius: 10, marginEnd: 14 } } source={ imgSource }></Image>
                <Text style={ {
                  fontSize: 15,
                  color: Util.isDark() ? "xm#FFFFFFE5" : "#333333",
                  fontWeight: '700',
                  maxWidth: kWindowWidth - 248
                } }>{ item.desc }</Text>
              </View></TouchableOpacity>;
          })
        }
      </View>
    );
  }

  _renderAlarmView00() {
    return (
      <View style={ { alignItems: "center", marginEnd: 20 } }>
        <Image
          style={ { width: 92, height: 60, marginTop: 40, marginBottom: 12 } }
          source={ Util.isDark() ? require("../../Resources/Images/icon_home_empty_d.webp") : require("../../Resources/Images/icon_home_empty.webp") }/>
        <Text style={ { marginBottom: 54 } }>{LocalizedStrings['no_car_event_video']}</Text>
      </View>
    );
  }

  _handleSdcardPtzClick(item = null, lstType = "", eventType = "Default") {
    // if (!this.canStepOut(true)) {
    //   return;
    // }

    // if (this.sdcardCode == 4) {
    //   Toast.success("formating_error");
    //   return;
    // }

    // if (VersionUtil.Model_Chuangmi_022 == Device.model) {
    //   let data = { sdcardGetSuccess: true, sdcardStatus: sdcardCode, isVip: this.isVip };
    //   Service.miotcamera.showPlaybackVideos(JSON.stringify(data));
    // } else {
    // let showSdcard = this.showSdcardPage;
    let showSdcard = true;
    if (this.sdcardCode == 3 || this.sdcardCode == 1 || this.sdcardCode == 4 || this.sdcardCode == 5 || !this.pickedFreeCloud) { // 卡异常，无卡，卡正在格式化，都跳cloud页面
      showSdcard = false;
    }

    let param = {
      sdcardCode: this.sdcardCode,
      isVip: this.isVip,
      isShowSdcardPage: showSdcard,
      isSupportCloud: this.mFirmwareSupportCloud && CameraConfig.isSupportCloud(),
      cloudSwich: this.getedDetectionSwitch && this.detectionSwitch
    };
    LogUtil.logOnAll("sdcard cloud timeline page: param==", JSON.stringify(param));
    // 进入回看前 清空一次SdFileManager里的列表。
    SdcardEventLoader.getInstance().clearSdcardFileList();
    console.log(param, 9999999999);
    // 添加这个原因是跳回看，播放器区域会一半黑一半显示
    if (this.cameraGLView != null && !this.isCheckingPermission) {
      this.cameraGLView.hidesSurfaceView();
    }
    this.props.navigation.navigate("SdcardCloudTimelinePageV2", Object.assign(param, { item, lstType, eventType }));// todo 待实现回看的代码
    // }
    // 显示事件类型时的埋点
    switch (eventType) {
      case 'BabyCry':
        // Toast.success("as_baby_cry")
        TrackUtil.reportClickEvent("Camera_Monitoring_BabyCrying_ClickNum");
        break;
      case 'PeopleCough':
        // Toast.success("cough_desc")
        TrackUtil.reportClickEvent("Camera_Monitoring_Coughing_ClickNum");
        break;
      case 'AI':
        // Toast.success("ai_desc")
        TrackUtil.reportClickEvent("Camera_Monitoring_Automation_ClickNum");
        break;
      case 'PeopleMotion':
        // Toast.success('people_move_desc')
        TrackUtil.reportClickEvent("Camera_Monitoring_PeopleDetected_ClickNum");
        break;
      case 'EmotionRecognition':
        // Toast.success('expression_desc')
        TrackUtil.reportClickEvent("Camera_Monitoring_ExpressionRecognition_ClickNum");
        break;
      case 'Face':
        // Toast.success('face_desc')
        TrackUtil.reportClickEvent("Camera_Monitoring_FaceDeteced_ClickNum");
        break;
      case 'FenceIn':
        // Toast.success('pass_in_desc')
        TrackUtil.reportClickEvent("Camera_Monitoring_LeaveTheFence_ClickNum");
        break;
      case 'FenceOut':
        // Toast.success('pass_out_desc')
        TrackUtil.reportClickEvent("Camera_Monitoring_EnterTheFence_ClickNum");
        break;
      case 'LouderSound':
        // Toast.success('loud_desc')
        TrackUtil.reportClickEvent("Camera_Monitoring_LargeSound_ClickNum");
        break;
      case 'ObjectMotion':
        // Toast.success("object_move_desc")
        TrackUtil.reportClickEvent("Camera_Monitoring_MotionDeteced_ClickNum");
        break;
      default:
        return;
    }
  }

}

const styles = StyleSheet.create({

  panelOptionItemLayout: {
    display: "flex",
    position: "relative",
    width: "100%",
    height: 50,
    flexDirection: "row",
    alignItems: "center",
    marginTop: 20,
    marginBottom: 20,
    paddingLeft: 20,
    paddingRight: 20
  },

  videoControl: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    height: 80
  }
});
