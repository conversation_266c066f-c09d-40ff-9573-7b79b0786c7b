import React from 'react';
import AIFaceSettingV2 from "./aicamera/AIFaceSettingV2";

class DummyPage extends React.Component {
  componentDidMount() {
  }
  componentWillUnmount() {
  }
  render() {
    return null;
  }
}

export default class RouteProxy {
  constructor(pName) {
    this.pageName = pName;
    this.isFirstTime = true;
    this.page = null;
  }

  getScreen() {
    if (this.isFirstTime) {
      // StackNavigator初始化路由的时候就会调一次getScreen(), 先返回一个DummyPage避免在没有调navigate的时候就加载页面
      this.isFirstTime = false;
      return DummyPage;
    }

    if (!this.page) {
      switch (this.pageName) {
        case 'Setting':
          this.page = require('./setting/Setting').default;
          break;
        case 'LiveStatePage':
          this.page = require('./live/LiveStatePage').default;
          break;
        case 'SDCardSetting':
          this.page = require('./setting/SDCardSetting').default;
          break;
        case 'CameraSetting':
          this.page = require('./setting/CameraSetting').default;
          break;
        case 'OneKeyCallPage':
          this.page = require('./aicamera/OneKeyCallPage').default;
          break;
        case 'OneKeyCallPageV2':
          this.page = require('./aicamera/OneKeyCallPageV2').default;
          break;
        case 'AudioVideoCallPage':
          this.page = require('./live/AudioVideoCallPage').default;
          break;
        case 'Album':
          this.page = require('./setting/Album').default;
          break;
        case 'AlbumPhotoViewPage':
          this.page = require('./setting/AlbumPhotoViewPage').default;
          break;
        case 'AlbumVideoViewPage':
          this.page = require('./setting/AlbumVideoViewPage').default;
          break;
        case 'DailyStoryVideoViewPage':
          this.page = require('./aicamera/DailyStoryVideoViewPage').default;
          break;
        case 'EditSelectPrePositions':
          this.page = require('./aicamera/EditSelectPrePositions').default;
          break;
        case 'CruiseSettingPage':
          this.page = require('./aicamera/CruiseSettingPage').default;
          break;
        case 'CruiseTimeSlotPage':
          this.page = require('./aicamera/CruiseTimeSlotPage').default;
          break;
        case 'SurvelillanceSetting':
          this.page = require('./setting/SurvelillanceSetting').default;
          break;
        case "SurvelillanceSettingOld":
          this.page = require('./setting/SurvelillanceSettingOld').default;
          break;
        case 'AlarmGuide':
          this.page = require('./setting/AlarmGuide').default;
          break;
        case 'StorageSetting':
          this.page = require('./setting/StorageSetting').default;
          break;
        case 'AISetting':
          this.page = require('./setting/AISetting').default;
          break;
        case 'AIFaceSetting':
          this.page = require('./aicamera/AIFaceSetting').default;
          break;
        case 'AIFaceSettingV2':
          this.page = require('./aicamera/AIFaceSettingV2').default;
          break;
        case 'SleepSetting':
          this.page = require('./setting/SleepSetting').default;
          break;
        case 'ImageSetting':
          this.page = require('./setting/ImageSetting').default;
          break;
        case 'WDRSetting':
          this.page = require('./setting/WDRSetting').default;
          break;
        case 'ImageRotateSetting':
          this.page = require('./setting/ImageRotateSetting').default;
          break;
        case 'NightVisionSetting':
          this.page = require('./setting/NightVisionSetting').default;
          break;
        case 'NightVisionSettingV2':
          this.page = require('./setting/NightVisionSettingV2').default;
          break;
        // // SurvelillanceSetting 的子页面
        case 'SurvelillancePeriodSetting':
          this.page = require('./setting/SurvelillancePeriodSetting').default;
          break;
        case 'SurveillancePeriodSettingV2':
          this.page = require('./setting/SurveillancePeriodSettingV2').default;
          break;
        case 'CustomSurvelillancePeriodSetting':
          this.page = require('./setting/CustomSurvelillancePeriodSetting').default;
          break;
        case 'NotificationTypeSetting':
          this.page = require('./setting/NotificationTypeSetting').default;
          break;
        case 'PartitionSensitivitySetting':
          this.page = require('./setting/PartitionSensitivitySetting').default;
          break;
        case 'CloudIntroPage':
          this.page = require('./setting/CloudIntroPage').default;
          break;
        case 'BandInfoPage':
          this.page = require('./setting/BandInfoPage').default;
          break;
        case 'BandNearbyClosePage':
          this.page = require('./setting/BandNearbyClosePage').default;
          break;
        case 'SdcardPage':
          this.page = require('./sdcard/SdcardPage').default;
          break;
        case 'SdcardHourPage':
          this.page = require('./sdcard/SdcardHourPage').default;
          break;
        case 'SdcardPlayerPage':
          this.page = require('./sdcard/SdcardPlayerPage').default;
          break;
        case 'SdcardTimelinePlayerPage':
          this.page = require('./sdcard/SdcardTimelinePlayerPage').default;
          break;
        case 'SdcardTimelinePlayerFragment':
          this.page = require('./sdcard/SdcardTimelinePlayerFragment').default;
          break;
        case 'CloudTimelinePlayerFragment':
          this.page = require('./sdcard/CloudTimelinePlayerFragment').default;
          break;
        case 'SdcardTimelinePlayerFragmentV2':
          this.page = require('./sdcard/SdcardTimelinePlayerFragmentV2').default;
          break;
        case 'CloudTimelinePlayerFragmentV2':
          this.page = require('./sdcard/CloudTimelinePlayerFragmentV2').default;
          break;
        case 'CloudTimelinePlayerPage':
          this.page = require('./sdcard/CloudTimelinePlayerPage').default;
          break;
        case 'CloudVideoEditPage':
          this.page = require('./sdcard/CloudVideoEditPage').default;
          break;
        case 'SdcardTimelinePage':
          this.page = require('./sdcard/SdcardTimelinePage').default;
          break;

        case 'MoreSetting':
          this.page = require('miot/ui/CommonSetting').MoreSetting;
          break;
        case 'FirmwareUpgrade':
          this.page = require('miot/ui/CommonSetting').MoreSetting;
          break;
        case 'SdcardCloudTimelinePage':
          this.page = require("./sdcard/SdcardCloudTimelinePage").default;
          break;
        case 'SdcardCloudTimelinePageV2':
          this.page = require("./sdcard/SdcardCloudTimelinePageV2").default;
          break;
        case "NativeWebPage":
          this.page = require("./setting/NativeWebPage").default;
          break;
        case 'AlarmVideoUI':
          this.page = require("./alarmDetail/AlarmVideoUI").default;
          break;
        case 'AllStorage':
          this.page = require("./allVideo/AllStorage").default;
          break;
        case 'DldPage':
          this.page = require('./allVideo/DldPage').default;
          break;
        case "TalkForPushSettings":
          this.page = require("./setting/TalkForPushSettings").default;
          break;
        case "DailyStorySetting":
          this.page = require("./setting/DailyStorySetting").default;
          break;
        case "DailyStoryList":
          this.page = require("./aicamera/DailyStoryList").default;
          break;
        case "DailyStoryFirstEnter":
          this.page = require("./aicamera/DailyStoryFirstEnter").default;
          break;
        case "LongTimeAlarmList":
          this.page = require("./aicamera/LongTimeAlarmList").default;
          break;
        case "IDMSettings":
          this.page = require("./aicamera/IDMSettings").default;
          break;
        case "BabySleepingSetting":
          this.page = require("./aicamera/BabySleepingSetting").default;
          break;
        case "AICameraSettins":
          this.page = require("./aicamera/AICameraSettins").default;
          break;
        case "AICameraSettingsV2":
          this.page = require("./aicamera/AICameraSettingsV2").default;
          break;
        case "MotionDetectionPage":
          this.page = require("./aicamera/MotionDetectionPage").default;
          break;
        case "KeyCallSetting":
          this.page = require("./aicamera/KeyCallSetting").default;
          break;
        case "GestureCallPage":
          this.page = require("./aicamera/GestureCallPage").default;
          break;
        case "ChoiceContactPage":
          this.page = require("./setting/ChoiceContactPage").default;
          // this.page = require("./aicamera/ChoiceContactPage").default;
          break;
        case "WXCallSetting":
          this.page = require("./setting/WXCallSetting").default;
          break;
        case "OnTimeAlarmPage":
          this.page = require("./aicamera/OnTimeAlarmPage").default;
          break;
        case "OnTimeSetting":
          this.page = require("./aicamera/OnTimeSetting").default;
          break;
        case "OnTimeSelect":
          this.page = require("./aicamera/OnTimeSelect").default;
          break;
        case "SetLongTimeAlarm":
          this.page = require("./aicamera/SetLongTimeAlarm").default;
          break;
        case 'AlarmPage':
          this.page = require('./alarm/AlarmPage').default;
          break;
        case 'AddNASSetting':
          this.page = require('./setting/AddNASSetting').default;
          break;
        case 'SelectNASLocation':
          this.page = require('./setting/SelectNASLocation').default;
          break;         
        case 'NASNetworkLocation':
          this.page = require('./setting/NASNetworkLocation').default;
          break;      
        case 'ChangeNAS':
          this.page = require('./setting/ChangeNAS').default;
          break;
        case 'ChangeNASDinfo':
          this.page = require('./setting/ChangeNASDinfo').default;
          break; 
        case 'ChangeNASDirectory':
          this.page = require('./setting/ChangeNASDirectory').default;
          break;
        case 'NasIntro':
          this.page = require('./setting/NasIntro').default;
          break;
        case 'NasUploadIntervalSetting':
          this.page = require('./setting/NasUploadIntervalSetting').default;
          break;
        case 'VisitRecordSetting':
          this.page = require('./setting/VisitRecordSetting').default;
          break;
        case 'CallRecordSetting':
          this.page = require('./setting/CallRecordSetting').default;
          break;
        case 'FaceManager':
          this.page = require('./setting/FaceManager').default;
          break;
        case 'FaceManager2':
          this.page = require('./setting/FaceManager2').default;
          break;
        case 'InformNoFace':
          this.page = require('./setting/InformNoFace').default;
          break;
        case 'FaceUnmarkedList':
          this.page = require('./setting/FaceUnmarkedList').default;
          break;
        case 'FacesDetailManager':
          this.page = require('./setting/FacesDetailManager').default;
          break;
        case 'LDCSetting':
          this.page = require('./setting/LDCSetting').default;
          break;
        case 'ThemeSetting':
          this.page = require('./setting/ThemeSetting').default;
          break;
        case 'ScreenLightSetting':
          this.page = require('./setting/ScreenLightSetting').default;
          break;
        case 'NightModeSetting':
          this.page = require('./setting/NightModeSetting').default;
          break;
        case 'NightModeTimeSetting':
          this.page = require('./setting/NightModeTimeSetting').default;
          break;
          
        case 'FaceCamera':
          this.page = require('./setting/FaceCamera').default;
          break;
        case 'FaceManagerNumber':
          this.page = require('./setting/FaceManagerNumber').default;
          break;
          
        case 'FaceEvents':
          this.page = require('./setting/FaceEvents').default;
          break;
        case 'NoVipFaceManager':
          this.page = require('./setting/NoVipFaceManager').default;
          break;
        case 'ClockAlarmPage':
          this.page = require('./setting/ClockAlarmPage').default;
          break;
        case 'ClockAlarmSetting':
          this.page = require('./setting/ClockAlarmSetting').default;
          break;
        case 'ClockAlarmSet':
          this.page = require('./setting/ClockAlarmSet').default;
          break;
        case 'AvatarDisplay':
          this.page = require('./setting/AvatarDisplay').default;
          break;
        case 'SceneSelectFigure':
          this.page = require('./scene/SceneSelectFigure').default;
          break;
        case 'SceneSelectGesture':
          this.page = require('./scene/SceneSelectGesture').default;
          break;
        case 'SceneSelectPreposition':
          this.page = require('./scene/SceneSelectPreposition').default;
          break;
        case 'SceneSelectContact':
          this.page = require('./scene/SceneSelectContact').default;
          break;
        case 'SmartMonitorSetting':
          this.page = require('./aicamera/SmartMonitorSetting').default;
          break;
        case 'GestureSwitchSetting':
          this.page = require('./setting/GestureSwitchSetting').default;
          break;
        case 'MonitorAreaModifyPage':
          this.page = require('./aicamera/MonitorAreaModifyPage').default;
          break;
        case 'MonitorDurationListPage':
          this.page = require('./aicamera/MonitorDurationListPage').default;
          break;
        case 'MonitorDurationSetting':
          this.page = require('./aicamera/MonitorDurationSetting').default;
          break;
          // 用户反馈信息
        case 'customFeedbackMessage':
          this.page = require('./setting/customFeedbackMessage').default;
          break;
        case 'FamilyDetectionSetting':
          this.page = require('./aicamera/FamilyDetectionSetting').default;
          break;
        case 'FamilyTimeSet':
          this.page = require('./aicamera/FamilyTimeSet').default;
          break;
        case 'CarSetting':
          this.page = require('./car/CarSetting').default;
          break;
        case 'CarStorage':
          this.page = require('./car/CarStorage').default;
          break;
        case 'IntroducePage':
          this.page = require('./car/IntroducePage').default;
          break;
        default:
          this.page = DummyPage;
          break;
      }
    }
    return this.page;
  }

}


