import PropTypes from 'prop-types';
import React from 'react';
import { Animated, StyleSheet, TouchableOpacity, View, I18nManager } from 'react-native';
import { ConfigContext } from "mhui-rn/dist/components/configProvider";
import DynamicColor from "mhui-rn/dist/styles/DynamicColor";
import { AccessibilityPropTypes, AccessibilityRoles, getAccessibilityConfig } from "mhui-rn/dist/utils/accessibility-helper";
import { preventRepeatClick } from '../../common/utils/click-util';
import { Color, Opacity } from '../../common/styles/Styles';

// 默认宽度
const DEFAULT_WIDTH = 90;
// 默认高度
const DEFAULT_HEIGHT = 48;
// 默认Circle Size
const DEFAULT_CIRCLE_SIZE = 32;

/**
 * @export public
 * @module Switch
 * @description Switch for CarIoT
 * @property {bool} value - 开关状态，默认值 false
 * @property {style} style - 开关样式，仅支持宽高
 * @property {string} onTintColor - 打开时的背景颜色
 * @property {string} tintColor - 关闭时的背景颜色
 * @property {bool} disabled - 是否禁用，默认值 false
 * @property {function} onValueChange - 切换开关的回调函数
 * @property {bool} disableAnim - 禁用动画，可选参数，true为禁用动画，false或者未定义为有动画
 */

class Switch extends React.PureComponent {
    static contextType = ConfigContext;
    static propTypes = {
      value: PropTypes.bool.isRequired,
      style: PropTypes.object,
      onTintColor: PropTypes.string,
      tintColor: PropTypes.string,
      disabled: PropTypes.bool,
      onValueChange: PropTypes.func.isRequired,
      name: PropTypes.string,
      ...AccessibilityPropTypes
    };
 
    defaultName = `Switch${ Math.floor(Math.random() * 10) }`
    defaultSwitchCircleColor = new DynamicColor(Color.Light.AccentOnPrimary, Color.Dark.AccentOnPrimary);
    defaultTintColor = new DynamicColor(Color.Light.AccentSecondary, Color.Dark.AccentSecondary);
    defaultOnTintColor = new DynamicColor(Color.Light.AccentPrimary, Color.Dark.AccentPrimary);

    constructor(props) {
      super(props);
      this.state = {
        value: this.props.value
      };

      // const {
      //   width,
      //   height
      // } = this.props.style;
      const backWidth = DEFAULT_WIDTH;
      const backHeight = DEFAULT_HEIGHT;

      // 根据style的宽度计算出滚球的大小
      const circleSize = DEFAULT_CIRCLE_SIZE;
      this.margin = (backHeight - circleSize) / 2;
      // 滚球滚动最大距离
      this.offsetXMax = backWidth - circleSize - this.margin;

      // 给 offsetX 一个初始值，避免进入页面时出现动画
      if (I18nManager.isRTL) {
        this.offsetXMax *= -1;
      }

      const toValue = this.props.value ? this.offsetXMax : this.margin;
      this.offsetX = new Animated.Value(toValue); // 容器实际样式

      this.backStyle = {
        width: backWidth,
        height: backHeight,
        borderRadius: backHeight / 2
      }; // 滚球实际样式

      this.circleStyle = {
        width: circleSize,
        height: circleSize,
        borderRadius: circleSize / 2
      };
    }

    UNSAFE_componentWillReceiveProps(newProps) {
      if (newProps.value !== this.state.value) {
        this.setState({
          value: newProps.value
        }, this.animated);
      }
    }

    render() {
      const {
        colorScheme
      } = this.context;
      const onTintColor = this.props.onTintColor ? this.props.onTintColor : this.defaultOnTintColor.color(colorScheme);
      const tintColor = this.props.tintColor ? this.props.tintColor : this.defaultTintColor.color(colorScheme);
      const backgroundColor = this.state.value ? onTintColor : tintColor;
      const opacity = this.props.disabled ? Opacity.Disabled : Opacity.Normal;
      return <View style={[styles.container, {
        opacity
      }, this.props.style]}>
        <TouchableOpacity style={[styles.back, this.backStyle, {
          backgroundColor
        }]} disabled={this.props.disabled} activeOpacity={0.8} onPress={() => {
          preventRepeatClick(this.props.name != undefined ? this.props.name : this.defaultName, () => {
            this._onValueChange();
          });
        }} {...getAccessibilityConfig({
          ...this.props,
          accessibilityRole: this.props.accessibilityRole || AccessibilityRoles.switch,
          accessibilityState: this.props.accessibilityState || {
            disabled: this.props.disabled,
            checked: !!this.state.value
          }
        })}>
          {// Android 黑暗模式下使用 Animated.Image 实现白色圆点
            this.context.colorScheme === 'dark' ? <Animated.Image style={[styles.circle, this.circleStyle, {
              backgroundColor: this.defaultSwitchCircleColor.color(colorScheme),
              transform: [{
                translateX: this.offsetX
              }]
            }]} /> : <Animated.View style={[styles.circle, this.circleStyle, {
              backgroundColor: this.defaultSwitchCircleColor.color(colorScheme),
              transform: [{
                translateX: this.offsetX
              }]
            }]} />}
        </TouchableOpacity>
      </View>;
    }

    animated() {
      const toValue = this.state.value ? this.offsetXMax : this.margin;
      if (this.props.disableAnim) {
        this.offsetX.setValue(toValue);
      } else {
        Animated.spring(this.offsetX, {
          toValue,
          bounciness: 9,
          speed: 9
        }).start();
      }
    }

    _onValueChange() {
      const value = !this.state.value;
      this.setState({
        value
      }, () => {
        this.animated();

        if (typeof this.props.onValueChange === 'function') {
          this.props.onValueChange(value);
        }
      });
    }

    componentDidMount() {
      this.animated();
    }

}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  back: {
    justifyContent: 'center'
  },
  circle: {
    position: 'absolute'
  }
});
export default Switch;
export { Switch };