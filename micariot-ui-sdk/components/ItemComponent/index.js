import React from 'react';
import { View, TouchableOpacity, Image, Text } from 'react-native';
import { Constants, styles, Opacity } from '../../common/styles/Styles';
import PropTypes from 'prop-types';

const DEFAULT_TEXT_MARGIN = 16;

/**
 * @export public
 * @module ItemCompenent
 * @description ItemCompenent for CarIoT
 * @property {bool} disabled - 是否禁用，默认值 false
 * @property {object} style - 自定义style
 * @property {string} title - 标题
 * @property {array} itemArray - 自定义item数组
 * @property {number} itemsOfLine - 每行item个数，自动计算行数，不足一行靠左放置
 * @property {object} itemStyle - item style
 * @property {object} itemImageStyle - item image style
 * @property {number} itemMargin - item margin
 * @property {number} lineMargin - line margin
 * @property {number} selectedIndex - 选中index
 * @property {object} selectedImageSource - 选中后 image source
 * @property {object} selectedItemStyle - selected Item style
 * @property {object} disableIndex - 置灰的item index
 * @property {function} onItemClick - item点击回调
 * @property {object} itemNameTextStyle - item name text style
 * @property {object} itemSubNameTextStyle: item subName Text style
 * @property {object} itemSubImageTextStyle: item subImage style
 * @property {number} userDefinedIndex - 自定义index
 * @property {object} renderUserDefinedItem - render userDefined
 */
class ItemCompenent extends React.PureComponent {
  static propTypes = {
    disabled: PropTypes.bool,
    style: PropTypes.object,
    title: PropTypes.string,
    itemArray: PropTypes.array,
    itemsOfLine: PropTypes.number,
    itemStyle: PropTypes.object,
    itemImageStyle: PropTypes.object,
    itemNameTextStyle: PropTypes.object,
    itemSubNameTextStyle: PropTypes.object,
    itemSubImageStyle: PropTypes.object,
    itemMargin: PropTypes.number,
    lineMargin: PropTypes.number,
    selectedIndex: PropTypes.number,
    selectedImageSource: PropTypes.object,
    selectedItemStyle: PropTypes.object,
    disabledIndex: PropTypes.number,
    onItemClick: PropTypes.func,
    userDefinedIndex: PropTypes.number,
    renderUserDefinedItem: PropTypes.object
  };

  constructor(props) {
    super(props);

    this.state = {
      selectedIndex: this.props.selectedIndex
    };
  }

  componentDidUpdate(pervProps) {
    if (this.props.selectedIndex !== pervProps.selectedIndex) {
      this.setState({ selectedIndex: this.props.selectedIndex });
    }
  }

  render() {
    const opacity = this.props.disabled ? Opacity.Disabled : Opacity.Normal;
    let totalArray = this.props.itemArray;
    let itemsOfLine = this.props.itemsOfLine ? this.props.itemsOfLine : totalArray.length;
    let itemLines = totalArray.length % itemsOfLine == 0 ? totalArray.length / itemsOfLine : parseInt(totalArray.length / itemsOfLine + 1);
    console.log(`split, total: ${ totalArray.length } lines:${ itemLines }`);
    let arrays = [];
    for (let i = 0; i < itemLines; i++) {
      let start = itemsOfLine * i;
      let end = itemsOfLine * i + itemsOfLine > totalArray.length ? totalArray.length : itemsOfLine * i + itemsOfLine;
      console.log(`split, start: ${ start } end:${ end }`);
      arrays.push(totalArray.slice(start, end));
    }
    console.log("arrays: ", JSON.stringify(arrays));
    return (
      <View style={[{
        marginTop: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
        marginBottom: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
        opacity
      }]}
      pointerEvents={this.props.disabled ? "none" : "auto"}
      >
        {this.props.title ? <Text style={[styles.titleTextStyle, { marginBottom: Constants.DEFAULT_TEXT_MARGIN_BOTTOM, opacity }]}>{this.props.title}</Text> : null}

        {arrays.map((key, lineIndex) => {
          return (
            // eslint-disable-next-line react/jsx-key
            <View style={[{ 
              flexDirection: 'row', 
              alignItems: 'flex-start',
              justifyContent: "flex-start",
              marginTop: lineIndex == 0 ? 0 : (this.props.lineMargin ? this.props.lineMargin : 0)
            }, this.props.style]}>
              {arrays[lineIndex].map((item, index) => {
                let curIndex = lineIndex * itemsOfLine + index;
                let selected = (curIndex == this.state.selectedIndex);
                return (
                  <TouchableOpacity 
                    style={[{ alignItems: 'center', marginRight: index == itemsOfLine - 1 ? 0 : this.props.itemMargin }, { opacity: curIndex == this.props.disabledIndex ? 0.5 : 1 }]} 
                    onPress={() => {
                      this.props.onItemClick && this.props.onItemClick(curIndex);
                      if (curIndex != this.props.disabledIndex) {
                        this.setState({ selectedIndex: curIndex });
                      }
                    }} 
                    key={item.id}
                    disabled={selected && index != this.props.userDefinedIndex}
                  >
                    {selected ? 
                      <Image  
                        style={[{ position: 'absolute' }, this.props.selectedItemStyle]} 
                        resizeMode={"stretch"} 
                        source={this.props.selectedImageSource}/>
                      : null}
                    
                    <View style={[{ alignItems: 'center' }, this.props.itemStyle]}>
                      {index == this.props.userDefinedIndex ? this.props.renderUserDefinedItem() : 
                        <Image
                          style={this.props.itemImageStyle}
                          source={item.imageSource}
                          resizeMode={"stretch"}
                        />}
                      <View style={[{ flexDirection: 'row', width: this.props.itemStyle.width }]}>
                        <Text numberOfLines={1} style={[selected ? styles.itemHighlightTextStyle : styles.itemTextStyle, { marginTop: DEFAULT_TEXT_MARGIN }, this.props.itemNameTextStyle]}>{item.name}</Text>
                        {item.subName ? <Text numberOfLines={1} style={[styles.subTitleTextStyle, { marginTop: DEFAULT_TEXT_MARGIN }, this.props.itemSubNameTextStyle]} >{item.subName}</Text> : null }
                        {item.subImage ? <Image style={this.props.itemSubImageStyle} source={item.subImage}/> : null }
                      </View>
                    </View>
                  </TouchableOpacity>
                );
              })}
            </View>);

        })}
      </View>

    );
  }

}

export default ItemCompenent;
export { ItemCompenent };
