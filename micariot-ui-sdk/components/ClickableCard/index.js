import React from 'react';
import { Text, View, Image, TouchableOpacity } from 'react-native';
import PropTypes from 'prop-types';
import { ConfigContext } from "mhui-rn/dist/components/configProvider";
import { styles, Radius, Constants, Opacity } from '../../common/styles/Styles';


const DEFAULT_HEIGHT = 104;
const DEFAULT_PADDING = 32;
const DEFAULT_IMAGE_SIZE = 40;
const DEFAULT_TEXT_MARGIN_RIGHT = 16;

/**
 * @export public
 * @module ClickableCard
 * @description ClickableCard for CarIoT
 * @property {object} style - style
 * @property {string} title - 标题文本
 * @property {string} tipText - 提示文本内容
 * @property {object} tipTextStyle - 提示文本style
 * @property {function} onPress - 点击回调方法
 * @property {bool} disabled - 是否禁用，默认值 false
 */
class ClickableCard extends React.PureComponent {
    static contextType = ConfigContext;
    static propTypes = {
      style: PropTypes.object,
      title: PropTypes.string,
      tipText: PropTypes.string,
      tipTextStyle: PropTypes.object,
      onPress: PropTypes.func,
      disabled: PropTypes.bool
    };

    constructor(props) {
      super(props);
    }

    componentDidMount() {
    
    }

    render() {
      const {
        colorScheme
      } = this.context;
      const opacity = this.props.disabled ? Opacity.Disabled : Opacity.Normal;

      return (
        <View
          style={{ opacity }}
          pointerEvents={this.props.disabled ? "none" : "auto"}
        >
          <TouchableOpacity style={[styles.itemSecondaryStyle, {
            flexDirection: 'row',
            alignItems: 'center',
            height: DEFAULT_HEIGHT,
            paddingLeft: DEFAULT_PADDING,
            paddingRight: DEFAULT_PADDING,
            marginTop: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
            marginBottom: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
            borderRadius: Radius.WidgetLevel
          }]}
          onPress={() => {
            if (this.props.onPress) {
              this.props.onPress();
            }
          }}>
            <Text numberOfLines={1} style={[styles.titleTextStyle, { flex: 1, marginRight: DEFAULT_PADDING }]}>{this.props.title}</Text>
            <View
              style={{
                alignItems: 'center',
                flexDirection: "row"
              }}
            >
              <Text numberOfLines={1} style={[styles.subTitleTextStyle, { marginRight: DEFAULT_TEXT_MARGIN_RIGHT }, this.props.tipTextStyle]}>{this.props.tipText}</Text>
              <Image style={{ width: DEFAULT_IMAGE_SIZE, height: DEFAULT_IMAGE_SIZE }} source={colorScheme == 'dark' ? require('../../resources/images/right_arrow.png') : require('../../resources/images/right_arrow_light.png')} />
            </View>
          
          
          </TouchableOpacity>
        </View>
      );
    }
}

export default ClickableCard;
export { ClickableCard };