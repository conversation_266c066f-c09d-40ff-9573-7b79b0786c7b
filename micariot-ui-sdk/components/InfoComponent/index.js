import React from 'react';
import { Text, View } from 'react-native';
import PropTypes from 'prop-types';

import { styles, Constants, Font, Opacity } from '../../common/styles/Styles';

const DEFAULT_HEIGHT = 180;
const DEFAULT_TEXT_MARGIN = 12;

/**
 * @export public
 * @module InfoComponent
 * @description InfoComponent for CarIoT
 * @property {object} style - style
 * @property {array[object, object]} textArray [{text: "", textStyle: {}}] - 文本及style数组
 * @property {bool} disabled - 是否禁用，默认值 false
 */
class InfoComponent extends React.PureComponent {
    static propTypes = {
      style: PropTypes.object,
      textArray: PropTypes.array,
      disabled: PropTypes.bool
    };

    constructor(props) {
      super(props);
    }

    componentDidMount() {
    
    }

    render() { 
      const opacity = this.props.disabled ? Opacity.Disabled : Opacity.Normal; 
      return (
        <View style={[{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          height: DEFAULT_HEIGHT,
          marginTop: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
          marginBottom: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
          opacity
        }, this.props.style]}>
          {this.props.textArray.map((item) => {
            return (
              // eslint-disable-next-line react/jsx-key
              <View style={{ flex: 1 }}>
                <Text style={[styles.titleTextStyle, item[0].textStyle, { fontSize: Font.Size._48, textAlign: 'center' }]}>{item[0].text}</Text>
                <Text style={[styles.subTitleTextStyle, item[1].textStyle, { fontSize: Font.Size._24, marginTop: DEFAULT_TEXT_MARGIN, textAlign: 'center' }]}>{item[1].text}</Text>
              </View> 
            );
          })}

        </View>
      );
    }
}

export default InfoComponent;
export { InfoComponent };