import React from 'react';
import { View, Text } from 'react-native';
import PropTypes from 'prop-types';
import { styles, Constants, Opacity } from '../../common/styles/Styles';
import { Button } from '../Button';


const DEFAULT_MARGIN = 24;

export const Site = {
  Left: 0,
  Right: 1
};

/**
 * @export public
 * @module ButtonComponent
 * @description ButtonComponent for CarIoT
 * @property {object} style - style
 * @property {string} title - 标题文本
 * @property {string} leftBtnText - 左按钮文本
 * @property {string} rightBtnText - 右按钮文本
 * @property {number} selectIndex - 选中的index
 * @property {function} onPress - 点击回调方法
 * @property {bool} disabled - 是否禁用，默认值 false
 */
class ButtonComponent extends React.PureComponent {
  static propTypes = {
    style: PropTypes.object,
    title: PropTypes.string,
    leftBtnText: PropTypes.string,
    rightBtnText: PropTypes.string,
    selectIndex: PropTypes.number,
    onPress: PropTypes.func,
    disabled: PropTypes.bool
  };

  constructor(props) {
    super(props);

    this.state = {
      selectIndex: this.props.selectIndex
    };
  }

  componentDidMount() {

  }

  UNSAFE_componentWillReceiveProps(newProps) {
    if (newProps.selectIndex !== this.state.selectIndex) {
      this.setState({
        selectIndex: newProps.selectIndex
      });
    }
  }

  render() {
    const opacity = this.props.disabled ? Opacity.Disabled : Opacity.Normal;
    
    return (
      <View
        style={[{
          marginTop: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
          marginBottom: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
          opacity
        }, this.props.style]}
        pointerEvents={this.props.disabled ? "none" : "auto"}
      >
        {this.props.title ? <Text style={[styles.titleTextStyle, { marginBottom: DEFAULT_MARGIN }]}>{this.props.title}</Text> : null }
        <View
          style={{
            flexDirection: "row",
            alignItems: "center"
          }}
        >
          <View
            pointerEvents={this.state.selectIndex === Site.Left ? "none" : "auto"}
          >
            <Button
              text={this.props.leftBtnText}
              selected={this.state.selectIndex === Site.Left}
              onPress={() => this.onPress(Site.Left)}
            />
          </View>
          <View
            pointerEvents={this.state.selectIndex === Site.Right ? "none" : "auto"}
          >
            <Button
              style={{ marginLeft: DEFAULT_MARGIN }}
              text={this.props.rightBtnText}
              selected={this.state.selectIndex === Site.Right}
              onPress={() => this.onPress(Site.Right)}
              pointerEvents={this.state.selectIndex === Site.Right ? "none" : "auto"}
            />
          </View>
        </View>
      </View>
    );
  }

  onPress(index) {
    this.state.selectIndex = index;
    this.props.onPress(index);
  }
}

export default ButtonComponent;
export { ButtonComponent };