import React from 'react';
import { Text, TouchableOpacity } from 'react-native';
import PropTypes from 'prop-types';
import { styles, Opacity } from '../../common/styles/Styles';


const DEFAULT_HEIGHT = 80;
const DEFAULT_WIDTH = 364;

/**
 * @export public
 * @module Button
 * @description Button for CarIoT
 * @property {object} style - style
 * @property {string} text - 文本
 * @property {bool} selected - 是否选中
 * @property {function} onPress - 点击回调方法
 * @property {bool} disabled - 是否禁用，默认值 false
 */
class But<PERSON> extends React.PureComponent {
    static propTypes = {
      style: PropTypes.object,
      text: PropTypes.string,
      selected: PropTypes.bool,
      onPress: PropTypes.func,
      disabled: PropTypes.bool
    };

    constructor(props) {
      super(props);

      this.state = {
        selected: this.props.selected
      };
    }

    componentDidMount() {
    
    }
    UNSAFE_componentWillReceiveProps(newProps) {
      if (newProps.selected !== this.state.selected) {
        this.setState({
          selected: newProps.selected
        });
      }
    }

    render() {
      const opacity = this.props.disabled ? Opacity.Disabled : Opacity.Normal;

      return (
        <TouchableOpacity
          style={[this.state.selected ? styles.itemHighlightStyle : styles.itemSecondaryStyle, styles.buttonBaseStyle, { height: DEFAULT_HEIGHT, width: DEFAULT_WIDTH, opacity }, this.props.style]}
          onPress={() => {
            this.setState({ selected: !this.state.selected });
            this.props.onPress && this.props.onPress(this.state.selected);
          }}
          pointerEvents={this.props.disabled ? "none" : "auto"}
        >
          <Text style={[this.state.selected ? styles.buttonHightLightTextStyle : styles.buttonTextStyle]}>{this.props.text}</Text>
        </TouchableOpacity>
      );
    }
}

export default Button;
export { Button };