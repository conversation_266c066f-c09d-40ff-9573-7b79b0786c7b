import React from 'react';
import { Text, View, Image } from 'react-native';
import PropTypes from 'prop-types';

import { styles, Radius, Constants, Font, Opacity } from '../../common/styles/Styles';

export const LEVEL = {
  Warning: 0,
  Error: 1
};

const DEFAULT_HEIGHT = 104;
const DEFAULT_PADDING = 32;
const DEFAULT_IMAGE_SIZE = 48;
const DEFAULT_TEXT_MARGIN_LEFT = 12;
const DEFAULT_TEXT_MAX_LINES = 1;

/**
 * @export public
 * @module WarningCard
 * @description WarningCard for CarIoT
 * @property {string} info - 内容
 * @property {int} level - 级别
 * @property {bool} disabled - 是否禁用，默认值 false
 * @property {object} style - 自定义style
 */
class WarningCard extends React.PureComponent {
    static propTypes = {
      style: PropTypes.object,
      info: PropTypes.string,
      level: PropTypes.number,
      disabled: PropTypes.bool
    };

    constructor(props) {
      super(props);
    }

    componentDidMount() {
    
    }

    render() {
      const opacity = this.props.disabled ? Opacity.Disabled : Opacity.Normal;
      return (
        <View style={[styles.itemSecondaryStyle, {
          flexDirection: 'row',
          alignItems: 'center',
          height: DEFAULT_HEIGHT,
          paddingLeft: DEFAULT_PADDING,
          paddingRight: DEFAULT_PADDING,
          marginTop: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
          marginBottom: Constants.DEFAULT_MARGIN_TOP_BOTTOM,
          borderRadius: Radius.WidgetLevel
        }, opacity, this.props.style]}>
          <Image
            style={{ width: DEFAULT_IMAGE_SIZE, height: DEFAULT_IMAGE_SIZE }}
            source={ this.props.level === LEVEL.Error ? require('../../resources/images/error_icon.png') : require('../../resources/images/warning_icon.png')} />
          <Text style={[this.props.level === LEVEL.Error ? styles.errorTextStyle : styles.warningTextStyle, { fontSize: Font.Size._28, marginLeft: DEFAULT_TEXT_MARGIN_LEFT, flex: 1 }]} numberOfLines={DEFAULT_TEXT_MAX_LINES}>{this.props.info}</Text>
        </View>
      );
    }
}

export default WarningCard;
export { WarningCard };