let preventClickKeyMap = {};

/**
 * 防止短时间重复点击方法
 * 
 * sample 1:
 * <TouchableOpacity onPress={(event) => { 
      preventRepeatClick(event, () => {
        // call function detail
      });
    }}>
 * 
 * sample 2:
    <TouchableOpacity onPress={() => { 
      preventRepeatClick('key-name', () => {
        // call function detail
      });
    }}>
 * 
 * sample 3:
    <TouchableOpacity onPress={(event) => { 
      preventRepeatClick(event, this.callFunction, null, 1000);
    }}>
 * 
 * @param {Object, string} event 点击事件 or key，用于区分不同按钮，当为事件时会取currentTarget值作为key
 * @param {function} callFunction 调用的业务方法
 * @param {function} skipFunction 被判定重复调用，跳过时执行的方法
 * @param {number} interval 间隔时间，默认500ms
 */
export const preventRepeatClick = (event, callFunction, skipFunction = null, interval = 500) => {
  let key = event;
  if (event instanceof Object && 'currentTarget' in event) {
    key = event['currentTarget'];
  }
  console.log(`click key: ${ key }`);
  let lastTime = preventClickKeyMap[key];
  if (!lastTime) {
    lastTime = 0;
  }
  let currentTime = new Date().getTime();
  if (currentTime - lastTime > interval) {
    preventClickKeyMap[key] = currentTime;
    callFunction();
  } else {
    console.log(`skip: ${ key }`);
    if (skipFunction != null) {
      skipFunction();
    }
  }
};