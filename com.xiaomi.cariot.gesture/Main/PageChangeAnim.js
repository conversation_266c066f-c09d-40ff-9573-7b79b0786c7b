/*
* 从上至下动画
* */
import { Animated, Easing, I18nManager } from "react-native";

const forVerticalTop = (sceneProps) => {
  const { layout, position, scene } = sceneProps;
  const { route } = scene;
  const index = scene.index;
  const height = layout.initHeight;
  const width = layout.initWidth;
  if (route.routeName.indexOf('OverTimeApply') < 0) {
    console.log('没有动画');
    return null;
  }
  // console.log('index',index);
  // console.log('position',position);
  const opacity = position.interpolate({
    inputRange: ([
      index - 1,
      index - 0.99,
      index,
      index + 0.99,
      index + 1
    ]: Array<number>),
    outputRange: ([1, 1, 1, 0.85, 0]: Array<number>)
  });

  const translateX = 0;
  // const translateX = position.interpolate({
  //     inputRange: [index - 1, index, index + 1],
  //     outputRange: [layout.initWidth, 0, 0]
  // });
  // const translateX = position.interpolate({
  //     inputRange: ([index, index+1, index + 1]: Array<number>),
  //     outputRange: ([WIDTH,WIDTH,WIDTH]: Array<number>),
  // });
  const translateY = position.interpolate({
    inputRange: ([index, index + 1, index + 1]: Array<number>),
    outputRange: ([0, height, height]: Array<number>)
  });

  return {
    opacity,
    transform: [{ translateX }, { translateY }]
  };

};

/*
 * 从左至右动画
 * */
const forHorizontalLeft = (sceneProps) => {
  const { layout, position, scene } = sceneProps;

  const index = scene.index;
  const inputRange = [index - 1, index, index + 1];

  const width = layout.initWidth;
  const outputRange = I18nManager.isRTL
    ? ([width, 0, -width * 0.3]: Array<number>)
    : ([-width, 0, width * -0.3]: Array<number>);

  // Add [index - 1, index - 0.99] to the interpolated opacity for screen transition.
  // This makes the screen's shadow to disappear smoothly.
  const opacity = position.interpolate({
    inputRange: ([
      index - 1,
      index - 0.99,
      index,
      index + 0.99,
      index + 1
    ]: Array<number>),
    outputRange: ([0, 1, 1, 0.85, 0]: Array<number>)
  });

  const translateY = 0;
  const translateX = position.interpolate({
    inputRange,
    outputRange
  });

  return {
    opacity,
    transform: [{ translateX }, { translateY }]
  };
};


/*
 * 从左至右动画
 * */
const freeStyle = (sceneProps) => {
  const { layout, position, scene } = sceneProps;
  if (!layout.isMeasured) {
    return (props) => {
      const { navigation, scene } = props;

      const focused = navigation.state.index === scene.index;
      const opacity = focused ? 1 : 0;
      // If not focused, move the scene far away.
      const translate = focused ? 0 : 1000000;
      return {
        opacity,
        transform: [{ translateX: translate }, { translateY: translate }]
      };
    };
  }
  const index = scene.index;
  const inputRange = [index - 1, index, index + 1];

  const width = layout.initWidth;
  // const outputRange = I18nManager.isRTL
  //     ? ([width, 0, -width * 0.3])
  //     : ([-width, 0, width * -0.3]);
  const outputRange = [width, 0, -width * 0.3];
  // Add [index - 1, index - 0.99] to the interpolated opacity for screen transition.
  // This makes the screen's shadow to disappear smoothly.
  const opacity = position.interpolate({
    inputRange: ([
      index - 1,
      index - 0.99,
      index,
      index + 0.99,
      index + 1
    ]),
    outputRange: ([0, 1, 1, 0.85, 0])
  });

  const translateY = 0;
  const translateX = position.interpolate({
    inputRange,
    outputRange
  });
  const scale = position.interpolate({
    inputRange: ([
      index - 1,
      index - 0.99,
      index,
      index + 0.99,
      index + 1
    ]),
    outputRange: ([0, 1, 1, 0.85, 0])
  });

  return {
    opacity,
    transform: [{ translateX }, { translateY }, { scale: scale }]
  };
};

const TransitionConfiguration = () => {
  return {
    transitionSpec: {
      duration: 300,
      easing: Easing.linear(),
      timing: Animated.timing
    },
    screenInterpolator: (sceneProps) => {
      const { scene, scenes } = sceneProps;
      const { route, index } = scene;
      const params = route.params || {};
      // const transition = params.transition || 'forHorizontal';
      const transition = params.transition || 'forHorizontal';

      switch (transition) {
        case 'forVerticalTop':
          return forVerticalTop(sceneProps);
        case 'forHorizontalLeft':
          return forHorizontalLeft(sceneProps);
        case 'freeStyle':
          return freeStyle(sceneProps);
        default:
          // return CardStackStyleInterpolator[transition](sceneProps);
          return null;
      }
    }
  };
};