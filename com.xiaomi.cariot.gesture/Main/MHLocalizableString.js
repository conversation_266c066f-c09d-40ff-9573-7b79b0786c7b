import LocalizedStrings from '../CommonModules/LocalizedStrings';
import IntlMessageFormat from 'intl-messageformat';
import 'intl';
import 'intl/locale-data/jsonp/en.js';
import 'intl/locale-data/jsonp/zh-Hans.js';
import 'intl/locale-data/jsonp/zh-Hant.js';
import 'intl/locale-data/jsonp/ko-KR.js';

// TODO: 添加多语言
export const strings = {
  // "en": {
  //   // 车机端字符
  // },
  "zh": {
    "product_name": "手势摄像头",
    "car_high_temperature_protection": "高温保护：设备温度过高，请稍后使用",
    "car_gesture_yeah": "比耶",
    "car_gesture_five": "手掌",
    "car_gesture_thumbleft": "拇指向左",
    "car_gesture_thumbright": "拇指向右",
    "car_gesture_thumbup": "拇指向上",
    "car_gesture_ok": "OK",
    "car_preset_function": "预置功能",
    "car_grid_no_function": "无功能",
    "car_grid_play_pause": "播放/暂停",
    "car_grid_prev": "上一首",
    "car_grid_next": "下一首",
    "car_grid_map": "地图全览",
    "car_grid_photo": "行车拍照",
    "car_custom": "Xiaomi HyperTask 动作",
    "car_not_set_customData": "执行自定义动作",
    "car_not_set_autoData": "执行自定义任务",
    "car_auto_delete": "绑定功能已删除",
    "car_automation": "Xiaomi HyperTask 任务",
    "car_sensitivity_setting": "灵敏度设置",
    "car_sensitivity_low": "低",
    "car_sensitivity_middle": "中",
    "car_sensitivity_high": "高",
    "c_set_fail": "设置失败",
  }
};

export const localStrings = new LocalizedStrings(strings);

export function getString(key, obj = null) {
  if (obj) {
    return new IntlMessageFormat(localStrings[key], localStrings.language).format(obj);
  } else {
    return localStrings[key];
  }
}
