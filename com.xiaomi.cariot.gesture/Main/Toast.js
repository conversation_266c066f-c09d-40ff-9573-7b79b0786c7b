import RNToast from 'react-native-root-toast';
import { localStrings as LocalizedStrings } from './MHLocalizableString';

export class _Toast {
  constructor() {
  }

  lasToast = null

  _showToast(message, isLong = false, hide = false) {

    if (this.lasToast != null) {
      RNToast.hide(this.lasToast);
      this.lasToast = null;
    }
    this.lasToast = RNToast.show(message, {
      duration: isLong ? RNToast.durations.LONG : RNToast.durations.SHORT,
      position: RNToast.positions.CENTER
    });
    // 这里设置隐藏
    // if (this.lasToast) {
    //   clearTimeout(this.hideToast);
    //   this.hideToast = setTimeout(() => {
    //     RNToast.hide(this.lasToast);
    //     this.lasToast = null;
    //   }, 1000);
    // }

  }
  // hide(localizeKey) {
  //   RNToast.hide(RNToast.show(localizeKey, {
  //     duration: 100,
  //     position: RNToast.positions.CENTER
  //   })); 
  // }
  /**
   * @param {string} localizeKey 多语言的key
   */
  loading(localizeKey = '') {
    this._showToast(LocalizedStrings[localizeKey], true);
  }

  /**
     * @param {string} localizeKey 多语言的key
     */
  success(localizeKey, isLong = false) {
    this._showToast(LocalizedStrings[localizeKey], isLong);
  }


  /**
   * @param {string} localizeKey 多语言的key
   * @param {Object} error 错误信息, 用于log
   */
  fail(localizeKey, error = null) {
    this._showToast(LocalizedStrings[localizeKey]);
    if (error) {
      console.log(JSON.stringify(error));
    }
  }
}

const Toast = new _Toast();
export default Toast;
