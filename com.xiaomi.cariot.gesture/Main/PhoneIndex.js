import React from 'react';

import { createStackNavigator } from "react-navigation";
import MHSetting from "./phone/MHSetting";

import { NavigationBar } from "mhui-rn";
import { StatusBar, View } from "react-native";
import { Package } from "miot";

let rootStack = null;

export function getStack() {
  return rootStack;
}

const RootStack = createStackNavigator(
  {
    MHSetting: MHSetting
  },
  {
    // ThirdPartyDemo
    initialRouteName: 'MHSetting',
    navigationOptions: ({ navigation }) => {
      return {
        header: /* <TitleBarBlack title={navigation.state.params ? navigation.state.params.title : ''}
                                       style={{ backgroundColor: '#fff' }}
                                       onPressLeft={() => {
                                           navigation.goBack();
                                           // Package.exit();
                                       }} />, */
          <View >
            <StatusBar backgroundColor={'transparent'}/>
            <NavigationBar
              left={[
                {
                  key: NavigationBar.ICON.BACK,
                  onPress: (_) => { Package.exit(); }
                }
              ]}
              title={navigation.state.params ? navigation.state.params.title : '设置'}
              backgroundColor={'#fff'}
              transparent={false}
            />

          </View>


      };
    }
  }
);


export default class PhoneIndex extends React.Component {
  render() {
    return <RootStack />;
  }

}