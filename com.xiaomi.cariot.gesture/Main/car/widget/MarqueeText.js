import React, { useEffect, useRef, useState } from 'react';
import { Animated, Easing, StyleSheet, View, findNodeHandle, UIManager, NativeModules } from 'react-native';
import LogUtil from "../../util/LogUtil";

const MarqueeText = ({
  text,
  speed = 50,       // 滚动速度（像素/秒）
  delay = 2000,     // 开始前的延迟
  style,            // 自定义文本样式
  containerStyle,   // 容器样式
  loop = true,      // 是否循环
  isSelected = false // 是否选中
}) => {
  const DISTANCE = 40;
  const translateX = useRef(new Animated.Value(0)).current;
  const [textWidth, setTextWidth] = useState(0);
  const [containerWidth, setContainerWidth] = useState(0);
  const [isAnimateRun, setIsAnimateRun] = useState(false);
  const [isMeasured, setIsMeasured] = useState(false); // 添加测量标记
  const animation = React.useRef(null);
  const prevTextRef = useRef(text);
  const textRef = useRef(null); // 添加一个新的 ref 来引用 Text 组件

  // 动态计算第二个文本的位置（始终在第一个文本右侧）
  const translateX2 = translateX.interpolate({
    inputRange: [-(textWidth + DISTANCE), 0],
    outputRange: [0, DISTANCE + textWidth]
  });

  // 监听文本变化
  useEffect(() => {
    if (prevTextRef.current !== text) {
      prevTextRef.current = text;
      stopAnimation();
      setTextWidth(0); // 重置文本宽度，触发重新测量
      setIsMeasured(false); // 重置测量标记为false
      LogUtil.logOnAll(` 文案改变，重置文本宽度，测量标记设为false`);

      const timeoutId = setTimeout(() => {
        LogUtil.logOnAll(` 定时器启动 textWidth: ` + textWidth + `, textRef.current: "${textRef.current}", isMeasured: ${isMeasured}`);
        // 只有当测量标记为false时才执行测量工作
        if (!isMeasured && textRef.current) {
          const nodeHandle = findNodeHandle(textRef.current);
          if (nodeHandle) {
            LogUtil.logOnAll(` 开始 UIManager.measure`);
            UIManager.measure(nodeHandle, (x, y, width, height, pageX, pageY) => {
              if (width > 0) { // 确保获取到有效宽度
                LogUtil.logOnAll(` [MarqueeText HACKY MEASURE] Fallback measure for text: "${text}", width: ${width}`);
                setTextWidth(width);
              } else {
                LogUtil.logOnAll(` [MarqueeText HACKY MEASURE] Fallback measure got 0 width for text: "${text}"`);
              }
            });
          }
        } else {
          LogUtil.logOnAll(` 跳过测量，isMeasured: ${isMeasured}`);
        }
      }, 10); // 少量延迟，让组件有时间完成布局

      return () => clearTimeout(timeoutId); // 清理定时器
    }
  }, [text]);

  // 测量容器宽度
  const measureContainer = (event) => {
    setContainerWidth(event.nativeEvent.layout.width);
  };

  // 测量文本宽度
  const measureText = (event) => {
    setTextWidth(event.nativeEvent.layout.width);
    setIsMeasured(true); // 设置测量标记为true
    LogUtil.logOnAll(` 测量完成，设置isMeasured为true`);
  };

  // 监听文本或容器宽度变化
  useEffect(() => {
    LogUtil.logOnAll(`${ text } isSelected(${ isSelected }), contentWidth(${ textWidth }), contentContainerWidth(${ containerWidth })`);
    // 如果文本宽度还未测量完成，不执行动画判断
    if (textWidth === 0 || containerWidth === 0) {
      return;
    }
    if (isSelected && textWidth > containerWidth) {
      startAnimation();
    } else {
      stopAnimation();
    }
  }, [isSelected, textWidth, containerWidth]);

  // 启动动画
  const startAnimation = () => {
    if (isAnimateRun) {
      stopAnimation();
    }
    setIsAnimateRun(true);
    let totalDistance = textWidth + DISTANCE;
    let toValue = -(textWidth + DISTANCE);
    const duration = (totalDistance / speed) * 1000;
    translateX.setValue(0);

    // 创建动画序列：暂停2s -> 跑马灯 -> 暂停2s
    animation.current = Animated.sequence([
      // 初始暂停2秒
      Animated.timing(translateX, {
        toValue: 0,
        duration: 0,
        useNativeDriver: true
      }),
      Animated.delay(delay),
      // 跑马灯动画
      Animated.timing(translateX, {
        toValue: toValue,
        duration: duration,
        useNativeDriver: true,
        easing: Easing.linear
      })
    ]);
    animation.current.start(({ finished }) => {
      if (finished) {
        if (loop) {
          startAnimation();
        } else {
          translateX.setValue(0);
        }
      }
    });
  };

  const stopAnimation = () => {
    setIsAnimateRun(false);
    animation.current?.stop();
    translateX.setValue(0);
  };

  return (
    <View
      style={ [styles.container, containerStyle] }
      onLayout={ measureContainer }
      pointerEvents="none">
      <View style={ { display: 'flex', flexWrap: isSelected ? 'wrap' : null } }>
        <Animated.Text
          ref={textRef}
          key={text} // 添加 key 属性以强制重新渲染
          style={ [
            styles.text,
            style,
            {
              transform: [{ translateX: translateX }]
            }
          ] }
          onLayout={ measureText }
          numberOfLines={ 1 }>
          { text }
        </Animated.Text>
      </View>
      { isAnimateRun &&
        <View style={ { position: 'absolute', display: 'flex', flexWrap: isSelected ? 'wrap' : null } }>
          <Animated.Text
            style={ [
              styles.text,
              style,
              {
                transform: [{ translateX: translateX2 }]
              }
            ] }
            numberOfLines={ 1 }>
            { text }
          </Animated.Text>
        </View> }
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden'
  },
  text: {
    flexShrink: 1,
    flexWrap: 'nowrap'
  }
});

export default MarqueeText;