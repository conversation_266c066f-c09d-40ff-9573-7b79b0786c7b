# 小米车载IoT项目总览文档

## 项目概述

本工作空间包含两个主要的小米车载IoT项目，都基于 React Native 和 MIOT SDK 开发，专为车载环境设计，提供智能摄像头监控和手势识别控制功能。

## 项目列表

### 1. com.xiaomi.cariot.camera - 车载摄像头项目
**项目路径**: `com.xiaomi.cariot.camera/`  
**版本**: 1.0.28  
**主要功能**: 车载摄像头监控、AI检测、存储管理

#### 核心特性
- 🎥 实时视频监控
- 🤖 AI智能检测（人脸、手势、声音等）
- 💾 多种存储方案（云存储、本地、SD卡）
- 🚗 车机专用界面
- 📱 手机端完整功能

#### 技术栈
- React Native + MIOT SDK
- 视频流处理
- P2P通信
- 云存储服务

### 2. com.xiaomi.cariot.gesture - 车载手势识别项目
**项目路径**: `com.xiaomi.cariot.gesture/`  
**版本**: 1.0.28  
**主要功能**: 车载手势识别控制、智能场景绑定

#### 核心特性
- ✋ 6种手势识别
- 🎵 音乐播放控制
- 🏠 智能家居场景联动
- ⚙️ 自定义动作绑定
- 🌡️ 温度保护机制

#### 技术栈
- React Native + MIOT SDK
- 手势识别算法
- SPEC协议通信
- URI Scheme深度链接

## 技术架构对比

| 特性 | Camera项目 | Gesture项目 |
|------|------------|-------------|
| **主要功能** | 视频监控 | 手势控制 |
| **复杂度** | 高 | 中 |
| **AI能力** | 多种AI检测 | 手势识别 |
| **存储需求** | 大量视频数据 | 配置数据 |
| **网络依赖** | 高（视频流） | 低（控制指令） |
| **车机适配** | 完整适配 | 专为车机设计 |

## 共同技术基础

### 开发框架
- **React Native**: 跨平台移动应用开发
- **MIOT SDK**: 小米IoT设备SDK (API Level 10080)
- **micariot-ui-sdk**: 车载专用UI组件库

### 平台支持
- ✅ Android 手机端
- ✅ iOS 手机端
- ✅ Android 车机系统
- ✅ iOS 车机系统

### 通信协议
- **SPEC协议**: 设备属性和动作控制
- **RPC通信**: 远程过程调用
- **HTTP API**: 云端服务接口
- **P2P通信**: 设备直连（Camera项目）

## 项目依赖对比

### Camera项目依赖
```json
{
  "base64-js": "^1.2",
  "dayjs": "^1.10.5",
  "micariot-ui-sdk": "file:../../micariot-ui-sdk",
  "patch-package": "^6.4.7",
  "react-native-canvas": "^0.1.37",
  "react-native-image-pan-zoom": "2.1.12",
  "react-native-image-zoom-viewer": "^2.2.27",
  "react-native-parsed-text": "0.0.22",
  "react-native-root-toast": "^3.2.0",
  "react-native-scrollable-tab-view": "^1.0.0"
}
```

### Gesture项目依赖
```json
{
  "micariot-ui-sdk": "file:../../micariot-ui-sdk",
  "patch-package": "^6.4.7",
  "react-native-root-toast": "^3.2.0",
  "react-native-svg-uri": "^1.2.3"
}
```

## 开发环境配置

### 系统要求
- Node.js >= 12.0
- React Native >= 0.60
- MIOT SDK >= 10080

### 工作空间结构
```
miot-plugin-sdk-SDK_10080/
├── projects/
│   ├── com.xiaomi.cariot.camera/     # 摄像头项目
│   ├── com.xiaomi.cariot.gesture/    # 手势识别项目
│   └── ...
├── miot-sdk/                         # MIOT SDK
├── micariot-ui-sdk/                  # 车载UI组件库
├── eslint-mihome-plugin/             # ESLint插件
└── bin/                              # 构建工具
```

### 开发流程

#### 1. 环境准备
```bash
# 安装工作空间依赖
npm install

# 进入具体项目
cd projects/com.xiaomi.cariot.camera
# 或
cd projects/com.xiaomi.cariot.gesture

# 安装项目依赖
npm install
```

#### 2. 启动开发
```bash
# 启动项目
npm start

# 或使用工作空间命令
npm run start com.xiaomi.cariot.camera
```

#### 3. 构建发布
```bash
# 发布项目
npm run publish com.xiaomi.cariot.camera
```

## 代码质量管理

### ESLint配置
- 使用自定义的 `eslint-mihome-plugin`
- 针对小米IoT插件的特殊规则
- 代码风格统一管理

### 版本管理
- 统一的版本号管理 (1.0.28)
- Git hooks 自动化检查
- 构建前代码质量验证

## 部署和发布

### 构建配置
- **bundle_command**: "plain-bundle"
- **min_sdk_api_level**: 10080
- **version_code**: 1

### 发布流程
1. 代码质量检查
2. 依赖安全扫描
3. 构建打包
4. 生成 .mpkg 文件
5. 上传到小米IoT平台

## 最佳实践

### 1. 代码组织
- 模块化设计
- 组件复用
- 工具类抽象

### 2. 性能优化
- 懒加载
- 内存管理
- 网络优化

### 3. 错误处理
- 统一错误处理机制
- 用户友好的错误提示
- 详细的日志记录

### 4. 车载适配
- 触控优化
- 界面简化
- 安全交互设计

## 维护和支持

### 版本兼容性
- 向后兼容旧版本固件
- 渐进式功能升级
- 优雅的降级处理

### 监控和诊断
- 实时日志收集
- 性能监控
- 错误追踪

### 文档维护
- API文档
- 用户手册
- 开发指南

## 未来规划

### 功能扩展
- 更多AI检测能力
- 增强的手势识别
- 更丰富的智能场景

### 技术升级
- React Native版本升级
- 新的车载标准支持
- 性能优化

### 生态集成
- 更多小米生态产品联动
- 第三方服务集成
- 开放API接口

---

## 快速开始

### 开发Camera项目
```bash
cd projects/com.xiaomi.cariot.camera
npm install
npm start
```

### 开发Gesture项目
```bash
cd projects/com.xiaomi.cariot.gesture
npm install
npm start
```

### 查看详细文档
- [Camera项目详细文档](./com.xiaomi.cariot.camera/功能文档.md)
- [Gesture项目详细文档](./com.xiaomi.cariot.gesture/功能文档.md)

---
*最后更新时间: 2024年12月*
