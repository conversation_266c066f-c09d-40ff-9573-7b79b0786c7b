<a name="module_miot/ui/CameraRender"></a>

## miot/ui/CameraRender
摄像机视频渲染组件

**Export**: public  
**Doc_name**: 常用UI组件  
**Doc_index**: 1  
**Doc_directory**: ui  
**Since**: 10031  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| videoCodec | <code>MISSCodec</code> | 接收视频的编码格式 默认：MISS_CODEC_VIDEO_H264 |
| audioCodec | <code>MISSCodec</code> | 对讲发送音频的编码格式 默认：MISS_CODEC_AUDIO_G711A |
| audioRecordSampleRate | <code>MISSSampleRate</code> | 对讲音频的 sample rate 默认：FLAG_AUDIO_SAMPLE_8K |
| audioRecordChannel | <code>MISSAudioChannel</code> | 对讲音频的 channel 默认：FLAG_AUDIO_CHANNEL_MONO |
| audioRecordDataBits | <code>MISSDataBits</code> | 对讲音频的 data bits 默认：FLAG_AUDIO_DATABITS_16 |
| audioRecordCodec | <code>MISSCodec</code> | 对讲音频的codecId，默认与audioCodec一样。 |
| videoRate | <code>number</code> | ios端录制视频时的帧率。 |
| maximumZoomScale | <code>number</code> | 最大缩放比例 默认2.0 ;only ios |
| minimumZoomScale | <code>number</code> | 最小缩放比例 默认1.0 ;only ios |
| scale | <code>number</code> | 缩放比例 默认1.0 ;only ios |
| useLenCorrent | <code>bool</code> | 是否开启畸变矫正 default true |
| correctRadius | <code>number</code> | 畸变矫正-radius default 1.1 |
| osdx | <code>number</code> | 畸变矫正-osdx default 0.0 |
| osdy | <code>number</code> | 畸变矫正-osdy default 0.0 |
| fullscreenState | <code>bool</code> | 是否是全屏状态 since 10033 |
| forceSoftDecode | <code>bool</code> | 强制软解 since 10033 |
| recordingVideoParam | <code>object</code> | only for android;限制录制视频时的分辨率，开始录制视频前，要调整分辨率到指定分辨率。 since 10041 {width:111, height:111，fps： 20}  fps指定录制视频时，对应的视频帧帧率，默认是20，不是20的需要手动指定；如果调整到固定帧率，依旧不work，则指定成-1，app端会按照收到视频帧的时间插入视频文件里。 |
| isFull | <code>boolean</code> | 画面是否填充满屏幕 |
| whiteBackground | <code>boolean</code> | 是否使用白色背景 @since 10047 |
| playRate | <code>number</code> | android端播放直播/回看时的帧率，默认是20   since 10048 |

**Example**  
```js
<CameraRenderView
    style={{ width: 300, height: 300, backgroundColor: '#ffffff'}}
    maximumZoomScale={3.0}
    videoCodec={MISSCodec.MISS_CODEC_VIDEO_H264}
    audioCodec={MISSCodec.MISS_CODEC_AUDIO_G711A}
    audioRecordSampleRate={MISSSampleRate.FLAG_AUDIO_SAMPLE_8K}
    audioRecordChannel={MISSAudioChannel.FLAG_AUDIO_CHANNEL_MONO}
    audioRecordDataBits={MISSDataBits.FLAG_AUDIO_DATABITS_16}
    fullscreenState={false}
    videoRate={15} >
/>
```

* [miot/ui/CameraRender](#module_miot/ui/CameraRender)
    * _instance_
        * [.startRender()](#module_miot/ui/CameraRender+startRender)
        * [.stopRender()](#module_miot/ui/CameraRender+stopRender)
        * [.startAudioPlay()](#module_miot/ui/CameraRender+startAudioPlay)
        * [.stopAudioPlay()](#module_miot/ui/CameraRender+stopAudioPlay)
        * [.startAudioRecord()](#module_miot/ui/CameraRender+startAudioRecord)
        * [.stopAudioRecord()](#module_miot/ui/CameraRender+stopAudioRecord)
        * [.hidesSurfaceView()](#module_miot/ui/CameraRender+hidesSurfaceView)
        * [.startRecord(存储位置filePath, timeCallBackName, did)](#module_miot/ui/CameraRender+startRecord)
        * [.stopRecord()](#module_miot/ui/CameraRender+stopRecord)
        * [.snapShot(存储位置filePath, did)](#module_miot/ui/CameraRender+snapShot)
    * _inner_
        * [~onVideoClick](#module_miot/ui/CameraRender..onVideoClick) : <code>func</code>
        * [~onScaleChanged](#module_miot/ui/CameraRender..onScaleChanged)
        * [~onPTZDirectionCtr](#module_miot/ui/CameraRender..onPTZDirectionCtr)
        * [~MISSCodec](#module_miot/ui/CameraRender..MISSCodec) : <code>object</code>
            * [.MISS_CODEC_VIDEO_H264](#module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_VIDEO_H264)
            * [.MISS_CODEC_VIDEO_H265](#module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_VIDEO_H265)
            * [.MISS_CODEC_AUDIO_G711U](#module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_AUDIO_G711U)
            * [.MISS_CODEC_AUDIO_G711A](#module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_AUDIO_G711A)
            * [.MISS_CODEC_AUDIO_AAC](#module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_AUDIO_AAC)
            * [.MISS_CODEC_AUDIO_PCM](#module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_AUDIO_PCM)
            * [.MISS_CODEC_AUDIO_OPUS](#module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_AUDIO_OPUS)
        * [~MISSSampleRate](#module_miot/ui/CameraRender..MISSSampleRate) : <code>object</code>
            * [.FLAG_AUDIO_SAMPLE_8K](#module_miot/ui/CameraRender..MISSSampleRate.FLAG_AUDIO_SAMPLE_8K)
            * [.FLAG_AUDIO_SAMPLE_16K](#module_miot/ui/CameraRender..MISSSampleRate.FLAG_AUDIO_SAMPLE_16K)
        * [~MISSDataBits](#module_miot/ui/CameraRender..MISSDataBits) : <code>object</code>
            * [.FLAG_AUDIO_DATABITS_8](#module_miot/ui/CameraRender..MISSDataBits.FLAG_AUDIO_DATABITS_8)
            * [.FLAG_AUDIO_DATABITS_16](#module_miot/ui/CameraRender..MISSDataBits.FLAG_AUDIO_DATABITS_16)
        * [~MISSAudioChannel](#module_miot/ui/CameraRender..MISSAudioChannel) : <code>object</code>
            * [.FLAG_AUDIO_CHANNEL_MONO](#module_miot/ui/CameraRender..MISSAudioChannel.FLAG_AUDIO_CHANNEL_MONO)
            * [.FLAG_AUDIO_CHANNEL_STERO](#module_miot/ui/CameraRender..MISSAudioChannel.FLAG_AUDIO_CHANNEL_STERO)


* * *

<a name="module_miot/ui/CameraRender+startRender"></a>

### miot/ui/CameraRender.startRender()
开始渲染视频

**Kind**: instance method of [<code>miot/ui/CameraRender</code>](#module_miot/ui/CameraRender)  

* * *

<a name="module_miot/ui/CameraRender+stopRender"></a>

### miot/ui/CameraRender.stopRender()
停止渲染视频

**Kind**: instance method of [<code>miot/ui/CameraRender</code>](#module_miot/ui/CameraRender)  

* * *

<a name="module_miot/ui/CameraRender+startAudioPlay"></a>

### miot/ui/CameraRender.startAudioPlay()
开始播放声音

**Kind**: instance method of [<code>miot/ui/CameraRender</code>](#module_miot/ui/CameraRender)  

* * *

<a name="module_miot/ui/CameraRender+stopAudioPlay"></a>

### miot/ui/CameraRender.stopAudioPlay()
停止播放声音

**Kind**: instance method of [<code>miot/ui/CameraRender</code>](#module_miot/ui/CameraRender)  

* * *

<a name="module_miot/ui/CameraRender+startAudioRecord"></a>

### miot/ui/CameraRender.startAudioRecord()
开始录制声音

**Kind**: instance method of [<code>miot/ui/CameraRender</code>](#module_miot/ui/CameraRender)  

* * *

<a name="module_miot/ui/CameraRender+stopAudioRecord"></a>

### miot/ui/CameraRender.stopAudioRecord()
停止录制声音

**Kind**: instance method of [<code>miot/ui/CameraRender</code>](#module_miot/ui/CameraRender)  

* * *

<a name="module_miot/ui/CameraRender+hidesSurfaceView"></a>

### miot/ui/CameraRender.hidesSurfaceView()
隐藏SurfaceView only for Android

**Kind**: instance method of [<code>miot/ui/CameraRender</code>](#module_miot/ui/CameraRender)  
**Since**: 10033  

* * *

<a name="module_miot/ui/CameraRender+startRecord"></a>

### miot/ui/CameraRender.startRecord(存储位置filePath, timeCallBackName, did)
开始录像

**Kind**: instance method of [<code>miot/ui/CameraRender</code>](#module_miot/ui/CameraRender)  

| Param | Type | Description |
| --- | --- | --- |
| 存储位置filePath | <code>string</code> | filePath必须是带 Host.file.storageBasePath前缀的path，native端会校验这个路径合法性。 |
| timeCallBackName | <code>string</code> | 录制时长回调 |
| did | <code>\*</code> |  |


* * *

<a name="module_miot/ui/CameraRender+stopRecord"></a>

### miot/ui/CameraRender.stopRecord()
停止录像

**Kind**: instance method of [<code>miot/ui/CameraRender</code>](#module_miot/ui/CameraRender)  

* * *

<a name="module_miot/ui/CameraRender+snapShot"></a>

### miot/ui/CameraRender.snapShot(存储位置filePath, did)
截屏

**Kind**: instance method of [<code>miot/ui/CameraRender</code>](#module_miot/ui/CameraRender)  

| Param | Type | Description |
| --- | --- | --- |
| 存储位置filePath | <code>string</code> | filePath必须是带 Host.file.storageBasePath前缀的path，native端会校验这个路径合法性。 |
| did | <code>\*</code> |  |


* * *

<a name="module_miot/ui/CameraRender..onVideoClick"></a>

### miot/ui/CameraRender~onVideoClick : <code>func</code>
用户单击回调

**Kind**: inner property of [<code>miot/ui/CameraRender</code>](#module_miot/ui/CameraRender)  

* * *

<a name="module_miot/ui/CameraRender..onScaleChanged"></a>

### miot/ui/CameraRender~onScaleChanged
缩放的回调

**Kind**: inner property of [<code>miot/ui/CameraRender</code>](#module_miot/ui/CameraRender)  

* * *

<a name="module_miot/ui/CameraRender..onPTZDirectionCtr"></a>

### miot/ui/CameraRender~onPTZDirectionCtr
提供给云台机，向左向右滑动view，让云台机跟着转动

**Kind**: inner property of [<code>miot/ui/CameraRender</code>](#module_miot/ui/CameraRender)  

* * *

<a name="module_miot/ui/CameraRender..MISSCodec"></a>

### miot/ui/CameraRender~MISSCodec : <code>object</code>
音视频codec

**Kind**: inner namespace of [<code>miot/ui/CameraRender</code>](#module_miot/ui/CameraRender)  

* [~MISSCodec](#module_miot/ui/CameraRender..MISSCodec) : <code>object</code>
    * [.MISS_CODEC_VIDEO_H264](#module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_VIDEO_H264)
    * [.MISS_CODEC_VIDEO_H265](#module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_VIDEO_H265)
    * [.MISS_CODEC_AUDIO_G711U](#module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_AUDIO_G711U)
    * [.MISS_CODEC_AUDIO_G711A](#module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_AUDIO_G711A)
    * [.MISS_CODEC_AUDIO_AAC](#module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_AUDIO_AAC)
    * [.MISS_CODEC_AUDIO_PCM](#module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_AUDIO_PCM)
    * [.MISS_CODEC_AUDIO_OPUS](#module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_AUDIO_OPUS)


* * *

<a name="module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_VIDEO_H264"></a>

#### MISSCodec.MISS\_CODEC\_VIDEO\_H264
H264

**Kind**: static constant of [<code>MISSCodec</code>](#module_miot/ui/CameraRender..MISSCodec)  

* * *

<a name="module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_VIDEO_H265"></a>

#### MISSCodec.MISS\_CODEC\_VIDEO\_H265
H265

**Kind**: static constant of [<code>MISSCodec</code>](#module_miot/ui/CameraRender..MISSCodec)  

* * *

<a name="module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_AUDIO_G711U"></a>

#### MISSCodec.MISS\_CODEC\_AUDIO\_G711U
G711u

**Kind**: static constant of [<code>MISSCodec</code>](#module_miot/ui/CameraRender..MISSCodec)  
**Since**: 10060  

* * *

<a name="module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_AUDIO_G711A"></a>

#### MISSCodec.MISS\_CODEC\_AUDIO\_G711A
G711

**Kind**: static constant of [<code>MISSCodec</code>](#module_miot/ui/CameraRender..MISSCodec)  

* * *

<a name="module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_AUDIO_AAC"></a>

#### MISSCodec.MISS\_CODEC\_AUDIO\_AAC
AAC

**Kind**: static constant of [<code>MISSCodec</code>](#module_miot/ui/CameraRender..MISSCodec)  

* * *

<a name="module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_AUDIO_PCM"></a>

#### MISSCodec.MISS\_CODEC\_AUDIO\_PCM
PCM

**Kind**: static constant of [<code>MISSCodec</code>](#module_miot/ui/CameraRender..MISSCodec)  
**Since**: 10047  

* * *

<a name="module_miot/ui/CameraRender..MISSCodec.MISS_CODEC_AUDIO_OPUS"></a>

#### MISSCodec.MISS\_CODEC\_AUDIO\_OPUS
OPUS

**Kind**: static constant of [<code>MISSCodec</code>](#module_miot/ui/CameraRender..MISSCodec)  
**Since**: 10062  

* * *

<a name="module_miot/ui/CameraRender..MISSSampleRate"></a>

### miot/ui/CameraRender~MISSSampleRate : <code>object</code>
音频sample rate

**Kind**: inner namespace of [<code>miot/ui/CameraRender</code>](#module_miot/ui/CameraRender)  

* [~MISSSampleRate](#module_miot/ui/CameraRender..MISSSampleRate) : <code>object</code>
    * [.FLAG_AUDIO_SAMPLE_8K](#module_miot/ui/CameraRender..MISSSampleRate.FLAG_AUDIO_SAMPLE_8K)
    * [.FLAG_AUDIO_SAMPLE_16K](#module_miot/ui/CameraRender..MISSSampleRate.FLAG_AUDIO_SAMPLE_16K)


* * *

<a name="module_miot/ui/CameraRender..MISSSampleRate.FLAG_AUDIO_SAMPLE_8K"></a>

#### MISSSampleRate.FLAG\_AUDIO\_SAMPLE\_8K
8000

**Kind**: static constant of [<code>MISSSampleRate</code>](#module_miot/ui/CameraRender..MISSSampleRate)  

* * *

<a name="module_miot/ui/CameraRender..MISSSampleRate.FLAG_AUDIO_SAMPLE_16K"></a>

#### MISSSampleRate.FLAG\_AUDIO\_SAMPLE\_16K
16000

**Kind**: static constant of [<code>MISSSampleRate</code>](#module_miot/ui/CameraRender..MISSSampleRate)  

* * *

<a name="module_miot/ui/CameraRender..MISSDataBits"></a>

### miot/ui/CameraRender~MISSDataBits : <code>object</code>
音频 data bits

**Kind**: inner namespace of [<code>miot/ui/CameraRender</code>](#module_miot/ui/CameraRender)  

* [~MISSDataBits](#module_miot/ui/CameraRender..MISSDataBits) : <code>object</code>
    * [.FLAG_AUDIO_DATABITS_8](#module_miot/ui/CameraRender..MISSDataBits.FLAG_AUDIO_DATABITS_8)
    * [.FLAG_AUDIO_DATABITS_16](#module_miot/ui/CameraRender..MISSDataBits.FLAG_AUDIO_DATABITS_16)


* * *

<a name="module_miot/ui/CameraRender..MISSDataBits.FLAG_AUDIO_DATABITS_8"></a>

#### MISSDataBits.FLAG\_AUDIO\_DATABITS\_8
8bits

**Kind**: static constant of [<code>MISSDataBits</code>](#module_miot/ui/CameraRender..MISSDataBits)  

* * *

<a name="module_miot/ui/CameraRender..MISSDataBits.FLAG_AUDIO_DATABITS_16"></a>

#### MISSDataBits.FLAG\_AUDIO\_DATABITS\_16
16bits

**Kind**: static constant of [<code>MISSDataBits</code>](#module_miot/ui/CameraRender..MISSDataBits)  

* * *

<a name="module_miot/ui/CameraRender..MISSAudioChannel"></a>

### miot/ui/CameraRender~MISSAudioChannel : <code>object</code>
音频 channel

**Kind**: inner namespace of [<code>miot/ui/CameraRender</code>](#module_miot/ui/CameraRender)  

* [~MISSAudioChannel](#module_miot/ui/CameraRender..MISSAudioChannel) : <code>object</code>
    * [.FLAG_AUDIO_CHANNEL_MONO](#module_miot/ui/CameraRender..MISSAudioChannel.FLAG_AUDIO_CHANNEL_MONO)
    * [.FLAG_AUDIO_CHANNEL_STERO](#module_miot/ui/CameraRender..MISSAudioChannel.FLAG_AUDIO_CHANNEL_STERO)


* * *

<a name="module_miot/ui/CameraRender..MISSAudioChannel.FLAG_AUDIO_CHANNEL_MONO"></a>

#### MISSAudioChannel.FLAG\_AUDIO\_CHANNEL\_MONO
单通道

**Kind**: static constant of [<code>MISSAudioChannel</code>](#module_miot/ui/CameraRender..MISSAudioChannel)  

* * *

<a name="module_miot/ui/CameraRender..MISSAudioChannel.FLAG_AUDIO_CHANNEL_STERO"></a>

#### MISSAudioChannel.FLAG\_AUDIO\_CHANNEL\_STERO
双通道

**Kind**: static constant of [<code>MISSAudioChannel</code>](#module_miot/ui/CameraRender..MISSAudioChannel)  

* * *

<a name="module_miot/ui/InputDialog"></a>

## miot/ui/InputDialog
输入对话框

**Export**: public  
**Doc_name**: 常用UI组件  
**Doc_index**: 1  
**Doc_directory**: ui  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| visible | <code>bool</code> | 是否可见 |
| cancelable | <code>bool</code> | 是否允许点击空白区域取消显示,仅限Android |
| singleLine | <code>bool</code> | 是否单行显示 |
| title | <code>string</code> | 标题 |
| message | <code>string</code> | 副标题，内容 |
| placeholder | <code>string</code> | 输入框placeholder，默认为空 |
| defaultText | <code>string</code> | 输入框默认初始值，默认为空 |
| cancel | <code>string</code> | 取消标题 |
| confirm | <code>string</code> | 确认标题 |
| onConfirm | <code>func</code> | 确认点击回调 |
| onCancel | <code>func</code> | 取消点击回调 |
| onDismiss | <code>func</code> | 对话框消失回调 |
| timeout | <code>number</code> | 超时自动隐藏，设置0或者不设置不会自动隐藏 |


* * *

<a name="module_miot/ui/LoadingDialog"></a>

## miot/ui/LoadingDialog
输入对话框

**Export**: public  
**Doc_name**: 常用UI组件  
**Doc_index**: 1  
**Doc_directory**: ui  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| visible | <code>bool</code> | 是否可见 |
| cancelable | <code>bool</code> | 是否允许点击空白区域取消显示,仅限Android |
| title | <code>string</code> | 标题 |
| message | <code>string</code> | 副标题，内容 |
| onDismiss | <code>func</code> | 对话框消失回调 |
| timeout | <code>number</code> | 超时自动隐藏，设置0或者不设置不会自动隐藏 |


* * *

<a name="module_miot/ui/MessageDialog"></a>

## miot/ui/MessageDialog
消息对话框

**Export**: public  
**Doc_name**: 常用UI组件  
**Doc_index**: 1  
**Doc_directory**: ui  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| visible | <code>bool</code> | 是否可见 |
| cancelable | <code>bool</code> | 是否允许点击空白区域取消显示,仅限Android |
| title | <code>string</code> | 标题 |
| message | <code>string</code> | 副标题，内容 |
| cancel | <code>string</code> | 取消标题 |
| confirm | <code>string</code> | 确认标题 |
| onConfirm | <code>func</code> | 确认点击回调 |
| onCancel | <code>func</code> | 取消点击回调 |
| onDismiss | <code>func</code> | 对话框消失回调 |


* * *

<a name="miot/ui/MultiChoseDialog"></a>

## miot/ui/MultiChoseDialog : <code>func</code>
回调会带一个 object 的参数，object.position为点击第几个条目，object.check 为选中状态

**Kind**: global variable  
**Export**: public  
**Doc_name**: 常用UI组件  
**Doc_index**: 1  
**Doc_directory**: ui  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| visible | <code>bool</code> | 是否可见 |
| cancelable | <code>bool</code> | 是否允许点击空白区域取消显示,仅限Android |
| title | <code>string</code> | 标题 |
| timeout | <code>number</code> | 超时自动隐藏，设置0或者不设置不会自动隐藏 |
| dataSource | <code>array</code> | 数据源，建议 array 的每个item 是一个 object，object 至少有展示条目名称、选中状态两个字段 |
| dataKey | <code>string</code> | 用于表示显示的字段名，dataSource每个条目显示名称 object 的字段名 |
| checkKey | <code>string</code> | 用于表示选中的字段名，dataSource每个条目选中状态 object 的字段名 |
| cancel | <code>string</code> | 取消标题 |
| confirm | <code>string</code> | 确认标题 |
| onConfirm | <code>func</code> | 确认点击回调 |
| onCancel | <code>func</code> | 取消点击回调 |
| onDismiss | <code>func</code> | 对话框消失回调 |
| onCheck | <code>func</code> | 某一行选中状态变更回调 |

**Example**  
```js
import {MultiChoseDialog} from 'miot/ui'
//dataSource列表数据中，dataKey所定义的值('dataKeyName') 对应项为展示的名称， 与checkKey所定义的值('checkKeyName') 对应的boolean值表示是否选中
<MultiChoseDialog 
dataSource = {[{'dataKeyName':'displayName1','checkKeyName':false}, {'dataKeyName':'displayName2','checkKeyName':true} ]}
dataKey = {'dataKeyName'}
checkKey = {'checkKeyName'}
/>
```
**Example**  
```js
某一行选中状态变更回调
```
**Example**  
```js
import {MultiChoseDialog} from 'miot/ui'
<MultiChoseDialog 
...
onCheck={res => {
 console.log('click at row ', res.position, ' with checked ', res.check)
}}
/>
```

* * *

<a name="module_miot/ui/ProgressDialog"></a>

## miot/ui/ProgressDialog
进度对话框，当进度到达max设置之后自动消失

**Export**: public  
**Doc_name**: 常用UI组件  
**Doc_index**: 1  
**Doc_directory**: ui  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| visible | <code>bool</code> | 是否可见 |
| cancelable | <code>bool</code> | 是否允许点击空白区域取消显示,仅限Android |
| title | <code>string</code> | 标题 |
| message | <code>string</code> | 副标题，内容 |
| max | <code>number</code> | 最大进度值 |
| progress | <code>number</code> | 当前进度值 |
| onDismiss | <code>func</code> | 对话框消失回调 |
| timeout | <code>number</code> | 超时自动隐藏，设置0或者不设置不会自动隐藏 |


* * *

<a name="module_miot/ui/"></a>

## miot/ui/
RTSP视频渲染组件

**Export**: public  
**Doc_name**: 常用UI组件  
**Doc_index**: 1  
**Doc_directory**: ui  
**Since**: 10036  

* * *

<a name="module_miot/ui/RobotMap"></a>

## miot/ui/RobotMap
扫地机器人地图组件

**Export**: public  
**Doc_name**: 常用UI组件  
**Doc_index**: 1  
**Doc_directory**: ui  
**Since**: 10029  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| imageSources | <code>[ &#x27;array&#x27; ].&lt;object&gt;</code> | 需要展示在地图上的图片 |
| imageSources.source | <code>source</code> | 图片resource，必传。 |
| imageSources.name | <code>string</code> | 图片名称，后续更新方便确定图片,必传 |
| imageSources.bgSource | <code>source</code> | 图片背景图，类似扫地机清扫时周边的那一圈，如果传了这个，一定会有动画 |
| imageSources.position | <code>string</code> | 图片相对位置，如果不传，则保持上一次的位置不动，但是首次必传 |
| imageSources.size | <code>string</code> | 图片在view中展示的大小，如果不传，则保持上一次的大小不动，同样首次必传 |
| imageSources.rotation | <code>number</code> | 图片的逆时针旋转角度 0-360，可不传 images=[{   image:url,   bgImage:url1,   position:{127,125},   size:{10,10},   rotation:180,   name:charge }] |
| mapStyle.wallColor | <code>string</code> | 文字颜色 默认值 #000000 |
| mapStyle.floorColor | <code>string</code> | 文字颜色 默认值 #000000 |
| mapStyle.lineColor | <code>string</code> | 文字颜色 默认值 #000000 |

**Example**  
```js
<MHRobotMap
    style={{ width: 300, height: 300, backgroundColor: '#ffffff'}}
    mapStyle={{wallColor: '#75c4fa',floorColor: '#468ad6',lineColor: '#9bc4e3'}}
    images={[{image:require(''),position:{125,125},size:{5,5},rotation:0,name:'charge'}]}
/>
```

* * *

<a name="module_miot/ui/"></a>

## miot/ui/
摇杆组件，仅供追觅皮皮灯设备使用

**Export**: public  
**Doc_name**: 常用UI组件  
**Doc_index**: 1  
**Doc_directory**: ui  
**Since**: 10074  

* * *

<a name="module_miot/ui/SingleChoseDialog"></a>

## miot/ui/SingleChoseDialog
单选对话框

**Export**: public  
**Doc_name**: 常用UI组件  
**Doc_index**: 1  
**Doc_directory**: ui  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| visible | <code>bool</code> | 是否可见 |
| cancelable | <code>bool</code> | 是否允许点击空白区域取消显示,仅限Android |
| title | <code>string</code> | 标题 |
| timeout | <code>number</code> | 超时自动隐藏，设置0或者不设置不会自动隐藏 |
| dataSource | <code>[ &#x27;array&#x27; ].&lt;string&gt;</code> | 数据源 |
| check | <code>number</code> | 选中第几个数据源 |
| cancel | <code>string</code> | 取消标题 |
| confirm | <code>string</code> | 确认标题 |
| onConfirm | <code>func</code> | 确认点击回调 |
| onCancel | <code>func</code> | 取消点击回调 |
| onDismiss | <code>func</code> | 对话框消失回调 |
| onCheck | <code>func</code> | 某一行选中状态变更回调 |

**Example**  
```js
import {SingleChoseDialog} from 'miot/ui'
<SingleChoseDialog 
dataSource={['message0', 'message1', 'message2', 'message3', 'message4', 'message5', 'message6']}
...
/>
```

* * *

<a name="module_miot/ui/TitleBarBlack"></a>

## ~~miot/ui/TitleBarBlack~~
***Deprecated***

黑色标题栏

**Export**: public  
**Doc_name**: 常用UI组件  
**Doc_index**: 1  
**Doc_directory**: ui  
**Properties**

| Name | Description |
| --- | --- |
| leftTextStyle | 左侧文字样式，和 leftText 一起使用，不设置使用米家默认值 |
| leftText | 左侧文字 |
| onPressLeft | 左侧点击事件，设置了才显示左侧文字或图片，如果设置了leftText则显示设置的文字，否则显示默认的返回按钮。 |
| onPressLeft2 | 左侧的第二个点击事件，设置了才显示默认的关闭按钮， |
| rightTextStyle | 右侧文字样式，和 rightText 一起使用，不设置使用米家默认值 |
| rightText | 右侧文字 |
| onPressRight | 右侧点击事件，设置了才显示右侧文字或图片，如果设置了 rightText 则显示设置的文字，否则显示默认的更多按钮。 |
| onPressRight2 | 右侧的第二个点击事件，设置了才显示默认的分享按钮 |
| title | 中间的标题 |
| subTitle | 中间的子标题 |
| onPressTitle | 点击标题的事件 |
| showDot | 是否显示右侧更多按钮的空点 |


* * *

<a name="module_miot/ui/TitleBarWhite"></a>

## ~~miot/ui/TitleBarWhite~~
***Deprecated***

白色标题栏

**Export**: public  
**Doc_name**: 常用UI组件  
**Doc_index**: 1  
**Doc_directory**: ui  
**Properties**

| Name | Description |
| --- | --- |
| leftTextStyle | 左侧文字样式，和 leftText 一起使用，不设置使用米家默认值 |
| leftText | 左侧文字 |
| onPressLeft | 左侧点击事件，设置了才显示左侧文字或图片，如果设置了leftText则显示设置的文字，否则显示默认的返回按钮。 |
| onPressLeft2 | 左侧的第二个点击事件，设置了才显示默认的关闭按钮， |
| rightTextStyle | 右侧文字样式，和 rightText 一起使用，不设置使用米家默认值 |
| rightText | 右侧文字 |
| onPressRight | 右侧点击事件，设置了才显示右侧文字或图片，如果设置了 rightText 则显示设置的文字，否则显示默认的更多按钮。 |
| onPressRight2 | 右侧的第二个点击事件，设置了才显示默认的分享按钮 |
| title | 中间的标题 |
| subTitle | 中间的子标题 |
| onPressTitle | 点击标题的事件 |
| showDot | 是否显示右侧更多按钮的空点 |


* * *

