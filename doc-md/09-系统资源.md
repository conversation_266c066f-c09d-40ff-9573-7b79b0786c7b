<a name="module_miot/resources"></a>

## miot/resources
系统提供的静态资源, 包括图片, 文字, 基础 styleSheet css 等等

**Export**: public  
**Doc_name**: 系统资源  
**Doc_index**: 9  
**Doc_directory**: sdk  
**Example**  
```js
import res, {Language} from "miot/resources"

res.logo
...

console.log(res.systemStrings.mijia)
console.log(res.getSystemString('mijia'))

res.registerStrings({
  zh:{t1:"测试"},
  en:{t1:"test"}
})

console.log(res.strings.t1)
console.log(res.getString('t1'))

res.setLanguage(Language.zh_hk)

console.log(res.getLanaguage())

function createI18n(langStrings, defaultLanguage){}
```

* [miot/resources](#module_miot/resources)
    * _static_
        * [.logo](#module_miot/resources.logo)
        * [.systemStrings](#module_miot/resources.systemStrings)
        * [.strings](#module_miot/resources.strings)
        * [.registerStrings(langStrings)](#module_miot/resources.registerStrings)
        * [.getLanguage()](#module_miot/resources.getLanguage)
        * [.setLanguage(lang)](#module_miot/resources.setLanguage)
        * [.getSystemString(key, ...params)](#module_miot/resources.getSystemString) ⇒ <code>string</code>
        * [.getString(key, ...params)](#module_miot/resources.getString) ⇒ <code>string</code>
        * [.createI18n(langStrings, defaultLanguage)](#module_miot/resources.createI18n)
    * _inner_
        * [~Language](#module_miot/resources..Language) : <code>object</code>
            * [.zh](#module_miot/resources..Language.zh)
            * [.zh_tw](#module_miot/resources..Language.zh_tw)
            * [.zh_hk](#module_miot/resources..Language.zh_hk)
            * [.zh_bo](#module_miot/resources..Language.zh_bo)
            * [.en](#module_miot/resources..Language.en)
            * [.es](#module_miot/resources..Language.es)
            * [.ko](#module_miot/resources..Language.ko)
            * [.ru](#module_miot/resources..Language.ru)
            * [.it](#module_miot/resources..Language.it)
            * [.fr](#module_miot/resources..Language.fr)
            * [.de](#module_miot/resources..Language.de)
            * [.id](#module_miot/resources..Language.id)
            * [.pl](#module_miot/resources..Language.pl)
            * [.vi](#module_miot/resources..Language.vi)
            * [.ja](#module_miot/resources..Language.ja)
            * [.th](#module_miot/resources..Language.th)
            * [.pt](#module_miot/resources..Language.pt)
            * [.nl](#module_miot/resources..Language.nl)
            * [.ar](#module_miot/resources..Language.ar)
            * [.tr](#module_miot/resources..Language.tr)
            * [.he](#module_miot/resources..Language.he)


* * *

<a name="module_miot/resources.logo"></a>

### miot/resources.logo
米家标志

**Kind**: static property of [<code>miot/resources</code>](#module_miot/resources)  

* * *

<a name="module_miot/resources.systemStrings"></a>

### miot/resources.systemStrings
获取系统字符串

**Kind**: static property of [<code>miot/resources</code>](#module_miot/resources)  
**Example**  
```js
console.log(res.systemStrings.mijia)
```

* * *

<a name="module_miot/resources.strings"></a>

### miot/resources.strings
获取用户自定义字符串

**Kind**: static property of [<code>miot/resources</code>](#module_miot/resources)  

* * *

<a name="module_miot/resources.registerStrings"></a>

### miot/resources.registerStrings(langStrings)
注册多语言

**Kind**: static method of [<code>miot/resources</code>](#module_miot/resources)  

| Param | Type |
| --- | --- |
| langStrings | <code>json</code> | 

**Example**  
```js
import res from 'miot/resources'
     res.registerStrings({
        zh:{
            t1:"测试字符串",
            t2:"数值为{1}",
            t3:["从{1}到{2}", [0, "非法数据"], [1, "错误数据"], [2, "从 二 到 {2}"], [(v1,v2)=>v1>100, "太多了"]],
            t4:{
                t5:()=>"好的",
                t6:["最多{1}"],
                t7:(a,b,c)=>`${a}|${b}|${c}`,
                t8:"你好"
            }
        },
        en:{
            t1:"test strigns",
            t2:"value is {1}",
            t3:["from {1} to {2}", [0, "invalid data"], [1, "wrong value"], [3, "from three to {2}"], [v1=>v1>100, "too more"]],
            t4:{
                t5:[()=>"good"],
                t6:"{1} at most",
                t7:(a,b,c)=>`${a}/${b}/${c}`
            }
        }
     });

    //style recommend
    console.log(res.strings.t1);
    console.log(res.strings.t2(123));
    console.log(res.strings.t3(0, 1));
    console.log(res.strings.t3(1, 2));
    console.log(res.strings.t3(2, 200));
    console.log(res.strings.t3(100, 3000));
    console.log(res.strings.t3(101, 500));
    console.log(res.strings.t4.t5());
    console.log(res.strings.t4.t6(20));
    console.log(res.strings.t4.t7(5,6,7));
    console.log(res.strings.t4.t8);

    //style traditional
    console.log(res.getString('t1');
    console.log(res.getString('t2',123));
    console.log(res.getString('t3', 0, 1));
    console.log(res.getString('t3', 1, 2));
    console.log(res.getString('t3', 2, 200));
    console.log(res.getString('t3', 100, 3000));
    console.log(res.getString('t3', 101, 500));
    console.log(res.getString('t4.t5');
    console.log(res.getString('t4.t6', 20));
    console.log(res.getString('t4.t7', 5,6,7));
    console.log(res.getString('t4.t8');
```

* * *

<a name="module_miot/resources.getLanguage"></a>

### miot/resources.getLanguage()
获取当前使用中的语言, 缺省为Host.locale.language

**Kind**: static method of [<code>miot/resources</code>](#module_miot/resources)  

* * *

<a name="module_miot/resources.setLanguage"></a>

### miot/resources.setLanguage(lang)
设置当前语言, 如果 lang 为 false 或 null, 则恢复为Host.locale.language

**Kind**: static method of [<code>miot/resources</code>](#module_miot/resources)  

| Param | Type |
| --- | --- |
| lang | <code>Language</code> | 


* * *

<a name="module_miot/resources.getSystemString"></a>

### miot/resources.getSystemString(key, ...params) ⇒ <code>string</code>
根据主键名获取系统的国际化字符串

**Kind**: static method of [<code>miot/resources</code>](#module_miot/resources)  

| Param | Type | Description |
| --- | --- | --- |
| key | <code>string</code> | 主键名 |
| ...params | <code>any</code> | 参数 |

**Example**  
```js
res.getSystemString('mijia')
```

* * *

<a name="module_miot/resources.getString"></a>

### miot/resources.getString(key, ...params) ⇒ <code>string</code>
根据主键名获取用户自定义的国际化字符串

**Kind**: static method of [<code>miot/resources</code>](#module_miot/resources)  

| Param | Type | Description |
| --- | --- | --- |
| key | <code>string</code> | 主键名 |
| ...params | <code>any</code> | 参数 |

**Example**  
```js
res.getString('t1.tx', 1)
   res.getString('t2')
```

* * *

<a name="module_miot/resources.createI18n"></a>

### miot/resources.createI18n(langStrings, defaultLanguage)
创建本地化字符串

**Kind**: static method of [<code>miot/resources</code>](#module_miot/resources)  

| Param | Type | Description |
| --- | --- | --- |
| langStrings | <code>json</code> | 多语言字符串 |
| defaultLanguage | <code>Language</code> | 默认语言 |

**Example**  
```js
const i18n = res.createI18n({
    zh:{test:"测试"},
    en:{test:"test"}
}, Language.zh)

...
console.log(i18n.strings.test) //> 测试
i18n.language = Language.en;
console.log(i18n.strings.test) //> test
i18n.language = null;
console.log(i18n.strings.test) //> 测试
```

* * *

<a name="module_miot/resources..Language"></a>

### miot/resources~Language : <code>object</code>
常用语言类型

**Kind**: inner namespace of [<code>miot/resources</code>](#module_miot/resources)  

* [~Language](#module_miot/resources..Language) : <code>object</code>
    * [.zh](#module_miot/resources..Language.zh)
    * [.zh_tw](#module_miot/resources..Language.zh_tw)
    * [.zh_hk](#module_miot/resources..Language.zh_hk)
    * [.zh_bo](#module_miot/resources..Language.zh_bo)
    * [.en](#module_miot/resources..Language.en)
    * [.es](#module_miot/resources..Language.es)
    * [.ko](#module_miot/resources..Language.ko)
    * [.ru](#module_miot/resources..Language.ru)
    * [.it](#module_miot/resources..Language.it)
    * [.fr](#module_miot/resources..Language.fr)
    * [.de](#module_miot/resources..Language.de)
    * [.id](#module_miot/resources..Language.id)
    * [.pl](#module_miot/resources..Language.pl)
    * [.vi](#module_miot/resources..Language.vi)
    * [.ja](#module_miot/resources..Language.ja)
    * [.th](#module_miot/resources..Language.th)
    * [.pt](#module_miot/resources..Language.pt)
    * [.nl](#module_miot/resources..Language.nl)
    * [.ar](#module_miot/resources..Language.ar)
    * [.tr](#module_miot/resources..Language.tr)
    * [.he](#module_miot/resources..Language.he)


* * *

<a name="module_miot/resources..Language.zh"></a>

#### Language.zh
中文

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.zh_tw"></a>

#### Language.zh\_tw
繁体中文(台湾)

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.zh_hk"></a>

#### Language.zh\_hk
繁体中文(香港)

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.zh_bo"></a>

#### Language.zh\_bo
藏语

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.en"></a>

#### Language.en
英语

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.es"></a>

#### Language.es
西班牙语

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.ko"></a>

#### Language.ko
朝鲜语

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.ru"></a>

#### Language.ru
俄语

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.it"></a>

#### Language.it
意大利

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.fr"></a>

#### Language.fr
法语

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.de"></a>

#### Language.de
德语

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.id"></a>

#### Language.id
印尼

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.pl"></a>

#### Language.pl
波兰

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.vi"></a>

#### Language.vi
越南

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.ja"></a>

#### Language.ja
日语

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.th"></a>

#### Language.th
傣语

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.pt"></a>

#### Language.pt
葡萄牙语

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.nl"></a>

#### Language.nl
荷兰语

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.ar"></a>

#### Language.ar
阿拉伯语

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.tr"></a>

#### Language.tr
土耳其语

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

<a name="module_miot/resources..Language.he"></a>

#### Language.he
希伯来语

**Kind**: static constant of [<code>Language</code>](#module_miot/resources..Language)  

* * *

