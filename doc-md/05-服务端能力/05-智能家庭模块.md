<a name="module_miot/service/smarthome"></a>

## miot/service/smarthome
智能家庭 API

**Export**: public  
**Doc_name**: 智能家庭模块  
**Doc_index**: 5  
**Doc_directory**: service  

* [miot/service/smarthome](#module_miot/service/smarthome)
    * [~ISmartHome](#module_miot/service/smarthome..ISmartHome)
        * ~~[.getUserInfo(uid)](#module_miot/service/smarthome..ISmartHome+getUserInfo) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;UserInfo&gt;</code>~~
        * ~~[.getUserInfoList(uids)](#module_miot/service/smarthome..ISmartHome+getUserInfoList) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;object&gt;&gt;</code>~~
        * [.reportGPSInfo(deviceID, gpsInfo)](#module_miot/service/smarthome..ISmartHome+reportGPSInfo) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
        * [.getWeatherInfo(deviceID)](#module_miot/service/smarthome..ISmartHome+getWeatherInfo) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;WeatherInfo&gt;</code>
        * [.checkDeviceVersion(设备did, pid)](#module_miot/service/smarthome..ISmartHome+checkDeviceVersion) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;DeviceVersion&gt;</code>
        * [.getAvailableFirmwareForDids(deviceIDs)](#module_miot/service/smarthome..ISmartHome+getAvailableFirmwareForDids) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
        * ~~[.getLatestVersion(model)](#module_miot/service/smarthome..ISmartHome+getLatestVersion) ⇒ <code>Promise</code>~~
        * [.getLatestVersionV2(did)](#module_miot/service/smarthome..ISmartHome+getLatestVersionV2)
        * [._getFirmwareOriginalUpdateInfo(did)](#module_miot/service/smarthome..ISmartHome+_getFirmwareOriginalUpdateInfo)
        * [._updateDevice(did, pid)](#module_miot/service/smarthome..ISmartHome+_updateDevice)
        * [.getAutoUpgradeConfig(did)](#module_miot/service/smarthome..ISmartHome+getAutoUpgradeConfig) ⇒
        * [._getFirmwareReleaseVersionHistory(did)](#module_miot/service/smarthome..ISmartHome+_getFirmwareReleaseVersionHistory)
        * [.getFirmwareUpdateInfo(did)](#module_miot/service/smarthome..ISmartHome+getFirmwareUpdateInfo)
        * [.reportLog(model, log)](#module_miot/service/smarthome..ISmartHome+reportLog) ⇒ <code>void</code>
        * [.reportSDKFileLog()](#module_miot/service/smarthome..ISmartHome+reportSDKFileLog)
        * [.reportRecords(deviceID, records)](#module_miot/service/smarthome..ISmartHome+reportRecords)
        * ~~[.deviceSetExtraData(params)](#module_miot/service/smarthome..ISmartHome+deviceSetExtraData) ⇒ <code>Promise</code>~~
        * ~~[.getDevicesConfig(params)](#module_miot/service/smarthome..ISmartHome+getDevicesConfig) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>~~
        * ~~[.delDevicesConfig(params)](#module_miot/service/smarthome..ISmartHome+delDevicesConfig) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>~~
        * ~~[.getDeviceTimeZone(did)](#module_miot/service/smarthome..ISmartHome+getDeviceTimeZone)~~
        * [.getUserStatistics(params)](#module_miot/service/smarthome..ISmartHome+getUserStatistics) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
        * [.getVoiceCtrlDevices(deviceID)](#module_miot/service/smarthome..ISmartHome+getVoiceCtrlDevices) ⇒ <code>Promise</code>
        * [.getAiServiceProxy(params)](#module_miot/service/smarthome..ISmartHome+getAiServiceProxy) ⇒ <code>Promise</code>
        * ~~[.getDeviceSetting(params)](#module_miot/service/smarthome..ISmartHome+getDeviceSetting) ⇒ <code>Promise</code>~~
        * [.getDeviceSettingV2(params)](#module_miot/service/smarthome..ISmartHome+getDeviceSettingV2) ⇒ <code>Promise</code>
        * [.setDeviceSetting(params)](#module_miot/service/smarthome..ISmartHome+setDeviceSetting) ⇒ <code>Promise</code>
        * [.delDeviceSetting(params)](#module_miot/service/smarthome..ISmartHome+delDeviceSetting) ⇒ <code>Promise</code>
        * [.setDeviceData(params)](#module_miot/service/smarthome..ISmartHome+setDeviceData) ⇒ <code>Promise</code>
        * [.getDeviceData(params)](#module_miot/service/smarthome..ISmartHome+getDeviceData) ⇒ <code>Promise</code>
        * [.getDeviceDataV2(params)](#module_miot/service/smarthome..ISmartHome+getDeviceDataV2) ⇒ <code>Promise</code>
        * [.delDeviceData(params)](#module_miot/service/smarthome..ISmartHome+delDeviceData)
        * [.getUserDeviceLog(params)](#module_miot/service/smarthome..ISmartHome+getUserDeviceLog)
        * [.getUserColl(params)](#module_miot/service/smarthome..ISmartHome+getUserColl) ⇒ <code>Promise</code>
        * [.setUserColl(params)](#module_miot/service/smarthome..ISmartHome+setUserColl) ⇒ <code>Promise</code>
        * [.editUserColl(params)](#module_miot/service/smarthome..ISmartHome+editUserColl) ⇒ <code>Promise</code>
        * [.delUserColl(params)](#module_miot/service/smarthome..ISmartHome+delUserColl) ⇒ <code>Promise</code>
        * [.getMapfileUrl(params)](#module_miot/service/smarthome..ISmartHome+getMapfileUrl) ⇒ <code>Promise</code>
        * [.getRobomapUrl(arams)](#module_miot/service/smarthome..ISmartHome+getRobomapUrl) ⇒ <code>Promise</code>
        * [.delUsermap(params)](#module_miot/service/smarthome..ISmartHome+delUsermap) ⇒ <code>Promise</code>
        * [.getHomeDevice(params)](#module_miot/service/smarthome..ISmartHome+getHomeDevice) ⇒ <code>Promise</code>
        * [.getAppConfig(params)](#module_miot/service/smarthome..ISmartHome+getAppConfig)
        * ~~[.getAppConfigV2(params)](#module_miot/service/smarthome..ISmartHome+getAppConfigV2)~~
        * [.getCountry(params)](#module_miot/service/smarthome..ISmartHome+getCountry) ⇒ <code>Promise</code>
        * [.getBleLockBindInfo(params)](#module_miot/service/smarthome..ISmartHome+getBleLockBindInfo) ⇒ <code>Promise</code>
        * [.batchGetDeviceDatas(params)](#module_miot/service/smarthome..ISmartHome+batchGetDeviceDatas) ⇒ <code>Promise</code>
        * [.batchSetDeviceDatas(params)](#module_miot/service/smarthome..ISmartHome+batchSetDeviceDatas)
        * [.setDeviceProp(params)](#module_miot/service/smarthome..ISmartHome+setDeviceProp)
        * [.getThirdConfig(params)](#module_miot/service/smarthome..ISmartHome+getThirdConfig) ⇒ <code>Promise</code>
        * [.thirdSyncCall(params)](#module_miot/service/smarthome..ISmartHome+thirdSyncCall) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
        * [.callThirdPartyAPI(params)](#module_miot/service/smarthome..ISmartHome+callThirdPartyAPI) ⇒ <code>Promise</code>
        * [.getMiWatchConfig()](#module_miot/service/smarthome..ISmartHome+getMiWatchConfig) ⇒ <code>Promise</code>
        * [.getUserDeviceAuth(string})](#module_miot/service/smarthome..ISmartHome+getUserDeviceAuth) ⇒ <code>Promise</code>
        * [.getInterimFileUrl(params)](#module_miot/service/smarthome..ISmartHome+getInterimFileUrl) ⇒ <code>Promise</code>
        * ~~[.getFileUrl(params)](#module_miot/service/smarthome..ISmartHome+getFileUrl) ⇒ <code>Promise</code>~~
        * [.getUserDeviceDataTab(params)](#module_miot/service/smarthome..ISmartHome+getUserDeviceDataTab) ⇒ <code>Promise</code>
        * [.rangeGetOpenConfig(params)](#module_miot/service/smarthome..ISmartHome+rangeGetOpenConfig) ⇒ <code>Promise</code>
        * [.bindNFCCard(params)](#module_miot/service/smarthome..ISmartHome+bindNFCCard)
        * [.getNFCCard(params)](#module_miot/service/smarthome..ISmartHome+getNFCCard) ⇒ <code>json</code>
        * [.insertunmodel(params)](#module_miot/service/smarthome..ISmartHome+insertunmodel)
        * [.getIDFY(params)](#module_miot/service/smarthome..ISmartHome+getIDFY)
        * [.editIDFY(params)](#module_miot/service/smarthome..ISmartHome+editIDFY)
        * ~~[.getRangeOpenConfig(params)](#module_miot/service/smarthome..ISmartHome+getRangeOpenConfig)~~
        * [.createMember(type, info)](#module_miot/service/smarthome..ISmartHome+createMember)
        * [.updateMember(type, member_id, info)](#module_miot/service/smarthome..ISmartHome+updateMember)
        * [.deleteMember(type, member_id)](#module_miot/service/smarthome..ISmartHome+deleteMember)
        * [.loadMembers(type)](#module_miot/service/smarthome..ISmartHome+loadMembers)
        * [.setUserPDData(params)](#module_miot/service/smarthome..ISmartHome+setUserPDData)
        * [.getUserPDData(params)](#module_miot/service/smarthome..ISmartHome+getUserPDData)
        * [.getDeviceDataRaw(params)](#module_miot/service/smarthome..ISmartHome+getDeviceDataRaw)
        * [.createSeSession(params)](#module_miot/service/smarthome..ISmartHome+createSeSession)
        * [.replaceSEISDkey(params)](#module_miot/service/smarthome..ISmartHome+replaceSEISDkey)
        * [.resetLockPrimaryKey(params)](#module_miot/service/smarthome..ISmartHome+resetLockPrimaryKey)
        * [.handleSEResponse(params)](#module_miot/service/smarthome..ISmartHome+handleSEResponse)
        * [.reportBLEDeviceInfo(params)](#module_miot/service/smarthome..ISmartHome+reportBLEDeviceInfo)
        * [.reportEvent(eventName, params)](#module_miot/service/smarthome..ISmartHome+reportEvent)
        * [.addStat(key, value, extra)](#module_miot/service/smarthome..ISmartHome+addStat)
        * [.getMultiSwitchName(did)](#module_miot/service/smarthome..ISmartHome+getMultiSwitchName) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
        * [.createGroupDevice(name, member_dids, member_tags)](#module_miot/service/smarthome..ISmartHome+createGroupDevice) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
        * [.getVirtualGroupSubDevices(group_did)](#module_miot/service/smarthome..ISmartHome+getVirtualGroupSubDevices) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code>
        * [.getVirtualGroupSubDevicesTags(group_did)](#module_miot/service/smarthome..ISmartHome+getVirtualGroupSubDevicesTags) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code>
        * [.getVirtualDeviceCombineStatus(param)](#module_miot/service/smarthome..ISmartHome+getVirtualDeviceCombineStatus) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
        * [.setTempUnit()](#module_miot/service/smarthome..ISmartHome+setTempUnit) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code>
        * [.getTempUnit()](#module_miot/service/smarthome..ISmartHome+getTempUnit) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code>
        * [.checkFirmwareAutoUpgradeOpen(aDevId)](#module_miot/service/smarthome..ISmartHome+checkFirmwareAutoUpgradeOpen) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;{code:xx, data:xx}&gt;</code>
        * [.setFirmwareAutoUpgradeSwitch(aOpen, aDevId)](#module_miot/service/smarthome..ISmartHome+setFirmwareAutoUpgradeSwitch) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;{code:xx, data:xx}&gt;</code>
        * [.getCustomPluginInfo(model)](#module_miot/service/smarthome..ISmartHome+getCustomPluginInfo) ⇒ <code>object</code>
        * [.getPrivacyChanges(version:)](#module_miot/service/smarthome..ISmartHome+getPrivacyChanges) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
        * [.requestAuthForAlexaVoiceService(params)](#module_miot/service/smarthome..ISmartHome+requestAuthForAlexaVoiceService)
        * [.getHomeList(params)](#module_miot/service/smarthome..ISmartHome+getHomeList) ⇒ <code>object</code>
        * [.getCarManualSceneData(params)](#module_miot/service/smarthome..ISmartHome+getCarManualSceneData) ⇒ <code>Promise</code>
        * [.updateCarManualSceneData(params)](#module_miot/service/smarthome..ISmartHome+updateCarManualSceneData) ⇒ <code>Promise</code>
        * [.getConsumableDetails(param)](#module_miot/service/smarthome..ISmartHome+getConsumableDetails) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
    * [~MemberType](#module_miot/service/smarthome..MemberType) : <code>object</code>
        * [.Person](#module_miot/service/smarthome..MemberType.Person)
        * [.Pet](#module_miot/service/smarthome..MemberType.Pet)
    * [~UserInfo](#module_miot/service/smarthome..UserInfo) : <code>Object</code>
    * [~GPSInfo](#module_miot/service/smarthome..GPSInfo)
    * [~WeatherInfo](#module_miot/service/smarthome..WeatherInfo)
    * [~DeviceVersion](#module_miot/service/smarthome..DeviceVersion)
    * [~OTAState](#module_miot/service/smarthome..OTAState)
    * [~MemberPet](#module_miot/service/smarthome..MemberPet)
    * [~MemberPerson](#module_miot/service/smarthome..MemberPerson)


* * *

<a name="module_miot/service/smarthome..ISmartHome"></a>

### miot/service/smarthome~ISmartHome
**Kind**: inner class of [<code>miot/service/smarthome</code>](#module_miot/service/smarthome)  
**Export**:   

* [~ISmartHome](#module_miot/service/smarthome..ISmartHome)
    * ~~[.getUserInfo(uid)](#module_miot/service/smarthome..ISmartHome+getUserInfo) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;UserInfo&gt;</code>~~
    * ~~[.getUserInfoList(uids)](#module_miot/service/smarthome..ISmartHome+getUserInfoList) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;object&gt;&gt;</code>~~
    * [.reportGPSInfo(deviceID, gpsInfo)](#module_miot/service/smarthome..ISmartHome+reportGPSInfo) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
    * [.getWeatherInfo(deviceID)](#module_miot/service/smarthome..ISmartHome+getWeatherInfo) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;WeatherInfo&gt;</code>
    * [.checkDeviceVersion(设备did, pid)](#module_miot/service/smarthome..ISmartHome+checkDeviceVersion) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;DeviceVersion&gt;</code>
    * [.getAvailableFirmwareForDids(deviceIDs)](#module_miot/service/smarthome..ISmartHome+getAvailableFirmwareForDids) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
    * ~~[.getLatestVersion(model)](#module_miot/service/smarthome..ISmartHome+getLatestVersion) ⇒ <code>Promise</code>~~
    * [.getLatestVersionV2(did)](#module_miot/service/smarthome..ISmartHome+getLatestVersionV2)
    * [._getFirmwareOriginalUpdateInfo(did)](#module_miot/service/smarthome..ISmartHome+_getFirmwareOriginalUpdateInfo)
    * [._updateDevice(did, pid)](#module_miot/service/smarthome..ISmartHome+_updateDevice)
    * [.getAutoUpgradeConfig(did)](#module_miot/service/smarthome..ISmartHome+getAutoUpgradeConfig) ⇒
    * [._getFirmwareReleaseVersionHistory(did)](#module_miot/service/smarthome..ISmartHome+_getFirmwareReleaseVersionHistory)
    * [.getFirmwareUpdateInfo(did)](#module_miot/service/smarthome..ISmartHome+getFirmwareUpdateInfo)
    * [.reportLog(model, log)](#module_miot/service/smarthome..ISmartHome+reportLog) ⇒ <code>void</code>
    * [.reportSDKFileLog()](#module_miot/service/smarthome..ISmartHome+reportSDKFileLog)
    * [.reportRecords(deviceID, records)](#module_miot/service/smarthome..ISmartHome+reportRecords)
    * ~~[.deviceSetExtraData(params)](#module_miot/service/smarthome..ISmartHome+deviceSetExtraData) ⇒ <code>Promise</code>~~
    * ~~[.getDevicesConfig(params)](#module_miot/service/smarthome..ISmartHome+getDevicesConfig) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>~~
    * ~~[.delDevicesConfig(params)](#module_miot/service/smarthome..ISmartHome+delDevicesConfig) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>~~
    * ~~[.getDeviceTimeZone(did)](#module_miot/service/smarthome..ISmartHome+getDeviceTimeZone)~~
    * [.getUserStatistics(params)](#module_miot/service/smarthome..ISmartHome+getUserStatistics) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
    * [.getVoiceCtrlDevices(deviceID)](#module_miot/service/smarthome..ISmartHome+getVoiceCtrlDevices) ⇒ <code>Promise</code>
    * [.getAiServiceProxy(params)](#module_miot/service/smarthome..ISmartHome+getAiServiceProxy) ⇒ <code>Promise</code>
    * ~~[.getDeviceSetting(params)](#module_miot/service/smarthome..ISmartHome+getDeviceSetting) ⇒ <code>Promise</code>~~
    * [.getDeviceSettingV2(params)](#module_miot/service/smarthome..ISmartHome+getDeviceSettingV2) ⇒ <code>Promise</code>
    * [.setDeviceSetting(params)](#module_miot/service/smarthome..ISmartHome+setDeviceSetting) ⇒ <code>Promise</code>
    * [.delDeviceSetting(params)](#module_miot/service/smarthome..ISmartHome+delDeviceSetting) ⇒ <code>Promise</code>
    * [.setDeviceData(params)](#module_miot/service/smarthome..ISmartHome+setDeviceData) ⇒ <code>Promise</code>
    * [.getDeviceData(params)](#module_miot/service/smarthome..ISmartHome+getDeviceData) ⇒ <code>Promise</code>
    * [.getDeviceDataV2(params)](#module_miot/service/smarthome..ISmartHome+getDeviceDataV2) ⇒ <code>Promise</code>
    * [.delDeviceData(params)](#module_miot/service/smarthome..ISmartHome+delDeviceData)
    * [.getUserDeviceLog(params)](#module_miot/service/smarthome..ISmartHome+getUserDeviceLog)
    * [.getUserColl(params)](#module_miot/service/smarthome..ISmartHome+getUserColl) ⇒ <code>Promise</code>
    * [.setUserColl(params)](#module_miot/service/smarthome..ISmartHome+setUserColl) ⇒ <code>Promise</code>
    * [.editUserColl(params)](#module_miot/service/smarthome..ISmartHome+editUserColl) ⇒ <code>Promise</code>
    * [.delUserColl(params)](#module_miot/service/smarthome..ISmartHome+delUserColl) ⇒ <code>Promise</code>
    * [.getMapfileUrl(params)](#module_miot/service/smarthome..ISmartHome+getMapfileUrl) ⇒ <code>Promise</code>
    * [.getRobomapUrl(arams)](#module_miot/service/smarthome..ISmartHome+getRobomapUrl) ⇒ <code>Promise</code>
    * [.delUsermap(params)](#module_miot/service/smarthome..ISmartHome+delUsermap) ⇒ <code>Promise</code>
    * [.getHomeDevice(params)](#module_miot/service/smarthome..ISmartHome+getHomeDevice) ⇒ <code>Promise</code>
    * [.getAppConfig(params)](#module_miot/service/smarthome..ISmartHome+getAppConfig)
    * ~~[.getAppConfigV2(params)](#module_miot/service/smarthome..ISmartHome+getAppConfigV2)~~
    * [.getCountry(params)](#module_miot/service/smarthome..ISmartHome+getCountry) ⇒ <code>Promise</code>
    * [.getBleLockBindInfo(params)](#module_miot/service/smarthome..ISmartHome+getBleLockBindInfo) ⇒ <code>Promise</code>
    * [.batchGetDeviceDatas(params)](#module_miot/service/smarthome..ISmartHome+batchGetDeviceDatas) ⇒ <code>Promise</code>
    * [.batchSetDeviceDatas(params)](#module_miot/service/smarthome..ISmartHome+batchSetDeviceDatas)
    * [.setDeviceProp(params)](#module_miot/service/smarthome..ISmartHome+setDeviceProp)
    * [.getThirdConfig(params)](#module_miot/service/smarthome..ISmartHome+getThirdConfig) ⇒ <code>Promise</code>
    * [.thirdSyncCall(params)](#module_miot/service/smarthome..ISmartHome+thirdSyncCall) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
    * [.callThirdPartyAPI(params)](#module_miot/service/smarthome..ISmartHome+callThirdPartyAPI) ⇒ <code>Promise</code>
    * [.getMiWatchConfig()](#module_miot/service/smarthome..ISmartHome+getMiWatchConfig) ⇒ <code>Promise</code>
    * [.getUserDeviceAuth(string})](#module_miot/service/smarthome..ISmartHome+getUserDeviceAuth) ⇒ <code>Promise</code>
    * [.getInterimFileUrl(params)](#module_miot/service/smarthome..ISmartHome+getInterimFileUrl) ⇒ <code>Promise</code>
    * ~~[.getFileUrl(params)](#module_miot/service/smarthome..ISmartHome+getFileUrl) ⇒ <code>Promise</code>~~
    * [.getUserDeviceDataTab(params)](#module_miot/service/smarthome..ISmartHome+getUserDeviceDataTab) ⇒ <code>Promise</code>
    * [.rangeGetOpenConfig(params)](#module_miot/service/smarthome..ISmartHome+rangeGetOpenConfig) ⇒ <code>Promise</code>
    * [.bindNFCCard(params)](#module_miot/service/smarthome..ISmartHome+bindNFCCard)
    * [.getNFCCard(params)](#module_miot/service/smarthome..ISmartHome+getNFCCard) ⇒ <code>json</code>
    * [.insertunmodel(params)](#module_miot/service/smarthome..ISmartHome+insertunmodel)
    * [.getIDFY(params)](#module_miot/service/smarthome..ISmartHome+getIDFY)
    * [.editIDFY(params)](#module_miot/service/smarthome..ISmartHome+editIDFY)
    * ~~[.getRangeOpenConfig(params)](#module_miot/service/smarthome..ISmartHome+getRangeOpenConfig)~~
    * [.createMember(type, info)](#module_miot/service/smarthome..ISmartHome+createMember)
    * [.updateMember(type, member_id, info)](#module_miot/service/smarthome..ISmartHome+updateMember)
    * [.deleteMember(type, member_id)](#module_miot/service/smarthome..ISmartHome+deleteMember)
    * [.loadMembers(type)](#module_miot/service/smarthome..ISmartHome+loadMembers)
    * [.setUserPDData(params)](#module_miot/service/smarthome..ISmartHome+setUserPDData)
    * [.getUserPDData(params)](#module_miot/service/smarthome..ISmartHome+getUserPDData)
    * [.getDeviceDataRaw(params)](#module_miot/service/smarthome..ISmartHome+getDeviceDataRaw)
    * [.createSeSession(params)](#module_miot/service/smarthome..ISmartHome+createSeSession)
    * [.replaceSEISDkey(params)](#module_miot/service/smarthome..ISmartHome+replaceSEISDkey)
    * [.resetLockPrimaryKey(params)](#module_miot/service/smarthome..ISmartHome+resetLockPrimaryKey)
    * [.handleSEResponse(params)](#module_miot/service/smarthome..ISmartHome+handleSEResponse)
    * [.reportBLEDeviceInfo(params)](#module_miot/service/smarthome..ISmartHome+reportBLEDeviceInfo)
    * [.reportEvent(eventName, params)](#module_miot/service/smarthome..ISmartHome+reportEvent)
    * [.addStat(key, value, extra)](#module_miot/service/smarthome..ISmartHome+addStat)
    * [.getMultiSwitchName(did)](#module_miot/service/smarthome..ISmartHome+getMultiSwitchName) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
    * [.createGroupDevice(name, member_dids, member_tags)](#module_miot/service/smarthome..ISmartHome+createGroupDevice) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
    * [.getVirtualGroupSubDevices(group_did)](#module_miot/service/smarthome..ISmartHome+getVirtualGroupSubDevices) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code>
    * [.getVirtualGroupSubDevicesTags(group_did)](#module_miot/service/smarthome..ISmartHome+getVirtualGroupSubDevicesTags) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code>
    * [.getVirtualDeviceCombineStatus(param)](#module_miot/service/smarthome..ISmartHome+getVirtualDeviceCombineStatus) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
    * [.setTempUnit()](#module_miot/service/smarthome..ISmartHome+setTempUnit) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code>
    * [.getTempUnit()](#module_miot/service/smarthome..ISmartHome+getTempUnit) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code>
    * [.checkFirmwareAutoUpgradeOpen(aDevId)](#module_miot/service/smarthome..ISmartHome+checkFirmwareAutoUpgradeOpen) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;{code:xx, data:xx}&gt;</code>
    * [.setFirmwareAutoUpgradeSwitch(aOpen, aDevId)](#module_miot/service/smarthome..ISmartHome+setFirmwareAutoUpgradeSwitch) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;{code:xx, data:xx}&gt;</code>
    * [.getCustomPluginInfo(model)](#module_miot/service/smarthome..ISmartHome+getCustomPluginInfo) ⇒ <code>object</code>
    * [.getPrivacyChanges(version:)](#module_miot/service/smarthome..ISmartHome+getPrivacyChanges) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
    * [.requestAuthForAlexaVoiceService(params)](#module_miot/service/smarthome..ISmartHome+requestAuthForAlexaVoiceService)
    * [.getHomeList(params)](#module_miot/service/smarthome..ISmartHome+getHomeList) ⇒ <code>object</code>
    * [.getCarManualSceneData(params)](#module_miot/service/smarthome..ISmartHome+getCarManualSceneData) ⇒ <code>Promise</code>
    * [.updateCarManualSceneData(params)](#module_miot/service/smarthome..ISmartHome+updateCarManualSceneData) ⇒ <code>Promise</code>
    * [.getConsumableDetails(param)](#module_miot/service/smarthome..ISmartHome+getConsumableDetails) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>


* * *

<a name="module_miot/service/smarthome..ISmartHome+getUserInfo"></a>

#### ~~iSmartHome.getUserInfo(uid) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;UserInfo&gt;</code>~~
***Deprecated***

获取用户的昵称和头像信息

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;UserInfo&gt;</code> - 用户信息  

| Param | Type | Description |
| --- | --- | --- |
| uid | <code>\*</code> | 获取用户信息的uid或者手机号 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getUserInfoList"></a>

#### ~~iSmartHome.getUserInfoList(uids) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;object&gt;&gt;</code>~~
***Deprecated***

通过UID批量获取用户信息

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10005  

| Param | Type | Description |
| --- | --- | --- |
| uids | <code>[ &#x27;Array&#x27; ].&lt;string&gt;</code> | uid数组，仅支持uid，不支持手机号查询 |

**Example**  
```js
Service.smarthome.getUserInfoList([uid1,uid2]).then(res => {
 console.log('user info :', res.list)
})
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+reportGPSInfo"></a>

#### iSmartHome.reportGPSInfo(deviceID, gpsInfo) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
上报gps信息 /location/set

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| deviceID | <code>string</code> | 设备ID |
| gpsInfo | <code>GPSInfo</code> | {lng,lat,countryCode,adminArea,locality,subLocality,thoroughfare,language} 依次为 {，，，，，，，} |

**Example**  
```js
//获取手机地理信息，iOS必须是真机且开启定位权限
Host.locale.getLocation().then(res => {
 console.log('get location: ', res)
 var {longitude,latitude} = res;
})
if (latitude && longitude) {
 Service.smarthome.reportGPSInfo(Device.deviceID, {})
}
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+getWeatherInfo"></a>

#### iSmartHome.getWeatherInfo(deviceID) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;WeatherInfo&gt;</code>
获取天气 /location/weather
该API 改为私有

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| deviceID | <code>string</code> | 设备ID |


* * *

<a name="module_miot/service/smarthome..ISmartHome+checkDeviceVersion"></a>

#### iSmartHome.checkDeviceVersion(设备did, pid) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;DeviceVersion&gt;</code>
获取指定设备的新版本信息
/home/<USER>

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| 设备did | <code>string</code> |  |
| pid | <code>number</code> | 设备类型，使用Device.type,即可 |

**Example**  
```js
Device.getDeviceWifi().checkVersion()
 .then(res => console.log('success:', res))
 .catch(err => console.log('failed:', err))
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+getAvailableFirmwareForDids"></a>

#### iSmartHome.getAvailableFirmwareForDids(deviceIDs) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
// 获取可用固件更新，传参为dids。 /home/<USER>

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| deviceIDs | <code>[ &#x27;array&#x27; ].&lt;string&gt;</code> | 设备ID |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getLatestVersion"></a>

#### ~~iSmartHome.getLatestVersion(model) ⇒ <code>Promise</code>~~
***Deprecated***

获取服务器中最新的版本信息，内部调用米家代理接口/home/<USER>

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| model | <code>string</code> | 设备的 model |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getLatestVersionV2"></a>

#### iSmartHome.getLatestVersionV2(did)
获取服务器中蓝牙设备可用的固件更新版本信息
内部调用米家代理接口/v2/device/latest_ver

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10004  

| Param | Type | Description |
| --- | --- | --- |
| did | <code>string</code> | 设备did |


* * *

<a name="module_miot/service/smarthome..ISmartHome+_getFirmwareOriginalUpdateInfo"></a>

#### iSmartHome.\_getFirmwareOriginalUpdateInfo(did)
调用Device.getWifiDevice()的检测固件是否有升级的api, 内部使用，暂时不对外公开

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Hide**:   
**Since**: 10043  

| Param | Type | Description |
| --- | --- | --- |
| did | <code>\*</code> | 设备did |


* * *

<a name="module_miot/service/smarthome..ISmartHome+_updateDevice"></a>

#### iSmartHome.\_updateDevice(did, pid)
调用Device.getWifiDevice()的直接升级设备的api, 内部使用，暂时不对外公开

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Hide**:   
**Since**: 10043  

| Param | Type | Description |
| --- | --- | --- |
| did | <code>\*</code> | 设备did |
| pid | <code>\*</code> | 设备pid |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getAutoUpgradeConfig"></a>

#### iSmartHome.getAutoUpgradeConfig(did) ⇒
调用Device.getWifiDevice()的自动升级配置信息

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Returns**: Promise  
**Since**: 10043  

| Param | Type | Description |
| --- | --- | --- |
| did | <code>\*</code> | 设备did |


* * *

<a name="module_miot/service/smarthome..ISmartHome+_getFirmwareReleaseVersionHistory"></a>

#### iSmartHome.\_getFirmwareReleaseVersionHistory(did)
调用Device.getWifiDevice()的所有的发布的固件版本的信息 (release版本，不包含白名单)
接口文档 https://wiki.n.miui.com/pages/viewpage.action?pageId=415980350

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Hide**:   
**Since**: 10043  

| Param | Type | Description |
| --- | --- | --- |
| did | <code>\*</code> | 设备did |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getFirmwareUpdateInfo"></a>

#### iSmartHome.getFirmwareUpdateInfo(did)
调用Device.getWifiDevice()的检测固件是否有升级的api, 达到与相关行为一致的目的。

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10037  

| Param | Type | Description |
| --- | --- | --- |
| did | <code>\*</code> | 设备did |


* * *

<a name="module_miot/service/smarthome..ISmartHome+reportLog"></a>

#### iSmartHome.reportLog(model, log) ⇒ <code>void</code>
添加一条日志打点。
开发者应该在拓展程序内合适时机调用该接口，打点信息会自动写入文件，按 Model 归类，即一个 Model 生成一个日志文件。
当用户反馈问题时，勾选 “同时上传日志”，则该 Model 的日志会跟随用户反馈上传，
开发者可在 IoT 平台查看用户反馈及下载对应日志文件。用户反馈查看入口：数据中心—用户反馈，如果看不到数据中心入口，联系自己所属企业管理员修改账号权限。
查看地址：https://iot.mi.com/fe-op/operationCenter/userFeedback

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| model | <code>string</code> | 要打 log 到哪个 model 下, 格式必须形如aaa.bbb.ccc, 否者无效 |
| log | <code>string</code> | 具体的 log 数据 |

**Example**  
```js
Service.smarthome.reportLog("a.b.c", "hello");
    Service.smarthome.reportLog(Device.model, `[info]test value is :${v1},${v2},${v3}`)
    Package.isDebug&&Service.smarthome.reportLog(...)
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+reportSDKFileLog"></a>

#### iSmartHome.reportSDKFileLog()
添加一条SDK日志调试信息，SDK内部使用，不对外暴露

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

* * *

<a name="module_miot/service/smarthome..ISmartHome+reportRecords"></a>

#### iSmartHome.reportRecords(deviceID, records)
上报设备数据 /device/event
会更新状态+存到历史(相当于调用setDeviceData 接口)+触发自动化
仅支持WiFi设备

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| deviceID | <code>string</code> | 设备ID |
| records | <code>[ &#x27;array&#x27; ].&lt;map&gt;</code> | [{type,key,value}] 其中：type为'prop'或'event'，key，value均为自定义string |

**Example**  
```js
Service.smarthome.reportRecords("deviceID", [{type:"prop",key:"b",value:"c"}])
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+deviceSetExtraData"></a>

#### ~~iSmartHome.deviceSetExtraData(params) ⇒ <code>Promise</code>~~
***Deprecated***

/v2/device/set_extra_data
 写extra_data 字段，必须为map[string] string格式

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10002  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | 参数 {did, extra_data} |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getDevicesConfig"></a>

#### ~~iSmartHome.getDevicesConfig(params) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>~~
***Deprecated***

通过前缀分批拉取设备的配置信息
- /v2/device/range_get_extra_data

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | {did:string,prefix:string,limit:int,offset:int} |


* * *

<a name="module_miot/service/smarthome..ISmartHome+delDevicesConfig"></a>

#### ~~iSmartHome.delDevicesConfig(params) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>~~
***Deprecated***

删除设备上传的信息 /v2/device/del_extra_data

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | {did:string, keys:[key1,key2]} |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getDeviceTimeZone"></a>

#### ~~iSmartHome.getDeviceTimeZone(did)~~
***Deprecated***

获取设备时区

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type |
| --- | --- |
| did | <code>string</code> | 


* * *

<a name="module_miot/service/smarthome..ISmartHome+getUserStatistics"></a>

#### iSmartHome.getUserStatistics(params) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
提供返回设备数据统计服务，使用该接口需要配置产品model以支持使用，建议找对接的产品人员进行操作。
图表📈统计接口 /v2/user/statistics
注:由于sds限额问题，可能会出现一次拉不到或者拉不完数据的情况，会返回code:0和message:“sds throttle”

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code> - {
        "code": 0,
        "message": "ok",
        "result": [
            {
                "value": "[12,34]", // 为一个数组形式json串
                "time": 1543593600 // 时间戳
            },
            {
                "value": "[10,11]",
                "time": 1541001600
            }]
    }  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> |  |
| params.did | <code>string</code> | did |
| params.data_type | <code>string</code> | 数据类型 包括： 采样统计 日统计:stat_day_v3 / 周统计:stat_week_v3 / 月统计:stat_month_v3; |
| params.key | <code>string</code> | 需要统计的字段，即统计上报对应的key eg: 如果是profile协议设备，如电量:key = powerCost.... 如果是spec设备，key = siid.piid |
| params.time_start | <code>number</code> | 开始时间 |
| params.time_end | <code>number</code> | 结束时间 |
| params.limit | <code>number</code> | 限制次数，0为默认条数 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getVoiceCtrlDevices"></a>

#### iSmartHome.getVoiceCtrlDevices(deviceID) ⇒ <code>Promise</code>
获取支持语音的设备 可以控制的设备列表。 /voicectrl/ai_devs

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Description |
| --- | --- |
| deviceID | 语音设备的 did |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getAiServiceProxy"></a>

#### iSmartHome.getAiServiceProxy(params) ⇒ <code>Promise</code>
获取小爱接口数据，内部调用米家代理接口/v2/api/aivs

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | 请求参数 {path:string,params:map,header:map,payload:map,env:int,req_method:string,req_header:map} |
| params.path | <code>string</code> |  |
| params.params | <code>string</code> |  |
| params.params.did | <code>string</code> |  |
| params.params.client_id | <code>string</code> |  |
| params.header | <code>string</code> |  |
| params.env | <code>string</code> |  |
| params.req_method | <code>string</code> |  |
| params.req_header | <code>string</code> |  |

**Example**  
```js
Service.smarthome.getAiServiceProxy({
 path: "/api/aivs/xxx",
 params: { did : "xx", client_id: "xx"},
 header: { name : "xx"},
 env: 1,
 req_method: "POST",
 req_header: {"Content-Type":"xx"}
}).then()
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+getDeviceSetting"></a>

#### ~~iSmartHome.getDeviceSetting(params) ⇒ <code>Promise</code>~~
***Deprecated***

获取服务器中 device 对应的数据，内部调用米家代理接口 /device/getsetting

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | 请求参数 |
| params.did | <code>string</code> | did |
| params.settings | <code>[ &#x27;Array&#x27; ].&lt;string&gt;</code> | 指定设置的key数组 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getDeviceSettingV2"></a>

#### iSmartHome.getDeviceSettingV2(params) ⇒ <code>Promise</code>
获取服务器中 device 对应的数据，内部调用米家代理接口 /v2/device/getsettingv2

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10010  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> |  |
| params.did | <code>string</code> | 设备did |
| params.last_id | <code>string</code> | 上一次请求返回的id，用于分页 |
| params.prefix_filter | <code>string</code> | filter |
| params.settings | <code>[ &#x27;Array&#x27; ].&lt;string&gt;</code> | 指定设置的key数组 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+setDeviceSetting"></a>

#### iSmartHome.setDeviceSetting(params) ⇒ <code>Promise</code>
设置服务器中 device 对应的数据，内部调用米家代理接口/device/setsetting

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | 请求参数 {did:string,settings:map<key,value>} |
| params.did | <code>string</code> | did |
| params.settings | <code>object</code> | 指定设置的key数组，保存的内容不能超过1000个字符 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+delDeviceSetting"></a>

#### iSmartHome.delDeviceSetting(params) ⇒ <code>Promise</code>
删除服务器中 device 对应的数据，内部调用米家代理接口/device/delsetting

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | 请求参数 |
| params.did | <code>string</code> | did |
| params.settings | <code>object</code> | 指定要删除的key数组 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+setDeviceData"></a>

#### iSmartHome.setDeviceData(params) ⇒ <code>Promise</code>
添加设备属性和事件历史记录，/user/set_user_device_data
对于蓝牙设备，params.key 可参考文档  https://iot.mi.com/new/doc/accesses/direct-access/embedded-development/ble/object-definition

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | 参数 |
| params.did | <code>string</code> | 设备did， |
| params.uid | <code>string</code> | 添加到哪个用户下,一般为 Device.ownerId， |
| params.type | <code>string</code> | 属性为prop, 事件为event |
| params.key | <code>string</code> | 要保存的数据K, 属性或事件名，(注意：如果设备是蓝牙设备，传入的是object id， 且为十进制数据；如果是wifi设备，才传入自定义属性或事件名，可以在开发者平台-产品-功能定义中查看) |
| params.value | <code>string</code> | 要保存的数据V |
| params.time | <code>number</code> | 触发时间戳， |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getDeviceData"></a>

#### iSmartHome.getDeviceData(params) ⇒ <code>Promise</code>
查询用户名下设备上报的属性和事件
获取设备属性和事件历史记录，订阅消息直接写入到服务器，不需要插件添加，最多查询90天前的记录。
通下面的set_user_device_data的参数一一对应， /user/get_user_device_data
对于蓝牙设备，params.key 可参考文档 [米家BLE Object定义](https://iot.mi.com/new/doc/accesses/direct-access/embedded-development/ble/object-definition)

error code:

| code | desc |
| :-: | --- |
|  0  | 成功 |
| -8  | 请求参数缺失或者类型不对 |
| -4  | 服务器错误 |
| -1  | 请求uid无权限获取did的相关数据 |

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | 参数\{did,type,key,time_start,time_end,limit}含义如下： |
| params.did | <code>string</code> | 设备id。 必选参数 |
| params.key | <code>string</code> | 属性或事件名，可选参数。(注意：如果设备是蓝牙设备，传入的是object id， 且为十进制数据；如果是wifi设备，才传入自定义属性或事件名，可以在开发者平台-产品-功能定义中查看)，如果是miot-spec设备，请传入（siid.piid或者siid.eiid） |
| params.type | <code>string</code> | 必选参数[prop/event], 如果是查询上报的属性则type为prop，查询上报的事件则type为event, |
| params.time_start | <code>number</code> | 数据起点，单位是秒。必选参数 |
| params.time_end | <code>number</code> | 数据终点，单位是秒。必选参数，time_end必须大于time_start, |
| params.limit | <code>string</code> | 返回数据的条数，默认20，最大1000。可选参数. |
| params.uid | <code>number</code> | 要查询的用户id 。可选参数 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getDeviceDataV2"></a>

#### iSmartHome.getDeviceDataV2(params) ⇒ <code>Promise</code>
查询用户名下设备上报的属性和事件
获取设备属性和事件历史记录，订阅消息直接写入到服务器，不需要插件添加，最多查询90天前的记录。
对于蓝牙设备，params.key 可参考文档 [米家BLE Object定义](https://iot.mi.com/new/doc/embedded-development/ble/object-definition.html)

error code:

| code | desc |
| :-: | --- |
|  0  | 成功 |
| -8  | 请求参数缺失或者类型不对 |
| -4  | 服务器错误 |
| -1  | 请求uid无权限获取did的相关数据 |

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10062  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | 参数\{did,type,key,time_start,time_end,limit}含义如下： |
| params.did | <code>string</code> | 设备id。 必选参数 |
| params.key | <code>string</code> | 属性或事件名，可选参数。(注意：如果设备是蓝牙设备，传入的是object id， 且为十进制数据；如果是wifi设备，才传入自定义属性或事件名，可以在开发者平台-产品-功能定义中查看)，如果是miot-spec设备，请传入（siid.piid或者siid.eiid） |
| params.type | <code>string</code> | 必选参数[prop/event], 如果是查询上报的属性则type为prop，查询上报的事件则type为event, |
| params.time_start | <code>number</code> | 数据起点，单位是秒。必选参数 |
| params.time_end | <code>number</code> | 数据终点，单位是秒。必选参数，time_end必须大于time_start, |
| params.group | <code>string</code> | 返回数据的方式，默认raw,可选值为hour、day、week、month。可选参数. |
| params.limit | <code>string</code> | 返回数据的条数，默认20，最大1000。可选参数. |
| params.uid | <code>number</code> | 要查询的用户id 。可选参数 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+delDeviceData"></a>

#### iSmartHome.delDeviceData(params)
删除用户的设备信息（prop和event 除外）.
删除对应时间戳的上报的数据，无法删除type为prop和event,删除后可用get_user_device_data校验。
如果get_user_device_data校验返回的为[]表示删除成功。
user/del_user_device_data

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10004  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | {did:'', type: '', key:'',time:number} did:设备ID ;type: 要删除的类型 ;key: 事件名称. motion/alarm ;time:时间戳，单位秒 |
| params.did | <code>string</code> | 设备id。 必选参数 |
| params.type | <code>string</code> | type 定义与SDS表中type一致。必选参数。可参考SDS文档中的示例：https://iot.mi.com/new/doc/accesses/direct-access/cloud-service/storage/sds |
| params.key | <code>string</code> | key 事件名，可自定义,定义与SDS表中key一致。必选参数 |
| params.time | <code>string</code> | 指定时间戳 |
| params.value | <code>string</code> | 指定值 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getUserDeviceLog"></a>

#### iSmartHome.getUserDeviceLog(params)
用于按照时间顺序拉取指定uid,did的发生的属性事件
/v2/user/get_user_device_log
仅限lumi.xxx.xxx的model设备可以使用

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10004  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | 参数 |
| params.did | <code>string</code> |  |
| params.limit | <code>number</code> | 目前最大为50 |
| params.time_start | <code>number</code> | 开始时间 |
| params.time_end | <code>number</code> | 结束时间 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getUserColl"></a>

#### iSmartHome.getUserColl(params) ⇒ <code>Promise</code>
获取用户收藏
/user/get_user_coll

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | 参数 |
| params.did | <code>string</code> | did |


* * *

<a name="module_miot/service/smarthome..ISmartHome+setUserColl"></a>

#### iSmartHome.setUserColl(params) ⇒ <code>Promise</code>
设置用户收藏
/user/get_user_coll

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | 参数 |
| params.did | <code>string</code> | did |
| params.name | <code>string</code> | name |
| params.content | <code>string</code> | content |


* * *

<a name="module_miot/service/smarthome..ISmartHome+editUserColl"></a>

#### iSmartHome.editUserColl(params) ⇒ <code>Promise</code>
/user/edit_user_coll
 编辑用户收藏

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10002  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | 参数 {coll_id, newname， content} |


* * *

<a name="module_miot/service/smarthome..ISmartHome+delUserColl"></a>

#### iSmartHome.delUserColl(params) ⇒ <code>Promise</code>
删除用户收藏
/user/get_user_coll

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>\*</code> | 参数 |
| params.did | <code>string</code> | did |
| params.coll_id | <code>string</code> | coll_id |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getMapfileUrl"></a>

#### iSmartHome.getMapfileUrl(params) ⇒ <code>Promise</code>
石头扫地机专用
添加设备属性和事件历史记录，/home/<USER>

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type |
| --- | --- |
| params | <code>json</code> | 


* * *

<a name="module_miot/service/smarthome..ISmartHome+getRobomapUrl"></a>

#### iSmartHome.getRobomapUrl(arams) ⇒ <code>Promise</code>
石头扫地机器人专用，获取fds存储文件url
 /home/<USER>

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| arams | <code>\*</code> | {“obj_name”:”xxx/12345678/87654321/1.0”}，obj_name格式为:fds存储文件夹/did/uid/obj_name |


* * *

<a name="module_miot/service/smarthome..ISmartHome+delUsermap"></a>

#### iSmartHome.delUsermap(params) ⇒ <code>Promise</code>
石头扫地机器人专用，撤销隐私时删除扫地机地图
 /user/del_user_map

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | {did} 设备ID |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getHomeDevice"></a>

#### iSmartHome.getHomeDevice(params) ⇒ <code>Promise</code>
添加设备属性和事件历史记录，/home/<USER>
当ssid和bssid均不为空时，表示同时搜索这个局域网内所有未被绑定过的设备

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | {pid:string ,ssid:string ,bssid:string ,localDidList:array<string>,checkMoreWifi:bool,dids:array<string>} |
| params.pid | <code>string</code> | Device.type |
| params.ssid | <code>string</code> | wifi 的 ssid |
| params.bssid | <code>string</code> | wifi 的bssid |
| params.dids | <code>string</code> | 要拉取列表的设备的didi，如果为空表示所有设备 |
| params.localDidList | <code>string</code> | 本地设备did列表，补充ssid和bssid的本地查询条件，会与ssid查到的本地列表一起返回其中未被绑定的在线设备 |
| params.checkMoreWifi | <code>string</code> | 检查2.4gwifi下的本地设备列表 |
| params.getHuamiDevices | <code>boolean</code> | 获取华米设备,如华米手环 其中，pid：设备PID，ssid：wifi名称，bssid：wifi网关mac，locatDidList：本地设备did列表，补充ssid和bssid的本地查询条件，会与ssid查到的本地列表一起返回其中未被绑定的在线设备，checkMoreWifi：检查2.4gwifi下的本地设备列表，did：要拉取列表的设备的did，如果为空表示所有设备 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getAppConfig"></a>

#### iSmartHome.getAppConfig(params)
获取AppConfig配置文件，1. 插件端有一些自己的信息需要配置，可使用此接口 2. 局限性：只有小米内部有权配置，之后可能会出对外版（目前只能找米家产品经理/工程师帮忙配置）
 **维护起来很不方便，不建议使用。**
默认获取的是release版数据， 如果需要获取preview版数据， 可以在米家APP中 我的-->开发者设置-->其他设置的界面中 “AppConfig接口拉取preview版数据”  置为选中状态

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | 请求参数 |
| params.name | <code>string</code> | configName 配置的名字 |
| params.lang | <code>string</code> | lang 可选: zh_CN、zh_TW、en，zh-hant，一般请使用zh_CN和en |
| params.result_level | <code>string</code> | 正常传"0"，若传“1”，则会提供一个downloadurl，而不是直接返回content，以节省流量。取得downloadurl后，通过Host.file.downloadFile下载文件，然后使用 |
| params.version | <code>string</code> | version 后台配置的version，大概率为"1"，如果不对，可以找米家工程师帮忙查询，查询地址：http://plato.io.mi.srv/#/appconfig/client |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getAppConfigV2"></a>

#### ~~iSmartHome.getAppConfigV2(params)~~
***Deprecated***

用于获取插件所需的一些默认配置信息

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | {'name':'自定义值','lang':'自定义值','version':'自定义值','model':'modelId'} /service/getappconfigv2 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getCountry"></a>

#### iSmartHome.getCountry(params) ⇒ <code>Promise</code>
获取设备所在网络的IP地址所属国家
/home/<USER>

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | {"did": "xx"} |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getBleLockBindInfo"></a>

#### iSmartHome.getBleLockBindInfo(params) ⇒ <code>Promise</code>
获取蓝牙锁绑定的时间，/device/blelockbindinfo

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | 参数 |
| params.did | <code>string</code> | did |


* * *

<a name="module_miot/service/smarthome..ISmartHome+batchGetDeviceDatas"></a>

#### iSmartHome.batchGetDeviceDatas(params) ⇒ <code>Promise</code>
获取设备的属性，属性设置会在设备被删除时清空
api call /device/batchdevicedatas
对于蓝牙设备，params.props 可参考文档 [米家BLE Object定义](https://iot.mi.com/new/doc/embedded-development/ble/object-definition.html)

error code:

| code | desc |
| :-: | --- |
|  0  | 成功 |
| -7  | 没有找到注册的设备 |
| -6  | 设备对应uid不为0 |
| -4  | server err |

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10005  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>[ &#x27;Array&#x27; ].&lt;object&gt;</code> | 参数 |
| params[].did | <code>string</code> | did |
| params[].props | <code>[ &#x27;Array&#x27; ].&lt;string&gt;</code> | props 列表,属性需要以"prop.s_"开头 e.g ["prop.s_aaa","prop.s_bbb"]，如果设备是蓝牙设备，传入的是object id， 且为十进制数据，如prop.4100 |

**Example**  
```js
let params = {'did':Device.deviceID, 'props': [
 "prop.s_push_switch"
]}
Service.smarthome.batchGetDeviceDatas([params]).then(...)
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+batchSetDeviceDatas"></a>

#### iSmartHome.batchSetDeviceDatas(params)
设置设备属性, 属性设置会在设备被删除时清空
备注： props最多20个，最多同时300个设备（目前max设备数)，属性需要以prop.s_ 开头

error code:
0 - 成功
7 - 没有找到注册的设备
6 - 设备对应uid为0
4 - server err

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10005  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>[ &#x27;Array&#x27; ].&lt;object&gt;</code> | {did: string, props: json} |
| params[].did | <code>string</code> | did |
| params[].props | <code>object</code> | props 键值对， 属性需要以"prop.s_"开头 |

**Example**  
```js
let params = {'did':Device.deviceID, 'props': {
 "prop.s_push_switch_xxx":"0"
}}
Service.smarthome.batchSetDeviceDatas([params]).then(...)
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+setDeviceProp"></a>

#### iSmartHome.setDeviceProp(params)
设置设备属性，e.g 配置摄像头/门铃设备的属性
props最多20个, 属性需要以"prop.s_"开头。

error code:
0 - 成功
-7 - 没有找到注册的设备
-6 - 设备对应uid不为0
-4 - server err

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10004  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | 参数 |
| params.did | <code>string</code> | did |
| params.props | <code>object</code> | props 键值对， 属性需要以"prop.s_"开头 |

**Example**  
```js
let params = {'did':Device.deviceID, 'props': {
 "prop.s_notify_screen_dev_enable":"0", //0,关； 1，开
 "prop.s_notify_screen_dev_did":"123456789" // 接收rpc的音响设备
}}
Service.smarthome.setDeviceProp(params).then(...)
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+getThirdConfig"></a>

#### iSmartHome.getThirdConfig(params) ⇒ <code>Promise</code>
从服务器获取配置文件，/device/getThirdConfig

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | 参数 {"name":"config_version","version":1,"lang":"en","app_id":"XXX"} |
| params.name | <code>string</code> | configName |
| params.model | <code>string</code> | device model |
| params.app_id | <code>string</code> | app_id |
| params.lang | <code>string</code> | lang e.g: zh_CN |
| params.result_level | <code>string</code> | 值为1，则不返回content来节省流量， 默认为0 |
| params.version | <code>string</code> | version |


* * *

<a name="module_miot/service/smarthome..ISmartHome+thirdSyncCall"></a>

#### iSmartHome.thirdSyncCall(params) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
/v2/third/synccall. 兼容三方厂商使用

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - {"code": 0, "policy": <POLICY_NUMBER">, ...}  
**Since**: 10003  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | {"uid": , "did":, "api_name": , ...} |


* * *

<a name="module_miot/service/smarthome..ISmartHome+callThirdPartyAPI"></a>

#### iSmartHome.callThirdPartyAPI(params) ⇒ <code>Promise</code>
异步调用第三方云接口  /third/api

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | 参数 {"app_id":"123","dids":["1","2"],"params":json} |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getMiWatchConfig"></a>

#### iSmartHome.getMiWatchConfig() ⇒ <code>Promise</code>
华米watch配置使用
Android not support yet

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

* * *

<a name="module_miot/service/smarthome..ISmartHome+getUserDeviceAuth"></a>

#### iSmartHome.getUserDeviceAuth(string}) ⇒ <code>Promise</code>
获取authCode来做鉴权

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Description |
| --- | --- |
| string} | did 设备的 did |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getInterimFileUrl"></a>

#### iSmartHome.getInterimFileUrl(params) ⇒ <code>Promise</code>
获取InterimFileUrl 获取临时文件。

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | 参数 {obj_name : '{ownerId}/{deviceId}/{index}'} |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getFileUrl"></a>

#### ~~iSmartHome.getFileUrl(params) ⇒ <code>Promise</code>~~
***Deprecated***

获取文件下载地址

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | 参数 {obj_name : '2018/06/08/123456/xiaomi123_181030106.mp3'} |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getUserDeviceDataTab"></a>

#### iSmartHome.getUserDeviceDataTab(params) ⇒ <code>Promise</code>
日志分页拉取
仅限lumi.xxx.xxx的model设备可以使用
最多可以查询90天前的数据

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10001  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | 参数 |
| params.did | <code>string</code> |  |
| params.key | <code>string</code> |  |
| params.type | <code>string</code> |  |
| params.timestamp | <code>number</code> |  |
| params.limit | <code>number</code> |  |


* * *

<a name="module_miot/service/smarthome..ISmartHome+rangeGetOpenConfig"></a>

#### iSmartHome.rangeGetOpenConfig(params) ⇒ <code>Promise</code>
/v2/home/<USER>
通过appid、category、configid获取对应的配置，请参考文档文档：https://iot.mi.com/new/doc/accesses/direct-access/cloud-service/storage/kv-openconfig

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10002  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | 参数 {did,category,configids,offset,limit} |


* * *

<a name="module_miot/service/smarthome..ISmartHome+bindNFCCard"></a>

#### iSmartHome.bindNFCCard(params)
门锁米家APP上传Cid,Did,Uid，返回处理结果。函数内部与金服APP建立http连接签名传输配置信息与NFC卡片信息
Service.smarthome.BindNFCCard(params)

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10003  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | {did:'', uid:'', cid:''} |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getNFCCard"></a>

#### iSmartHome.getNFCCard(params) ⇒ <code>json</code>
米家app查询NFC卡信息，使用did查询did下绑定的NFC卡列表信息

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Returns**: <code>json</code> - 卡片结果数组  
**Since**: 10003  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | {did:''} |

**Example**  
```js
response:
ret={
        "code":0,
        "message":"ok",
        "result":{
            "list":[{
                "did":"1234567",
                "uid":123456789,//设备owner的用户id
                "cid":"111122223333444455",
                "name":"家",//用户设置的卡名称
                "type":1, //卡片类型，1：手机NFC卡，2：实体卡
                "status":1,//卡片状态，1：有效， 0： 无效
                "issuer_id":"666666",
                "time_stamp":1234567890,// 开卡时间
                "extra":{
                    "deviceModel":"RedMi 4X",
                    "OS":"MIUI 9.5"
                }
            },
            {
            ...
            }]
        }
    }
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+insertunmodel"></a>

#### iSmartHome.insertunmodel(params)
/yaokan/insertunmodel

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10004  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | {device:int, id: int, brand: string, model: string} |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getIDFY"></a>

#### iSmartHome.getIDFY(params)
call api /scene/idfy_get

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10005  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | json params |
| params.indetify | <code>string</code> | 唯一标识符，场景的id，一般填did |

**Example**  
```js
let params = {identify:Device.deviceID}
Service.smarthome.getIDFY(params)
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+editIDFY"></a>

#### iSmartHome.editIDFY(params)
call api /scene/idfy_get

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10005  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | json params |

**Example**  
```js
let params = {"identify":"554011","st_id":7,"setting":{"aqi_link":"0","exception_alert":"1","blue_sky_alert":"0"},"authed":["554011"]}
Service.smarthome.editIDFY(params)
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+getRangeOpenConfig"></a>

#### ~~iSmartHome.getRangeOpenConfig(params)~~
***Deprecated***

call api /v2/home/<USER>

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10005  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>json</code> | json params {did:string, category:string, configids:array, offset: int, limit:int}, did: 设备did。 category 配置类别， configids： 配置id 为空时返回所有配置，不超过20个，不为空时没有数量限制， offset 偏移；limit 数量，不超过20 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+createMember"></a>

#### iSmartHome.createMember(type, info)
创建 成员， 参考 MemberPerson 或者 MemberPet 的内容，按需填写。

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10005  

| Param | Type | Description |
| --- | --- | --- |
| type | <code>MemberType</code> | 成员类型 pet or person |
| info | <code>MemberPerson</code> | MemberPerson 或者 MemberPet |


* * *

<a name="module_miot/service/smarthome..ISmartHome+updateMember"></a>

#### iSmartHome.updateMember(type, member_id, info)
更新成员信息

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10005  

| Param | Type | Description |
| --- | --- | --- |
| type | <code>MemberType</code> |  |
| member_id | <code>string</code> |  |
| info | <code>MemberPerson</code> | MemberPerson 或者 MemberPet 只填写需要更新的项目 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+deleteMember"></a>

#### iSmartHome.deleteMember(type, member_id)
删除成员

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10005  

| Param | Type | Description |
| --- | --- | --- |
| type | <code>MemberType</code> |  |
| member_id | <code>Array</code> | 成员id列表 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+loadMembers"></a>

#### iSmartHome.loadMembers(type)
加载指定种类的成员列表

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10005  

| Param | Type |
| --- | --- |
| type | <code>MemberType</code> | 


* * *

<a name="module_miot/service/smarthome..ISmartHome+setUserPDData"></a>

#### iSmartHome.setUserPDData(params)
设置用户信息
call /user/setpdata, 其中的time为关键信息，在getpdata使用时将利用此值。

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10010  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | params |
| params.time | <code>long</code> | setpddata的时间戳 |
| params.key | <code>string</code> | key 字串 |
| params.value | <code>string</code> | value值 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getUserPDData"></a>

#### iSmartHome.getUserPDData(params)
获取用户信息
call /user/getpdata
此接口的时间戳范围是反的，即：time_start > time_end ,否则获取不到。

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10010  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | params |
| params.time_end | <code>object</code> | 筛选结果的时间戳 |
| params.time_start | <code>object</code> | 筛选结果的时间戳 |
| params.key | <code>object</code> | 获取的key |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getDeviceDataRaw"></a>

#### iSmartHome.getDeviceDataRaw(params)
App获取设备上报操作记录
request /v2/user/get_device_data_raw

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10011  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | 参数 |
| params.did | <code>string</code> | 设备did |
| params.uid | <code>string</code> | 用户UID |
| params.type | <code>string</code> | 查询事件；当查询属性时使用 'prop', 否则使用 'store'操作 |
| params.key | <code>string</code> | 事件名称；当查询属性时value填具体属性，比如"aqi" |
| params.time_start | <code>string</code> | 开始UTC时间 |
| params.time_end | <code>string</code> | 结束UTC时间 |
| params.limit | <code>string</code> | 最多返回结果数目，上限500。注意按需填写，返回数据越多查询越慢 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+createSeSession"></a>

#### iSmartHome.createSeSession(params)
透传米家APP与小米支付创建session
request /v2/nfckey/create_se_session

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10011  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | params |
| params.did | <code>string</code> | did |
| params.reqData | <code>object</code> | // 透传给Mipay的数据 |
| params.reqData.userId | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.cplc | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.deviceType | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.deviceId | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.timestamp | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.sign | <code>string</code> | // 透传给Mipay的数据 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+replaceSEISDkey"></a>

#### iSmartHome.replaceSEISDkey(params)
透传替换ISD key
request /v2/nfckey/replace_se_isdkey

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10011  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | params |
| params.did | <code>string</code> | did |
| params.reqData | <code>object</code> | // 透传给Mipay的数据 |
| params.reqData.sessionId | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.partnerId | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.userId | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.cplc | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.timestamp | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.sign | <code>string</code> | // 透传给Mipay的数据 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+resetLockPrimaryKey"></a>

#### iSmartHome.resetLockPrimaryKey(params)
透传锁主密钥重置
request /v2/nfckey/reset_lock_primarykey

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10011  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | params |
| params.did | <code>string</code> | did |
| params.reqData | <code>object</code> | // 透传给Mipay的数据 |
| params.reqData.sessionId | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.partnerId | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.userId | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.cplc | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.timestamp | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.sign | <code>string</code> | // 透传给Mipay的数据 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+handleSEResponse"></a>

#### iSmartHome.handleSEResponse(params)
处理芯片返回
request /v2/nfckey/handle_se_response

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10011  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | params |
| params.did | <code>string</code> | did |
| params.reqData | <code>object</code> | // 透传给Mipay的数据 |
| params.reqData.sessionId | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.userId | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.cplc | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.seResps | <code>[ &#x27;Array&#x27; ].&lt;object&gt;</code> | // 这是一个数组透传给Mipay的数据 |
| params.reqData.seResps[].data | <code>string</code> | // 这是一个透传给Mipay的数据 |
| params.reqData.seResps[].statusWord | <code>string</code> | // 这是一个透传给Mipay的数据 |
| params.reqData.timestamp | <code>string</code> | // 透传给Mipay的数据 |
| params.reqData.sign | <code>string</code> | // 透传给Mipay的数据 |

**Example**  
```js
let param = {
 "did":"1234567",
 "reqData":{ // 透传给Mipay的数据
     "sessionId":"999999999",
     "userId":"12340000",
     "cplc":"asdghjklmnbvd",
     "seResps":[
         {"data":"","statusWord":"9000"},
         {"data":"","statusWord":"6A80"}
     ],
     "timestamp":1234567890,
     "sign":"shaddgkldsjlkeri"
 }
}
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+reportBLEDeviceInfo"></a>

#### iSmartHome.reportBLEDeviceInfo(params)
上报蓝牙设备信息
call: /v2/device/bledevice_info
等效于: /v2/blemesh/dev_info

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10020  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | 参数 |
| prarms.did | <code>string</code> | 设备did |
| prarms.fw_ver | <code>string</code> | 设备当前固件版本号 |
| prarms.hw_ver | <code>string</code> | 设备的硬件平台 |
| prarms.latitude | <code>string</code> | 纬度，number字符串 |
| prarms.longitude | <code>string</code> | 经度，number字符串 |
| prarms.iternetip | <code>string</code> | app/网关IP地址 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+reportEvent"></a>

#### iSmartHome.reportEvent(eventName, params)
since 10036

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| eventName | <code>string</code> | 事件名 |
| params | <code>Object</code> | kv键值对，key必须是string类型，value是基础类型（int,strig,float,boolean） |

**Example**  
```js
let eventName = 'testEvent';
let params = {'key1':'value1','key2':'value2','tip':'tips'};
Service.smarthome.reportEvent(eventName,params);
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+addStat"></a>

#### iSmartHome.addStat(key, value, extra)
since 10053
打点上报接口，同原生插件的addRecord接口；如非必要，请使用reportEvent接口

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  

| Param | Type | Description |
| --- | --- | --- |
| key | <code>string</code> | 事件名 |
| value | <code>string</code> | 上报的值 |
| extra | <code>json</code> | 需要上报的额外内容 |

**Example**  
```js
let key = 'testEvent';
let value ="123";
let extra = {'key1':'value1','key2':'value2'};
Service.smarthome.addStat(key,value,extra);
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+getMultiSwitchName"></a>

#### iSmartHome.getMultiSwitchName(did) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
获取多键开关名称
调用接口 /device/deviceinfo

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code> - 成功时：{"1":{"id":X,"name":"XX","room_id":XXXXXX,"home_id":XXXX,"ai_desc":"XX","icon":"X","subclass_id":X,"ai_ctrl":X}, "2":{...}, "3":{...}, ...}
失败时：{"code": XX, "msg": "XXX"}  
**Since**: 10039  

| Param | Type | Description |
| --- | --- | --- |
| did | <code>string</code> | 设备id |


* * *

<a name="module_miot/service/smarthome..ISmartHome+createGroupDevice"></a>

#### iSmartHome.createGroupDevice(name, member_dids, member_tags) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code>
创建组设备，(窗帘组设备)

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;object&gt;</code> - 成功时：返回
{
   "group_did":"group.123456xxx",
   "need_alter_device":false  //与Mesh组相关，窗帘组可以忽略
}
失败时：返回
{ "code":xxx,"message":"xxx" }  
**Since**: 10046  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| name | <code>string</code> |  | 设备的名称,可选 |
| member_dids | <code>array</code> |  | 子设备的们id |
| member_tags | <code>json</code> | <code></code> | since 10049，可选，tags内容客户端自定义，服务器只做存储不理解tags含义，一般用于记录子设备之间的关系 已知使用场景：保存窗帘组下的子设备的左右信息,例: tags={   "did1":"left",   "did2":"right" } |


* * *

<a name="module_miot/service/smarthome..ISmartHome+getVirtualGroupSubDevices"></a>

#### iSmartHome.getVirtualGroupSubDevices(group_did) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code>
获取组成组设备的子设备们的did(窗帘组)

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code> - 成功时：返回
[
   {
     "did":"group.123456xxx",
     "status":"1",
     "membership":{  //key为子设备did
         "1041565620":"1",
         "1041565621":"1"
      }
   }
]
 失败时：返回
{ "code":xxx, "message":"xxx" }  
**Since**: 10046  

| Param | Type |
| --- | --- |
| group_did | <code>string</code> | 


* * *

<a name="module_miot/service/smarthome..ISmartHome+getVirtualGroupSubDevicesTags"></a>

#### iSmartHome.getVirtualGroupSubDevicesTags(group_did) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code>
获取组设备下与子设备相关的tags
返回的是[createGroupDevice](createGroupDevice) 方法的第三个参数member_tags

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10049
成功时（以窗帘组的左右窗帘信息为例）: 返回
[
  {
     "did":"group.123456xxx",
      "member_tags":{            //key为子设备did
          "did1":"left",
          "did2":"right"
      }
  }
]

失败时：返回
{ "code":xxx, "message":"xxx"}  

| Param | Type |
| --- | --- |
| group_did | <code>string</code> | 


* * *

<a name="module_miot/service/smarthome..ISmartHome+getVirtualDeviceCombineStatus"></a>

#### iSmartHome.getVirtualDeviceCombineStatus(param) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
获取虚拟组设备的成组状态，因为成组有可能失败

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10071
成功时（以窗帘组的左右窗帘信息为例）: 返回

{
  code:0,
  data:{
    status:xxx   //status含义: success = "1" faild = "3" initializing = "0",string类型
  }
}

失败时：返回
{ "code":xxx, "message":"xxx"}  

| Param | Type | Description |
| --- | --- | --- |
| param | <code>Object</code> | param.groupDid{string} 组设备的did |


* * *

<a name="module_miot/service/smarthome..ISmartHome+setTempUnit"></a>

#### iSmartHome.setTempUnit() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code>
切换用户米家温度单位信息

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code> - 成功返回参数:
 {"code":0, "message":"ok", "result":""}
 失败返回参数:
{ "code":xxx, "message":"xxx"}  
**Since**: 10055
请求参数：
{
  "configInfos":[
    {
      "key":"xxx",    //string  非空，属性key：1、米家温度单位：miot_temperature_style
      "value":"c"    //string，非空，属性value：1，当 key=miot_temperature_style 时，value取值：摄氏度：c，华氏度：f
     }
   ]
 }  

* * *

<a name="module_miot/service/smarthome..ISmartHome+getTempUnit"></a>

#### iSmartHome.getTempUnit() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code>
获取用户米家温度单位信息

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;array&gt;</code> - 成功返回参数：
 "configInfos":[
  {
    "key":"xxx",     //属性key
    "value":"f"      //属性value： 1、当key=miot_temperature_style时，value值：摄氏度：c，华氏度：f，未设置：空字符串
   }
]
失败时：返回
{ "code":xxx, "message":"xxx"}  
**Since**: 10055
请求参数：
{
   "keys":["xxx"],     //要查询的属性key数组，1、温度单位切换：miot_temperature_style
}  

* * *

<a name="module_miot/service/smarthome..ISmartHome+checkFirmwareAutoUpgradeOpen"></a>

#### iSmartHome.checkFirmwareAutoUpgradeOpen(aDevId) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;{code:xx, data:xx}&gt;</code>
获取是否开启自动升级

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;{code:xx, data:xx}&gt;</code> - 成功
code == 0
失败
code != 0 data 失败详情  
**Since**: 10059  

| Param | Type |
| --- | --- |
| aDevId | <code>string</code> | 


* * *

<a name="module_miot/service/smarthome..ISmartHome+setFirmwareAutoUpgradeSwitch"></a>

#### iSmartHome.setFirmwareAutoUpgradeSwitch(aOpen, aDevId) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;{code:xx, data:xx}&gt;</code>
开启自动升级

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;{code:xx, data:xx}&gt;</code> - 成功
 code == 0
 失败
 code != 0 data 失败详情  
**Since**: 10059  

| Param | Type |
| --- | --- |
| aOpen | <code>bool</code> | 
| aDevId | <code>string</code> | 


* * *

<a name="module_miot/service/smarthome..ISmartHome+getCustomPluginInfo"></a>

#### iSmartHome.getCustomPluginInfo(model) ⇒ <code>object</code>
是否有自研插件

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Returns**: <code>object</code> - 成功时：{ code: 0, data: { hasCustomPlugin: true/false }} //有自研插件是返回true,没有返回false
失败时：{ code : -1, message: 'xxxxx'}  
**Since**: 10060  

| Param | Type |
| --- | --- |
| model | <code>string</code> | 

**Example**  
```js
Service.smarthome.getCustomPluginInfo(Device.model).then(res=>{
   console.log('----res:',JSON.stringify(res));
}).catch(err=>{
   console.log('----err:',JSON.stringify(err));
});
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+getPrivacyChanges"></a>

#### iSmartHome.getPrivacyChanges(version:) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
获取隐私变更的版本，更改信息以及弹窗信息

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code> - 成功时：返回
   {
     "ver":"0002",
     "change_log":"xxx",
     "pop_type":1
   }
 失败时：返回
{ "code":xxx, "message":"xxx" }  
**Since**: 10060  

| Param | Type | Description |
| --- | --- | --- |
| version: | <code>String</code> | 隐私政策版本,没有隐私版本的情况，传空字符串""，默认为"" |


* * *

<a name="module_miot/service/smarthome..ISmartHome+requestAuthForAlexaVoiceService"></a>

#### iSmartHome.requestAuthForAlexaVoiceService(params)
**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10070
设备授权Alexa语音服务 对应文档：https://developer.amazon.com/en-US/docs/alexa/alexa-voice-service/authorize-companion-app.html  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>jsonObject</code> | 传递的jsonObject对象参数 |

**Example**  
```js
let params={
 productId: xx
 productDsn：xx
}
service.smarthome.requestAuthForAlexaVoiceService(params);
 * @returns {object} 成功时，返回：
{ code: 0,
   data: {
    authCode: xx,
    clientId: xx,
    redirectUri: xx
   }
}
失败时，返回：
{ code: -1, message: 'User authorization failed due to an error: xx' }
取消时，返回：
{ code: -2, message: 'Authorization was cancelled prior to completion. To continue, you will need to try logging in again.' }
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+getHomeList"></a>

#### iSmartHome.getHomeList(params) ⇒ <code>object</code>
当前账号下的所有家庭列表

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Returns**: <code>object</code> - 返回格式：
{ code: 0,
  data: [
    {homeId:xx, homeName:xx, isOwner:true/false},
     ...
  ]
}  
**Since**: 10078  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| params | <code>object</code> | <code></code> | 预留参数 |

**Example**  
```js
Service.smarthome.getHomeList().then(res=>{
 console.log("res:",JSON.stringify(res))
}).catch(err=>{
 console.log("err:",JSON.stringify(err))
})
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+getCarManualSceneData"></a>

#### iSmartHome.getCarManualSceneData(params) ⇒ <code>Promise</code>
**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10078
获取车家批量控制场景数据，/business/car_scene/get_manual_scenes  

| Param | Type | Default | Description |
| --- | --- | --- | --- |
| params | <code>object</code> | <code></code> | 预留，接口参数透传 |


* * *

<a name="module_miot/service/smarthome..ISmartHome+updateCarManualSceneData"></a>

#### iSmartHome.updateCarManualSceneData(params) ⇒ <code>Promise</code>
**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Since**: 10078
更新车家批量控制场景数据，/business/car_scene/update_manual_scenes  

| Param | Type | Description |
| --- | --- | --- |
| params | <code>object</code> | 接口参数透传 |

**Example**  
```js
const params = {
    "manualScenes": [
        {
            "homeId": xxxx,
            "sceneId": xxxx
        },
        {
            "homeId": xxxx,
            "sceneId": xxxxxx
        }
    ]
}
```

* * *

<a name="module_miot/service/smarthome..ISmartHome+getConsumableDetails"></a>

#### iSmartHome.getConsumableDetails(param) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code>
获取当前设备的耗材详情信息，如果有插件想用一些信息的可以自己获取
如果插件不用这个信息，可以直接把获取到的数据传给Host.ui.openConsumesPageWithParams方法

**Kind**: instance method of [<code>ISmartHome</code>](#module_miot/service/smarthome..ISmartHome)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Object&gt;</code> - 返回值结构:
{
  "items": [
    {
      "state": 1,     // 设备所在家庭下耗材的整体状态，1.充足： 2：未知  3：不足
      "count": 1,     // 该状态耗材的设备数量
      "ignore_count":0,  // 被忽略的did数量，插件无需关心
      "consumes_data": [  // 这下面才是与这个设备耗材相关的数据,length == 1，因为查询的是当前设备的耗材情况
        {
          "details": [    // 数组，长度为设备耗材类型的数量。比如一个洗衣机，又有洗衣液，又有柔顺剂，那么这个数组的长度就是2，调用Host.ui.openConsumesDetailPage时传入的参数就是这里面的元素
            {
              "id": 45 ，              //耗材id
              "description": "滤芯",  //耗材名称
              "value": "45",         //剩余百分比，比如这里就是耗材还剩余45%可用
              "update_time": 1644991964,   //数据更新时间，秒
              "state": 1,             //耗材状态  1.充足： 2：未知  3：不足 4：耗尽
              "inadeq": "{"val":"5","unit":"percentage","type":"value"}",   //不足 type：value(值)，range(范围)，list(列表)，boolean(布尔值)
              "exhaust": "{"val":"0","unit":"percentage","type":"value"}",   //耗材耗尽的标志位
              "extra_url": "https://m.xiaomiyoupin.com/detail?gid=102955/u0026source=mijia_pc102955",//耗材链接
              "left_time": "1100",//耗材剩余寿命，小时
              "total_life": "11500",   //耗材的平均寿命，小时
              "prop": "prop.filter1_life",   //耗材对应属性
              "consumable_type": "***",   //耗材型号
              "intro":"*****",  //功能介绍
              "type_name": "battery",   //增加耗材类型字段，目前只有battery
              "pic_urls": [          //耗材图片链接
                "***",
                "***"
              ],
              "reset_method": "action.11.1",  //新的重置方法action
              "change_instruction": [    //更换教学
                {
                  "pic_url":"***",
                  "desc":"***"
                }
              ],
              "reset_state": 0  // 是否支持重置：0：不支持，1：旧的重置方法，2：新的重置方法
            }
          ],
          "did": "172362445",
          "model":"****", // 设备的model
          "is_ignore":true,    // 该did的状态是否被忽略，true是被忽略，false没有被忽略
          "is_online":false,   // 判断是否在线
          "name":"***", // 设备名称
          "room_id": "979234672",
          "skip_rpc": true,    //看下面说明
          "ble_gateway": false, // 看下面说明
          "time_stamp":77777, //同一个did下相同状态的耗材的value最新的时间戳
        }
      ]
    }
  ]
}


skip_rpc说明
skip_rpc = true 表示rpc失败，或调用方指定不进行rpc，但缺少上报数据需要rpc获取，且设备在线
ble_gateway说明
当设备model为需要蓝牙网关的model时，返回true  

| Param | Description |
| --- | --- |
| param | 预留 |


* * *

<a name="module_miot/service/smarthome..MemberType"></a>

### miot/service/smarthome~MemberType : <code>object</code>
成员类型

**Kind**: inner namespace of [<code>miot/service/smarthome</code>](#module_miot/service/smarthome)  

* [~MemberType](#module_miot/service/smarthome..MemberType) : <code>object</code>
    * [.Person](#module_miot/service/smarthome..MemberType.Person)
    * [.Pet](#module_miot/service/smarthome..MemberType.Pet)


* * *

<a name="module_miot/service/smarthome..MemberType.Person"></a>

#### MemberType.Person
人

**Kind**: static constant of [<code>MemberType</code>](#module_miot/service/smarthome..MemberType)  

* * *

<a name="module_miot/service/smarthome..MemberType.Pet"></a>

#### MemberType.Pet
宠物

**Kind**: static constant of [<code>MemberType</code>](#module_miot/service/smarthome..MemberType)  

* * *

<a name="module_miot/service/smarthome..UserInfo"></a>

### miot/service/smarthome~UserInfo : <code>Object</code>
**Kind**: inner typedef of [<code>miot/service/smarthome</code>](#module_miot/service/smarthome)  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| uid | <code>number</code> | user id; since 10010 |
| nickName | <code>string</code> | user nick name |
| avatarURL | <code>string</code> | user avatarURL |


* * *

<a name="module_miot/service/smarthome..GPSInfo"></a>

### miot/service/smarthome~GPSInfo
**Kind**: inner typedef of [<code>miot/service/smarthome</code>](#module_miot/service/smarthome)  
**Properties**

| Name | Description |
| --- | --- |
| lng | 经度 |
| lat | 维度 |
| adminArea | 省 |
| countryCode | 国家代号（CN等） |
| locality | 城市 |
| thoroughfare | 街道 |
| language | 语言代号（zh_CN等） |
| subLocality | 区 |


* * *

<a name="module_miot/service/smarthome..WeatherInfo"></a>

### miot/service/smarthome~WeatherInfo
**Kind**: inner typedef of [<code>miot/service/smarthome</code>](#module_miot/service/smarthome)  
**Properties**

| Name | Description |
| --- | --- |
| city | 城市名称 |
| city_id | 城市ID |
| pub_time | 发布时间 |
| aqi | 空气指数 |
| pm25 | PM2.5 |
| pm10 | PM1.0 |
| so2 | 二氧化硫 |
| no2 | 二氧化氮 |
| src | 数据来源，eg：中国环境监测总站 |


* * *

<a name="module_miot/service/smarthome..DeviceVersion"></a>

### miot/service/smarthome~DeviceVersion
设备固件版本信息

**Kind**: inner typedef of [<code>miot/service/smarthome</code>](#module_miot/service/smarthome)  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| isUpdating | <code>boolean</code> | 是否ota升级中 为true时，otaState才有效 |
| isLatest | <code>boolean</code> | 是否是最新版本 |
| isForce | <code>boolean</code> | 是否强制升级 |
| hasNewFirmware | <code>boolean</code> | 是否有新固件 |
| curVersion | <code>string</code> | 当前固件版本 |
| newVersion | <code>string</code> | 新固件版本 |
| description | <code>string</code> | 描述 |
| otaState | <code>OTAState</code> | 设备OTA状态， since 10011 |


* * *

<a name="module_miot/service/smarthome..OTAState"></a>

### miot/service/smarthome~OTAState
设备固件otaState

**Kind**: inner typedef of [<code>miot/service/smarthome</code>](#module_miot/service/smarthome)  
**Since**: 10011  

| Param | Type | Description |
| --- | --- | --- |
| state | <code>string</code> | ota 状态 |
| startTime | <code>number</code> | 开始时间 |
| progress | <code>number</code> | 进度 |
| failedReason | <code>string</code> | 失败原因 |
| failedCode | <code>number</code> | 失败code |


* * *

<a name="module_miot/service/smarthome..MemberPet"></a>

### miot/service/smarthome~MemberPet
**Kind**: inner typedef of [<code>miot/service/smarthome</code>](#module_miot/service/smarthome)  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| id | <code>string</code> | 成员id（必填且不可为空） 标识用户下成员id，若设置小米id则为对应小米id |
| name | <code>string</code> | 名称  成员昵称（必填且不可为空）  不得加入emoji及米家命名标准以外的特殊字符，长度定义为20中文或40个英文 |
| sex | <code>string</code> | 性别  公：male  母：female   未选择：unknown |
| birth | <code>string</code> | 生日  格式：xxxx-xx |
| weight | <code>double</code> | 重量 |
| species | <code>string</code> | 物种 |
| variety | <code>string</code> | 品种 |
| food_cate | <code>string</code> | 食品 |
| active_rate | <code>int</code> | 活跃度 |
| castrated | <code>int</code> | 阉割   定义：-1:否   0:未设定   1:是 |
| special_mark | <code>int</code> | 特殊标志 |


* * *

<a name="module_miot/service/smarthome..MemberPerson"></a>

### miot/service/smarthome~MemberPerson
**Kind**: inner typedef of [<code>miot/service/smarthome</code>](#module_miot/service/smarthome)  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| id | <code>string</code> | 成员id（必填且不可为空） 标识用户下成员id，若设置小米id则为对应小米id |
| name | <code>string</code> | 姓名  成员昵称（必填且不可为空）  不得加入emoji及米家命名标准以外的特殊字符，长度定义为20中文或40个英文 |
| sex | <code>string</code> | 性别  成员性别（必填且不可为空）  男性：male  女性：female  未选择：unknown |
| birth | <code>string</code> | 生日  格式：xxxx-xx-xx |
| height | <code>double</code> | 身高 |
| weight | <code>double</code> | 体重 |
| relation | <code>string</code> | 关系  与主账号关系 |
| icon | <code>string</code> | 预留项，暂不支持设置 |
| xiaomi_id | <code>int</code> | 小米uid |
| region | <code>string</code> | 国家区域 |
| special_mark | <code>int</code> | 特殊标志 |


* * *

