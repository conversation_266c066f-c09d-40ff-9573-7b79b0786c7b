<a name="module_miot/device"></a>

## miot/device
设备相关 API
IDeviceWifi 当前设备网络操作实例对象，用于发送设备网络操作请求等

**Export**: public  
**Doc_name**: 插件网络操作模块  
**Doc_index**: 3  
**Doc_directory**: device  
**Example**  
```js
...
//wifi方法 e.g RPC请求
Device.getDeviceWifi().callMethod('method_name', params)
 .then(res => {//here is the success result})
 .catch(err => {//error happened})
...
其余具体使用请参考具体API文档

名词解释：
云端：特指小米iot云平台，接入小米的设备，都是指接入小米iot云平台的设备，设备一般都可以直接或者间接和iot云平台通讯
本地局域网：指设备和手机在同一个局域网时，手机可直接与设备通讯，读取设备数据，本地局域网时，一般通过udp协议来与设备进行交互
透传：服务端给客户端什么数据，客户端就返回给插件什么数据。而且，callMethod等请求，固件到服务端的数据也是透传：固件给什么数据到服务端，服务端就给什么数据到客户端

 **注意：callMethod，loadProperties等几个直接和设备打交道的方法，排查错误的流程一般为：抓包查看请求参数是否没问题，插件和固件端联调看看固件端是否有收到正确的参数并返回正确的值！**
```

* [miot/device](#module_miot/device)
    * [module.exports](#exp_module_miot/device--module.exports) ⏏
        * _instance_
            * ~~[.deviceID](#module_miot/device--module.exports+deviceID) : <code>string</code>~~
            * [.readDeviceNetWorkInfo(did)](#module_miot/device--module.exports+readDeviceNetWorkInfo) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;NetworkInfo&gt;</code>
            * [.loadProperties(...propNames)](#module_miot/device--module.exports+loadProperties) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Map&gt;</code>
            * [.loadPropertiesFromCloud(...propNames)](#module_miot/device--module.exports+loadPropertiesFromCloud) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Map&gt;</code>
            * [.callMethod(method, args, extraPayload)](#module_miot/device--module.exports+callMethod) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
            * [.callMethodFromCloud(method, args, extraPayload)](#module_miot/device--module.exports+callMethodFromCloud) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
            * [.callMethodFromLocal(method, args, extraPayload)](#module_miot/device--module.exports+callMethodFromLocal) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
            * [.sendKeyFramePayLoad(payload, length, type)](#module_miot/device--module.exports+sendKeyFramePayLoad)
            * [.localPing()](#module_miot/device--module.exports+localPing) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;boolean&gt;</code>
            * [.subscribeMessages(...propertyOrEventNames)](#module_miot/device--module.exports+subscribeMessages) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;EventSubscription&gt;</code>
            * [.getVersion()](#module_miot/device--module.exports+getVersion) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
            * [.startUpgradingFirmware()](#module_miot/device--module.exports+startUpgradingFirmware) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;DeviceVersion&gt;</code>
            * ~~[.setFirmwareUpdateErrDic(message)](#module_miot/device--module.exports+setFirmwareUpdateErrDic) ⇒~~
            * ~~[.setFirmwareNotCheckUpdate(notCheck)](#module_miot/device--module.exports+setFirmwareNotCheckUpdate) ⇒ <code>Promise</code>~~
            * [.checkFirmwareUpdateAndAlert()](#module_miot/device--module.exports+checkFirmwareUpdateAndAlert) ⇒ <code>Promise</code>
            * [.checkIsHomeKitDevice()](#module_miot/device--module.exports+checkIsHomeKitDevice) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;boolean&gt;</code>
            * [.checkHomeKitConnected()](#module_miot/device--module.exports+checkHomeKitConnected) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;boolean&gt;</code>
            * [.bindToHomeKit()](#module_miot/device--module.exports+bindToHomeKit) ⇒ <code>Promise</code>
            * [.requestAuthorizedDeviceListData(model)](#module_miot/device--module.exports+requestAuthorizedDeviceListData) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;DeviceProductInfo&gt;&gt;</code>
            * [.getVirtualDevices(type)](#module_miot/device--module.exports+getVirtualDevices) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;BasicDevice&gt;&gt;</code>
            * [.getRecommendScenes(model, did)](#module_miot/device--module.exports+getRecommendScenes) ⇒ <code>Promise</code>
        * _inner_
            * [~NetworkInfo](#module_miot/device--module.exports..NetworkInfo) : <code>Object</code>
            * [~DeviceVersion](#module_miot/device--module.exports..DeviceVersion)
            * [~DeviceExtra](#module_miot/device--module.exports..DeviceExtra) : <code>Object</code>
            * [~DeviceProductInfo](#module_miot/device--module.exports..DeviceProductInfo) : <code>Object</code>


* * *

<a name="exp_module_miot/device--module.exports"></a>

### module.exports ⏏
**Kind**: Exported interface  

* * *

<a name="module_miot/device--module.exports+deviceID"></a>

#### ~~module.exports.deviceID : <code>string</code>~~
***Deprecated***

获取设备ID，same as Device.deviceID

**Kind**: instance property of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Example**  
```js
import {Device} from 'miot'
...
let did = Device.getDeviceWifi().deviceID
```

* * *

<a name="module_miot/device--module.exports+readDeviceNetWorkInfo"></a>

#### module.exports.readDeviceNetWorkInfo(did) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;NetworkInfo&gt;</code>
实时获取设备的网络信息包括网络强度，此方法一般情况下不走reject

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;NetworkInfo&gt;</code> - resolve：NetworkInfo
     reject：不会走reject  

| Param | Type | Description |
| --- | --- | --- |
| did | <code>String</code> | 设备id |


* * *

<a name="module_miot/device--module.exports+loadProperties"></a>

#### module.exports.loadProperties(...propNames) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Map&gt;</code>
加载属性数据，
内部调用get_prop 方法,Android会依据当前环境选择从本地局域网或者云端获取, iOS因获取不到wifi信息，会默认走云端获取，并将返回数据写成{key:value}格式

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Map&gt;</code> - Map<name, value>  

| Param | Type | Description |
| --- | --- | --- |
| ...propNames | <code>\*</code> | 属性名称，若propNames长度小于一个，则走reject。普通设备传：prop.propertyName, miot-spec设备传prop.siid.piid |

**Example**  
```js
Device.getDeviceWifi().loadProperties("prop.light", "prop.2.1").then(map=>{
 const a = map.get("a")
 const b = map.get("b")
})
```

* * *

<a name="module_miot/device--module.exports+loadPropertiesFromCloud"></a>

#### module.exports.loadPropertiesFromCloud(...propNames) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Map&gt;</code>
强制从云端加载属性数据
内部调用get_prop 方法, 并将返回数据写成{key:value}格式

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Map&gt;</code> - Map<name, value> 同上  

| Param | Type | Description |
| --- | --- | --- |
| ...propNames | <code>\*</code> | 属性名称，若propNames长度小于一个，则走reject |


* * *

<a name="module_miot/device--module.exports+callMethod"></a>

#### module.exports.callMethod(method, args, extraPayload) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
调用设备方法
Android里面，若与设备通信处于同一个 wifi 下会使用局域网直接传输数据，如果不在同一个 wifi 下由云端转发请求。iOS里面，因获取不到wifi信息，一般默认走云端

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - {code:0,result:{},id:""} 透传  

| Param | Type | Description |
| --- | --- | --- |
| method | <code>string</code> | 方法名 |
| args | <code>json</code> | 参数，传输的数据大小由固件决定，一般最大为1K |
| extraPayload | <code>json</code> | 额外参数，根据设备需求设定。在payload数据中设置额外参数，暂时只提供给绿米网关使用，如有需求，请联系米家。 |

**Example**  
```js
Device.getDeviceWifi().callMethod('getProps', [prop1,prop2])
 .then(res => console.log('success:', res))
 .catch(err => console.error('failed:', err))
//对应payload参考：
//{'method': 'getProps', 'params':[prop1,prop2]}

Device.getDeviceWifi().callMethod('getProps', [prop1,prop2], {sid:Device.deviceID, 'key1':'xxxx'})
 .then(res => console.log('success:', res))
 .catch(err => console.error('failed:', err))
//对应payload参考：
//{'method': 'getProps', 'params':[prop1,prop2], 'sid':'xxxxx', 'key1': 'xxxx'}
```

* * *

<a name="module_miot/device--module.exports+callMethodFromCloud"></a>

#### module.exports.callMethodFromCloud(method, args, extraPayload) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
强制通过云端调用设备方法
Android同callMethod函数不在同一个wifi下的情况，iOS一般情况下等于callMethod方法

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 请求成功返回 {code:0,result:{} } 透传  

| Param | Type | Description |
| --- | --- | --- |
| method | <code>string</code> | 方法名 |
| args | <code>json</code> | 参数 |
| extraPayload | <code>json</code> | (API Level 10027新增)额外参数，根据设备需求设定。在payload数据中设置额外参数 |


* * *

<a name="module_miot/device--module.exports+callMethodFromLocal"></a>

#### module.exports.callMethodFromLocal(method, args, extraPayload) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code>
本地调用设备方法，会直接根据设备ip和端口，发送udp请求，直接和设备通讯。**注意：如果不在同一个路由器，rpc会失败，而不会自动的走云端的方法，使用此方法前，可通过下面的localPing去判断是否是同一个局域网**

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;json&gt;</code> - 请求成功返回 {code:0,result:{} } 透传  

| Param | Type | Description |
| --- | --- | --- |
| method | <code>string</code> | 方法名 |
| args | <code>json</code> | 参数 |
| extraPayload | <code>json</code> | (API Level 10027新增)额外参数，根据设备需求设定。在payload数据中设置额外参数 |


* * *

<a name="module_miot/device--module.exports+sendKeyFramePayLoad"></a>

#### module.exports.sendKeyFramePayLoad(payload, length, type)
**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Retun**: <code>Promise&lt;json&gt;</code> 请求成功返回 {code:0,result:{} }
请求失败返回 {code:xxx, message:xxx}  

| Param | Type | Description |
| --- | --- | --- |
| payload | <code>json</code> | 数据格式 @{@"id" : @(id), @"method" : @"method", @"params" : originData, @"other" : other} |
| length | <code>number</code> | 每一帧的长度 |
| type | <code>string</code> | 类型，例如“scene” |


* * *

<a name="module_miot/device--module.exports+localPing"></a>

#### module.exports.localPing() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;boolean&gt;</code>
ping 操作 检查设备本地局域网通信是否可用，如果某个功能需要强制走本地,又不确定它是否在同一个局域网下，可以先调用此方法检查。

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Example**  
```js
Device.getDeviceWifi().localPing()
 .then(res => console.log('success:', res))
 .catch(err => console.log('failed:', err))
```

* * *

<a name="module_miot/device--module.exports+subscribeMessages"></a>

#### module.exports.subscribeMessages(...propertyOrEventNames) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;EventSubscription&gt;</code>
订阅设备消息。指插件端监听设备属性变化或者事件执行的消息。比如：洗衣机洗完衣服了，需要手机发出“嘀嘀”的声音通知用户，我们就可以监听衣服洗完了这个事件。
订阅设备消息的前提是：设备属性变化/设备事件 正确上报。你需要在：https://iot.mi.com/productDetail_new.html#/pushMessage?model=yourmodel 里面正确的配置消息推送，然后固件端实现消息推送协议。最后在客户端使用此方法订阅。
具体使用办法，错误排查和注意点可参考iot平台的设备订阅文档：https://iot.mi.com/new/doc/extension-development/basic-functions/device-subscribe/Wi-Fi

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;EventSubscription&gt;</code> - resolve：EventSubscription 订阅监听
     reject：订阅ID或者空  

| Param | Type | Description |
| --- | --- | --- |
| ...propertyOrEventNames | <code>string</code> | 在开发平台上声明的 prop 与 event 名，注意消息格式为：prop.xxxxx 或者 event.xxxxx ，表示订阅的是设备的属性变化，还是设备的事件响应.如果是miot-spec设备。则为prop.siid.piid，event.siid.eiid |

**Example**  
```js
import {Device, DeviceEvent} from 'miot'
...
//监听 属性变化和事件响应
const listener = DeviceEvent.deviceReceivedMessages.addListener(
(device, messages)=>{
 if(messages.has('prop.color')){
   console.log('获取到属性变化：',messages.get('prop.color'));
    ...
 } else if (messages.has('event.powerOn')){
   console.log('获取到事件响应：',messages.get('event.powerOn'));
   ...
 }
 ...
})
...
  //添加订阅：属性变更和事件响应
let msgSubscription = null;
Device.getDeviceWifi().subscribeMessages('prop.color','event.powerOn')
.then(subcription => {
   //call this when you need to unsubscribe the message
  msgSubscription = subcription;
})
.catch(() => console.log('subscribe failed'))
...

...
//unsubscribe the props
msgSubscription&&msgSubscription.remove();
listener&&listener.remove();
```

* * *

<a name="module_miot/device--module.exports+getVersion"></a>

#### module.exports.getVersion() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code>
获取当前设备固件版本信息。蓝牙设备请不要用此方法，需要用BTDevice.getVersion()方法。
万一为空,你可以使用Service.smarthome.checkDeviceVersion()来获取版本（curVersion字段）。

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;any&gt;</code> - resolve：version
     reject：null  

* * *

<a name="module_miot/device--module.exports+startUpgradingFirmware"></a>

#### module.exports.startUpgradingFirmware() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;DeviceVersion&gt;</code>
升级设备固件.可以和Service.smarthome.getAvailableFirmwareForDids搭配使用，先检查是否有可用版本，如果有，展示信息给用户，让用户确认，或者直接升级。
/home/<USER>

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;DeviceVersion&gt;</code> - {}
     resolve：DeviceVersion
     reject：{code: xxx, message: xxx} 其他code:网络错误/服务端错误  
**Example**  
```js
Device.getDeviceWifi().startUpgradingFirmware()
 .then(res => console.log('success:', res))
 .catch(err => console.log('failed:', err))
```

* * *

<a name="module_miot/device--module.exports+setFirmwareUpdateErrDic"></a>

#### ~~module.exports.setFirmwareUpdateErrDic(message) ⇒~~
***Deprecated***

为设备固件升级失败添加自定义的errorCode与错误提示信息的索引，以便给用户以友好易懂的错误提示，暂时仅供石头扫地机使用。注意 分享过来的设备是无法进行固件升级的，所以此时此方法也无效。
**Android暂未适配，正常情况下请不要使用这种双端不统一的方法**

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Returns**: boolean 设置是否成功  

| Param | Type | Description |
| --- | --- | --- |
| message | <code>json</code> | 以errorCode为key，以错误提示信息为value的字典。key和value的数据类型都须是string。 |

**Example**  
```js
let ret = Device.getDeviceWifi().setFirmwareUpdateErrDic({"3":'无法连接'})
```

* * *

<a name="module_miot/device--module.exports+setFirmwareNotCheckUpdate"></a>

#### ~~module.exports.setFirmwareNotCheckUpdate(notCheck) ⇒ <code>Promise</code>~~
***Deprecated***

设置设备控制页不检查固件升级，避免出现弹框，已废弃。
**Android暂未适配**

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Returns**: <code>Promise</code> - resolve: "set success"
     reject: "not a MHDeviceViewControllerBase"  

| Param | Type | Description |
| --- | --- | --- |
| notCheck | <code>boolean</code> | 是否 不检查更新 true-不自动检查 false-自动检查 |

**Example**  
```js
Device.getDeviceWifi().setFirmwareNotCheckUpdate(true|false)
 .then(res => console.log('success:', res))
 .catch(err => console.log('failed:', err))
```

* * *

<a name="module_miot/device--module.exports+checkFirmwareUpdateAndAlert"></a>

#### module.exports.checkFirmwareUpdateAndAlert() ⇒ <code>Promise</code>
检查wifi设备固件升级弹窗。该方法会触发升级弹窗alert提示。
建议使用场景为需要屏蔽默认的插件启动检测的弹窗，自行寻找合适的时机触发该检测机制。
支持wifi设备，combo设备，zigbee设备
不支持单模蓝牙、组设备、虚拟设备、离线设备、分享设备。蓝牙检查升级请使用Service.smarthome.getLatestVersionV2()。

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Returns**: <code>Promise</code> - resolve：res={needUpgradge,force,upgrading，latestVersion} ,其中：needUpgrade:是否需要升级，force：是否需要强制升级，updrading：是否正在升级，latestVersion：最新版本版本号
     reject：{code: xxx, message: xxx} 401:设备所有者才可升级  其他code:网络错误/服务端错误  
**Example**  
```js
//首先屏蔽默认弹窗
Package.disableAutoCheckUpgrade = true;
//....
//在合适的时间触发
Device.checkFirmwareUpdateAndAlert().then(res => { }).catch(err => { })
```

* * *

<a name="module_miot/device--module.exports+checkIsHomeKitDevice"></a>

#### module.exports.checkIsHomeKitDevice() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;boolean&gt;</code>
检查当前设备是否支持HomeKit，Android系统不支持HomeKit设备。需要在plato平台配置homekit_config，包含在内的设备，isHomekit才可能返回true

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;boolean&gt;</code> - 是否支持  res = true or false  
**Since**: 10021  

* * *

<a name="module_miot/device--module.exports+checkHomeKitConnected"></a>

#### module.exports.checkHomeKitConnected() ⇒ <code>[ &#x27;Promise&#x27; ].&lt;boolean&gt;</code>
检查当前设备是否已经接入了HomeKit，Android不支持。如果没有接入，可以调用下面的bindToHomeKit方法，将设备接入

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;boolean&gt;</code> - 是否接入 res = true or false  
**Since**: 10021  

* * *

<a name="module_miot/device--module.exports+bindToHomeKit"></a>

#### module.exports.bindToHomeKit() ⇒ <code>Promise</code>
将当前设备绑定到HomeKit中
绑定失败部分code：-1:system version 10.0 support hard auth bind or system version 11.3 support soft auth bind

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Returns**: <code>Promise</code> - 成功进入then，失败进入catch resolve ：res = true；reject：res={code:xxx,message:xxx}  
**Since**: 10021  

* * *

<a name="module_miot/device--module.exports+requestAuthorizedDeviceListData"></a>

#### module.exports.requestAuthorizedDeviceListData(model) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;DeviceProductInfo&gt;&gt;</code>
获取当前设备列表中的指定model的设备列表。需要在common_extra_config增加配置，暂时用于秒秒测的互联互通功能。
用途：秒秒测有一个设备互联的功能，比如牙刷可以连电子表，然后电子表上展示倒计时。

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;DeviceProductInfo&gt;&gt;</code> - resolve：设备列表数组
     reject：{message:xxx} 找不到授权的model  无法找到任何设备  
**Since**: 10003  

| Param | Type | Description |
| --- | --- | --- |
| model | <code>string</code> | 设备model |


* * *

<a name="module_miot/device--module.exports+getVirtualDevices"></a>

#### module.exports.getVirtualDevices(type) ⇒ <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;BasicDevice&gt;&gt;</code>
获取虚拟设备的子设备列表，暂时已上线的虚拟设备有：yeelink和philips灯组和窗帘组。其他的暂不支持。注意：Mesh灯组，和灯组2.0，无法通过此接口获取子设备（暂未开放）

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Returns**: <code>[ &#x27;Promise&#x27; ].&lt;Array.&lt;BasicDevice&gt;&gt;</code> - resolve:子设备列表
     reject：{code: xxx, message: xxx}网络错误  
**Since**: 10003。
change on 10046 获取虚拟窗帘组设备的子设备列表
通过传入的type判断返回哪种设备的子设备  yeelink和philips灯组的组设备是N->1，子设备不会出现在设备列表中，但是窗帘设备是N->N+1,所以请求的接口不同,，为兼容旧版，默认type=1  

| Param | Default | Description |
| --- | --- | --- |
| type | <code>1</code> | ===1时表示yeelink和philips灯组的组设备   ===2时表示窗帘组设备 涉及接口：/home/<USER>/v2/groupv2/query_status。可抓包此接口查看，返回的为此接口的数据 使用场景：展示组设备的子设备列表，可通过此接口获取数据 |


* * *

<a name="module_miot/device--module.exports+getRecommendScenes"></a>

#### module.exports.getRecommendScenes(model, did) ⇒ <code>Promise</code>
获取设备定向推荐信息，展示推荐入口使用：用于获取插件上方偶尔弹出的提示条/广告条数据，比如：设备信号差，请调整设备位置。
若需要使用此接口而且不会用，请联系米家的同学。

**Kind**: instance method of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Returns**: <code>Promise</code> - resolve:定向推荐数据。米家的同学可通过下面的wiki查询
     reject: {code: xxx, message: xxx} -1 获取设备定向推荐数据失败  
**Since**: 10024  

| Param | Type |
| --- | --- |
| model | <code>String</code> | 
| did | <code>String</code> | 


* * *

<a name="module_miot/device--module.exports..NetworkInfo"></a>

#### module.exports~NetworkInfo : <code>Object</code>
**Kind**: inner typedef of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| bssid | <code>string</code> | wifi 的mac地址 |
| rssi | <code>number</code> | wifi的原始信号强度，android和iOS为保持一致，不要使用。 |
| ssid | <code>string</code> | wifi 的名称 |
| wifiStrength | <code>number</code> | wifi的信号强度，adnroid和iOS保持一致后的，推荐使用。正常wifi/combo设备返回0-100之间的值，蓝牙设备返回0 |


* * *

<a name="module_miot/device--module.exports..DeviceVersion"></a>

#### module.exports~DeviceVersion
设备固件版本信息

**Kind**: inner typedef of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| isUpdating | <code>boolean</code> | 是否ota升级中 为true时，otaState才有效 |
| isLatest | <code>boolean</code> | 是否是最新版本 |
| isForce | <code>boolean</code> | 是否强制升级 |
| hasNewFirmware | <code>boolean</code> | 是否有新固件 |
| curVersion | <code>string</code> | 当前固件版本 |
| newVersion | <code>string</code> | 新固件版本 |
| description | <code>string</code> | 描述 |


* * *

<a name="module_miot/device--module.exports..DeviceExtra"></a>

#### module.exports~DeviceExtra : <code>Object</code>
**Kind**: inner typedef of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| name | <code>string</code> | 设备名称 |
| did | <code>string</code> | 设备did |
| mac | <code>string</code> | 设备mac地址 |
| share_flag | <code>bool</code> | 设备是否是分享设备 |


* * *

<a name="module_miot/device--module.exports..DeviceProductInfo"></a>

#### module.exports~DeviceProductInfo : <code>Object</code>
**Kind**: inner typedef of [<code>module.exports</code>](#exp_module_miot/device--module.exports)  
**Properties**

| Name | Type | Description |
| --- | --- | --- |
| model | <code>string</code> | 设备model |
| product_name | <code>string</code> | 设备的产品名称 |
| product_icon | <code>string</code> | 设备图标 |
| product_id | <code>string</code> | 设备类别id |
| devices | <code>[ &#x27;Object&#x27; ].&lt;Array.&lt;DeviceExtra&gt;&gt;</code> |  |


* * *

